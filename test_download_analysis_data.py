#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试分析数据下载接口
"""

import requests
import json
import os
import zipfile
import tempfile

def test_download_analysis_data():
    """测试分析数据下载接口"""
    print("🧪 测试分析数据下载接口...")
    
    base_url = "http://127.0.0.1:8091/api/analysis/download-data/"
    
    # 测试数据 - 使用实际存在的文件路径
    test_data = {
        "file_paths": [
            "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp",
            "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp"
        ]
    }
    
    try:
        print(f"\n📤 发送下载请求...")
        print(f"   文件数量: {len(test_data['file_paths'])}")
        for i, path in enumerate(test_data['file_paths'], 1):
            print(f"   文件{i}: {os.path.basename(path)}")
        
        # 发送POST请求
        response = requests.post(base_url, json=test_data, timeout=120)
        
        print(f"\n📊 响应状态: {response.status_code}")
        print(f"📊 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            # 检查是否是ZIP文件
            content_type = response.headers.get('content-type', '')
            if 'application/zip' in content_type:
                print(f"✅ 成功获取ZIP文件")
                
                # 保存ZIP文件到临时位置
                temp_dir = tempfile.mkdtemp()
                zip_path = os.path.join(temp_dir, "downloaded_analysis_data.zip")
                
                with open(zip_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"💾 ZIP文件已保存: {zip_path}")
                print(f"📊 文件大小: {len(response.content)} 字节")
                
                # 验证ZIP文件内容
                verify_zip_content(zip_path)
                
                return True
            else:
                print(f"❌ 响应不是ZIP文件: {content_type}")
                print(f"响应内容: {response.text[:200]}")
                return False
        else:
            print(f"❌ 下载失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def verify_zip_content(zip_path):
    """验证ZIP文件内容"""
    print(f"\n🔍 验证ZIP文件内容...")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            file_list = zipf.namelist()
            print(f"📁 ZIP文件包含 {len(file_list)} 个文件:")
            
            # 按文件夹分组显示
            folders = {}
            for file_name in file_list:
                if '/' in file_name:
                    folder = file_name.split('/')[0]
                    if folder not in folders:
                        folders[folder] = []
                    folders[folder].append(file_name.split('/')[-1])
                else:
                    if 'root' not in folders:
                        folders['root'] = []
                    folders['root'].append(file_name)
            
            for folder, files in folders.items():
                print(f"  📂 {folder}:")
                for file_name in files:
                    print(f"    📄 {file_name}")
            
            # 验证SHP文件组的完整性
            verify_shapefile_completeness(folders)
            
    except Exception as e:
        print(f"❌ ZIP文件验证失败: {e}")

def verify_shapefile_completeness(folders):
    """验证SHP文件组的完整性"""
    print(f"\n🔍 验证SHP文件组完整性...")
    
    for folder, files in folders.items():
        if folder == 'root':
            continue
            
        print(f"  📂 检查文件夹: {folder}")
        
        # 找出所有SHP文件的基础名称
        shp_bases = set()
        for file_name in files:
            if file_name.endswith('.shp'):
                base_name = file_name[:-4]  # 移除.shp扩展名
                shp_bases.add(base_name)
        
        print(f"    发现 {len(shp_bases)} 个SHP文件组")
        
        for base_name in shp_bases:
            print(f"    🔍 检查SHP文件组: {base_name}")
            
            # 检查必需文件
            required_extensions = ['.shp', '.shx', '.dbf']
            optional_extensions = ['.prj', '.cpg', '.sbn', '.sbx', '.shp.xml']
            
            required_files = []
            optional_files = []
            
            for ext in required_extensions:
                file_name = base_name + ext
                if file_name in files:
                    required_files.append(file_name)
                    print(f"      ✅ {file_name} (必需)")
                else:
                    print(f"      ❌ {file_name} (必需，缺失)")
            
            for ext in optional_extensions:
                file_name = base_name + ext
                if file_name in files:
                    optional_files.append(file_name)
                    print(f"      ✅ {file_name} (可选)")
            
            # 判断SHP文件组是否完整
            if len(required_files) == len(required_extensions):
                print(f"      🎉 SHP文件组完整 ({len(required_files)} 必需 + {len(optional_files)} 可选)")
            else:
                print(f"      ⚠️ SHP文件组不完整，缺少必需文件")

def test_path_conversion():
    """测试路径转换功能"""
    print(f"\n🧪 测试路径转换功能...")
    
    test_paths = [
        "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/test.shp",
        "C:/Data/ODM/Output/test.shp",
        "/home/<USER>/data/ODM/results/test.shp",
        "E:/Projects/GIS/ODM/analysis/test.shp"
    ]
    
    expected_urls = [
        "http://127.0.0.1:81/ODM/AI/20250705171600/arableLand/test.shp",
        "http://127.0.0.1:81/ODM/Output/test.shp",
        "http://127.0.0.1:81/ODM/results/test.shp",
        "http://127.0.0.1:81/ODM/analysis/test.shp"
    ]
    
    print(f"📋 路径转换测试:")
    for i, (path, expected) in enumerate(zip(test_paths, expected_urls), 1):
        print(f"  {i}. 原始路径: {path}")
        print(f"     期望URL: {expected}")
        # 这里只是展示预期结果，实际转换在服务器端进行

def test_error_handling():
    """测试错误处理"""
    print(f"\n🧪 测试错误处理...")
    
    base_url = "http://127.0.0.1:8091/api/analysis/download-data/"
    
    test_cases = [
        {
            'name': '缺少参数',
            'data': {},
            'expected_status': 400
        },
        {
            'name': '空文件列表',
            'data': {'file_paths': []},
            'expected_status': 400
        },
        {
            'name': '无效路径格式',
            'data': {'file_paths': ['invalid_path_without_odm.shp']},
            'expected_status': 400
        },
        {
            'name': '参数类型错误',
            'data': {'file_paths': 'not_a_list'},
            'expected_status': 400
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            print(f"  {i}. 测试: {test_case['name']}")
            
            response = requests.post(base_url, json=test_case['data'], timeout=30)
            
            expected_status = test_case['expected_status']
            actual_status = response.status_code
            
            if actual_status == expected_status:
                print(f"     ✅ 状态码正确: {actual_status}")
                
                try:
                    error_data = response.json()
                    print(f"     📝 错误消息: {error_data.get('message', 'N/A')}")
                except:
                    pass
            else:
                print(f"     ❌ 状态码错误: {actual_status} (期望: {expected_status})")
                
        except Exception as e:
            print(f"     ❌ 测试异常: {e}")

def main():
    """主函数"""
    print("🚀 开始测试分析数据下载接口")
    
    print(f"\n📝 功能说明:")
    print(f"1. 📁 路径转换: 动态识别ODM目录，转换为HTTP URL")
    print(f"2. 📥 文件下载: 下载完整的SHP文件组")
    print(f"3. 📦 数据打包: 将所有文件打包为ZIP文件")
    print(f"4. 🔄 批量处理: 支持多个文件的批量下载")
    
    # 测试路径转换
    test_path_conversion()
    
    # 测试错误处理
    test_error_handling()
    
    # 测试主要功能
    success = test_download_analysis_data()
    
    if success:
        print(f"\n🎉 分析数据下载接口测试通过！")
        
        print(f"\n📖 使用说明:")
        print(f"1. 接口地址: POST /api/analysis/download-data/")
        print(f"2. 请求格式: JSON，包含file_paths数组")
        print(f"3. 返回格式: ZIP文件下载")
        print(f"4. 文件结构: analysis_data_{{timestamp}}/文件名")
        print(f"5. 支持格式: 完整的SHP文件组")
        
    else:
        print(f"\n❌ 分析数据下载接口测试失败")
        print(f"请检查:")
        print(f"1. Django服务器是否正常运行")
        print(f"2. 127.0.0.1:81文件服务是否可用")
        print(f"3. 测试文件路径是否存在")
        print(f"4. 网络连接是否正常")

if __name__ == "__main__":
    main()
