2025-08-29 16:57:01 - INFO - === 合并分析任务开始 ===
2025-08-29 16:57:01 - INFO - 任务ID: 57712686-0a19-41a4-a2c2-1f22634e8013
2025-08-29 16:57:01 - INFO - [UPDATE] TaskInfo.json已由队列管理器创建，跳过重复创建
2025-08-29 16:57:01 - INFO - 影像ID: 20250705171599
2025-08-29 16:57:01 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-29 16:57:01 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-29 16:57:01 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-29 16:57:01 - INFO - 分析类别: arableLand
2025-08-29 16:57:01 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 16:57:01 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_2_1756457821.shp
2025-08-29 16:57:01 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171599
2025-08-29 16:57:01 - INFO - === 开始执行合并分析任务 ===
2025-08-29 16:57:01 - WARNING - [WARNING] TaskInfo路径丢失，尝试重新构建...
2025-08-29 16:57:01 - INFO - [LOCATION] 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-29 16:57:01 - INFO - [UPDATE] 更新字段: status = 进行中
2025-08-29 16:57:01 - INFO - [UPDATE] 更新字段: message = 开始AI语义分割...
2025-08-29 16:57:02 - INFO - [UPDATE] 更新字段: progress = 5
2025-08-29 16:57:02 - INFO - [UPDATE] TaskInfo.json已更新: ['status', 'message', 'progress']
2025-08-29 16:57:02 - INFO - [SEARCH] 验证更新结果:
2025-08-29 16:57:02 - INFO -   状态: 进行中
2025-08-29 16:57:02 - INFO -   进度: 5%
2025-08-29 16:57:02 - INFO -   AI处理时间: None
2025-08-29 16:57:02 - INFO -   成功状态: None
2025-08-29 16:57:02 - INFO -   空间统计:
2025-08-29 16:57:02 - INFO -     area_threshold: 400.0
2025-08-29 16:57:02 - INFO - [AI] 开始AI语义分割...
2025-08-29 16:57:02 - INFO - [DIR] 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-29 16:57:02 - INFO - [AI] 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-29 16:57:02 - INFO - [FOLDER] 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 16:57:02 - INFO - [CONFIG] 模型类型: deeplabv3_plus
2025-08-29 16:57:02 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-29 16:57:02 - INFO - 🔍 检测到合并分析任务: image_id=20250705171599, category=arableLand
2025-08-29 16:57:02 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171599/TaskInfo.json
2025-08-29 16:57:02 - INFO - ✅ 使用真实任务ID: 57712686-0a19-41a4-a2c2-1f22634e8013
2025-08-29 16:57:02 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-29 16:57:02 - INFO - 📄 读取现有TaskInfo文件...
2025-08-29 16:57:02 - INFO - 📋 读取到数组格式，包含 3 个历史任务
2025-08-29 16:57:02 - INFO - 🔄 更新现有任务记录: 57712686-0a19-41a4-a2c2-1f22634e8013
2025-08-29 16:57:02 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-29 16:57:02 - INFO - 📊 文件验证: 大小=8091字节，任务总数: 3
2025-08-29 16:57:05 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-29 16:57:05 - INFO - 🔧 创建处理参数:
2025-08-29 16:57:05 - INFO -   模型类型: deeplabv3_plus
2025-08-29 16:57:05 - INFO -   类别数量: 2
2025-08-29 16:57:05 - INFO -   目标类别: [1]
2025-08-29 16:57:05 - INFO -   窗口大小: 512
2025-08-29 16:57:05 - INFO -   批处理大小: 16
2025-08-29 16:57:05 - INFO -   重叠比例: 0.5
2025-08-29 16:57:05 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-29 16:57:05 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-29 16:57:05 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-29 16:57:05 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 16:57:05 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-29 16:57:05 - INFO - 🔄 开始AI模型推理...
2025-08-29 16:57:05 - INFO - 
==================================================
2025-08-29 16:57:05 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-29 16:57:05 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 16:57:05 - INFO - ==================================================
2025-08-29 16:57:05 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-29 16:57:05 - INFO - 📏 图像大小: 5683x5165, 波段数: 3
2025-08-29 16:57:05 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-29 16:57:05 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-29 16:57:05 - INFO - 🔢 类别数量: 2
2025-08-29 16:57:05 - INFO - 🔄 重叠区域: 37 像素
2025-08-29 16:57:05 - INFO - 📦 批处理大小: 16
2025-08-29 16:57:05 - INFO - 💻 计算设备: cuda:0
2025-08-29 16:57:09 - INFO - 读取tif并裁剪预处理完毕,目前耗时间: 4s
2025-08-29 16:57:09 - INFO - 🔧 创建模型...
2025-08-29 16:57:11 - INFO - ✅ 模型创建完成
2025-08-29 16:57:11 - INFO - 🧠 开始模型预测...
2025-08-29 16:57:27 - INFO - ✅ AI处理成功完成，耗时: 22.00秒
2025-08-29 16:57:27 - INFO - 🔄 更新TaskInfo.json: AI处理完成，成功=True
2025-08-29 16:57:27 - INFO - ✅ 找到并更新任务记录: 57712686-0a19-41a4-a2c2-1f22634e8013
2025-08-29 16:57:27 - INFO - 📝 TaskInfo.json已更新: AI处理时间=22秒
2025-08-29 16:57:27 - INFO - AI语义分割完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 16:57:27 - INFO - [PROCESS] 更新TaskInfo.json - AI分割完成
2025-08-29 16:57:27 - INFO - [UPDATE] 更新字段: status = 进行中
2025-08-29 16:57:27 - INFO - [UPDATE] 更新字段: message = AI语义分割完成，开始后续处理
2025-08-29 16:57:27 - INFO - [UPDATE] 更新字段: progress = 40
2025-08-29 16:57:27 - INFO - [UPDATE] 更新嵌套字段: results.ai_processing_time = 22
2025-08-29 16:57:27 - INFO - [UPDATE] TaskInfo.json已更新: ['status', 'message', 'progress', 'results.ai_processing_time']
2025-08-29 16:57:27 - INFO - [SEARCH] 验证更新结果:
2025-08-29 16:57:27 - INFO -   状态: 进行中
2025-08-29 16:57:27 - INFO -   进度: 40%
2025-08-29 16:57:27 - INFO -   AI处理时间: 22
2025-08-29 16:57:27 - INFO -   成功状态: True
2025-08-29 16:57:27 - INFO -   空间统计:
2025-08-29 16:57:27 - INFO -     area_threshold: 400.0
2025-08-29 16:57:27 - INFO - [SUCCESS] TaskInfo.json更新完成 - AI分割阶段
2025-08-29 16:57:27 - INFO - [UPDATE] 更新字段: message = 提取影像有效范围...
2025-08-29 16:57:27 - INFO - [UPDATE] 更新字段: progress = 45
2025-08-29 16:57:27 - INFO - [UPDATE] TaskInfo.json已更新: ['message', 'progress']
2025-08-29 16:57:27 - INFO - [SEARCH] 验证更新结果:
2025-08-29 16:57:27 - INFO -   状态: 进行中
2025-08-29 16:57:27 - INFO -   进度: 45%
2025-08-29 16:57:27 - INFO -   AI处理时间: 22
2025-08-29 16:57:27 - INFO -   成功状态: True
2025-08-29 16:57:27 - INFO -   空间统计:
2025-08-29 16:57:27 - INFO -     area_threshold: 400.0
2025-08-29 16:57:27 - INFO - [MAP] 开始提取影像有效范围...
2025-08-29 16:57:27 - INFO - 范围文件输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_area_1756457821.shp
2025-08-29 16:57:27 - INFO - 分析类别目录: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand
2025-08-29 16:57:27 - INFO - 调用影像范围提取: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif -> D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_area_1756457821.shp
2025-08-29 16:57:29 - INFO - [SUCCESS] 影像范围提取成功
2025-08-29 16:57:29 - INFO - 输出文件: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_area_1756457821.shp
2025-08-29 16:57:29 - INFO - 有效区域数量: 1
2025-08-29 16:57:29 - INFO - 坐标系: WGS 84 / UTM zone 48N (EPSG:32648)
2025-08-29 16:57:29 - INFO - 影像范围提取完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_area_1756457821.shp
2025-08-29 16:57:29 - INFO - [UPDATE] 更新字段: message = 开始空间变化分析...
2025-08-29 16:57:29 - INFO - [UPDATE] 更新字段: progress = 60
2025-08-29 16:57:29 - INFO - [UPDATE] TaskInfo.json已更新: ['message', 'progress']
2025-08-29 16:57:29 - INFO - [SEARCH] 验证更新结果:
2025-08-29 16:57:29 - INFO -   状态: 进行中
2025-08-29 16:57:29 - INFO -   进度: 60%
2025-08-29 16:57:29 - INFO -   AI处理时间: 22
2025-08-29 16:57:29 - INFO -   成功状态: True
2025-08-29 16:57:29 - INFO -   空间统计:
2025-08-29 16:57:29 - INFO -     area_threshold: 400.0
2025-08-29 16:57:29 - INFO - [STATS] 开始空间变化分析...
2025-08-29 16:57:29 - INFO - [FOLDER] 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-29 16:57:29 - INFO - [FOLDER] 新数据: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 16:57:29 - INFO - [MEASURE] 面积阈值: 400.0 平方米
2025-08-29 16:57:29 - INFO - 使用裁剪范围: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_area_1756457821.shp
2025-08-29 17:05:28 - INFO - [SUCCESS] 空间变化分析完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_2_1756457821.shp
2025-08-29 17:05:28 - INFO - [STATS] 获取到的空间分析统计信息: {'outflow_count': 0, 'inflow_count': 3, 'total_count': 3, 'outflow_area': 0, 'inflow_area': 59459.27, 'area_threshold': 400.0}
2025-08-29 17:05:28 - INFO - [PROCESS] 开始更新空间统计字段...
2025-08-29 17:05:28 - INFO -   添加字段: results.spatial_statistics.outflow_count = 0
2025-08-29 17:05:28 - INFO -   添加字段: results.spatial_statistics.inflow_count = 3
2025-08-29 17:05:28 - INFO -   添加字段: results.spatial_statistics.total_count = 3
2025-08-29 17:05:28 - INFO -   添加字段: results.spatial_statistics.outflow_area = 0
2025-08-29 17:05:28 - INFO -   添加字段: results.spatial_statistics.inflow_area = 59459.27
2025-08-29 17:05:28 - INFO -   添加字段: results.spatial_statistics.area_threshold = 400.0
2025-08-29 17:05:28 - INFO - [PROCESS] 准备更新TaskInfo.json，更新字段: ['status', 'message', 'progress', 'end_time', 'results.success', 'results.spatial_statistics.outflow_count', 'results.spatial_statistics.inflow_count', 'results.spatial_statistics.total_count', 'results.spatial_statistics.outflow_area', 'results.spatial_statistics.inflow_area', 'results.spatial_statistics.area_threshold']
2025-08-29 17:05:28 - INFO - [UPDATE] 更新字段: status = 完成
2025-08-29 17:05:28 - INFO - [UPDATE] 更新字段: message = 合并分析任务完成
2025-08-29 17:05:28 - INFO - [UPDATE] 更新字段: progress = 100
2025-08-29 17:05:28 - INFO - [UPDATE] 更新字段: end_time = 2025-08-29T17:05:28.012065
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.success = True
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.spatial_statistics.outflow_count = 0
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.spatial_statistics.inflow_count = 3
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.spatial_statistics.total_count = 3
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.spatial_statistics.outflow_area = 0
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.spatial_statistics.inflow_area = 59459.27
2025-08-29 17:05:28 - INFO - [UPDATE] 更新嵌套字段: results.spatial_statistics.area_threshold = 400.0
2025-08-29 17:05:28 - INFO - [UPDATE] TaskInfo.json已更新: ['status', 'message', 'progress', 'end_time', 'results.success', 'results.spatial_statistics.outflow_count', 'results.spatial_statistics.inflow_count', 'results.spatial_statistics.total_count', 'results.spatial_statistics.outflow_area', 'results.spatial_statistics.inflow_area', 'results.spatial_statistics.area_threshold']
2025-08-29 17:05:28 - INFO - [SEARCH] 验证更新结果:
2025-08-29 17:05:28 - INFO -   状态: 完成
2025-08-29 17:05:28 - INFO -   进度: 100%
2025-08-29 17:05:28 - INFO -   AI处理时间: 22
2025-08-29 17:05:28 - INFO -   成功状态: True
2025-08-29 17:05:28 - INFO -   空间统计:
2025-08-29 17:05:28 - INFO -     outflow_count: 0
2025-08-29 17:05:28 - INFO -     inflow_count: 3
2025-08-29 17:05:28 - INFO -     total_count: 3
2025-08-29 17:05:28 - INFO -     outflow_area: 0
2025-08-29 17:05:28 - INFO -     inflow_area: 59459.27
2025-08-29 17:05:28 - INFO -     area_threshold: 400.0
2025-08-29 17:05:28 - INFO - [SUCCESS] TaskInfo.json最终更新完成
2025-08-29 17:05:28 - INFO - [PUBLISH] 开始自动发布到GeoServer...
2025-08-29 17:05:28 - INFO - [PUBLISH] 开始自动发布分析结果到GeoServer
2025-08-29 17:05:28 - INFO - [STATS] 发布参数:
2025-08-29 17:05:28 - INFO -   工作区: arableLand
2025-08-29 17:05:28 - INFO -   坐标系: EPSG:32648
2025-08-29 17:05:28 - INFO -   AI结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 17:05:28 - INFO -   最终结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_2_1756457821.shp
2025-08-29 17:05:28 - INFO - [UPLOAD] 发布AI分析结果...
2025-08-29 17:05:28 - INFO - [UPLOAD] 发布AI结果尝试 1/3...
2025-08-29 17:05:29 - INFO - [SUCCESS] AI结果发布成功: arableLand:20250705171599_1_1756457821
2025-08-29 17:05:29 - INFO - [UPLOAD] 发布最终分析结果...
2025-08-29 17:05:29 - INFO - [UPLOAD] 发布最终结果尝试 1/3...
2025-08-29 17:05:29 - INFO - [SUCCESS] 最终结果发布成功: arableLand:20250705171599_2_1756457821
2025-08-29 17:05:29 - INFO - [STATS] GeoServer发布总结:
2025-08-29 17:05:29 - INFO -   AI结果: [SUCCESS] 成功
2025-08-29 17:05:29 - INFO -   最终结果: [SUCCESS] 成功
2025-08-29 17:05:29 - INFO -   总体状态: [SUCCESS] 成功
2025-08-29 17:05:29 - INFO - [COMPLETE] 所有分析结果已成功发布到GeoServer
2025-08-29 17:05:29 - INFO - [UPDATE] 更新字段: geoserver_publish = {'ai_result': {'success': True, 'message': 'Shapefile发布成功 (EPSG:32648)', 'layer_name': '20250705171599_1_1756457821', 'store_name': '20250705171599_1_1756457821_store', 'details': {'file_path': 'D:/Drone_Project/nginxData\\ODM\\AI\\20250705171599\\arableLand\\20250705171599_1_1756457821.shp', 'workspace': 'arableLand', 'store': '20250705171599_1_1756457821_store', 'layer': '20250705171599_1_1756457821', 'charset': 'UTF-8', 'crs': 'EPSG:32648', 'replaced_existing': False}}, 'final_result': {'success': True, 'message': 'Shapefile发布成功 (EPSG:32648)', 'layer_name': '20250705171599_2_1756457821', 'store_name': '20250705171599_2_1756457821_store', 'details': {'file_path': 'D:/Drone_Project/nginxData\\ODM\\AI\\20250705171599\\arableLand\\20250705171599_2_1756457821.shp', 'workspace': 'arableLand', 'store': '20250705171599_2_1756457821_store', 'layer': '20250705171599_2_1756457821', 'charset': 'UTF-8', 'crs': 'EPSG:32648', 'replaced_existing': False}}, 'overall_success': True, 'workspace': 'arableLand', 'epsg': '32648', 'publish_time': 1756458328.065114}
2025-08-29 17:05:29 - INFO - [UPDATE] TaskInfo.json已更新: ['geoserver_publish']
2025-08-29 17:05:29 - INFO - [SEARCH] 验证更新结果:
2025-08-29 17:05:29 - INFO -   状态: 完成
2025-08-29 17:05:29 - INFO -   进度: 100%
2025-08-29 17:05:29 - INFO -   AI处理时间: 22
2025-08-29 17:05:29 - INFO -   成功状态: True
2025-08-29 17:05:29 - INFO -   空间统计:
2025-08-29 17:05:29 - INFO -     outflow_count: 0
2025-08-29 17:05:29 - INFO -     inflow_count: 3
2025-08-29 17:05:29 - INFO -     total_count: 3
2025-08-29 17:05:29 - INFO -     outflow_area: 0
2025-08-29 17:05:29 - INFO -     inflow_area: 59459.27
2025-08-29 17:05:29 - INFO -     area_threshold: 400.0
2025-08-29 17:05:29 - INFO - [COMPLETE] 合并分析任务完成，已成功发布到GeoServer
2025-08-29 17:05:29 - INFO - === 合并分析任务完成 ===
2025-08-29 17:05:29 - INFO - AI分析结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_1_1756457821.shp
2025-08-29 17:05:29 - INFO - 最终分析结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\arableLand\20250705171599_2_1756457821.shp
2025-08-29 17:05:29 - INFO - GeoServer发布状态: [SUCCESS] 成功
2025-08-29 17:05:29 - INFO - TaskInfo.json已更新完成
