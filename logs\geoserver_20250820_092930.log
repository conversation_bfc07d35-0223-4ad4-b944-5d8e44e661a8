2025-08-20 09:29:30,221 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_092930.log
2025-08-20 09:29:30,222 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:29:30,223 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:29:30,241 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:29:30,246 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:29:30,247 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:29:30,258 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:29:30,260 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:29:30,261 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:29:30,270 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:29:30,272 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:29:30,272 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:29:30,283 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:29:30,285 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:29:30,285 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:29:30,297 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:29:30,298 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:29:30,299 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:29:30,309 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:29:30,324 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 09:29:30,328 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:29:34,208 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 09:29:34,427 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 09:29:34,441 - analysis_executor - INFO - 加载了 28 个任务状态
