2025-08-28 17:24:54,927 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_172454.log
2025-08-28 17:24:54,930 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:54,931 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:54,949 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:54,952 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:54,953 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:54,988 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,000 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,001 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,014 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,016 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,017 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,031 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,033 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,033 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,046 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,048 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,049 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,061 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,083 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:24:55,088 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:24:55,248 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:24:55,262 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:24:55,266 - analysis_executor - INFO - 加载了 28 个任务状态
