#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API集成 - 模拟实际的API调用场景
"""

import os
import sys
import django
import requests
import json

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_django.settings')
    django.setup()

def test_large_image_detection():
    """测试大图像检测功能"""
    print("=== 测试大图像检测功能 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        
        processor = LargeImageProcessor()
        
        # 测试问题图像的大小
        height, width = 67180, 56283
        
        print(f"测试图像大小: {height}x{width}")
        
        # 检查是否需要分块
        needs_tiling = processor.should_use_tiling(height, width, 3)
        print(f"需要分块处理: {needs_tiling}")
        
        if needs_tiling:
            # 计算网格大小
            grid_rows, grid_cols = processor.calculate_grid_size(height, width, 3)
            print(f"推荐网格大小: {grid_rows}x{grid_cols}")
            
            # 计算块信息
            blocks = processor.calculate_block_bounds(height, width, grid_rows, grid_cols)
            print(f"总块数: {len(blocks)}")
            
            if blocks:
                first_block = blocks[0]
                print(f"第一个块大小: {first_block['ext_height']}x{first_block['ext_width']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 大图像检测失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_api_endpoint():
    """测试API端点是否可访问"""
    print("\n=== 测试API端点 ===")
    
    try:
        # 检查Django服务器是否运行
        test_url = "http://127.0.0.1:8091/api/"
        
        print(f"尝试访问: {test_url}")
        
        response = requests.get(test_url, timeout=5)
        
        if response.status_code == 200:
            print("✅ Django服务器正在运行")
            return True
        else:
            print(f"⚠️ Django服务器响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("⚠️ Django服务器未运行")
        print("请先启动服务器: python manage.py runserver 0.0.0.0:8091")
        return False
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def simulate_large_image_request():
    """模拟大图像处理请求"""
    print("\n=== 模拟大图像处理请求 ===")
    
    # 模拟请求参数
    request_data = {
        "id": "test_large_image_20250829",
        "image_path": "D:/test_data/large_image_67180x56283.tif",
        "model_path": "D:/models/deeplabv3_plus_best.pth",
        "old_data_path": "D:/test_data/old_data.shp",
        "analysis_category": "arableLand",
        "area_threshold": 400.0,
        "model_type": "deeplabv3_plus",
        "num_classes": 2
    }
    
    print("模拟请求参数:")
    for key, value in request_data.items():
        print(f"  {key}: {value}")
    
    # 检查大图像处理逻辑
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        
        processor = LargeImageProcessor()
        
        # 模拟图像大小检查
        height, width = 67180, 56283
        needs_tiling = processor.should_use_tiling(height, width, 3)
        
        print(f"\n处理策略:")
        if needs_tiling:
            print("✅ 将使用分块处理模式")
            grid_rows, grid_cols = processor.calculate_grid_size(height, width, 3)
            print(f"✅ 网格大小: {grid_rows}x{grid_cols}")
            print(f"✅ 预计处理 {grid_rows * grid_cols} 个块")
        else:
            print("✅ 将使用直接处理模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟请求失败: {e}")
        return False

def check_integration_status():
    """检查集成状态"""
    print("\n=== 检查集成状态 ===")
    
    status = {
        "large_image_processor": False,
        "django_integration": False,
        "api_ready": False
    }
    
    # 检查LargeImageProcessor
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        processor = LargeImageProcessor()
        status["large_image_processor"] = True
        print("✅ LargeImageProcessor可用")
    except Exception as e:
        print(f"❌ LargeImageProcessor不可用: {e}")
    
    # 检查Django集成
    try:
        setup_django()
        status["django_integration"] = True
        print("✅ Django集成正常")
    except Exception as e:
        print(f"❌ Django集成失败: {e}")
    
    # 检查API就绪状态
    if status["large_image_processor"] and status["django_integration"]:
        status["api_ready"] = True
        print("✅ API就绪，可以处理大图像")
    else:
        print("❌ API未就绪")
    
    return status

def main():
    """主函数"""
    print("API集成测试")
    print("="*50)
    
    # 设置Django环境
    try:
        setup_django()
        print("✅ Django环境设置成功")
    except Exception as e:
        print(f"❌ Django环境设置失败: {e}")
        return
    
    # 运行测试
    tests = [
        ("大图像检测", test_large_image_detection),
        ("API端点", test_api_endpoint),
        ("模拟请求", simulate_large_image_request),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*30}")
        print(f"运行测试: {test_name}")
        print(f"{'='*30}")
        
        success = test_func()
        results.append((test_name, success))
    
    # 检查集成状态
    print(f"\n{'='*30}")
    print("集成状态检查")
    print(f"{'='*30}")
    status = check_integration_status()
    
    # 显示结果
    print(f"\n{'='*50}")
    print("测试结果汇总:")
    print(f"{'='*50}")
    
    for test_name, success in results:
        status_icon = "✅" if success else "❌"
        print(f"{status_icon} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    # 最终状态
    if status["api_ready"]:
        print("\n🎉 大图像处理功能已集成成功！")
        print("\n现在当你调用以下API时：")
        print("POST /api/analysis/queued-combined-ai-spatial-analysis")
        print("\n系统会自动：")
        print("1. 检测图像大小")
        print("2. 如果图像过大，自动启用分块处理")
        print("3. 将大图像分割为小块")
        print("4. 逐个处理小块")
        print("5. 合并结果为完整输出")
        print("\n这样就解决了内存溢出问题！")
    else:
        print("\n⚠️ 集成未完全成功，但基本功能可用。")
        print("建议检查依赖安装和配置。")

if __name__ == '__main__':
    main()
