#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Other模块视图
"""

import os
import json
import logging
import requests
from datetime import datetime
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from config import DJANGO_BASE_URL

# 设置日志
logger = logging.getLogger(__name__)

@api_view(['GET'])
def update_task_map_config(request):
    """
    批量更新所有Input目录下任务的地图配置信息

    功能:
        1. 扫描Input目录下的所有任务目录
        2. 为每个任务目录查找TaskInfo.json文件
        3. 查找所有status为"完成"的任务
        4. 为每个任务自动获取图层范围
        5. 批量更新所有任务的map_config信息

    返回: 批量更新结果
    """
    try:
        logger.info(f"开始批量更新任务地图配置")

        # 1. 扫描Input目录获取所有任务
        all_tasks_info = scan_all_input_tasks()

        if not all_tasks_info:
            return Response({
                'status': 'error',
                'message': 'Input目录下未找到任何TaskInfo.json文件'
            }, status=status.HTTP_404_NOT_FOUND)

        logger.info(f"扫描到 {len(all_tasks_info)} 个任务的TaskInfo.json文件")

        # 2. 扫描Output目录获取所有可用的图层
        available_layers = scan_output_directory()
        logger.info(f"扫描到 {len(available_layers)} 个Output目录")

        # 3. 收集所有已完成的任务
        all_completed_tasks = []
        task_file_mapping = {}  # 记录任务ID到文件路径的映射

        for task_info_data in all_tasks_info:
            task_info_path = task_info_data['file_path']
            task_info = task_info_data['content']

            for task in task_info.get('Task', []):
                if task.get('status') == '完成':
                    task_id = task.get('id')
                    all_completed_tasks.append(task)
                    task_file_mapping[task_id] = task_info_path

        if not all_completed_tasks:
            return Response({
                'status': 'success',
                'message': '没有找到状态为"完成"的任务',
                'data': {
                    'total_task_files': len(all_tasks_info),
                    'completed_tasks': 0,
                    'updated_tasks': 0,
                    'failed_tasks': 0,
                    'available_layers': len(available_layers),
                    'results': []
                }
            })

        logger.info(f"找到 {len(all_completed_tasks)} 个已完成的任务")

        # 5. 批量处理每个已完成的任务
        updated_count = 0
        failed_count = 0
        skipped_count = 0
        results = []
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        updated_files = set()  # 记录需要更新的文件

        for task in all_completed_tasks:
            task_id = task.get('id')
            task_result = {
                'task_id': task_id,
                'status': 'processing'
            }

            try:
                logger.info(f"处理任务: {task_id}")

                # 检查是否已经有bbox信息
                existing_map_config = task.get('map_config', {})
                if existing_map_config and 'bbox' in existing_map_config:
                    logger.info(f"任务 {task_id} 已有bbox信息，跳过")
                    task_result.update({
                        'status': 'skipped',
                        'message': '任务已有bbox配置'
                    })
                    skipped_count += 1
                    results.append(task_result)
                    continue

                # 检查Output目录中是否存在对应的图层
                if task_id not in available_layers:
                    logger.warning(f"Output目录中未找到任务 {task_id} 对应的图层")
                    task_result.update({
                        'status': 'failed',
                        'message': f'Output目录中未找到对应的图层目录'
                    })
                    failed_count += 1
                    results.append(task_result)
                    continue

                # 自动获取图层范围（尝试多个workspace）
                bbox_data = None
                workspaces_to_try = ['testodm', 'geoserver', 'default']

                for workspace in workspaces_to_try:
                    bbox_data = get_layer_bbox(workspace, task_id)
                    if bbox_data:
                        logger.info(f"在workspace '{workspace}' 中找到图层 {task_id}")
                        break

                if not bbox_data:
                    logger.warning(f"无法获取任务 {task_id} 的图层范围")
                    task_result.update({
                        'status': 'failed',
                        'message': f'无法获取图层范围，尝试的workspace: {workspaces_to_try}'
                    })
                    failed_count += 1
                    results.append(task_result)
                    continue

                # 获取现有的map_config或创建新的
                existing_map_config = task.get('map_config', {})

                # 生成主题（如果没有现有主题）
                if not existing_map_config.get('theme'):
                    try:
                        start_time = task.get('startTime', current_time)
                        dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                        theme = f"{dt.year}年{dt.month:02d}月"
                    except:
                        theme = "默认主题"
                else:
                    theme = existing_map_config['theme']

                # 构建或更新map_config数据
                map_config = {
                    "status": existing_map_config.get("status", "完成"),
                    "start_time": existing_map_config.get("start_time", current_time),
                    "end_time": existing_map_config.get("end_time", current_time),
                    "bbox": bbox_data,  # 添加或更新bbox信息
                    "layer_id": existing_map_config.get("layer_id", f"{workspace}:{task_id}"),
                    "theme": theme
                }

                # 保留其他可能存在的字段
                for key, value in existing_map_config.items():
                    if key not in map_config:
                        map_config[key] = value

                # 更新任务的map_config
                task['map_config'] = map_config
                updated_count += 1

                # 记录需要更新的文件
                task_file_path = task_file_mapping[task_id]
                updated_files.add(task_file_path)

                task_result.update({
                    'status': 'success',
                    'message': '地图配置更新成功',
                    'workspace': workspace,
                    'map_config': map_config
                })

                logger.info(f"成功更新任务 {task_id} 的地图配置")

            except Exception as e:
                logger.error(f"处理任务 {task_id} 时出错: {str(e)}")
                task_result.update({
                    'status': 'failed',
                    'message': f'处理异常: {str(e)}'
                })
                failed_count += 1

            results.append(task_result)

        # 6. 保存更新后的TaskInfo.json文件
        if updated_count > 0:
            for task_info_data in all_tasks_info:
                task_info_path = task_info_data['file_path']
                if task_info_path in updated_files:
                    task_info_content = task_info_data['content']
                    with open(task_info_path, 'w', encoding='utf-8') as f:
                        json.dump(task_info_content, f, ensure_ascii=False, indent=4)
                    logger.info(f"已保存更新后的TaskInfo.json文件: {task_info_path}")

        logger.info(f"批量更新完成: 总计 {len(all_completed_tasks)} 个任务，成功 {updated_count} 个，跳过 {skipped_count} 个，失败 {failed_count} 个")

        return Response({
            'status': 'success',
            'message': f'批量更新完成',
            'data': {
                'total_task_files': len(all_tasks_info),
                'completed_tasks': len(all_completed_tasks),
                'updated_tasks': updated_count,
                'skipped_tasks': skipped_count,
                'failed_tasks': failed_count,
                'available_layers': len(available_layers),
                'updated_time': current_time,
                'results': results
            }
        })

    except json.JSONDecodeError as e:
        logger.error(f"JSON解析错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'TaskInfo.json文件格式错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        logger.error(f"批量更新任务地图配置失败: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

def get_task_info_path():
    """获取TaskInfo.json文件路径 - 扫描Input目录查找"""
    try:
        # 首先尝试通过ODM配置获取基础路径
        config, config_exists = get_odm_config()

        base_paths = []

        if config_exists and 'PATHS' in config and 'window_data_path' in config['PATHS']:
            window_data_path = config['PATHS']['window_data_path']
            base_paths.append(window_data_path)
            logger.info(f"从ODM配置获取基础路径: {window_data_path}")

        # 添加其他可能的基础路径
        base_paths.extend([
            'D:/Drone_Project/nginxData',
            '/opt/airflow/data',
            'D:/Drone_Project/data',
            '/data'
        ])

        # 在每个基础路径下查找TaskInfo.json
        for base_path in base_paths:
            if not os.path.exists(base_path):
                continue

            logger.info(f"扫描基础路径: {base_path}")

            # 1. 直接在基础路径下查找
            direct_path = os.path.join(base_path, 'TaskInfo.json')
            if os.path.exists(direct_path):
                logger.info(f"找到TaskInfo.json文件: {direct_path}")
                return direct_path

            # 2. 在ODM/Input目录下的任务子目录中查找
            odm_input_path = os.path.join(base_path, 'ODM', 'Input')
            if os.path.exists(odm_input_path):
                logger.info(f"扫描ODM Input目录: {odm_input_path}")

                try:
                    for task_dir in os.listdir(odm_input_path):
                        task_path = os.path.join(odm_input_path, task_dir)
                        if os.path.isdir(task_path):
                            task_info_path = os.path.join(task_path, 'TaskInfo.json')
                            if os.path.exists(task_info_path):
                                logger.info(f"在任务目录中找到TaskInfo.json: {task_info_path}")
                                return task_info_path
                except Exception as e:
                    logger.error(f"扫描ODM Input目录时出错: {str(e)}")

            # 3. 在ODM/Output目录下查找
            odm_output_path = os.path.join(base_path, 'ODM', 'Output')
            if os.path.exists(odm_output_path):
                logger.info(f"扫描ODM Output目录: {odm_output_path}")

                try:
                    for task_dir in os.listdir(odm_output_path):
                        task_path = os.path.join(odm_output_path, task_dir)
                        if os.path.isdir(task_path):
                            task_info_path = os.path.join(task_path, 'TaskInfo.json')
                            if os.path.exists(task_info_path):
                                logger.info(f"在输出目录中找到TaskInfo.json: {task_info_path}")
                                return task_info_path
                except Exception as e:
                    logger.error(f"扫描ODM Output目录时出错: {str(e)}")

    except Exception as e:
        logger.error(f"获取TaskInfo.json路径时出错: {str(e)}")

    # 如果都没找到，返回默认路径（用于错误提示）
    default_path = os.path.abspath('D:/Drone_Project/nginxData/ODM/Input/TaskInfo.json')
    logger.warning(f"未找到TaskInfo.json文件，将使用默认路径: {default_path}")

    return default_path

def get_odm_config():
    """
    读取ODM配置文件中的路径配置 - 与ODM模块保持一致

    返回: (配置字典, 是否成功)
    """
    try:
        # 从ODM服务器获取配置文件
        url = f"http://127.0.0.1:81/ODM/Task.cfg"
        headers = {"Accept": "text/plain"}

        response = requests.get(url, headers=headers, timeout=30)

        if response.status_code == 200:
            # 解析配置文件内容
            config_content = response.text
            config = {}
            current_section = None

            for line in config_content.splitlines():
                line = line.strip()
                if not line or line.startswith("#"):
                    continue

                # 解析节名
                if line.startswith("[") and line.endswith("]"):
                    current_section = line[1:-1]
                    config[current_section] = {}
                    continue

                # 解析键值对
                if "=" in line and current_section:
                    key, value = line.split("=", 1)
                    config[current_section][key.strip()] = value.strip()

            logger.info(f"成功读取ODM配置")
            return config, True
        else:
            logger.error(f"获取ODM配置文件失败, 状态码: {response.status_code}")
            return {}, False

    except Exception as e:
        logger.error(f"获取ODM配置文件时出错: {str(e)}")
        return {}, False

def scan_all_input_tasks():
    """
    扫描Input目录下所有任务的TaskInfo.json文件

    Returns:
        list: 包含TaskInfo.json文件路径和内容的列表
    """
    all_tasks_info = []

    try:
        # 首先尝试通过ODM配置获取基础路径
        config, config_exists = get_odm_config()

        base_paths = []

        if config_exists and 'PATHS' in config and 'window_data_path' in config['PATHS']:
            window_data_path = config['PATHS']['window_data_path']
            base_paths.append(window_data_path)

        # 添加其他可能的基础路径
        base_paths.extend([
            'D:/Drone_Project/nginxData',
            '/opt/airflow/data',
            'D:/Drone_Project/data',
            '/data'
        ])

        # 在每个基础路径下查找Input目录
        for base_path in base_paths:
            input_dir = os.path.join(base_path, 'ODM', 'Input')

            if os.path.exists(input_dir) and os.path.isdir(input_dir):
                logger.info(f"扫描Input目录: {input_dir}")

                try:
                    # 扫描Input目录下的所有任务子目录
                    for task_dir in os.listdir(input_dir):
                        task_path = os.path.join(input_dir, task_dir)
                        if os.path.isdir(task_path):
                            task_info_path = os.path.join(task_path, 'TaskInfo.json')

                            if os.path.exists(task_info_path):
                                logger.info(f"找到TaskInfo.json: {task_info_path}")

                                try:
                                    # 读取TaskInfo.json文件
                                    with open(task_info_path, 'r', encoding='utf-8') as f:
                                        task_info_content = json.load(f)

                                    all_tasks_info.append({
                                        'file_path': task_info_path,
                                        'task_dir': task_dir,
                                        'content': task_info_content
                                    })

                                    logger.debug(f"成功读取TaskInfo.json: {task_dir}")

                                except Exception as e:
                                    logger.error(f"读取TaskInfo.json文件失败 {task_info_path}: {str(e)}")
                                    continue

                    # 找到一个有效的Input目录就够了
                    if all_tasks_info:
                        break

                except Exception as e:
                    logger.error(f"扫描Input目录 {input_dir} 时出错: {str(e)}")
                    continue

        logger.info(f"扫描Input目录完成，找到 {len(all_tasks_info)} 个TaskInfo.json文件")

    except Exception as e:
        logger.error(f"扫描Input目录时出错: {str(e)}")

    return all_tasks_info

def scan_output_directory():
    """
    扫描Output目录获取所有可用的图层 - 与TaskInfo.json路径保持一致

    Returns:
        list: 可用的图层ID列表
    """
    available_layers = []

    try:
        # 首先尝试通过ODM配置获取基础路径
        config, config_exists = get_odm_config()

        base_paths = []

        if config_exists and 'PATHS' in config and 'window_data_path' in config['PATHS']:
            window_data_path = config['PATHS']['window_data_path']
            base_paths.append(window_data_path)

        # 添加其他可能的基础路径
        base_paths.extend([
            'D:/Drone_Project/nginxData',
            '/opt/airflow/data',
            'D:/Drone_Project/data',
            '/data'
        ])

        # 在每个基础路径下查找Output目录
        for base_path in base_paths:
            output_dir = os.path.join(base_path, 'ODM', 'Output')

            if os.path.exists(output_dir) and os.path.isdir(output_dir):
                logger.info(f"找到Output目录: {output_dir}")

                try:
                    # 扫描Output目录下的所有子目录
                    for item in os.listdir(output_dir):
                        item_path = os.path.join(output_dir, item)
                        if os.path.isdir(item_path):
                            # 检查是否是有效的任务ID格式（通常是数字）
                            if item.isdigit() or len(item) >= 10:  # 任务ID通常是长数字字符串
                                if item not in available_layers:  # 避免重复
                                    available_layers.append(item)
                                    logger.debug(f"找到图层目录: {item}")

                    # 找到一个有效的Output目录就够了
                    if available_layers:
                        break

                except Exception as e:
                    logger.error(f"扫描Output目录 {output_dir} 时出错: {str(e)}")
                    continue

        logger.info(f"扫描Output目录完成，找到 {len(available_layers)} 个图层目录")

    except Exception as e:
        logger.error(f"扫描Output目录时出错: {str(e)}")

    return available_layers

def get_layer_bbox(workspace, layer_name):
    """
    调用bbox接口获取图层范围

    Args:
        workspace: 工作空间名称
        layer_name: 图层名称

    Returns:
        dict: bbox数据 (latLon部分) 或 None
    """
    try:
        # 构建bbox接口URL - 直接传递参数，不检查必需性
        bbox_url = f"{DJANGO_BASE_URL}/api/management/layers/bbox/"
        params = {
            'workspace': workspace,
            'layer': layer_name
        }

        logger.info(f"调用bbox接口: {bbox_url}, params: {params}")

        # 调用bbox接口
        response = requests.get(bbox_url, params=params, timeout=30)

        if response.status_code == 200:
            bbox_result = response.json()

            # 提取latLon部分的数据
            if 'bbox' in bbox_result and 'latLon' in bbox_result['bbox']:
                lat_lon_bbox = bbox_result['bbox']['latLon']
                logger.info(f"成功获取bbox数据: {lat_lon_bbox}")
                return lat_lon_bbox
            else:
                logger.warning(f"bbox接口返回数据格式不完整: {bbox_result}")
                return None
        elif response.status_code == 400:
            # 参数错误，可能是图层不存在
            logger.warning(f"图层可能不存在: workspace={workspace}, layer={layer_name}")
            return None
        else:
            logger.error(f"bbox接口调用失败: {response.status_code}, {response.text}")
            return None

    except requests.exceptions.RequestException as e:
        logger.error(f"调用bbox接口异常: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"获取bbox数据异常: {str(e)}")
        return None
