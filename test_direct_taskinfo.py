#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
直接测试TaskInfo.json创建功能
"""

import os
import sys
import json
import uuid
from datetime import datetime

# 添加项目路径
sys.path.append('.')

def test_direct_taskinfo_creation():
    """直接测试TaskInfo.json创建功能"""
    print("🧪 直接测试TaskInfo.json创建功能...")
    
    # 测试数据
    image_id = "20250705171601"
    task_id = str(uuid.uuid4())
    task_info_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    print(f"📁 TaskInfo路径: {task_info_path}")
    print(f"📋 任务ID: {task_id}")
    
    # 记录启动前状态
    before_exists = os.path.exists(task_info_path)
    before_count = 0
    
    if before_exists:
        try:
            with open(task_info_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if isinstance(data, list):
                before_count = len(data)
            print(f"📋 启动前: 文件存在，包含 {before_count} 个任务")
        except:
            print(f"📋 启动前: 文件存在但格式错误")
    else:
        print(f"📋 启动前: 文件不存在")
    
    # 创建新的任务信息
    try:
        print(f"\n🔧 开始创建TaskInfo.json...")
        
        # 确保目录存在
        task_dir = os.path.dirname(task_info_path)
        if not os.path.exists(task_dir):
            os.makedirs(task_dir, exist_ok=True)
            print(f"📂 创建目录: {task_dir}")
        
        # 创建新的任务信息模板
        new_task_info = {
            'task_id': task_id,
            'image_id': image_id,
            'analysis_category': 'arableLand',
            'timestamp': int(datetime.now().timestamp()),
            'datetime': datetime.now().isoformat(),
            'input_files': {
                'image_path': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
                'model_path': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
                'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp'
            },
            'output_files': {
                'ai_output_path': f'D:/Drone_Project/nginxData/ODM/AI/{image_id}/arableLand/{image_id}_1_{int(datetime.now().timestamp())}.shp',
                'final_output_path': f'D:/Drone_Project/nginxData/ODM/AI/{image_id}/arableLand/{image_id}_2_{int(datetime.now().timestamp())}.shp'
            },
            'parameters': {
                'model_type': 'deeplabv3_plus',
                'num_classes': 2,
                'area_threshold': 400.0
            },
            'results': {
                'ai_processing_time': None,
                'spatial_statistics': {
                    'outflow_count': None,
                    'inflow_count': None,
                    'total_count': None,
                    'outflow_area': None,
                    'inflow_area': None,
                    'area_threshold': 400.0
                },
                'success': None
            },
            'status': '进行中',
            'log_file': f'D:/Drone_Project/geoserverAPIDJV2/geoserver_api/logs/analysislog/{task_id}.log'
        }
        
        print(f"✅ 任务信息模板创建完成")
        
        # 读取现有的TaskInfo.json（如果存在）
        existing_tasks = []
        if os.path.exists(task_info_path):
            print(f"📄 读取现有TaskInfo文件...")
            try:
                with open(task_info_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                    
                if isinstance(existing_data, list):
                    existing_tasks = existing_data
                    print(f"📋 读取到数组格式，包含 {len(existing_tasks)} 个历史任务")
                elif isinstance(existing_data, dict):
                    existing_tasks = [existing_data]
                    print(f"📋 读取到对象格式，转换为数组格式")
                else:
                    existing_tasks = []
                    print(f"⚠️ 未知格式，创建新数组")
                    
            except Exception as e:
                print(f"⚠️ 读取历史任务信息失败: {str(e)}")
                existing_tasks = []
        else:
            print(f"📝 TaskInfo文件不存在，将创建新文件")
        
        # 检查是否已存在相同task_id的记录
        task_found = False
        print(f"🔍 检查是否存在相同task_id的记录...")
        for i, task in enumerate(existing_tasks):
            if task.get('task_id') == task_id:
                existing_tasks[i] = new_task_info
                task_found = True
                print(f"📝 更新现有任务记录: {task_id}")
                break
        
        if not task_found:
            existing_tasks.append(new_task_info)
            print(f"➕ 添加新任务记录: {task_id}")
        
        print(f"💾 准备保存TaskInfo.json到: {task_info_path}")
        
        # 保存更新后的TaskInfo.json
        with open(task_info_path, 'w', encoding='utf-8') as f:
            json.dump(existing_tasks, f, ensure_ascii=False, indent=2)
        
        print(f"✅ TaskInfo.json文件已写入完成")
        
        # 验证文件是否正确保存
        if os.path.exists(task_info_path):
            file_size = os.path.getsize(task_info_path)
            print(f"📊 文件验证: 大小={file_size}字节")
            
            # 重新读取验证内容
            with open(task_info_path, 'r', encoding='utf-8') as f:
                verify_data = json.load(f)
            
            if isinstance(verify_data, list):
                current_count = len(verify_data)
                print(f"📊 验证成功: 当前包含 {current_count} 个任务")
                
                # 查找我们刚添加的任务
                found_new_task = False
                for task in verify_data:
                    if task.get('task_id') == task_id:
                        found_new_task = True
                        print(f"🎯 找到新添加的任务:")
                        print(f"  任务ID: {task.get('task_id')}")
                        print(f"  状态: {task.get('status')}")
                        print(f"  影像ID: {task.get('image_id')}")
                        print(f"  分析类别: {task.get('analysis_category')}")
                        break
                
                if found_new_task:
                    print(f"✅ TaskInfo.json创建测试成功！")
                    return True
                else:
                    print(f"❌ 未找到新添加的任务")
                    return False
            else:
                print(f"❌ 验证失败: 文件格式不正确")
                return False
        else:
            print(f"❌ 文件保存失败，文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 创建TaskInfo.json失败: {str(e)}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")
        return False

def test_update_taskinfo():
    """测试更新TaskInfo.json功能"""
    print(f"\n🔄 测试更新TaskInfo.json功能...")
    
    image_id = "20250705171601"
    task_info_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    if not os.path.exists(task_info_path):
        print(f"❌ TaskInfo文件不存在，无法测试更新功能")
        return False
    
    try:
        # 读取现有文件
        with open(task_info_path, 'r', encoding='utf-8') as f:
            task_data = json.load(f)
        
        if not isinstance(task_data, list) or len(task_data) == 0:
            print(f"❌ TaskInfo文件格式不正确或为空")
            return False
        
        # 更新最后一个任务的状态
        last_task = task_data[-1]
        original_status = last_task.get('status', 'Unknown')
        
        print(f"📝 更新任务状态: {original_status} -> 完成")
        last_task['status'] = '完成'
        last_task['results']['success'] = True
        last_task['results']['ai_processing_time'] = 120
        
        # 保存更新
        with open(task_info_path, 'w', encoding='utf-8') as f:
            json.dump(task_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ TaskInfo.json更新成功")
        
        # 验证更新
        with open(task_info_path, 'r', encoding='utf-8') as f:
            verify_data = json.load(f)
        
        if verify_data[-1]['status'] == '完成':
            print(f"✅ 更新验证成功")
            return True
        else:
            print(f"❌ 更新验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 更新TaskInfo.json失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始直接测试TaskInfo.json功能")
    
    # 测试创建功能
    print("="*60)
    print("测试1: TaskInfo.json创建功能")
    print("="*60)
    
    success1 = test_direct_taskinfo_creation()
    
    if success1:
        # 测试更新功能
        print("\n" + "="*60)
        print("测试2: TaskInfo.json更新功能")
        print("="*60)
        
        success2 = test_update_taskinfo()
        
        if success1 and success2:
            print(f"\n🎉 所有TaskInfo.json功能测试通过！")
        else:
            print(f"\n❌ 部分功能测试失败")
    else:
        print(f"\n❌ TaskInfo.json创建功能测试失败")
    
    print(f"\n📖 测试总结:")
    print(f"1. 直接文件操作测试TaskInfo.json的创建和更新")
    print(f"2. 验证数组格式的正确性")
    print(f"3. 确认文件读写权限正常")
    print(f"4. 如果此测试通过，说明TaskInfo.json功能本身没问题")
    print(f"5. 问题可能在于Django服务器的模块加载")
