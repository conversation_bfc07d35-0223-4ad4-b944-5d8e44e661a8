#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试TaskInfo.json立即创建功能
"""

import requests
import time
import json
import os
import urllib.parse

def test_immediate_taskinfo_creation():
    """测试TaskInfo.json是否立即创建"""
    print("🧪 测试TaskInfo.json立即创建功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    image_id = "20250705171601"
    image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    model_path = "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
    old_data_path = "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
    
    # 预期的TaskInfo.json路径
    expected_taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  影像ID: {image_id}")
        print(f"  预期TaskInfo路径: {expected_taskinfo_path}")
        
        # 记录启动前的TaskInfo状态
        before_task_count = 0
        before_tasks = []
        
        if os.path.exists(expected_taskinfo_path):
            try:
                with open(expected_taskinfo_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                
                if isinstance(existing_data, list):
                    before_task_count = len(existing_data)
                    before_tasks = [task.get('task_id', 'N/A') for task in existing_data[-3:]]
                    print(f"📋 启动前TaskInfo包含 {before_task_count} 个任务")
                    print(f"  最近的任务ID: {before_tasks}")
                else:
                    print(f"⚠️ 启动前TaskInfo格式不正确")
                    
            except json.JSONDecodeError as e:
                print(f"❌ 启动前TaskInfo格式错误: {e}")
        else:
            print(f"📝 启动前TaskInfo文件不存在")
        
        # 构建请求参数
        params = {
            'id': image_id,
            'image': image_path,
            'model': model_path,
            'old_data_path': old_data_path,
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus'
        }
        
        print(f"\n🚀 启动合并分析任务...")
        print(f"📡 请求参数: {params}")
        
        # 记录请求开始时间
        request_start_time = time.time()
        
        # 发送请求
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        # 记录响应时间
        response_time = time.time() - request_start_time
        print(f"⏱️ 请求响应时间: {response_time:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 立即检查TaskInfo.json（多次检查）
            check_intervals = [0.5, 1.0, 2.0, 3.0, 5.0]  # 检查间隔（秒）
            
            for i, interval in enumerate(check_intervals):
                print(f"\n🔍 第{i+1}次检查 (等待{interval}秒后)...")
                time.sleep(interval)
                
                if os.path.exists(expected_taskinfo_path):
                    try:
                        with open(expected_taskinfo_path, 'r', encoding='utf-8') as f:
                            current_data = json.load(f)
                        
                        if isinstance(current_data, list):
                            current_task_count = len(current_data)
                            print(f"📊 当前TaskInfo包含 {current_task_count} 个任务")
                            
                            # 检查是否有新任务
                            if current_task_count > before_task_count:
                                print(f"✅ 发现新任务！任务数量从 {before_task_count} 增加到 {current_task_count}")
                                
                                # 查找新任务
                                for task in current_data:
                                    if task.get('task_id') == task_id:
                                        print(f"🎯 找到目标任务:")
                                        print(f"  任务ID: {task.get('task_id', 'N/A')}")
                                        print(f"  状态: {task.get('status', 'N/A')}")
                                        print(f"  消息: {task.get('message', 'N/A')}")
                                        print(f"  进度: {task.get('progress', 0)}%")
                                        print(f"  开始时间: {task.get('start_time', 'N/A')}")
                                        
                                        # 检查任务结构完整性
                                        required_fields = ['task_id', 'image_id', 'analysis_category', 'input_files', 'output_files', 'parameters']
                                        missing_fields = []
                                        for field in required_fields:
                                            if field not in task:
                                                missing_fields.append(field)
                                        
                                        if missing_fields:
                                            print(f"⚠️ 缺少字段: {missing_fields}")
                                        else:
                                            print(f"✅ 任务结构完整")
                                        
                                        return True
                                
                                print(f"❌ 未找到目标任务ID: {task_id}")
                            else:
                                print(f"⏳ 任务数量未变化，继续等待...")
                        else:
                            print(f"⚠️ TaskInfo格式不是数组")
                            
                    except json.JSONDecodeError as e:
                        print(f"❌ TaskInfo格式错误: {e}")
                    except Exception as e:
                        print(f"❌ 读取TaskInfo失败: {e}")
                else:
                    print(f"❌ TaskInfo文件仍不存在")
            
            print(f"\n❌ 在所有检查间隔内都未发现新任务")
            return False
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_server_logs():
    """检查服务器日志"""
    print(f"\n📋 检查服务器日志...")
    
    # 这里可以添加日志检查逻辑
    log_dir = "geoserver_api/logs/analysislog"
    if os.path.exists(log_dir):
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        recent_logs = sorted(log_files, key=lambda x: os.path.getmtime(os.path.join(log_dir, x)))[-3:]
        
        print(f"📁 最近的日志文件: {recent_logs}")
        
        # 检查最新日志文件的最后几行
        if recent_logs:
            latest_log = os.path.join(log_dir, recent_logs[-1])
            try:
                with open(latest_log, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                print(f"\n📄 最新日志文件 {recent_logs[-1]} 的最后10行:")
                for line in lines[-10:]:
                    print(f"  {line.strip()}")
                    
            except Exception as e:
                print(f"❌ 读取日志文件失败: {e}")
    else:
        print(f"❌ 日志目录不存在: {log_dir}")

def test_api_status():
    """测试API状态"""
    print(f"\n🔍 测试API基本功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        # 测试状态查询接口
        response = requests.get(f"{base_url}/status/", params={'task_id': 'test'}, timeout=10)
        print(f"📡 状态查询接口响应: {response.status_code}")
        
        if response.status_code == 200:
            print(f"✅ API服务正常")
        else:
            print(f"⚠️ API响应异常: {response.text}")
            
    except Exception as e:
        print(f"❌ API连接失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试TaskInfo.json立即创建功能")
    
    # 测试API基本功能
    test_api_status()
    
    # 测试TaskInfo立即创建
    print("\n" + "="*60)
    print("测试TaskInfo.json立即创建")
    print("="*60)
    
    success = test_immediate_taskinfo_creation()
    
    if success:
        print("\n🎉 TaskInfo.json立即创建功能正常！")
    else:
        print("\n❌ TaskInfo.json立即创建功能异常")
        
        # 检查服务器日志
        check_server_logs()
    
    print("\n📖 调试建议:")
    print("1. 检查服务器日志中是否有TaskInfo.json创建的相关信息")
    print("2. 确认任务目录是否正确创建")
    print("3. 检查文件权限是否允许写入")
    print("4. 验证合并分析执行器的初始化是否正常")
