2025-08-29 16:34:00 - INFO - === 合并分析任务开始 ===
2025-08-29 16:34:00 - INFO - 任务ID: 420e10a6-c26e-42f3-9e61-af36ba6a96ab
2025-08-29 16:34:00 - INFO - [UPDATE] TaskInfo.json已由队列管理器创建，跳过重复创建
2025-08-29 16:34:00 - INFO - 影像ID: 20250523123608
2025-08-29 16:34:00 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 16:34:00 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-29 16:34:00 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-29 16:34:00 - INFO - 分析类别: arableLand
2025-08-29 16:34:00 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250523123608\arableLand\20250523123608_1_1756456440.shp
2025-08-29 16:34:00 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250523123608\arableLand\20250523123608_2_1756456440.shp
2025-08-29 16:34:00 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250523123608
2025-08-29 16:34:00 - INFO - === 开始执行合并分析任务 ===
2025-08-29 16:34:00 - WARNING - [WARNING] TaskInfo路径丢失，尝试重新构建...
2025-08-29 16:34:00 - INFO - [LOCATION] 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250523123608\TaskInfo.json
2025-08-29 16:34:00 - INFO - [UPDATE] 更新字段: status = 进行中
2025-08-29 16:34:00 - INFO - [UPDATE] 更新字段: message = 开始AI语义分割...
2025-08-29 16:34:00 - INFO - [UPDATE] 更新字段: progress = 5
2025-08-29 16:34:00 - INFO - [UPDATE] TaskInfo.json已更新: ['status', 'message', 'progress']
2025-08-29 16:34:00 - INFO - [SEARCH] 验证更新结果:
2025-08-29 16:34:00 - INFO -   状态: 进行中
2025-08-29 16:34:00 - INFO -   进度: 5%
2025-08-29 16:34:00 - INFO -   AI处理时间: None
2025-08-29 16:34:00 - INFO -   成功状态: None
2025-08-29 16:34:00 - INFO -   空间统计:
2025-08-29 16:34:00 - INFO -     area_threshold: 400.0
2025-08-29 16:34:00 - INFO - [AI] 开始AI语义分割...
2025-08-29 16:34:00 - INFO - [DIR] 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 16:34:00 - INFO - [AI] 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-29 16:34:00 - INFO - [FOLDER] 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250523123608\arableLand\20250523123608_1_1756456440.shp
2025-08-29 16:34:00 - INFO - [CONFIG] 模型类型: deeplabv3_plus
2025-08-29 16:34:00 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-29 16:34:00 - INFO - 🔍 检测到合并分析任务: image_id=20250523123608, category=arableLand
2025-08-29 16:34:00 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250523123608/TaskInfo.json
2025-08-29 16:34:00 - INFO - ✅ 使用真实任务ID: 420e10a6-c26e-42f3-9e61-af36ba6a96ab
2025-08-29 16:34:00 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-29 16:34:00 - INFO - 📄 读取现有TaskInfo文件...
2025-08-29 16:34:00 - INFO - 📋 读取到数组格式，包含 2 个历史任务
2025-08-29 16:34:00 - INFO - 🔄 更新现有任务记录: 420e10a6-c26e-42f3-9e61-af36ba6a96ab
2025-08-29 16:34:00 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-29 16:34:00 - INFO - 📊 文件验证: 大小=4069字节，任务总数: 2
2025-08-29 16:34:03 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-29 16:34:03 - INFO - 🔧 创建处理参数:
2025-08-29 16:34:03 - INFO -   模型类型: deeplabv3_plus
2025-08-29 16:34:03 - INFO -   类别数量: 2
2025-08-29 16:34:03 - INFO -   目标类别: [1]
2025-08-29 16:34:03 - INFO -   窗口大小: 512
2025-08-29 16:34:03 - INFO -   批处理大小: 16
2025-08-29 16:34:03 - INFO -   重叠比例: 0.5
2025-08-29 16:34:03 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-29 16:34:03 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 16:34:03 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-29 16:34:03 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250523123608\arableLand\20250523123608_1_1756456440.shp
2025-08-29 16:34:03 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-29 16:34:03 - INFO - 🔄 开始AI模型推理...
2025-08-29 16:34:03 - INFO - 
==================================================
2025-08-29 16:34:03 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 16:34:03 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250523123608\arableLand\20250523123608_1_1756456440.shp
2025-08-29 16:34:03 - INFO - ==================================================
2025-08-29 16:34:03 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 16:34:03 - INFO - 📏 图像大小: 56283x67180, 波段数: 3
2025-08-29 16:34:03 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-29 16:34:03 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-29 16:34:03 - INFO - 🔢 类别数量: 2
2025-08-29 16:34:03 - INFO - 🔄 重叠区域: 37 像素
2025-08-29 16:34:03 - INFO - 📦 批处理大小: 16
2025-08-29 16:34:03 - INFO - 💻 计算设备: cuda:0
2025-08-29 16:34:03 - INFO - 系统总内存: 32.0 GB
2025-08-29 16:34:03 - INFO - 可用内存: 13.1 GB
2025-08-29 16:34:03 - INFO - 可使用内存: 10.5 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 67180x56283x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 84.51 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 338.06 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 422.57 GB
2025-08-29 16:34:03 - INFO - 图像过大，需要分块处理
2025-08-29 16:34:03 - INFO - 需要内存: 422.57 GB
2025-08-29 16:34:03 - INFO - 可用内存: 10.50 GB
2025-08-29 16:34:03 - INFO - 🔧 图像过大，启用分块处理模式
2025-08-29 16:34:03 - INFO - 系统总内存: 32.0 GB
2025-08-29 16:34:03 - INFO - 可用内存: 13.1 GB
2025-08-29 16:34:03 - INFO - 可使用内存: 10.5 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 67436x56539x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 85.22 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 340.89 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 426.11 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 33846x28398x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 21.48 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 85.93 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 107.42 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 22650x19017x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 9.63 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 38.51 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 48.14 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 17051x14327x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 5.46 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 21.84 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 27.30 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 13692x11513x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 3.52 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 14.09 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 17.62 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 11453x9637x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 2.47 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 9.87 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 12.34 GB
2025-08-29 16:34:03 - INFO - 图像尺寸: 9854x8297x3
2025-08-29 16:34:03 - INFO - 原始图像内存: 1.83 GB
2025-08-29 16:34:03 - INFO - 处理内存估算: 7.31 GB
2025-08-29 16:34:03 - INFO - 总内存需求: 9.14 GB
2025-08-29 16:34:03 - INFO - 选择网格大小: 7x7
2025-08-29 16:34:03 - INFO - 每个块大小: 9854x8297
2025-08-29 16:34:03 - INFO - 每个块内存需求: 9.14 GB
2025-08-29 16:34:03 - INFO - 🔧 分块网格: 7x7
2025-08-29 16:34:03 - INFO - 生成了 49 个处理块
2025-08-29 16:34:03 - INFO - 📦 生成 49 个处理块
2025-08-29 16:34:03 - INFO - 📁 临时目录: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w
2025-08-29 16:34:03 - INFO - 🔄 处理块 1/49: 0_0
2025-08-29 16:34:46 - INFO - 提取块 0_0: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_0.tif
2025-08-29 16:35:37 - INFO - ✅ 块 0_0 处理完成
2025-08-29 16:35:37 - INFO - 🔄 处理块 2/49: 0_1
2025-08-29 16:36:07 - INFO - 提取块 0_1: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_1.tif
2025-08-29 16:37:37 - INFO - ❌ 块 0_1 处理失败
2025-08-29 16:37:37 - INFO - 🔄 处理块 3/49: 0_2
2025-08-29 16:37:41 - INFO - 提取块 0_2: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_2.tif
2025-08-29 16:38:30 - INFO - ✅ 块 0_2 处理完成
2025-08-29 16:38:30 - INFO - 🔄 处理块 4/49: 0_3
2025-08-29 16:38:35 - INFO - 提取块 0_3: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_3.tif
2025-08-29 16:39:24 - INFO - ✅ 块 0_3 处理完成
2025-08-29 16:39:24 - INFO - 🔄 处理块 5/49: 0_4
2025-08-29 16:39:28 - INFO - 提取块 0_4: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_4.tif
2025-08-29 16:40:22 - INFO - ✅ 块 0_4 处理完成
2025-08-29 16:40:22 - INFO - 🔄 处理块 6/49: 0_5
2025-08-29 16:40:26 - INFO - 提取块 0_5: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_5.tif
2025-08-29 16:41:19 - INFO - ✅ 块 0_5 处理完成
2025-08-29 16:41:19 - INFO - 🔄 处理块 7/49: 0_6
2025-08-29 16:41:24 - INFO - 提取块 0_6: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_0_6.tif
2025-08-29 16:42:26 - INFO - ❌ 块 0_6 处理失败
2025-08-29 16:42:26 - INFO - 🔄 处理块 8/49: 1_0
2025-08-29 16:42:55 - INFO - 提取块 1_0: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_1_0.tif
2025-08-29 16:44:30 - INFO - ❌ 块 1_0 处理失败
2025-08-29 16:44:30 - INFO - 🔄 处理块 9/49: 1_1
2025-08-29 16:45:31 - INFO - 提取块 1_1: C:\Users\<USER>\AppData\Local\Temp\large_image_processing_4va2oo2w\20250523123608_out_1_1.tif
