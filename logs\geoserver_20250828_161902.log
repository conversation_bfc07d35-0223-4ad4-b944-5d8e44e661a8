2025-08-28 16:19:02,183 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_161902.log
2025-08-28 16:19:02,185 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:19:02,185 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:19:02,202 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:19:02,207 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:19:02,208 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:19:02,222 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:19:02,223 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:19:02,224 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:19:02,236 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:19:02,238 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:19:02,238 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:19:02,248 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:19:02,250 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:19:02,251 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:19:02,260 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:19:02,261 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:19:02,262 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:19:02,273 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:19:02,287 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 16:19:02,293 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 16:19:06,603 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 16:19:06,616 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 16:19:06,620 - analysis_executor - INFO - 加载了 28 个任务状态
