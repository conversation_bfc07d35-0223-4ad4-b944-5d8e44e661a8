#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试API文档更新验证
"""

import requests
import json

def test_all_analysis_apis():
    """测试所有分析API接口"""
    print("🧪 测试所有分析API接口...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 定义所有接口
    apis = [
        {
            'name': 'TaskInfo获取接口',
            'url': f'{base_url}/taskinfo/',
            'params': {'id': '20250705171601'},
            'method': 'GET',
            'expected_fields': ['status', 'message', 'data']
        },
        {
            'name': '权重信息接口',
            'url': f'{base_url}/weight-info/',
            'params': {},
            'method': 'GET',
            'expected_fields': ['status', 'message', 'data']
        },
        {
            'name': '权重配置接口',
            'url': f'{base_url}/weight-config/',
            'params': {},
            'method': 'GET',
            'expected_fields': ['status', 'message', 'data']
        },
        {
            'name': '任务状态查询接口',
            'url': f'{base_url}/status/',
            'params': {'task_id': 'test-task-id'},
            'method': 'GET',
            'expected_fields': ['status', 'message']
        },
        {
            'name': '分析日志接口',
            'url': f'{base_url}/logs/',
            'params': {'lines': 10},
            'method': 'GET',
            'expected_fields': ['status', 'message']
        }
    ]
    
    print(f"\n📋 开始测试 {len(apis)} 个API接口...")
    
    results = []
    
    for i, api in enumerate(apis, 1):
        print(f"\n{i}️⃣ 测试接口: {api['name']}")
        print(f"   URL: {api['url']}")
        print(f"   参数: {api['params']}")
        
        try:
            response = requests.get(api['url'], params=api['params'], timeout=30)
            
            print(f"   HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    # 检查基本字段
                    missing_fields = []
                    for field in api['expected_fields']:
                        if field not in result:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"   ❌ 缺少字段: {missing_fields}")
                        results.append({'api': api['name'], 'status': 'FAIL', 'reason': f'缺少字段: {missing_fields}'})
                    else:
                        print(f"   ✅ 基本字段完整")
                        
                        # 特殊检查
                        if api['name'] == 'TaskInfo获取接口':
                            check_taskinfo_response(result)
                        elif api['name'] == '权重信息接口':
                            check_weight_info_response(result)
                        elif api['name'] == '权重配置接口':
                            check_weight_config_response(result)
                        
                        results.append({'api': api['name'], 'status': 'PASS', 'reason': '接口正常'})
                    
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    results.append({'api': api['name'], 'status': 'FAIL', 'reason': f'JSON解析失败: {e}'})
                    
            else:
                print(f"   ⚠️ HTTP错误: {response.status_code}")
                print(f"   错误信息: {response.text[:200]}")
                results.append({'api': api['name'], 'status': 'WARN', 'reason': f'HTTP {response.status_code}'})
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
            results.append({'api': api['name'], 'status': 'ERROR', 'reason': f'请求异常: {e}'})
    
    # 输出测试总结
    print(f"\n📊 测试总结:")
    pass_count = sum(1 for r in results if r['status'] == 'PASS')
    fail_count = sum(1 for r in results if r['status'] == 'FAIL')
    warn_count = sum(1 for r in results if r['status'] == 'WARN')
    error_count = sum(1 for r in results if r['status'] == 'ERROR')
    
    print(f"   ✅ 通过: {pass_count}")
    print(f"   ❌ 失败: {fail_count}")
    print(f"   ⚠️ 警告: {warn_count}")
    print(f"   🚫 错误: {error_count}")
    
    print(f"\n📋 详细结果:")
    for result in results:
        status_icon = {'PASS': '✅', 'FAIL': '❌', 'WARN': '⚠️', 'ERROR': '🚫'}[result['status']]
        print(f"   {status_icon} {result['api']}: {result['reason']}")

def check_taskinfo_response(result):
    """检查TaskInfo接口响应"""
    print(f"   🔍 TaskInfo接口特殊检查:")
    
    data = result.get('data', [])
    print(f"     数据类型: {type(data)}")
    print(f"     数据长度: {len(data) if isinstance(data, list) else 'N/A'}")
    
    if isinstance(data, list) and len(data) > 0:
        task = data[0]
        required_fields = ['task_id', 'image_id', 'parameters', 'results', 'status']
        
        for field in required_fields:
            if field in task:
                print(f"     ✅ {field}: 存在")
                
                # 检查parameters中的model_name
                if field == 'parameters':
                    params = task[field]
                    if 'model_name' in params:
                        print(f"       ✅ model_name: {params['model_name']}")
                    else:
                        print(f"       ❌ model_name: 缺失")
                
                # 检查results中的spatial_statistics
                elif field == 'results':
                    results_data = task[field]
                    if 'spatial_statistics' in results_data:
                        spatial_stats = results_data['spatial_statistics']
                        non_null_count = sum(1 for v in spatial_stats.values() if v is not None)
                        total_count = len(spatial_stats)
                        print(f"       ✅ spatial_statistics: {non_null_count}/{total_count} 字段有值")
                    else:
                        print(f"       ❌ spatial_statistics: 缺失")
            else:
                print(f"     ❌ {field}: 缺失")
    elif isinstance(data, list):
        print(f"     ℹ️ 返回空列表（正常情况）")
    else:
        print(f"     ❌ 数据格式错误")

def check_weight_info_response(result):
    """检查权重信息接口响应"""
    print(f"   🔍 权重信息接口特殊检查:")
    
    data = result.get('data', {})
    print(f"     地物类型数量: {len(data)}")
    
    for land_type, info in data.items():
        print(f"     🏷️ {land_type}:")
        
        # 检查display_name字段
        if 'display_name' in info:
            print(f"       ✅ display_name: {info['display_name']}")
        else:
            print(f"       ❌ display_name: 缺失")
        
        # 检查其他必要字段
        required_fields = ['default', 'models']
        for field in required_fields:
            if field in info:
                print(f"       ✅ {field}: 存在")
            else:
                print(f"       ❌ {field}: 缺失")

def check_weight_config_response(result):
    """检查权重配置接口响应"""
    print(f"   🔍 权重配置接口特殊检查:")
    
    data = result.get('data', {})
    
    # 检查field_mapping字段
    if 'field_mapping' in data:
        field_mapping = data['field_mapping']
        print(f"     ✅ field_mapping: {len(field_mapping)} 个映射")
        
        expected_mappings = {
            'arableLand': '耕地',
            'constructionLand': '建设用地'
        }
        
        for key, expected_value in expected_mappings.items():
            if key in field_mapping:
                actual_value = field_mapping[key]
                if actual_value == expected_value:
                    print(f"       ✅ {key}: {actual_value}")
                else:
                    print(f"       ⚠️ {key}: {actual_value} (期望: {expected_value})")
            else:
                print(f"       ❌ {key}: 缺失")
    else:
        print(f"     ❌ field_mapping: 缺失")
    
    # 检查其他字段
    other_fields = ['window_data_path', 'default_weights']
    for field in other_fields:
        if field in data:
            print(f"     ✅ {field}: 存在")
        else:
            print(f"     ❌ {field}: 缺失")

def test_documentation_examples():
    """测试文档中的示例"""
    print(f"\n📖 测试文档示例...")
    
    examples = [
        {
            'name': 'TaskInfo接口示例',
            'url': 'http://127.0.0.1:8091/api/analysis/taskinfo/?id=20250705171601',
            'description': '获取存在的TaskInfo'
        },
        {
            'name': 'TaskInfo接口示例（不存在）',
            'url': 'http://127.0.0.1:8091/api/analysis/taskinfo/?id=20241231999999',
            'description': '获取不存在的TaskInfo，应返回空列表'
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['name']}")
        print(f"   描述: {example['description']}")
        print(f"   URL: {example['url']}")
        
        try:
            response = requests.get(example['url'], timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 请求成功")
                print(f"   状态: {result.get('status', 'N/A')}")
                print(f"   消息: {result.get('message', 'N/A')}")
                
                data = result.get('data', [])
                if isinstance(data, list):
                    print(f"   数据: {len(data)} 个任务")
                else:
                    print(f"   数据: {type(data)}")
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

if __name__ == "__main__":
    print("🚀 开始API文档更新验证")
    
    # 测试所有API接口
    test_all_analysis_apis()
    
    # 测试文档示例
    test_documentation_examples()
    
    print(f"\n🎉 API文档更新验证完成！")
    
    print(f"\n📖 文档更新总结:")
    print(f"1. ✅ 新增TaskInfo获取接口 (/api/analysis/taskinfo/)")
    print(f"2. ✅ 更新权重信息接口返回格式 (添加display_name字段)")
    print(f"3. ✅ 更新权重配置接口返回格式 (添加field_mapping字段)")
    print(f"4. ✅ 更新接口编号和目录结构")
    print(f"5. ✅ 添加详细的字段说明和使用示例")
    
    print(f"\n📚 文档位置: geoserver_api/readme/analysis_api.md")
    print(f"📝 新接口: GET /api/analysis/taskinfo/?id={'{影像ID}'}")
    print(f"🔗 示例: http://127.0.0.1:8091/api/analysis/taskinfo/?id=20250705171601")
