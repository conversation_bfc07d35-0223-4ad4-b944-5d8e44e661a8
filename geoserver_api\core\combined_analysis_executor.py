#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
合并分析执行器

功能说明:
- 合并AI语义分割和空间变化分析功能
- 统一的任务管理和日志记录
- 读取配置文件和管理输出路径
- 生成详细的任务信息JSON

处理流程:
1. 读取配置文件 (Task.cfg)
2. 执行AI语义分割
3. 执行空间变化分析
4. 保存结果和任务信息
"""

import os
import logging
import threading
import time
import uuid
import json
import configparser
from datetime import datetime
from typing import Dict, Any, Optional
from config import DJANGO_BASE_URL

# 导入现有的执行器
from .analysis_executor import analysis_executor, analysis_logger, ANALYSIS_LOGS_DIR
from .ai_model_processor import AIModelConfig, ai_model_processor

# 尝试导入AI模块
try:
    AI_AVAILABLE = True
except ImportError as e:
    AI_AVAILABLE = False
    print(f"AI模块不可用: {e}")

class CombinedAnalysisExecutor:
    """合并分析执行器"""
    
    def __init__(self):
        self.tasks = {}
        self.running_threads = {}
        self.log_dir = ANALYSIS_LOGS_DIR
        
        # 确保日志目录存在
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def execute_combined_analysis(self, image_id: str, image_path: str, model_path: str,
                                old_data_path: str, area_threshold: float = 400.0,
                                model_type: str = "deeplabv3_plus", num_classes: int = 2,
                                task_id: str = None) -> Dict[str, Any]:
        """
        执行合并分析任务

        参数:
            image_id: 影像ID
            image_path: TIF图像路径
            model_path: 模型权重路径
            old_data_path: 历史SHP数据路径
            area_threshold: 面积阈值
            model_type: 模型类型
            num_classes: 类别数量

        返回:
            dict: 任务信息
        """
        # 添加明显的日志标记，用于验证代码是否被加载
        analysis_logger.info("[START][START][START] COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED [START][START][START]")
        analysis_logger.info(f"[TARGET] 开始执行合并分析，参数: image_id={image_id}, image_path={image_path}")

        if not AI_AVAILABLE:
            return {
                'success': False,
                'message': 'AI模块不可用，请检查依赖安装',
                'task_id': None
            }
        
        try:
            # 使用传入的任务ID，如果没有则生成新的
            if task_id is None:
                task_id = str(uuid.uuid4())
            
            # 读取配置文件
            config_info = self._read_config()
            if not config_info['success']:
                return {
                    'success': False,
                    'message': f'读取配置文件失败: {config_info["message"]}',
                    'task_id': None
                }
            
            window_data_path = config_info['window_data_path']
            
            # 从模型路径中提取分析类别
            analysis_category = self._extract_category_from_model_path(model_path)

            # 使用统一的时间戳（从TaskInfo中获取，如果没有则生成新的）
            timestamp = self._get_unified_timestamp(task_id, image_id)
            ai_output_path, final_output_path, task_dir = self._generate_output_paths(
                window_data_path, image_id, analysis_category, timestamp
            )
            
            # 创建任务专用日志
            task_logger = self._create_task_logger(task_id)
            task_log_file = os.path.join(self.log_dir, f"{task_id}.log")
            
            # 记录任务信息
            task_info = {
                'task_id': task_id,
                'image_id': image_id,
                'status': '等待中',
                'message': '任务已创建，等待执行',
                'progress': 0,
                'start_time': datetime.now().isoformat(),
                'log_file': task_log_file,
                'parameters': {
                    'image_id': image_id,
                    'image_path': image_path,
                    'model_path': model_path,
                    'old_data_path': old_data_path,
                    'area_threshold': area_threshold,
                    'model_type': model_type,
                    'num_classes': num_classes,
                    'analysis_category': analysis_category,
                    'ai_output_path': ai_output_path,
                    'final_output_path': final_output_path,
                    'task_dir': task_dir,
                    'window_data_path': window_data_path
                }
            }
            
            self.tasks[task_id] = task_info

            analysis_logger.info(f"开始合并分析任务: {task_id}")
            task_logger.info(f"=== 合并分析任务开始 ===")
            task_logger.info(f"任务ID: {task_id}")

            # 注释掉重复创建TaskInfo.json的代码，因为队列管理器已经创建了
            # 立即创建TaskInfo.json（简化版本）
            # try:
            #     task_logger.info(f"[CONFIG] 立即创建TaskInfo.json...")
            #     self._create_taskinfo_immediately(task_id, task_info, task_logger)
            #     task_logger.info(f"[SUCCESS] TaskInfo.json创建完成")
            # except Exception as e:
            #     task_logger.error(f"[ERROR] TaskInfo.json创建失败: {str(e)}")
            #     import traceback
            #     task_logger.error(f"错误详情: {traceback.format_exc()}")

            task_logger.info(f"[UPDATE] TaskInfo.json已由队列管理器创建，跳过重复创建")

            # 保存任务状态
            self._save_status()
            task_logger.info(f"影像ID: {image_id}")
            task_logger.info(f"TIF图像: {image_path}")
            task_logger.info(f"模型权重: {model_path}")
            task_logger.info(f"历史数据: {old_data_path}")
            task_logger.info(f"分析类别: {analysis_category}")
            task_logger.info(f"AI输出路径: {ai_output_path}")
            task_logger.info(f"最终输出路径: {final_output_path}")
            task_logger.info(f"任务目录: {task_dir}")

            # 检查是否由队列管理器调用（通过task_id参数判断）
            if task_id and hasattr(self, '_called_by_queue_manager'):
                # 由队列管理器调用，同步执行任务
                analysis_logger.info(f"[PROCESS] 队列模式：同步执行合并分析任务: {task_id}")

                try:
                    # 直接调用任务执行方法，不启动新线程
                    self._run_combined_task(task_id)

                    # 检查任务状态
                    if task_id in self.tasks:
                        task_status = self.tasks[task_id]['status']
                        if task_status == '完成':
                            return {
                                'success': True,
                                'task_id': task_id,
                                'message': '合并分析任务执行完成',
                                'status': '完成'
                            }
                        else:
                            return {
                                'success': False,
                                'task_id': task_id,
                                'message': f'合并分析任务执行失败，状态: {task_status}',
                                'status': task_status
                            }
                    else:
                        return {
                            'success': False,
                            'task_id': task_id,
                            'message': '任务状态未知',
                            'status': '未知'
                        }

                except Exception as e:
                    analysis_logger.error(f"[ERROR] 同步执行任务失败: {str(e)}")
                    return {
                        'success': False,
                        'task_id': task_id,
                        'message': f'任务执行异常: {str(e)}',
                        'status': '失败'
                    }
            else:
                # 直接调用（非队列模式），异步执行任务
                analysis_logger.info(f"[START] 直接模式：异步启动合并分析任务: {task_id}")

                thread = threading.Thread(
                    target=self._run_combined_task,
                    args=(task_id,),
                    daemon=True
                )
                thread.start()
                self.running_threads[task_id] = thread

                return {
                    'success': True,
                    'task_id': task_id,
                    'message': '合并分析任务已启动',
                    'status': '等待中'
                }
            
        except Exception as e:
            error_msg = f"启动合并分析任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e),
                'task_id': None
            }
    
    def _read_config(self) -> Dict[str, Any]:
        """读取配置文件"""
        try:
            # 配置文件路径 (假设在127.0.0.1:81对应的目录)
            config_path = "D:/Drone_Project/nginxData/ODM/Task.cfg"
            
            if not os.path.exists(config_path):
                return {
                    'success': False,
                    'message': f'配置文件不存在: {config_path}'
                }
            
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            
            # 读取window_data_path
            window_data_path = config.get('PATHS', 'window_data_path', fallback=None)
            
            if not window_data_path:
                return {
                    'success': False,
                    'message': '配置文件中缺少window_data_path'
                }
            
            return {
                'success': True,
                'window_data_path': window_data_path,
                'config': config
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'读取配置文件失败: {str(e)}'
            }
    
    def _extract_category_from_model_path(self, model_path: str) -> str:
        """从模型路径中提取分析类别"""
        try:
            # 模型路径格式: .../AIWeight/arableLand/deeplabv3_plus/xxx.pth
            path_parts = model_path.replace('\\', '/').split('/')
            
            # 查找AIWeight后面的目录
            for i, part in enumerate(path_parts):
                if part == 'AIWeight' and i + 1 < len(path_parts):
                    return path_parts[i + 1]
            
            # 如果没找到，返回默认值
            return 'unknown'
            
        except Exception as e:
            analysis_logger.warning(f"提取分析类别失败: {str(e)}")
            return 'unknown'

    def _get_unified_timestamp(self, task_id: str, image_id: str) -> int:
        """
        获取统一的时间戳

        优先从TaskInfo.json中获取已存在的timestamp，
        如果不存在则生成新的时间戳

        Args:
            task_id: 任务ID
            image_id: 影像ID

        Returns:
            int: 统一的时间戳
        """
        try:
            # 首先尝试从TaskInfo.json中获取已存在的timestamp
            config_info = self._read_config()
            if config_info['success']:
                window_data_path = config_info['window_data_path']
                taskinfo_path = os.path.join(window_data_path, 'ODM', 'AI', image_id, 'TaskInfo.json')

                if os.path.exists(taskinfo_path):
                    with open(taskinfo_path, 'r', encoding='utf-8') as f:
                        taskinfo_data = json.load(f)

                    # 如果是列表，查找匹配的task_id
                    if isinstance(taskinfo_data, list):
                        for task_info in taskinfo_data:
                            if task_info.get('task_id') == task_id:
                                existing_timestamp = task_info.get('timestamp')
                                if existing_timestamp:
                                    analysis_logger.info(f"[TIMESTAMP] 使用TaskInfo中的时间戳: {existing_timestamp}")
                                    return int(existing_timestamp)
                    # 如果是单个对象
                    elif isinstance(taskinfo_data, dict):
                        if taskinfo_data.get('task_id') == task_id:
                            existing_timestamp = taskinfo_data.get('timestamp')
                            if existing_timestamp:
                                analysis_logger.info(f"[TIMESTAMP] 使用TaskInfo中的时间戳: {existing_timestamp}")
                                return int(existing_timestamp)

            # 如果没有找到现有的timestamp，生成新的
            new_timestamp = int(datetime.now().timestamp())
            analysis_logger.info(f"[TIMESTAMP] 生成新的时间戳: {new_timestamp}")
            return new_timestamp

        except Exception as e:
            # 如果出现任何异常，生成新的时间戳
            analysis_logger.warning(f"获取统一时间戳失败，生成新的: {str(e)}")
            return int(datetime.now().timestamp())
    
    def _generate_output_paths(self, window_data_path: str, image_id: str, 
                             analysis_category: str, timestamp: int) -> tuple:
        """生成输出路径"""
        # 任务目录: window_data_path/ODM/AI/影像ID/
        task_dir = os.path.join(window_data_path, "ODM", "AI", image_id)
        
        # 确保任务目录存在
        if not os.path.exists(task_dir):
            os.makedirs(task_dir)
        
        # AI分析输出路径: 分析类别/影像ID_1_时间戳.shp
        ai_output_filename = f"{image_id}_1_{timestamp}.shp"
        ai_output_path = os.path.join(task_dir, analysis_category, ai_output_filename)
        
        # 最终分析输出路径: 分析类别/影像ID_2_时间戳.shp
        final_output_filename = f"{image_id}_2_{timestamp}.shp"
        final_output_path = os.path.join(task_dir, analysis_category, final_output_filename)
        
        # 确保分析类别目录存在
        category_dir = os.path.join(task_dir, analysis_category)
        if not os.path.exists(category_dir):
            os.makedirs(category_dir)
        
        return ai_output_path, final_output_path, task_dir
    
    def _create_task_logger(self, task_id: str):
        """创建任务专用日志记录器"""
        logger = logging.getLogger(f'combined_task_{task_id}')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建文件处理器
        log_file = os.path.join(self.log_dir, f"{task_id}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger

    def _run_combined_task(self, task_id: str):
        """运行合并分析任务（在后台线程中执行）"""
        task_logger = None
        try:
            task_info = self.tasks[task_id]
            params = task_info['parameters']

            # 获取任务专用日志记录器
            task_logger = logging.getLogger(f'combined_task_{task_id}')

            task_logger.info("=== 开始执行合并分析任务 ===")

            # 更新任务状态
            self._update_task_status(task_id, '正在运行', '开始AI语义分割...', 5)

            # 更新TaskInfo.json
            self._update_combined_task_info(task_id, {
                'status': '进行中',
                'message': '开始AI语义分割...',
                'progress': 5
            }, task_logger)

            # 步骤1: 执行AI语义分割
            ai_result = self._execute_ai_segmentation(task_id, params, task_logger)

            if not ai_result['success']:
                self._update_task_status(task_id, '失败', f'AI语义分割失败: {ai_result["message"]}', 0)

                # 更新TaskInfo.json - AI分割失败
                self._update_combined_task_info(task_id, {
                    'status': '失败',
                    'message': f'AI语义分割失败: {ai_result["message"]}',
                    'progress': 0,
                    'end_time': datetime.now().isoformat(),
                    'results.success': False
                }, task_logger)
                return

            task_logger.info(f"AI语义分割完成: {ai_result['output_path']}")

            # 更新TaskInfo.json - AI分割完成，但整体任务仍在进行中
            task_logger.info(f"[PROCESS] 更新TaskInfo.json - AI分割完成")
            self._update_combined_task_info(task_id, {
                'status': '进行中',
                'message': 'AI语义分割完成，开始后续处理',
                'progress': 40,
                'results.ai_processing_time': ai_result.get('processing_time', 0)
            }, task_logger)
            task_logger.info(f"[SUCCESS] TaskInfo.json更新完成 - AI分割阶段")

            # 步骤2: 提取影像有效范围
            self._update_task_status(task_id, '正在运行', '提取影像有效范围...', 45)

            # 更新TaskInfo.json
            self._update_combined_task_info(task_id, {
                'message': '提取影像有效范围...',
                'progress': 45
            }, task_logger)

            extent_result = self._extract_image_extent(task_id, params, task_logger)

            if not extent_result['success']:
                self._update_task_status(task_id, '失败', f'影像范围提取失败: {extent_result["message"]}', 0)

                # 更新TaskInfo.json - 影像范围提取失败
                self._update_combined_task_info(task_id, {
                    'status': '失败',
                    'message': f'影像范围提取失败: {extent_result["message"]}',
                    'progress': 0,
                    'end_time': datetime.now().isoformat(),
                    'results.success': False
                }, task_logger)
                return

            task_logger.info(f"影像范围提取完成: {extent_result['output_path']}")

            # 步骤3: 执行空间变化分析
            self._update_task_status(task_id, '正在运行', '开始空间变化分析...', 60)

            # 更新TaskInfo.json
            self._update_combined_task_info(task_id, {
                'message': '开始空间变化分析...',
                'progress': 60
            }, task_logger)

            spatial_result = self._execute_spatial_analysis(task_id, params, ai_result['output_path'], extent_result['output_path'], task_logger)

            if not spatial_result['success']:
                self._update_task_status(task_id, '失败', f'空间变化分析失败: {spatial_result["message"]}', 0)

                # 更新TaskInfo.json - 空间变化分析失败
                self._update_combined_task_info(task_id, {
                    'status': '失败',
                    'message': f'空间变化分析失败: {spatial_result["message"]}',
                    'progress': 0,
                    'end_time': datetime.now().isoformat(),
                    'results.success': False
                }, task_logger)
                return

            task_logger.info(f"[SUCCESS] 空间变化分析完成: {spatial_result['output_path']}")

            # 步骤4: 完成任务
            self._update_task_status(task_id, '正在运行', '保存任务信息...', 90)

            # 更新TaskInfo.json - 任务完成
            end_time = datetime.now().isoformat()

            # 获取空间分析统计信息
            spatial_statistics = spatial_result.get('statistics', {})
            task_logger.info(f"[STATS] 获取到的空间分析统计信息: {spatial_statistics}")

            # 构建完整的更新数据
            update_data = {
                'status': '完成',
                'message': '合并分析任务完成',
                'progress': 100,
                'end_time': end_time,
                'results.success': True
            }

            # 逐个更新空间统计字段
            if spatial_statistics:
                task_logger.info(f"[PROCESS] 开始更新空间统计字段...")
                for key, value in spatial_statistics.items():
                    field_key = f'results.spatial_statistics.{key}'
                    update_data[field_key] = value
                    task_logger.info(f"  添加字段: {field_key} = {value}")
            else:
                task_logger.warning(f"[WARNING] 空间统计信息为空")

            task_logger.info(f"[PROCESS] 准备更新TaskInfo.json，更新字段: {list(update_data.keys())}")
            self._update_combined_task_info(task_id, update_data, task_logger)
            task_logger.info(f"[SUCCESS] TaskInfo.json最终更新完成")

            # 步骤5: 自动发布到GeoServer
            task_logger.info("[PUBLISH] 开始自动发布到GeoServer...")
            publish_results = self._publish_to_geoserver(task_id, ai_result['output_path'], spatial_result['output_path'], task_logger)

            # 更新TaskInfo.json中的发布信息
            self._update_combined_task_info(task_id, {
                'geoserver_publish': publish_results
            }, task_logger)

            # 完成任务
            if publish_results['overall_success']:
                self._update_task_status(task_id, '完成', '合并分析任务完成，已发布到GeoServer', 100)
                task_logger.info("[COMPLETE] 合并分析任务完成，已成功发布到GeoServer")
            else:
                self._update_task_status(task_id, '完成', '合并分析任务完成，但GeoServer发布部分失败', 100)
                task_logger.warning("[WARNING] 合并分析任务完成，但GeoServer发布部分失败")

            task_logger.info("=== 合并分析任务完成 ===")
            task_logger.info(f"AI分析结果: {ai_result['output_path']}")
            task_logger.info(f"最终分析结果: {spatial_result['output_path']}")
            task_logger.info(f"GeoServer发布状态: {'[SUCCESS] 成功' if publish_results['overall_success'] else '[ERROR] 部分失败'}")
            task_logger.info(f"TaskInfo.json已更新完成")

        except Exception as e:
            error_msg = f"合并分析任务执行失败: {str(e)}"
            if task_logger:
                task_logger.error(error_msg)
                import traceback
                task_logger.error(f"错误详情: {traceback.format_exc()}")

            self._update_task_status(task_id, '失败', error_msg, 0)

            # 更新TaskInfo.json - 异常失败
            self._update_combined_task_info(task_id, {
                'status': '失败',
                'message': error_msg,
                'progress': 0,
                'end_time': datetime.now().isoformat(),
                'results.success': False
            }, task_logger)

            analysis_logger.error(error_msg)

        finally:
            # 清理线程引用
            if task_id in self.running_threads:
                del self.running_threads[task_id]

    def _update_task_status(self, task_id: str, status: str, message: str, progress: int):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id]['status'] = status
            self.tasks[task_id]['message'] = message
            self.tasks[task_id]['progress'] = progress
            self.tasks[task_id]['update_time'] = datetime.now().isoformat()

    def _save_status(self):
        """保存任务状态到文件"""
        try:
            # 这里可以添加状态保存逻辑，目前主要依赖TaskInfo.json
            pass
        except Exception as e:
            analysis_logger.error(f"保存任务状态失败: {str(e)}")

    def _create_taskinfo_immediately(self, task_id: str, task_info: dict, task_logger):
        """立即创建TaskInfo.json模板"""
        try:
            task_logger.info(f"[CONFIG] 开始创建TaskInfo.json模板...")

            # TaskInfo.json文件路径
            task_dir = task_info['parameters']['task_dir']
            task_info_file = os.path.join(task_dir, 'TaskInfo.json')
            task_logger.info(f"[FOLDER] TaskInfo文件路径: {task_info_file}")

            # 确保目录存在
            if not os.path.exists(task_dir):
                os.makedirs(task_dir, exist_ok=True)
                task_logger.info(f"[DIR] 创建任务目录: {task_dir}")

            # 从parameters中获取参数
            params = task_info['parameters']

            # 创建新的任务信息模板
            # 使用统一的时间戳
            unified_timestamp = self._get_unified_timestamp(task_id, task_info['image_id'])
            new_task_info = {
                'task_id': task_id,
                'image_id': task_info['image_id'],
                'analysis_category': params['analysis_category'],
                'timestamp': unified_timestamp,
                'datetime': datetime.now().isoformat(),
                'input_files': {
                    'image_path': params['image_path'],
                    'model_path': params['model_path'],
                    'old_data_path': params['old_data_path']
                },
                'output_files': {
                    'ai_output_path': params['ai_output_path'],
                    'final_output_path': params['final_output_path']
                },
                'parameters': {
                    'model_type': params['model_type'],
                    'num_classes': params['num_classes'],
                    'area_threshold': params['area_threshold']
                },
                'results': {
                    'ai_processing_time': None,
                    'spatial_statistics': {
                        'outflow_count': None,
                        'inflow_count': None,
                        'total_count': None,
                        'outflow_area': None,
                        'inflow_area': None,
                        'area_threshold': params['area_threshold']
                    },
                    'success': None
                },
                'status': '进行中',
                'log_file': os.path.join(self.log_dir, f"{task_id}.log")
            }

            # 读取现有的TaskInfo.json（如果存在）
            existing_tasks = []
            if os.path.exists(task_info_file):
                task_logger.info(f"[FILE] 发现现有TaskInfo文件，正在读取...")
                try:
                    with open(task_info_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        # 确保是数组格式
                        if isinstance(existing_data, list):
                            existing_tasks = existing_data
                            task_logger.info(f"[QUEUE] 读取到数组格式，包含 {len(existing_tasks)} 个历史任务")
                        elif isinstance(existing_data, dict):
                            existing_tasks = [existing_data]
                            task_logger.info(f"[QUEUE] 读取到对象格式，转换为数组格式")
                        else:
                            existing_tasks = []
                            task_logger.warning(f"[WARNING] 未知格式，创建新数组")
                except Exception as e:
                    task_logger.warning(f"[WARNING] 读取历史任务信息失败: {str(e)}")
                    existing_tasks = []
            else:
                task_logger.info(f"[UPDATE] TaskInfo文件不存在，将创建新文件")

            # 检查是否已存在相同task_id的记录
            task_found = False
            for i, task in enumerate(existing_tasks):
                if task.get('task_id') == task_id:
                    # 更新现有记录
                    existing_tasks[i] = new_task_info
                    task_logger.info(f"[PROCESS] 更新现有任务记录: {task_id}")
                    task_found = True
                    break

            if not task_found:
                # 添加新任务到列表
                existing_tasks.append(new_task_info)
                task_logger.info(f"[ADD] 添加新任务记录: {task_id}")

            task_logger.info(f"[SAVE] 准备保存TaskInfo.json到: {task_info_file}")

            # 保存更新后的TaskInfo.json
            with open(task_info_file, 'w', encoding='utf-8') as f:
                json.dump(existing_tasks, f, ensure_ascii=False, indent=2)

            task_logger.info(f"[SUCCESS] TaskInfo.json文件已写入完成")

            # 验证文件是否正确保存
            if os.path.exists(task_info_file):
                file_size = os.path.getsize(task_info_file)
                task_logger.info(f"[STATS] 文件验证: 大小={file_size}字节")
            else:
                task_logger.error(f"[ERROR] 文件保存失败，文件不存在")

            task_logger.info(f"[SUCCESS] TaskInfo.json模板创建完成: {task_info_file}")
            task_logger.info(f"[STATS] 当前任务总数: {len(existing_tasks)}")

        except Exception as e:
            task_logger.error(f"[ERROR] 创建TaskInfo.json模板失败: {str(e)}")
            import traceback
            task_logger.error(f"错误详情: {traceback.format_exc()}")

    def _create_combined_task_info_template(self, task_id: str, task_info: dict, task_logger):
        """创建合并分析TaskInfo.json模板"""
        try:
            task_logger.info(f"[CONFIG] 开始创建TaskInfo.json模板...")

            # 从parameters中获取参数
            params = task_info['parameters']

            # TaskInfo.json文件路径
            task_info_file = os.path.join(params['task_dir'], 'TaskInfo.json')
            task_logger.info(f"[FOLDER] TaskInfo文件路径: {task_info_file}")

            # 确保目录存在
            task_dir = params['task_dir']
            if not os.path.exists(task_dir):
                os.makedirs(task_dir, exist_ok=True)
                task_logger.info(f"[DIR] 创建任务目录: {task_dir}")

            # 创建新的任务信息模板（与现有格式完全一致）
            # 使用统一的时间戳
            unified_timestamp = self._get_unified_timestamp(task_id, task_info['image_id'])
            new_task_info = {
                'task_id': task_id,
                'image_id': task_info['image_id'],
                'analysis_category': params['analysis_category'],
                'timestamp': unified_timestamp,
                'datetime': datetime.now().isoformat(),
                'input_files': {
                    'image_path': params['image_path'],
                    'model_path': params['model_path'],
                    'old_data_path': params['old_data_path']
                },
                'output_files': {
                    'ai_output_path': params['ai_output_path'],
                    'final_output_path': params['final_output_path']
                },
                'parameters': {
                    'model_type': params['model_type'],
                    'num_classes': params['num_classes'],
                    'area_threshold': params['area_threshold']
                },
                'results': {
                    'ai_processing_time': None,
                    'spatial_statistics': {
                        'outflow_count': None,
                        'inflow_count': None,
                        'total_count': None,
                        'outflow_area': None,
                        'inflow_area': None,
                        'area_threshold': params['area_threshold']
                    },
                    'success': None
                },
                'status': '进行中',
                'log_file': os.path.join(self.log_dir, f"{task_id}.log")
            }

            # 读取现有的TaskInfo.json（如果存在）
            existing_tasks = []
            if os.path.exists(task_info_file):
                task_logger.info(f"[FILE] 发现现有TaskInfo文件，正在读取...")
                try:
                    with open(task_info_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        # 确保是数组格式
                        if isinstance(existing_data, list):
                            existing_tasks = existing_data
                            task_logger.info(f"[QUEUE] 读取到数组格式，包含 {len(existing_tasks)} 个历史任务")
                        elif isinstance(existing_data, dict):
                            existing_tasks = [existing_data]
                            task_logger.info(f"[QUEUE] 读取到对象格式，转换为数组格式")
                        else:
                            existing_tasks = []
                            task_logger.warning(f"[WARNING] 未知格式，创建新数组")
                except Exception as e:
                    task_logger.warning(f"[WARNING] 读取历史任务信息失败: {str(e)}")
                    existing_tasks = []
            else:
                task_logger.info(f"[UPDATE] TaskInfo文件不存在，将创建新文件")

            # 检查是否已存在相同task_id的记录
            task_found = False
            for i, task in enumerate(existing_tasks):
                if task.get('task_id') == task_id:
                    existing_tasks[i] = new_task_info
                    task_found = True
                    task_logger.info(f"[UPDATE] 更新现有任务记录: {task_id}")
                    break

            if not task_found:
                existing_tasks.append(new_task_info)
                task_logger.info(f"[ADD] 添加新任务记录: {task_id}")

            task_logger.info(f"[SAVE] 准备保存TaskInfo.json到: {task_info_file}")

            # 保存更新后的TaskInfo.json
            with open(task_info_file, 'w', encoding='utf-8') as f:
                json.dump(existing_tasks, f, ensure_ascii=False, indent=2)

            task_logger.info(f"[SUCCESS] TaskInfo.json文件已写入完成")

            # 验证文件是否正确保存
            if os.path.exists(task_info_file):
                file_size = os.path.getsize(task_info_file)
                task_logger.info(f"[STATS] 文件验证: 大小={file_size}字节")
            else:
                task_logger.error(f"[ERROR] 文件保存失败，文件不存在")

            # 记录TaskInfo路径到任务状态
            self.tasks[task_id]['task_info_path'] = task_info_file

            task_logger.info(f"[SUCCESS] TaskInfo.json模板创建完成: {task_info_file}")
            task_logger.info(f"[STATS] 当前任务总数: {len(existing_tasks)}")

        except Exception as e:
            task_logger.error(f"[ERROR] 创建TaskInfo.json模板失败: {str(e)}")
            import traceback
            task_logger.error(f"错误详情: {traceback.format_exc()}")

    def _update_combined_task_info(self, task_id: str, updates: dict, task_logger=None):
        """更新合并分析TaskInfo.json文件"""
        try:
            # 获取TaskInfo.json路径
            task_info_path = None

            if task_id in self.tasks and 'task_info_path' in self.tasks[task_id]:
                task_info_path = self.tasks[task_id]['task_info_path']
            else:
                # 如果路径丢失，尝试重新构建路径
                if task_logger:
                    task_logger.warning(f"[WARNING] TaskInfo路径丢失，尝试重新构建...")

                # 从任务参数中获取必要信息重建路径
                if task_id in self.tasks:
                    task_params = self.tasks[task_id].get('parameters', {})
                    image_id = task_params.get('image_id')
                    window_data_path = task_params.get('window_data_path', 'D:/Drone_Project/nginxData')

                    if image_id:
                        task_dir = os.path.join(window_data_path, 'ODM', 'AI', image_id)
                        task_info_path = os.path.join(task_dir, 'TaskInfo.json')

                        # 更新路径到任务记录中
                        self.tasks[task_id]['task_info_path'] = task_info_path

                        if task_logger:
                            task_logger.info(f"[LOCATION] 重新构建TaskInfo路径: {task_info_path}")
                    else:
                        if task_logger:
                            task_logger.error(f"[ERROR] 无法获取image_id重建路径")
                else:
                    if task_logger:
                        task_logger.error(f"[ERROR] 任务{task_id}不存在于tasks记录中")

            if not task_info_path:
                if task_logger:
                    task_logger.error(f"[ERROR] 无法确定TaskInfo.json路径")
                return

            if not os.path.exists(task_info_path):
                if task_logger:
                    task_logger.error(f"[ERROR] TaskInfo.json文件不存在: {task_info_path}")
                return

            # 读取现有TaskInfo
            with open(task_info_path, 'r', encoding='utf-8') as f:
                task_info_data = json.load(f)

            # 确保是数组格式
            if isinstance(task_info_data, dict):
                task_info_list = [task_info_data]
            elif isinstance(task_info_data, list):
                task_info_list = task_info_data
            else:
                if task_logger:
                    task_logger.error(f"[ERROR] TaskInfo.json格式不正确")
                return

            # 查找并更新对应的任务记录
            task_found = False
            for i, task_info in enumerate(task_info_list):
                if task_info.get('task_id') == task_id:
                    # 更新字段
                    for key, value in updates.items():
                        if '.' in key:
                            # 支持嵌套字段更新，如 'results.ai_processing_time' 或 'results.spatial_statistics.outflow_count'
                            keys = key.split('.')
                            current = task_info
                            for k in keys[:-1]:
                                if k not in current:
                                    current[k] = {}
                                current = current[k]
                            current[keys[-1]] = value
                            if task_logger:
                                task_logger.info(f"[UPDATE] 更新嵌套字段: {key} = {value}")
                        else:
                            task_info[key] = value
                            if task_logger:
                                task_logger.info(f"[UPDATE] 更新字段: {key} = {value}")

                    task_info_list[i] = task_info
                    task_found = True
                    break

            if not task_found:
                if task_logger:
                    task_logger.warning(f"[WARNING] 未找到task_id为{task_id}的记录，不会创建新记录")
                    task_logger.warning(f"   当前TaskInfo中的task_id列表: {[t.get('task_id', 'N/A') for t in task_info_list]}")
                return

            # 保存更新后的TaskInfo
            with open(task_info_path, 'w', encoding='utf-8') as f:
                json.dump(task_info_list, f, ensure_ascii=False, indent=2)

            if task_logger:
                task_logger.info(f"[UPDATE] TaskInfo.json已更新: {list(updates.keys())}")

                # 验证更新结果
                updated_task = task_info_list[i] if task_found else None
                if updated_task:
                    task_logger.info(f"[SEARCH] 验证更新结果:")
                    task_logger.info(f"  状态: {updated_task.get('status', 'N/A')}")
                    task_logger.info(f"  进度: {updated_task.get('progress', 'N/A')}%")

                    # 验证results字段
                    results = updated_task.get('results', {})
                    if results:
                        task_logger.info(f"  AI处理时间: {results.get('ai_processing_time', 'N/A')}")
                        task_logger.info(f"  成功状态: {results.get('success', 'N/A')}")

                        # 验证空间统计
                        spatial_stats = results.get('spatial_statistics', {})
                        if spatial_stats:
                            task_logger.info(f"  空间统计:")
                            for key, value in spatial_stats.items():
                                if value is not None:
                                    task_logger.info(f"    {key}: {value}")

        except Exception as e:
            if task_logger:
                task_logger.error(f"[ERROR] 更新TaskInfo.json失败: {str(e)}")
                import traceback
                task_logger.error(f"错误详情: {traceback.format_exc()}")
            else:
                analysis_logger.error(f"[ERROR] 更新TaskInfo.json失败: {str(e)}")

    def _execute_ai_segmentation(self, task_id: str, params: dict, task_logger) -> Dict[str, Any]:
        """执行AI语义分割"""
        try:
            task_logger.info("[AI] 开始AI语义分割...")

            # 创建AI模型配置
            config = AIModelConfig(
                image=params['image_path'],
                model=params['model_path'],
                output=params['ai_output_path'],
                model_type=params['model_type'],
                num_classes=params['num_classes'],
                gpu=0  # 默认使用GPU 0
            )

            task_logger.info(f"[DIR] 输入图像: {config.image}")
            task_logger.info(f"[AI] 模型路径: {config.model}")
            task_logger.info(f"[FOLDER] 输出路径: {config.output}")
            task_logger.info(f"[CONFIG] 模型类型: {config.model_type}")

            # 调用现有的AI处理逻辑
            from .analysis_executor import AnalysisExecutor
            ai_executor = AnalysisExecutor()

            # 将真实的任务ID和其他信息传递给AI处理器
            config.combined_task_id = task_id
            config.old_data_path = params.get('old_data_path', 'N/A')
            config.area_threshold = params.get('area_threshold', 400.0)

            # 使用新的AI处理方法
            result = ai_executor._execute_new_ai_processing(config, task_logger)

            if result['success']:
                return {
                    'success': True,
                    'output_path': params['ai_output_path'],
                    'processing_time': result.get('processing_time', 0),
                    'message': 'AI语义分割完成'
                }
            else:
                return {
                    'success': False,
                    'message': result.get('message', 'AI处理失败')
                }

        except Exception as e:
            task_logger.error(f"AI语义分割执行失败: {str(e)}")
            return {
                'success': False,
                'message': f'AI语义分割执行失败: {str(e)}'
            }

    def _extract_image_extent(self, task_id: str, params: dict, task_logger) -> Dict[str, Any]:
        """提取影像有效范围"""
        try:
            task_logger.info("[MAP] 开始提取影像有效范围...")

            # 生成范围文件路径 - 放到与其他SHP文件相同的目录下
            image_id = params['image_id']
            # 使用统一的时间戳
            timestamp = self._get_unified_timestamp(task_id, image_id)
            extent_filename = f"{image_id}_area_{timestamp}.shp"

            # 构建分析类别目录路径
            task_dir = params['task_dir']
            analysis_category = params['analysis_category']
            analysis_category_dir = os.path.join(task_dir, analysis_category)
            extent_output_path = os.path.join(analysis_category_dir, extent_filename)

            # 确保分析类别目录存在
            os.makedirs(analysis_category_dir, exist_ok=True)

            task_logger.info(f"范围文件输出路径: {extent_output_path}")
            task_logger.info(f"分析类别目录: {analysis_category_dir}")

            # 直接调用分析处理器进行同步处理
            from .analysis_processor import analysis_processor

            task_logger.info(f"调用影像范围提取: {params['image_path']} -> {extent_output_path}")

            result = analysis_processor.extract_image_valid_extent(
                image_path=params['image_path'],
                output_path=extent_output_path,
                simplify_tolerance=1.0,
                min_area=1000.0,
                keep_original_crs=True
            )

            if result['success']:
                task_logger.info("[SUCCESS] 影像范围提取成功")
                task_logger.info(f"输出文件: {result['output_path']}")
                task_logger.info(f"有效区域数量: {result['contour_count']}")
                task_logger.info(f"坐标系: {result['coordinate_system']}")
                return {
                    'success': True,
                    'output_path': extent_output_path,
                    'result': result
                }
            else:
                task_logger.error(f"[ERROR] 影像范围提取失败: {result.get('message', '未知错误')}")
                return {
                    'success': False,
                    'message': result.get('message', '影像范围提取失败')
                }

        except Exception as e:
            task_logger.error(f"[ERROR] 影像范围提取异常: {str(e)}")
            return {
                'success': False,
                'message': f'影像范围提取执行失败: {str(e)}'
            }

    def _execute_spatial_analysis(self, task_id: str, params: dict, new_data_path: str, clip_area_path: str, task_logger) -> Dict[str, Any]:
        """执行空间变化分析"""
        try:
            task_logger.info("[STATS] 开始空间变化分析...")

            # 调用现有的空间分析逻辑
            from .analysis_processor import analysis_processor

            # 设置面积阈值
            analysis_processor.area_threshold = params['area_threshold']

            task_logger.info(f"[FOLDER] 历史数据: {params['old_data_path']}")
            task_logger.info(f"[FOLDER] 新数据: {new_data_path}")
            task_logger.info(f"[MEASURE] 面积阈值: {params['area_threshold']} 平方米")

            # 确定输出目录和文件名
            output_dir = os.path.dirname(params['final_output_path'])
            shp_filename = os.path.splitext(os.path.basename(params['final_output_path']))[0]

            # 执行分析
            task_logger.info(f"使用裁剪范围: {clip_area_path}")
            result = analysis_processor.analyze_farmland_changes(
                old_data_path=params['old_data_path'],
                new_data_path=new_data_path,
                output_dir=output_dir,
                shp_filename=shp_filename,
                clip_area_path=clip_area_path
            )

            if result['success']:
                return {
                    'success': True,
                    'output_path': params['final_output_path'],
                    'statistics': result.get('statistics', {}),
                    'message': '空间变化分析完成'
                }
            else:
                return {
                    'success': False,
                    'message': result.get('message', '空间变化分析失败')
                }

        except Exception as e:
            task_logger.error(f"空间变化分析执行失败: {str(e)}")
            return {
                'success': False,
                'message': f'空间变化分析执行失败: {str(e)}'
            }



    def _save_task_info(self, task_id: str, params: dict, ai_result: dict, spatial_result: dict, task_logger) -> Dict[str, Any]:
        """保存任务信息到TaskInfo.json"""
        try:
            task_logger.info("[SAVE] 保存任务信息...")

            # TaskInfo.json文件路径
            task_info_file = os.path.join(params['task_dir'], 'TaskInfo.json')

            # 创建任务信息
            # 使用统一的时间戳
            unified_timestamp = self._get_unified_timestamp(task_id, params['image_id'])
            task_info = {
                'task_id': task_id,
                'image_id': params['image_id'],
                'analysis_category': params['analysis_category'],
                'timestamp': unified_timestamp,
                'datetime': datetime.now().isoformat(),
                'input_files': {
                    'image_path': params['image_path'],
                    'model_path': params['model_path'],
                    'old_data_path': params['old_data_path']
                },
                'output_files': {
                    'ai_output_path': params['ai_output_path'],
                    'final_output_path': params['final_output_path']
                },
                'parameters': {
                    'model_type': params['model_type'],
                    'num_classes': params['num_classes'],
                    'area_threshold': params['area_threshold']
                },
                'results': {
                    'ai_processing_time': ai_result.get('processing_time', 0),
                    'spatial_statistics': spatial_result.get('statistics', {}),
                    'success': True
                },
                'status': '完成',
                'log_file': os.path.join(self.log_dir, f"{task_id}.log")
            }

            # 读取现有的TaskInfo.json（如果存在）
            existing_tasks = []
            if os.path.exists(task_info_file):
                try:
                    with open(task_info_file, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        # 确保是数组格式
                        if isinstance(existing_data, list):
                            existing_tasks = existing_data
                        elif isinstance(existing_data, dict):
                            existing_tasks = [existing_data]
                        else:
                            existing_tasks = []
                    task_logger.info(f"读取到 {len(existing_tasks)} 个历史任务")
                except Exception as e:
                    task_logger.warning(f"读取历史任务信息失败: {str(e)}")
                    existing_tasks = []

            # 检查是否已存在相同task_id的记录，如果存在则更新，否则添加
            task_found = False
            for i, existing_task in enumerate(existing_tasks):
                if existing_task.get('task_id') == task_id:
                    existing_tasks[i] = task_info
                    task_found = True
                    task_logger.info(f"更新现有任务记录: {task_id}")
                    break

            if not task_found:
                existing_tasks.append(task_info)
                task_logger.info(f"添加新任务记录: {task_id}")

            # 保存更新后的任务信息
            with open(task_info_file, 'w', encoding='utf-8') as f:
                json.dump(existing_tasks, f, ensure_ascii=False, indent=2)

            task_logger.info(f"任务信息已保存: {task_info_file}")
            task_logger.info(f"当前影像共有 {len(existing_tasks)} 个分析任务")

            return {
                'success': True,
                'task_info_file': task_info_file,
                'total_tasks': len(existing_tasks)
            }

        except Exception as e:
            task_logger.error(f"保存任务信息失败: {str(e)}")
            return {
                'success': False,
                'message': f'保存任务信息失败: {str(e)}'
            }

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return {
                'success': False,
                'message': '任务不存在'
            }

        task_info = self.tasks[task_id].copy()
        return {
            'success': True,
            'data': task_info
        }

    def get_all_tasks(self) -> Dict[str, Any]:
        """获取所有任务状态"""
        return {
            'success': True,
            'data': {
                'tasks': list(self.tasks.values()),
                'running_count': len(self.running_threads),
                'total_count': len(self.tasks)
            }
        }

    def _publish_to_geoserver(self, task_id: str, ai_output_path: str, final_output_path: str, task_logger) -> Dict[str, Any]:
        """
        自动发布分析结果到GeoServer

        参数:
            task_id: 任务ID
            ai_output_path: AI分析结果路径
            final_output_path: 最终分析结果路径
            task_logger: 任务日志记录器

        返回:
            dict: 发布结果
        """
        try:
            task_logger.info("[PUBLISH] 开始自动发布分析结果到GeoServer")

            # 获取任务参数
            task_info = self.tasks.get(task_id, {})
            parameters = task_info.get('parameters', {})
            analysis_category = parameters.get('analysis_category', 'unknown')

            task_logger.info(f"[STATS] 发布参数:")
            task_logger.info(f"  工作区: {analysis_category}")
            task_logger.info(f"  坐标系: EPSG:32648")
            task_logger.info(f"  AI结果: {ai_output_path}")
            task_logger.info(f"  最终结果: {final_output_path}")

            # 发布结果统计
            publish_results = {
                'ai_result': {'success': False, 'message': '', 'layer_name': ''},
                'final_result': {'success': False, 'message': '', 'layer_name': ''},
                'overall_success': False,
                'workspace': analysis_category,
                'epsg': '32648',
                'publish_time': time.time()
            }

            # 导入发布功能
            import requests

            # GeoServer发布接口URL
            publish_url = f"{DJANGO_BASE_URL}/api/geo/shapefile/publish-with-crs/"

            # 1. 发布AI分析结果
            if ai_output_path and os.path.exists(ai_output_path):
                task_logger.info("[UPLOAD] 发布AI分析结果...")

                ai_layer_name = os.path.splitext(os.path.basename(ai_output_path))[0]
                ai_params = {
                    'file_path': ai_output_path,
                    'workspace': analysis_category,
                    'store': f"{ai_layer_name}_store",
                    'layer': ai_layer_name,
                    'epsg': '32648'
                }

                # 使用重试机制发布AI结果
                ai_publish_success = False
                max_retries = 3
                timeout_seconds = 120  # 增加到120秒

                for attempt in range(max_retries):
                    try:
                        task_logger.info(f"[UPLOAD] 发布AI结果尝试 {attempt + 1}/{max_retries}...")
                        response = requests.get(publish_url, params=ai_params, timeout=timeout_seconds)

                        if response.status_code == 200:
                            result = response.json()
                            if result.get('status') == 'success':
                                publish_results['ai_result'] = {
                                    'success': True,
                                    'message': result.get('message', '发布成功'),
                                    'layer_name': ai_layer_name,
                                    'store_name': ai_params['store'],
                                    'details': result.get('details', {})
                                }
                                task_logger.info(f"[SUCCESS] AI结果发布成功: {analysis_category}:{ai_layer_name}")
                                ai_publish_success = True
                                break  # 成功后跳出重试循环
                            else:
                                error_msg = result.get('message', '发布失败')
                                task_logger.error(f"[ERROR] AI结果发布失败: {error_msg}")
                                if attempt == max_retries - 1:  # 最后一次尝试
                                    publish_results['ai_result'] = {
                                        'success': False,
                                        'message': error_msg,
                                        'layer_name': ai_layer_name
                                    }
                        else:
                            error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                            task_logger.error(f"[ERROR] AI结果发布请求失败: {error_msg}")
                            if attempt == max_retries - 1:  # 最后一次尝试
                                publish_results['ai_result'] = {
                                    'success': False,
                                    'message': error_msg,
                                    'layer_name': ai_layer_name
                                }

                    except Exception as e:
                        error_msg = f"发布AI结果异常: {str(e)}"
                        task_logger.error(f"[ERROR] 尝试 {attempt + 1} 失败: {error_msg}")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            publish_results['ai_result'] = {
                                'success': False,
                                'message': error_msg,
                                'layer_name': ai_layer_name
                            }
                        else:
                            # 等待一段时间后重试
                            time.sleep(5)

                # 如果所有重试都失败了
                if not ai_publish_success and 'ai_result' not in publish_results:
                    publish_results['ai_result'] = {
                        'success': False,
                        'message': '所有重试尝试都失败',
                        'layer_name': ai_layer_name
                    }
                    task_logger.error(f"[ERROR] AI结果发布失败: 所有 {max_retries} 次重试都失败")
            else:
                publish_results['ai_result'] = {
                    'success': False,
                    'message': f'AI结果文件不存在: {ai_output_path}',
                    'layer_name': ''
                }
                task_logger.warning(f"[WARNING] AI结果文件不存在，跳过发布: {ai_output_path}")

            # 2. 发布最终分析结果
            if final_output_path and os.path.exists(final_output_path):
                task_logger.info("[UPLOAD] 发布最终分析结果...")

                final_layer_name = os.path.splitext(os.path.basename(final_output_path))[0]
                final_params = {
                    'file_path': final_output_path,
                    'workspace': analysis_category,
                    'store': f"{final_layer_name}_store",
                    'layer': final_layer_name,
                    'epsg': '32648'
                }

                # 使用重试机制发布最终结果
                final_publish_success = False

                for attempt in range(max_retries):
                    try:
                        task_logger.info(f"[UPLOAD] 发布最终结果尝试 {attempt + 1}/{max_retries}...")
                        response = requests.get(publish_url, params=final_params, timeout=timeout_seconds)

                        if response.status_code == 200:
                            result = response.json()
                            if result.get('status') == 'success':
                                publish_results['final_result'] = {
                                    'success': True,
                                    'message': result.get('message', '发布成功'),
                                    'layer_name': final_layer_name,
                                    'store_name': final_params['store'],
                                    'details': result.get('details', {})
                                }
                                task_logger.info(f"[SUCCESS] 最终结果发布成功: {analysis_category}:{final_layer_name}")
                                final_publish_success = True
                                break  # 成功后跳出重试循环
                            else:
                                error_msg = result.get('message', '发布失败')
                                task_logger.error(f"[ERROR] 最终结果发布失败: {error_msg}")
                                if attempt == max_retries - 1:  # 最后一次尝试
                                    publish_results['final_result'] = {
                                        'success': False,
                                        'message': error_msg,
                                        'layer_name': final_layer_name
                                    }
                        else:
                            error_msg = f"HTTP {response.status_code}: {response.text[:200]}"
                            task_logger.error(f"[ERROR] 最终结果发布请求失败: {error_msg}")
                            if attempt == max_retries - 1:  # 最后一次尝试
                                publish_results['final_result'] = {
                                    'success': False,
                                    'message': error_msg,
                                    'layer_name': final_layer_name
                                }

                    except Exception as e:
                        error_msg = f"发布最终结果异常: {str(e)}"
                        task_logger.error(f"[ERROR] 尝试 {attempt + 1} 失败: {error_msg}")
                        if attempt == max_retries - 1:  # 最后一次尝试
                            publish_results['final_result'] = {
                                'success': False,
                                'message': error_msg,
                                'layer_name': final_layer_name
                            }
                        else:
                            # 等待一段时间后重试
                            time.sleep(5)

                # 如果所有重试都失败了
                if not final_publish_success and 'final_result' not in publish_results:
                    publish_results['final_result'] = {
                        'success': False,
                        'message': '所有重试尝试都失败',
                        'layer_name': final_layer_name
                    }
                    task_logger.error(f"[ERROR] 最终结果发布失败: 所有 {max_retries} 次重试都失败")
                    error_msg = f"发布最终结果异常: {str(e)}"
                    publish_results['final_result'] = {
                        'success': False,
                        'message': error_msg,
                        'layer_name': final_layer_name
                    }
                    task_logger.error(f"[ERROR] {error_msg}")
            else:
                publish_results['final_result'] = {
                    'success': False,
                    'message': f'最终结果文件不存在: {final_output_path}',
                    'layer_name': ''
                }
                task_logger.warning(f"[WARNING] 最终结果文件不存在，跳过发布: {final_output_path}")

            # 3. 计算总体发布结果
            ai_success = publish_results['ai_result']['success']
            final_success = publish_results['final_result']['success']
            publish_results['overall_success'] = ai_success and final_success

            # 4. 记录发布总结
            task_logger.info("[STATS] GeoServer发布总结:")
            task_logger.info(f"  AI结果: {'[SUCCESS] 成功' if ai_success else '[ERROR] 失败'}")
            task_logger.info(f"  最终结果: {'[SUCCESS] 成功' if final_success else '[ERROR] 失败'}")
            task_logger.info(f"  总体状态: {'[SUCCESS] 成功' if publish_results['overall_success'] else '[ERROR] 部分失败'}")

            if publish_results['overall_success']:
                task_logger.info("[COMPLETE] 所有分析结果已成功发布到GeoServer")
            else:
                task_logger.warning("[WARNING] 部分分析结果发布失败，请检查日志")

            return publish_results

        except Exception as e:
            error_msg = f"GeoServer发布过程异常: {str(e)}"
            task_logger.error(f"[ERROR] {error_msg}")
            import traceback
            task_logger.error(f"错误详情: {traceback.format_exc()}")

            return {
                'ai_result': {'success': False, 'message': error_msg, 'layer_name': ''},
                'final_result': {'success': False, 'message': error_msg, 'layer_name': ''},
                'overall_success': False,
                'workspace': analysis_category,
                'epsg': '32648',
                'error': error_msg,
                'publish_time': time.time()
            }

# 创建全局实例
combined_analysis_executor = CombinedAnalysisExecutor()
