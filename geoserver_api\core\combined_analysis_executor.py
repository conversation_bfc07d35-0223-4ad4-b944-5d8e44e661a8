#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
合并分析执行器

功能说明:
- 合并AI语义分割和空间变化分析功能
- 统一的任务管理和日志记录
- 读取配置文件和管理输出路径
- 生成详细的任务信息JSON

处理流程:
1. 读取配置文件 (Task.cfg)
2. 执行AI语义分割
3. 执行空间变化分析
4. 保存结果和任务信息
"""

import os
import logging
import threading
import time
import uuid
import json
import configparser
from datetime import datetime
from typing import Dict, Any, Optional

# 导入现有的执行器
from .analysis_executor import analysis_executor, analysis_logger, ANALYSIS_LOGS_DIR
from .ai_model_processor import AIModelConfig, ai_model_processor

# 尝试导入AI模块
try:
    AI_AVAILABLE = True
except ImportError as e:
    AI_AVAILABLE = False
    print(f"AI模块不可用: {e}")

class CombinedAnalysisExecutor:
    """合并分析执行器"""
    
    def __init__(self):
        self.tasks = {}
        self.running_threads = {}
        self.log_dir = ANALYSIS_LOGS_DIR
        
        # 确保日志目录存在
        if not os.path.exists(self.log_dir):
            os.makedirs(self.log_dir)
    
    def execute_combined_analysis(self, image_id: str, image_path: str, model_path: str,
                                old_data_path: str, area_threshold: float = 400.0,
                                model_type: str = "deeplabv3_plus", num_classes: int = 2) -> Dict[str, Any]:
        """
        执行合并分析任务
        
        参数:
            image_id: 影像ID
            image_path: TIF图像路径
            model_path: 模型权重路径
            old_data_path: 历史SHP数据路径
            area_threshold: 面积阈值
            model_type: 模型类型
            num_classes: 类别数量
            
        返回:
            dict: 任务信息
        """
        if not AI_AVAILABLE:
            return {
                'success': False,
                'message': 'AI模块不可用，请检查依赖安装',
                'task_id': None
            }
        
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 读取配置文件
            config_info = self._read_config()
            if not config_info['success']:
                return {
                    'success': False,
                    'message': f'读取配置文件失败: {config_info["message"]}',
                    'task_id': None
                }
            
            window_data_path = config_info['window_data_path']
            
            # 从模型路径中提取分析类别
            analysis_category = self._extract_category_from_model_path(model_path)
            
            # 生成输出路径
            timestamp = int(datetime.now().timestamp())
            ai_output_path, final_output_path, task_dir = self._generate_output_paths(
                window_data_path, image_id, analysis_category, timestamp
            )
            
            # 创建任务专用日志
            task_logger = self._create_task_logger(task_id)
            task_log_file = os.path.join(self.log_dir, f"{task_id}.log")
            
            # 记录任务信息
            task_info = {
                'task_id': task_id,
                'image_id': image_id,
                'status': '等待中',
                'message': '任务已创建，等待执行',
                'progress': 0,
                'start_time': datetime.now().isoformat(),
                'log_file': task_log_file,
                'parameters': {
                    'image_id': image_id,
                    'image_path': image_path,
                    'model_path': model_path,
                    'old_data_path': old_data_path,
                    'area_threshold': area_threshold,
                    'model_type': model_type,
                    'num_classes': num_classes,
                    'analysis_category': analysis_category,
                    'ai_output_path': ai_output_path,
                    'final_output_path': final_output_path,
                    'task_dir': task_dir,
                    'window_data_path': window_data_path
                }
            }
            
            self.tasks[task_id] = task_info
            
            analysis_logger.info(f"开始合并分析任务: {task_id}")
            task_logger.info(f"=== 合并分析任务开始 ===")
            task_logger.info(f"任务ID: {task_id}")
            task_logger.info(f"影像ID: {image_id}")
            task_logger.info(f"TIF图像: {image_path}")
            task_logger.info(f"模型权重: {model_path}")
            task_logger.info(f"历史数据: {old_data_path}")
            task_logger.info(f"分析类别: {analysis_category}")
            task_logger.info(f"AI输出路径: {ai_output_path}")
            task_logger.info(f"最终输出路径: {final_output_path}")
            task_logger.info(f"任务目录: {task_dir}")
            
            # 启动后台任务
            thread = threading.Thread(
                target=self._run_combined_task,
                args=(task_id,),
                daemon=True
            )
            thread.start()
            self.running_threads[task_id] = thread
            
            analysis_logger.info(f"合并分析任务已启动: {task_id}")
            
            return {
                'success': True,
                'task_id': task_id,
                'message': '合并分析任务已启动',
                'status': '等待中'
            }
            
        except Exception as e:
            error_msg = f"启动合并分析任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e),
                'task_id': None
            }
    
    def _read_config(self) -> Dict[str, Any]:
        """读取配置文件"""
        try:
            # 配置文件路径 (假设在127.0.0.1:81对应的目录)
            config_path = "D:/Drone_Project/nginxData/ODM/Task.cfg"
            
            if not os.path.exists(config_path):
                return {
                    'success': False,
                    'message': f'配置文件不存在: {config_path}'
                }
            
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            
            # 读取window_data_path
            window_data_path = config.get('PATHS', 'window_data_path', fallback=None)
            
            if not window_data_path:
                return {
                    'success': False,
                    'message': '配置文件中缺少window_data_path'
                }
            
            return {
                'success': True,
                'window_data_path': window_data_path,
                'config': config
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'读取配置文件失败: {str(e)}'
            }
    
    def _extract_category_from_model_path(self, model_path: str) -> str:
        """从模型路径中提取分析类别"""
        try:
            # 模型路径格式: .../AIWeight/arableLand/deeplabv3_plus/xxx.pth
            path_parts = model_path.replace('\\', '/').split('/')
            
            # 查找AIWeight后面的目录
            for i, part in enumerate(path_parts):
                if part == 'AIWeight' and i + 1 < len(path_parts):
                    return path_parts[i + 1]
            
            # 如果没找到，返回默认值
            return 'unknown'
            
        except Exception as e:
            analysis_logger.warning(f"提取分析类别失败: {str(e)}")
            return 'unknown'
    
    def _generate_output_paths(self, window_data_path: str, image_id: str, 
                             analysis_category: str, timestamp: int) -> tuple:
        """生成输出路径"""
        # 任务目录: window_data_path/ODM/AI/影像ID/
        task_dir = os.path.join(window_data_path, "ODM", "AI", image_id)
        
        # 确保任务目录存在
        if not os.path.exists(task_dir):
            os.makedirs(task_dir)
        
        # AI分析输出路径: 分析类别/影像ID_1_时间戳.shp
        ai_output_filename = f"{image_id}_1_{timestamp}.shp"
        ai_output_path = os.path.join(task_dir, analysis_category, ai_output_filename)
        
        # 最终分析输出路径: 分析类别/影像ID_2_时间戳.shp
        final_output_filename = f"{image_id}_2_{timestamp}.shp"
        final_output_path = os.path.join(task_dir, analysis_category, final_output_filename)
        
        # 确保分析类别目录存在
        category_dir = os.path.join(task_dir, analysis_category)
        if not os.path.exists(category_dir):
            os.makedirs(category_dir)
        
        return ai_output_path, final_output_path, task_dir
    
    def _create_task_logger(self, task_id: str):
        """创建任务专用日志记录器"""
        logger = logging.getLogger(f'combined_task_{task_id}')
        logger.setLevel(logging.INFO)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建文件处理器
        log_file = os.path.join(self.log_dir, f"{task_id}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 设置格式
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        return logger

    def _run_combined_task(self, task_id: str):
        """运行合并分析任务（在后台线程中执行）"""
        task_logger = None
        try:
            task_info = self.tasks[task_id]
            params = task_info['parameters']

            # 获取任务专用日志记录器
            task_logger = logging.getLogger(f'combined_task_{task_id}')

            task_logger.info("=== 开始执行合并分析任务 ===")

            # 更新任务状态
            self._update_task_status(task_id, '正在运行', '开始AI语义分割...', 5)

            # 步骤1: 执行AI语义分割
            ai_result = self._execute_ai_segmentation(task_id, params, task_logger)

            if not ai_result['success']:
                self._update_task_status(task_id, '失败', f'AI语义分割失败: {ai_result["message"]}', 0)
                return

            task_logger.info(f"AI语义分割完成: {ai_result['output_path']}")

            # 步骤2: 提取影像有效范围
            self._update_task_status(task_id, '正在运行', '提取影像有效范围...', 45)

            extent_result = self._extract_image_extent(task_id, params, task_logger)

            if not extent_result['success']:
                self._update_task_status(task_id, '失败', f'影像范围提取失败: {extent_result["message"]}', 0)
                return

            task_logger.info(f"影像范围提取完成: {extent_result['output_path']}")

            # 步骤3: 执行空间变化分析
            self._update_task_status(task_id, '正在运行', '开始空间变化分析...', 60)

            spatial_result = self._execute_spatial_analysis(task_id, params, ai_result['output_path'], extent_result['output_path'], task_logger)

            if not spatial_result['success']:
                self._update_task_status(task_id, '失败', f'空间变化分析失败: {spatial_result["message"]}', 0)
                return

            task_logger.info(f"空间变化分析完成: {spatial_result['output_path']}")

            # 步骤3: 保存任务信息
            self._update_task_status(task_id, '正在运行', '保存任务信息...', 90)

            task_info_result = self._save_task_info(task_id, params, ai_result, spatial_result, task_logger)

            # 完成任务
            self._update_task_status(task_id, '完成', '合并分析任务完成', 100)

            task_logger.info("=== 合并分析任务完成 ===")
            task_logger.info(f"AI分析结果: {ai_result['output_path']}")
            task_logger.info(f"最终分析结果: {spatial_result['output_path']}")
            task_logger.info(f"任务信息文件: {task_info_result.get('task_info_file', 'N/A')}")

        except Exception as e:
            error_msg = f"合并分析任务执行失败: {str(e)}"
            if task_logger:
                task_logger.error(error_msg)
                import traceback
                task_logger.error(f"错误详情: {traceback.format_exc()}")

            self._update_task_status(task_id, '失败', error_msg, 0)
            analysis_logger.error(error_msg)

        finally:
            # 清理线程引用
            if task_id in self.running_threads:
                del self.running_threads[task_id]

    def _update_task_status(self, task_id: str, status: str, message: str, progress: int):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id]['status'] = status
            self.tasks[task_id]['message'] = message
            self.tasks[task_id]['progress'] = progress
            self.tasks[task_id]['update_time'] = datetime.now().isoformat()

    def _execute_ai_segmentation(self, task_id: str, params: dict, task_logger) -> Dict[str, Any]:
        """执行AI语义分割"""
        try:
            task_logger.info("🤖 开始AI语义分割...")

            # 创建AI模型配置
            config = AIModelConfig(
                image=params['image_path'],
                model=params['model_path'],
                output=params['ai_output_path'],
                model_type=params['model_type'],
                num_classes=params['num_classes'],
                gpu=0  # 默认使用GPU 0
            )

            task_logger.info(f"📂 输入图像: {config.image}")
            task_logger.info(f"🤖 模型路径: {config.model}")
            task_logger.info(f"📁 输出路径: {config.output}")
            task_logger.info(f"🔧 模型类型: {config.model_type}")

            # 调用现有的AI处理逻辑
            from .analysis_executor import AnalysisExecutor
            ai_executor = AnalysisExecutor()

            # 使用新的AI处理方法
            result = ai_executor._execute_new_ai_processing(config, task_logger)

            if result['success']:
                return {
                    'success': True,
                    'output_path': params['ai_output_path'],
                    'processing_time': result.get('processing_time', 0),
                    'message': 'AI语义分割完成'
                }
            else:
                return {
                    'success': False,
                    'message': result.get('message', 'AI处理失败')
                }

        except Exception as e:
            task_logger.error(f"AI语义分割执行失败: {str(e)}")
            return {
                'success': False,
                'message': f'AI语义分割执行失败: {str(e)}'
            }

    def _extract_image_extent(self, task_id: str, params: dict, task_logger) -> Dict[str, Any]:
        """提取影像有效范围"""
        try:
            task_logger.info("🗺️ 开始提取影像有效范围...")

            # 生成范围文件路径
            image_id = params['image_id']
            timestamp = int(time.time())
            extent_filename = f"{image_id}_area_{timestamp}.shp"

            # 获取输出目录
            task_dir = params['task_dir']
            extent_output_path = os.path.join(task_dir, extent_filename)

            task_logger.info(f"范围文件输出路径: {extent_output_path}")

            # 直接调用分析处理器进行同步处理
            from .analysis_processor import analysis_processor

            task_logger.info(f"调用影像范围提取: {params['image_path']} -> {extent_output_path}")

            result = analysis_processor.extract_image_valid_extent(
                image_path=params['image_path'],
                output_path=extent_output_path,
                simplify_tolerance=1.0,
                min_area=1000.0,
                keep_original_crs=True
            )

            if result['success']:
                task_logger.info("✅ 影像范围提取成功")
                task_logger.info(f"输出文件: {result['output_path']}")
                task_logger.info(f"有效区域数量: {result['contour_count']}")
                task_logger.info(f"坐标系: {result['coordinate_system']}")
                return {
                    'success': True,
                    'output_path': extent_output_path,
                    'result': result
                }
            else:
                task_logger.error(f"❌ 影像范围提取失败: {result.get('message', '未知错误')}")
                return {
                    'success': False,
                    'message': result.get('message', '影像范围提取失败')
                }

        except Exception as e:
            task_logger.error(f"❌ 影像范围提取异常: {str(e)}")
            return {
                'success': False,
                'message': f'影像范围提取执行失败: {str(e)}'
            }

    def _execute_spatial_analysis(self, task_id: str, params: dict, new_data_path: str, clip_area_path: str, task_logger) -> Dict[str, Any]:
        """执行空间变化分析"""
        try:
            task_logger.info("📊 开始空间变化分析...")

            # 调用现有的空间分析逻辑
            from .analysis_processor import analysis_processor

            # 设置面积阈值
            analysis_processor.area_threshold = params['area_threshold']

            task_logger.info(f"📁 历史数据: {params['old_data_path']}")
            task_logger.info(f"📁 新数据: {new_data_path}")
            task_logger.info(f"📏 面积阈值: {params['area_threshold']} 平方米")

            # 确定输出目录和文件名
            output_dir = os.path.dirname(params['final_output_path'])
            shp_filename = os.path.splitext(os.path.basename(params['final_output_path']))[0]

            # 执行分析
            task_logger.info(f"使用裁剪范围: {clip_area_path}")
            result = analysis_processor.analyze_farmland_changes(
                old_data_path=params['old_data_path'],
                new_data_path=new_data_path,
                output_dir=output_dir,
                shp_filename=shp_filename,
                clip_area_path=clip_area_path
            )

            if result['success']:
                return {
                    'success': True,
                    'output_path': params['final_output_path'],
                    'statistics': result.get('statistics', {}),
                    'message': '空间变化分析完成'
                }
            else:
                return {
                    'success': False,
                    'message': result.get('message', '空间变化分析失败')
                }

        except Exception as e:
            task_logger.error(f"空间变化分析执行失败: {str(e)}")
            return {
                'success': False,
                'message': f'空间变化分析执行失败: {str(e)}'
            }

    def _save_task_info(self, task_id: str, params: dict, ai_result: dict, spatial_result: dict, task_logger) -> Dict[str, Any]:
        """保存任务信息到TaskInfo.json"""
        try:
            task_logger.info("💾 保存任务信息...")

            # TaskInfo.json文件路径
            task_info_file = os.path.join(params['task_dir'], 'TaskInfo.json')

            # 创建任务信息
            task_info = {
                'task_id': task_id,
                'image_id': params['image_id'],
                'analysis_category': params['analysis_category'],
                'timestamp': int(datetime.now().timestamp()),
                'datetime': datetime.now().isoformat(),
                'input_files': {
                    'image_path': params['image_path'],
                    'model_path': params['model_path'],
                    'old_data_path': params['old_data_path']
                },
                'output_files': {
                    'ai_output_path': params['ai_output_path'],
                    'final_output_path': params['final_output_path']
                },
                'parameters': {
                    'model_type': params['model_type'],
                    'num_classes': params['num_classes'],
                    'area_threshold': params['area_threshold']
                },
                'results': {
                    'ai_processing_time': ai_result.get('processing_time', 0),
                    'spatial_statistics': spatial_result.get('statistics', {}),
                    'success': True
                },
                'status': '完成',
                'log_file': os.path.join(self.log_dir, f"{task_id}.log")
            }

            # 读取现有的TaskInfo.json（如果存在）
            existing_tasks = []
            if os.path.exists(task_info_file):
                try:
                    with open(task_info_file, 'r', encoding='utf-8') as f:
                        existing_tasks = json.load(f)
                    task_logger.info(f"读取到 {len(existing_tasks)} 个历史任务")
                except Exception as e:
                    task_logger.warning(f"读取历史任务信息失败: {str(e)}")
                    existing_tasks = []

            # 添加新任务到列表
            existing_tasks.append(task_info)

            # 保存更新后的任务信息
            with open(task_info_file, 'w', encoding='utf-8') as f:
                json.dump(existing_tasks, f, ensure_ascii=False, indent=2)

            task_logger.info(f"任务信息已保存: {task_info_file}")
            task_logger.info(f"当前影像共有 {len(existing_tasks)} 个分析任务")

            return {
                'success': True,
                'task_info_file': task_info_file,
                'total_tasks': len(existing_tasks)
            }

        except Exception as e:
            task_logger.error(f"保存任务信息失败: {str(e)}")
            return {
                'success': False,
                'message': f'保存任务信息失败: {str(e)}'
            }

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        if task_id not in self.tasks:
            return {
                'success': False,
                'message': '任务不存在'
            }

        task_info = self.tasks[task_id].copy()
        return {
            'success': True,
            'data': task_info
        }

    def get_all_tasks(self) -> Dict[str, Any]:
        """获取所有任务状态"""
        return {
            'success': True,
            'data': {
                'tasks': list(self.tasks.values()),
                'running_count': len(self.running_threads),
                'total_count': len(self.tasks)
            }
        }

# 创建全局实例
combined_analysis_executor = CombinedAnalysisExecutor()
