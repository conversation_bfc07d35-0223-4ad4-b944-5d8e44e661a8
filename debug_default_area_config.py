#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试默认面积配置读取问题
"""

import requests
import configparser
import io

def debug_task_cfg_parsing():
    """调试Task.cfg文件解析"""
    print("🔍 调试Task.cfg文件解析...")
    
    try:
        # 1. 直接读取Task.cfg文件内容
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        response = requests.get(config_url, timeout=10)
        
        if response.status_code == 200:
            config_content = response.text
            print(f"✅ Task.cfg文件读取成功")
            print(f"📊 文件大小: {len(config_content)} 字符")
            
            # 2. 显示原始文件内容
            print(f"\n📄 原始文件内容:")
            lines = config_content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'Default_area' in line or ('arableLand' in line and '=' in line) or ('constructionLand' in line and '=' in line):
                    print(f"  {i:3d}: '{line}' (长度: {len(line)})")
            
            # 3. 手动解析Default_area节
            print(f"\n🔧 手动解析Default_area节:")
            in_default_area = False
            default_areas_manual = {}
            
            for i, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                if line_stripped == '[Default_area]':
                    in_default_area = True
                    print(f"  找到Default_area节开始 (第{i}行)")
                    continue
                elif line_stripped.startswith('[') and line_stripped.endswith(']') and in_default_area:
                    in_default_area = False
                    print(f"  Default_area节结束 (第{i}行): {line_stripped}")
                    continue
                
                if in_default_area and '=' in line_stripped:
                    try:
                        key, value = line_stripped.split('=', 1)
                        key = key.strip()
                        value = value.strip()
                        default_areas_manual[key] = value
                        print(f"  解析到: '{key}' = '{value}' (第{i}行)")
                        
                        # 尝试转换为数值
                        try:
                            numeric_value = float(value)
                            print(f"    转换为数值: {numeric_value}")
                        except (ValueError, TypeError) as e:
                            print(f"    ❌ 数值转换失败: {e}")
                            
                    except Exception as e:
                        print(f"  ❌ 解析失败 (第{i}行): {line_stripped}, 错误: {e}")
            
            print(f"\n📋 手动解析结果:")
            for key, value in default_areas_manual.items():
                print(f"  {key}: {value}")
            
            # 4. 使用configparser解析
            print(f"\n🔧 使用configparser解析:")
            try:
                config = configparser.ConfigParser()
                config.read_string(config_content)
                
                if 'Default_area' in config:
                    print(f"  ✅ 找到Default_area节")
                    default_area_section = config['Default_area']
                    
                    for key in default_area_section:
                        value = default_area_section[key]
                        print(f"  {key}: '{value}'")
                        
                        # 尝试转换
                        try:
                            numeric_value = float(value)
                            print(f"    转换为数值: {numeric_value}")
                        except Exception as e:
                            print(f"    ❌ 转换失败: {e}")
                else:
                    print(f"  ❌ 未找到Default_area节")
                    print(f"  可用节: {list(config.sections())}")
                    
            except Exception as e:
                print(f"  ❌ configparser解析失败: {e}")
            
            # 5. 模拟权重视图中的解析逻辑
            print(f"\n🔧 模拟权重视图解析逻辑:")
            simulate_weight_view_parsing(config_content)
            
        else:
            print(f"❌ 无法读取Task.cfg文件: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 调试Task.cfg解析失败: {e}")

def simulate_weight_view_parsing(config_content):
    """模拟权重视图中的解析逻辑"""
    try:
        config = configparser.ConfigParser()
        config.read_string(config_content)
        
        # 模拟权重视图中的逻辑
        default_areas = {}
        if 'Default_area' in config:
            print(f"  ✅ 找到Default_area配置节")
            default_areas = dict(config['Default_area'])
            print(f"  原始配置: {default_areas}")
            
            # 转换为数值类型
            for key, value in default_areas.items():
                print(f"  处理 {key}: '{value}'")
                try:
                    original_value = value
                    numeric_value = float(value)
                    default_areas[key] = numeric_value
                    print(f"    ✅ 转换成功: '{original_value}' -> {numeric_value}")
                except (ValueError, TypeError) as e:
                    print(f"    ❌ 转换失败: '{value}' -> 400.0 (默认值), 错误: {e}")
                    default_areas[key] = 400.0
            
            print(f"  最终配置: {default_areas}")
        else:
            print(f"  ❌ 未找到Default_area配置节")
            default_areas = {
                'arableLand': 400.0,
                'constructionLand': 200.0
            }
            print(f"  使用默认配置: {default_areas}")
        
        return default_areas
        
    except Exception as e:
        print(f"  ❌ 模拟解析失败: {e}")
        return {}

def test_weight_info_debug():
    """测试权重信息接口并显示调试信息"""
    print(f"\n🧪 测试权重信息接口...")
    
    try:
        base_url = "http://127.0.0.1:8091/api/analysis"
        response = requests.get(f"{base_url}/weight-info/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            
            data = result.get('data', {})
            print(f"\n📊 接口返回的默认面积:")
            
            for land_type, info in data.items():
                default_area = info.get('default_area', 'N/A')
                print(f"  {land_type}: {default_area}")
                
                # 检查是否正确
                expected_areas = {
                    'arableLand': 400.0,
                    'constructionLand': 200.0
                }
                
                if land_type in expected_areas:
                    expected = expected_areas[land_type]
                    if abs(float(default_area) - expected) < 0.01:
                        print(f"    ✅ 正确")
                    else:
                        print(f"    ❌ 错误，期望: {expected}")
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试权重信息接口失败: {e}")

def check_config_file_encoding():
    """检查配置文件编码"""
    print(f"\n🔍 检查配置文件编码...")
    
    try:
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        response = requests.get(config_url, timeout=10)
        
        if response.status_code == 200:
            # 尝试不同编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'ascii']
            
            for encoding in encodings:
                try:
                    if hasattr(response, 'content'):
                        content = response.content.decode(encoding)
                        print(f"  ✅ {encoding} 编码解析成功")
                        
                        # 查找Default_area相关内容
                        if 'constructionLand' in content:
                            lines = content.split('\n')
                            for line in lines:
                                if 'constructionLand' in line and '=' in line:
                                    print(f"    constructionLand行: '{line.strip()}'")
                                    # 显示每个字符的ASCII码
                                    chars = [f"'{c}'({ord(c)})" for c in line.strip()]
                                    print(f"    字符详情: {' '.join(chars)}")
                        break
                except Exception as e:
                    print(f"  ❌ {encoding} 编码解析失败: {e}")
                    
    except Exception as e:
        print(f"❌ 检查配置文件编码失败: {e}")

def suggest_fixes():
    """建议修复方案"""
    print(f"\n🔧 可能的问题和修复建议:")
    print(f"1. 配置文件格式问题:")
    print(f"   - 检查constructionLand行是否有多余的空格或特殊字符")
    print(f"   - 确保使用标准的等号分隔符")
    print(f"   - 检查是否有隐藏字符或编码问题")
    
    print(f"\n2. 数值转换问题:")
    print(f"   - 检查'200'是否能正确转换为浮点数")
    print(f"   - 查看日志中是否有转换失败的警告")
    
    print(f"\n3. 配置节解析问题:")
    print(f"   - 确保[Default_area]节格式正确")
    print(f"   - 检查是否有重复的配置项")
    
    print(f"\n4. 建议的Task.cfg格式:")
    print(f"[Default_area]")
    print(f"arableLand = 400")
    print(f"constructionLand = 200")
    
    print(f"\n5. 调试步骤:")
    print(f"   - 检查服务器日志中的默认面积配置信息")
    print(f"   - 确认配置文件是否被正确读取")
    print(f"   - 验证数值转换过程")

if __name__ == "__main__":
    print("🚀 开始调试默认面积配置问题")
    
    # 调试配置文件解析
    debug_task_cfg_parsing()
    
    # 测试接口
    test_weight_info_debug()
    
    # 检查编码
    check_config_file_encoding()
    
    # 建议修复方案
    suggest_fixes()
    
    print(f"\n📖 调试完成")
    print(f"请根据上述分析结果确定问题所在并进行修复")
