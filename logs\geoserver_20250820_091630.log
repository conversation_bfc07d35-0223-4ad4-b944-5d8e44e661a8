2025-08-20 09:16:30,073 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_091630.log
2025-08-20 09:16:30,075 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:30,076 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:30,095 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:30,099 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:30,100 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:30,114 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:30,116 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:30,116 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:30,127 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:30,129 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:30,129 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:30,139 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:30,140 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:30,141 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:30,152 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:30,154 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:30,154 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:30,165 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:30,180 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 09:16:30,185 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:16:33,775 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 09:16:33,786 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 09:16:33,790 - analysis_executor - INFO - 加载了 27 个任务状态
