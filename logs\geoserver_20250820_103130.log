2025-08-20 10:31:30,667 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_103130.log
2025-08-20 10:31:30,669 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 10:31:30,670 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 10:31:30,697 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 10:31:30,725 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 10:31:30,725 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 10:31:30,743 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 10:31:30,745 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 10:31:30,745 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 10:31:30,764 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 10:31:30,781 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 10:31:30,781 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 10:31:30,794 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 10:31:30,812 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 10:31:30,813 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 10:31:30,827 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 10:31:30,838 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 10:31:30,838 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 10:31:30,854 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 10:31:31,015 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 10:31:31,131 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:31:37,359 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 10:31:37,524 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 10:31:37,597 - analysis_executor - INFO - 加载了 28 个任务状态
