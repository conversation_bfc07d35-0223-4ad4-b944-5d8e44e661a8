2025-08-28 17:17:12,805 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_171712.log
2025-08-28 17:17:12,807 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:12,808 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:12,825 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:12,831 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:12,831 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:12,844 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:12,846 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:12,846 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:12,859 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:12,862 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:12,863 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:12,879 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:12,881 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:12,882 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:12,898 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:12,905 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:12,905 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:12,920 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:12,944 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:17:12,953 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:17:17,444 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:17:17,528 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:17:17,533 - analysis_executor - INFO - 加载了 28 个任务状态
