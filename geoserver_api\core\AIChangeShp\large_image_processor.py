#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大图像分块处理器
解决大图像内存溢出问题
"""

import os
import math
from typing import Tuple, List, Dict, Any
import logging

# 尝试导入可选依赖
try:
    import numpy as np
    NUMPY_AVAILABLE = True
except ImportError:
    NUMPY_AVAILABLE = False
    print("警告: numpy未安装，大图像处理功能将受限")

try:
    import rasterio
    RASTERIO_AVAILABLE = True
except ImportError:
    RASTERIO_AVAILABLE = False
    print("警告: rasterio未安装，无法处理地理空间数据")

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    print("警告: psutil未安装，将使用默认内存估算")

logger = logging.getLogger(__name__)

class LargeImageProcessor:
    """大图像分块处理器"""

    def __init__(self, overlap_pixels: int = 128, memory_usage_ratio: float = 0.8):
        """
        初始化大图像处理器

        Args:
            overlap_pixels: 重叠像素数，默认128
            memory_usage_ratio: 内存使用比例，默认0.8 (80%)
        """
        self.overlap_pixels = overlap_pixels
        self.memory_usage_ratio = memory_usage_ratio

        # 检查依赖可用性
        if not NUMPY_AVAILABLE:
            logger.warning("numpy不可用，某些功能将受限")
        if not RASTERIO_AVAILABLE:
            logger.warning("rasterio不可用，无法处理地理空间数据")
        if not PSUTIL_AVAILABLE:
            logger.warning("psutil不可用，将使用默认内存估算")
        
    def get_available_memory(self) -> int:
        """获取可用内存大小（字节）"""
        if PSUTIL_AVAILABLE:
            memory = psutil.virtual_memory()
            available_memory = memory.available
            usable_memory = int(available_memory * self.memory_usage_ratio)

            logger.info(f"系统总内存: {memory.total / (1024**3):.1f} GB")
            logger.info(f"可用内存: {available_memory / (1024**3):.1f} GB")
            logger.info(f"可使用内存: {usable_memory / (1024**3):.1f} GB")

            return usable_memory
        else:
            # 备用方案：假设有8GB可用内存
            default_memory = 8 * 1024**3  # 8GB
            usable_memory = int(default_memory * self.memory_usage_ratio)

            logger.warning(f"psutil不可用，使用默认内存估算: {usable_memory / (1024**3):.1f} GB")

            return usable_memory
    
    def estimate_memory_usage(self, height: int, width: int, bands: int = 3, dtype_size: int = 8) -> int:
        """
        估算图像处理所需内存
        
        Args:
            height: 图像高度
            width: 图像宽度
            bands: 波段数
            dtype_size: 数据类型大小（字节）
        
        Returns:
            估算的内存使用量（字节）
        """
        # 原始图像内存
        image_memory = height * width * bands * dtype_size
        
        # 处理过程中的临时数组（mask, labeled_mask等）
        # 保守估计需要原始图像的3-4倍内存
        processing_memory = image_memory * 4
        
        total_memory = image_memory + processing_memory
        
        logger.info(f"图像尺寸: {height}x{width}x{bands}")
        logger.info(f"原始图像内存: {image_memory / (1024**3):.2f} GB")
        logger.info(f"处理内存估算: {processing_memory / (1024**3):.2f} GB")
        logger.info(f"总内存需求: {total_memory / (1024**3):.2f} GB")
        
        return total_memory
    
    def calculate_grid_size(self, height: int, width: int, bands: int = 3) -> Tuple[int, int]:
        """
        计算最优的网格分割大小
        
        Args:
            height: 图像高度
            width: 图像宽度
            bands: 波段数
        
        Returns:
            (grid_rows, grid_cols): 网格行数和列数
        """
        available_memory = self.get_available_memory()
        
        # 从1x1开始尝试，找到合适的分割大小
        for grid_size in range(1, 10):  # 最多尝试到9x9
            rows = cols = grid_size
            
            # 计算每个块的大小（包含重叠）
            block_height = math.ceil(height / rows) + self.overlap_pixels * 2
            block_width = math.ceil(width / cols) + self.overlap_pixels * 2
            
            # 估算单个块的内存需求
            block_memory = self.estimate_memory_usage(block_height, block_width, bands)
            
            if block_memory <= available_memory:
                logger.info(f"选择网格大小: {rows}x{cols}")
                logger.info(f"每个块大小: {block_height}x{block_width}")
                logger.info(f"每个块内存需求: {block_memory / (1024**3):.2f} GB")
                return rows, cols
        
        # 如果9x9还不够，使用更大的网格
        logger.warning("需要更大的网格分割，使用10x10")
        return 10, 10
    
    def calculate_block_bounds(self, height: int, width: int, grid_rows: int, grid_cols: int) -> List[Dict[str, Any]]:
        """
        计算每个块的边界
        
        Args:
            height: 图像高度
            width: 图像宽度
            grid_rows: 网格行数
            grid_cols: 网格列数
        
        Returns:
            块信息列表
        """
        blocks = []
        
        # 计算每个网格的基本大小
        base_block_height = math.ceil(height / grid_rows)
        base_block_width = math.ceil(width / grid_cols)
        
        for row in range(grid_rows):
            for col in range(grid_cols):
                # 计算核心区域（不包含重叠）
                core_start_y = row * base_block_height
                core_end_y = min((row + 1) * base_block_height, height)
                core_start_x = col * base_block_width
                core_end_x = min((col + 1) * base_block_width, width)
                
                # 计算扩展区域（包含重叠）
                ext_start_y = max(0, core_start_y - self.overlap_pixels)
                ext_end_y = min(height, core_end_y + self.overlap_pixels)
                ext_start_x = max(0, core_start_x - self.overlap_pixels)
                ext_end_x = min(width, core_end_x + self.overlap_pixels)
                
                block_info = {
                    'row': row,
                    'col': col,
                    'core_bounds': (core_start_y, core_end_y, core_start_x, core_end_x),
                    'extended_bounds': (ext_start_y, ext_end_y, ext_start_x, ext_end_x),
                    'core_height': core_end_y - core_start_y,
                    'core_width': core_end_x - core_start_x,
                    'ext_height': ext_end_y - ext_start_y,
                    'ext_width': ext_end_x - ext_start_x
                }
                
                blocks.append(block_info)
        
        logger.info(f"生成了 {len(blocks)} 个处理块")
        return blocks
    
    def should_use_tiling(self, height: int, width: int, bands: int = 3) -> bool:
        """
        判断是否需要使用分块处理
        
        Args:
            height: 图像高度
            width: 图像宽度
            bands: 波段数
        
        Returns:
            是否需要分块处理
        """
        available_memory = self.get_available_memory()
        required_memory = self.estimate_memory_usage(height, width, bands)
        
        needs_tiling = required_memory > available_memory
        
        if needs_tiling:
            logger.info(f"图像过大，需要分块处理")
            logger.info(f"需要内存: {required_memory / (1024**3):.2f} GB")
            logger.info(f"可用内存: {available_memory / (1024**3):.2f} GB")
        else:
            logger.info(f"图像大小适中，可以直接处理")
        
        return needs_tiling
    
    def extract_block_from_image(self, image_path: str, block_info: Dict[str, Any], output_dir: str) -> str:
        """
        从大图像中提取一个块并保存

        Args:
            image_path: 原始图像路径
            block_info: 块信息
            output_dir: 输出目录

        Returns:
            提取的块文件路径
        """
        if not RASTERIO_AVAILABLE:
            raise ImportError("rasterio不可用，无法提取图像块")
        # 生成块文件名
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        block_filename = f"{base_name}_{block_info['row']}_{block_info['col']}.tif"
        block_path = os.path.join(output_dir, block_filename)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 提取块
        ext_start_y, ext_end_y, ext_start_x, ext_end_x = block_info['extended_bounds']
        
        with rasterio.open(image_path) as src:
            # 读取扩展区域的数据
            window = rasterio.windows.Window(
                ext_start_x, ext_start_y,
                ext_end_x - ext_start_x, ext_end_y - ext_start_y
            )
            
            block_data = src.read(window=window)
            
            # 更新地理变换
            transform = src.window_transform(window)
            
            # 保存块
            profile = src.profile.copy()
            profile.update({
                'height': block_data.shape[1],
                'width': block_data.shape[2],
                'transform': transform
            })
            
            with rasterio.open(block_path, 'w', **profile) as dst:
                dst.write(block_data)
        
        logger.info(f"提取块 {block_info['row']}_{block_info['col']}: {block_path}")
        return block_path

    def merge_block_results(self, block_results: List[Dict[str, Any]], output_path: str,
                          original_height: int, original_width: int) -> bool:
        """
        合并所有块的处理结果

        Args:
            block_results: 块结果列表，每个包含 {'block_info': ..., 'result_path': ...}
            output_path: 最终输出路径
            original_height: 原始图像高度
            original_width: 原始图像宽度

        Returns:
            是否成功合并
        """
        try:
            logger.info(f"开始合并 {len(block_results)} 个块的结果")

            # 获取第一个结果文件的元数据作为模板
            first_result = block_results[0]['result_path']
            with rasterio.open(first_result) as src:
                profile = src.profile.copy()

            # 更新输出文件的尺寸
            profile.update({
                'height': original_height,
                'width': original_width
            })

            # 创建输出文件
            with rasterio.open(output_path, 'w', **profile) as dst:
                # 初始化权重图，用于处理重叠区域
                weight_map = np.zeros((original_height, original_width), dtype=np.float32)
                result_map = np.zeros((profile['count'], original_height, original_width), dtype=profile['dtype'])

                # 处理每个块的结果
                for block_result in block_results:
                    block_info = block_result['block_info']
                    result_path = block_result['result_path']

                    if not os.path.exists(result_path):
                        logger.warning(f"块结果文件不存在: {result_path}")
                        continue

                    # 读取块结果
                    with rasterio.open(result_path) as block_src:
                        block_data = block_src.read()

                    # 获取核心区域在原图中的位置
                    core_start_y, core_end_y, core_start_x, core_end_x = block_info['core_bounds']

                    # 计算在块数据中的核心区域位置
                    ext_start_y, ext_end_y, ext_start_x, ext_end_x = block_info['extended_bounds']

                    # 核心区域在块数据中的相对位置
                    core_in_block_y = core_start_y - ext_start_y
                    core_in_block_x = core_start_x - ext_start_x
                    core_height = core_end_y - core_start_y
                    core_width = core_end_x - core_start_x

                    # 提取核心区域数据
                    core_data = block_data[:,
                                         core_in_block_y:core_in_block_y + core_height,
                                         core_in_block_x:core_in_block_x + core_width]

                    # 创建权重（中心权重高，边缘权重低）
                    weight = self._create_weight_map(core_height, core_width)

                    # 累加到结果图
                    for band in range(profile['count']):
                        result_map[band, core_start_y:core_end_y, core_start_x:core_end_x] += \
                            core_data[band] * weight

                    # 累加权重
                    weight_map[core_start_y:core_end_y, core_start_x:core_end_x] += weight

                # 归一化结果
                for band in range(profile['count']):
                    # 避免除零
                    valid_mask = weight_map > 0
                    result_map[band, valid_mask] /= weight_map[valid_mask]

                # 写入最终结果
                dst.write(result_map.astype(profile['dtype']))

            logger.info(f"成功合并结果到: {output_path}")
            return True

        except Exception as e:
            logger.error(f"合并块结果失败: {e}")
            return False

    def _create_weight_map(self, height: int, width: int):
        """
        创建权重图，中心权重高，边缘权重低
        用于处理重叠区域的融合

        Args:
            height: 高度
            width: 宽度

        Returns:
            权重图
        """
        if not NUMPY_AVAILABLE:
            raise ImportError("numpy不可用，无法创建权重图")

        # 创建距离变换，边缘为0，中心为1
        y_coords = np.arange(height)
        x_coords = np.arange(width)

        # 计算到边缘的最小距离
        y_dist = np.minimum(y_coords, height - 1 - y_coords)
        x_dist = np.minimum(x_coords, width - 1 - x_coords)

        # 创建2D权重图
        y_weight = np.minimum(y_dist / (height / 4), 1.0)  # 边缘1/4区域渐变
        x_weight = np.minimum(x_dist / (width / 4), 1.0)

        weight_map = np.outer(y_weight, x_weight)

        # 确保权重在0-1之间
        weight_map = np.clip(weight_map, 0.1, 1.0)  # 最小权重0.1，避免完全为0

        return weight_map

    def cleanup_temp_files(self, temp_files: List[str]):
        """
        清理临时文件

        Args:
            temp_files: 临时文件路径列表
        """
        for file_path in temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"删除临时文件: {file_path}")
            except Exception as e:
                logger.warning(f"删除临时文件失败 {file_path}: {e}")
