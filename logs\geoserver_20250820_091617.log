2025-08-20 09:16:17,880 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_091617.log
2025-08-20 09:16:17,883 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:17,883 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:17,898 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:17,903 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:17,904 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:17,915 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:17,917 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:17,918 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:17,927 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:17,928 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:17,929 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:17,940 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:17,942 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:17,942 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:17,953 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:17,954 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:16:17,955 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:16:17,964 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:16:17,977 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 09:16:17,983 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:16:21,632 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 09:16:21,645 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 09:16:21,650 - analysis_executor - INFO - 加载了 27 个任务状态
