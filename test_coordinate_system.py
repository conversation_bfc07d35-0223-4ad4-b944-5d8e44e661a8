#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试坐标系功能
"""

import requests
import time
import json

def test_original_crs():
    """测试保持原始坐标系"""
    print("🧪 测试保持原始坐标系...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    try:
        print(f"\n📁 测试文件: {test_image_path}")
        
        # 测试保持原始坐标系
        print("\n1️⃣ 测试保持原始坐标系（默认）...")
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0,
            'keep_original_crs': 'true'  # 明确指定
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务完成
            success = monitor_task(base_url, task_id, "保持原始坐标系")
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_wgs84_conversion():
    """测试转换为WGS84"""
    print("\n🧪 测试转换为WGS84...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    try:
        # 测试转换为WGS84
        print("\n2️⃣ 测试转换为WGS84...")
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0,
            'keep_original_crs': 'false'  # 转换为WGS84
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务完成
            success = monitor_task(base_url, task_id, "转换为WGS84")
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_task(base_url, task_id, test_name):
    """监控任务执行"""
    print(f"\n📊 监控 {test_name} 任务执行...")
    
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                task_status = status_data['task_status']
                message = status_data.get('message', '')
                progress = status_data.get('progress', 0)
                
                print(f"📈 {test_name}: {task_status} ({progress}%) - {message}")
                
                if task_status == '完成':
                    print(f"\n✅ {test_name} 任务完成！")
                    
                    # 显示结果信息
                    if 'result' in status_data:
                        result_info = status_data['result']
                        print(f"📊 有效区域数量: {result_info.get('contour_count', 0)}")
                        print(f"📐 总面积: {result_info.get('valid_area', 0):.6f}")
                        print(f"🗺️ 坐标系: {result_info.get('coordinate_system', 'Unknown')}")
                        print(f"📂 输出文件: {result_info.get('output_path', '')}")
                    
                    return True
                    
                elif task_status == '失败':
                    print(f"\n❌ {test_name} 任务失败: {message}")
                    
                    # 获取详细日志
                    try:
                        log_response = requests.get(f"{base_url}/logs/", params={'task_id': task_id, 'lines': 20}, timeout=10)
                        if log_response.status_code == 200:
                            log_data = log_response.json()['data']
                            logs = log_data.get('logs', [])
                            print(f"\n📋 最近的日志:")
                            for log_line in logs[-10:]:
                                print(f"  {log_line}")
                    except Exception as e:
                        print(f"❌ 获取日志失败: {e}")
                    
                    return False
                
                time.sleep(3)
            else:
                print(f"❌ 状态查询失败: {status_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
            time.sleep(5)
            continue
    
    print(f"\n⏰ {test_name} 任务超时")
    return False

def test_parameter_validation():
    """测试参数验证"""
    print("\n🧪 测试参数验证...")
    
    base_url = "http://127.0.0.1:8091/api/analysis/image-extent-extraction/"
    
    test_cases = [
        {
            'name': '默认坐标系参数',
            'params': {'image': 'D:/test.tif'},
            'expected': 200
        },
        {
            'name': '明确指定保持原始坐标系',
            'params': {'image': 'D:/test.tif', 'keep_original_crs': 'true'},
            'expected': 404  # 文件不存在
        },
        {
            'name': '明确指定转换为WGS84',
            'params': {'image': 'D:/test.tif', 'keep_original_crs': 'false'},
            'expected': 404  # 文件不存在
        },
        {
            'name': '无效的坐标系参数',
            'params': {'image': 'D:/test.tif', 'keep_original_crs': 'invalid'},
            'expected': 404  # 会被解析为false，文件不存在
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ 测试 {test_case['name']}...")
        try:
            response = requests.get(base_url, params=test_case['params'], timeout=10)
            print(f"状态码: {response.status_code} (期望: {test_case['expected']})")
            
            if response.status_code == test_case['expected']:
                print(f"✅ {test_case['name']} 测试通过")
            else:
                print(f"❌ {test_case['name']} 测试失败")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ {test_case['name']} 测试异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试坐标系功能")
    
    # 测试参数验证
    test_parameter_validation()
    
    # 测试实际功能
    print("\n" + "="*60)
    print("开始实际功能测试")
    print("="*60)
    
    # 测试保持原始坐标系
    success1 = test_original_crs()
    
    # 等待一段时间
    if success1:
        print("\n⏳ 等待5秒后进行下一个测试...")
        time.sleep(5)
        
        # 测试转换为WGS84
        success2 = test_wgs84_conversion()
        
        if success1 and success2:
            print("\n🎉 所有坐标系功能测试通过！")
        else:
            print("\n❌ 部分测试失败")
    else:
        print("\n❌ 第一个测试失败，跳过后续测试")
    
    print("\n📖 功能说明:")
    print("1. keep_original_crs=true（默认）：保持与输入TIF相同的坐标系")
    print("2. keep_original_crs=false：转换为WGS84坐标系")
    print("3. 输出的Shapefile坐标系信息会在任务结果中显示")
    print("4. 可以通过coordinate_system字段查看实际使用的坐标系")
