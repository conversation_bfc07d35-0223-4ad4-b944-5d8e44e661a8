2025-08-29 17:10:53,575 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_171053.log
2025-08-29 17:10:53,578 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:53,580 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:53,597 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:53,600 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:53,600 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:53,613 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:53,615 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:53,615 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:53,628 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:53,629 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:53,630 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:53,641 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:53,644 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:53,644 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:53,655 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:53,657 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:53,658 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:53,671 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:53,687 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 17:10:53,693 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 17:10:57,648 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 17:10:57,672 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 17:10:57,676 - analysis_executor - INFO - 加载了 28 个任务状态
