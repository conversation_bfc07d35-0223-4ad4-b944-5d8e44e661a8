2025-08-28 17:17:10,815 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_171710.log
2025-08-28 17:17:10,817 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:10,817 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:10,870 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:10,874 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:10,874 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:10,888 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:10,890 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:10,891 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:10,906 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:10,909 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:10,909 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:10,920 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:10,921 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:10,923 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:10,933 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:10,935 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:10,935 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:10,948 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:11,000 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:17:11,045 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:17:11,283 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:17:11,353 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:17:11,383 - analysis_executor - INFO - 加载了 28 个任务状态
