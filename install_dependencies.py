#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装大图像处理所需的依赖
"""

import subprocess
import sys

def install_package(package_name):
    """安装Python包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("安装大图像处理所需依赖...")
    
    # 需要安装的包
    packages = [
        "psutil",  # 系统内存监控
        "rasterio",  # 地理空间数据处理（如果还没有）
    ]
    
    success_count = 0
    for package in packages:
        if install_package(package):
            success_count += 1
    
    print(f"\n安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装成功！")
    else:
        print("⚠️ 部分依赖安装失败，请手动安装")

if __name__ == '__main__':
    main()
