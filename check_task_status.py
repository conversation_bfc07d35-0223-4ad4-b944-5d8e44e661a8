#!/usr/bin/env python3
"""
检查任务状态
"""

import requests
import os
from config import DJANGO_BASE_URL

def check_task(task_id):
    """检查任务状态"""
    try:
        response = requests.get(
            f'{DJANGO_BASE_URL}/api/analysis/status/',
            params={'task_id': task_id},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get('status') == 'success' and data.get('data'):
                task_data = data['data']
                status = task_data.get('status')
                progress = task_data.get('progress', 0)
                message = task_data.get('message', '')
                log_file = task_data.get('log_file')
                
                print(f"任务ID: {task_id}")
                print(f"状态: {status}")
                print(f"进度: {progress}%")
                print(f"消息: {message}")
                print(f"日志文件: {log_file}")
                
                # 检查输出文件
                if 'parameters' in task_data:
                    output_path = task_data['parameters'].get('output_path')
                    if output_path:
                        if os.path.exists(output_path):
                            print(f"✅ 输出文件已生成: {output_path}")
                        else:
                            print(f"⏳ 输出文件尚未生成: {output_path}")
                
                # 检查日志文件最后几行
                if log_file and os.path.exists(log_file):
                    print("\n📋 最后几行日志:")
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        for line in lines[-5:]:
                            if line.strip():
                                print(f"  {line.strip()}")
                
                return status
            else:
                print(f"API响应异常: {data}")
                return None
        else:
            print(f"HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"检查失败: {str(e)}")
        return None

if __name__ == "__main__":
    # 检查最新的任务
    task_id = "9a0b4563-2621-4e38-a6c0-e9b31e01c36a"
    status = check_task(task_id)
    
    if status:
        print(f"\n✅ 任务状态检查完成: {status}")
    else:
        print("\n❌ 任务状态检查失败")
