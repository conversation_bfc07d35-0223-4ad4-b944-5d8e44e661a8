#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入修复是否正常工作
"""

import os
import sys
import django

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_django.settings')
    django.setup()

def test_large_image_processor_import():
    """测试LargeImageProcessor导入"""
    print("=== 测试LargeImageProcessor导入 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        print("✅ LargeImageProcessor导入成功")
        
        # 测试创建实例
        processor = LargeImageProcessor()
        print("✅ LargeImageProcessor实例创建成功")
        
        # 测试基本功能
        needs_tiling = processor.should_use_tiling(1000, 1000, 3)
        print(f"✅ should_use_tiling测试: {needs_tiling}")
        
        return True
        
    except Exception as e:
        print(f"❌ LargeImageProcessor导入失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_pre_pytorch_new_import():
    """测试pre_pytorch_new中的导入"""
    print("\n=== 测试pre_pytorch_new导入 ===")
    
    try:
        # 模拟Django中的动态导入方式
        import importlib.util
        
        module_path = "geoserver_api/core/AIChangeShp/pre_pytorch_new.py"
        spec = importlib.util.spec_from_file_location("pre_pytorch_new", module_path)
        
        if spec is None:
            print(f"❌ 无法创建模块规范: {module_path}")
            return False
        
        pre_pytorch_new = importlib.util.module_from_spec(spec)
        
        # 这里会触发模块的导入，包括LargeImageProcessor的导入
        print("正在执行模块导入...")
        spec.loader.exec_module(pre_pytorch_new)
        
        print("✅ pre_pytorch_new模块导入成功")
        
        # 检查LargeImageProcessor是否可用
        if hasattr(pre_pytorch_new, 'LargeImageProcessor'):
            print("✅ LargeImageProcessor在模块中可用")
        else:
            print("⚠️ LargeImageProcessor在模块中不可用")
        
        # 检查process_large_image_with_tiling函数是否存在
        if hasattr(pre_pytorch_new, 'process_large_image_with_tiling'):
            print("✅ process_large_image_with_tiling函数存在")
        else:
            print("❌ process_large_image_with_tiling函数不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ pre_pytorch_new导入失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_memory_functionality():
    """测试内存相关功能"""
    print("\n=== 测试内存功能 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        
        processor = LargeImageProcessor()
        
        # 测试内存获取
        available_memory = processor.get_available_memory()
        print(f"✅ 可用内存: {available_memory / (1024**3):.1f} GB")
        
        # 测试内存估算
        required_memory = processor.estimate_memory_usage(10000, 10000, 3)
        print(f"✅ 10000x10000图像内存需求: {required_memory / (1024**3):.2f} GB")
        
        # 测试分块判断
        needs_tiling = processor.should_use_tiling(10000, 10000, 3)
        print(f"✅ 10000x10000图像需要分块: {needs_tiling}")
        
        if needs_tiling:
            grid_rows, grid_cols = processor.calculate_grid_size(10000, 10000, 3)
            print(f"✅ 推荐网格大小: {grid_rows}x{grid_cols}")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存功能测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("测试大图像处理导入修复")
    print("="*50)
    
    # 设置Django环境
    try:
        setup_django()
        print("✅ Django环境设置成功")
    except Exception as e:
        print(f"❌ Django环境设置失败: {e}")
        return
    
    # 运行测试
    tests = [
        ("LargeImageProcessor导入", test_large_image_processor_import),
        ("pre_pytorch_new导入", test_pre_pytorch_new_import),
        ("内存功能", test_memory_functionality),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*30}")
        print(f"运行测试: {test_name}")
        print(f"{'='*30}")
        
        success = test_func()
        results.append((test_name, success))
    
    # 显示结果
    print(f"\n{'='*50}")
    print("测试结果汇总:")
    print(f"{'='*50}")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！导入修复成功。")
        print("现在可以正常使用大图像分块处理功能了。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")

if __name__ == '__main__':
    main()
