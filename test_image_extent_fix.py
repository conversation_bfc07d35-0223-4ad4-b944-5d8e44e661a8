#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的影像范围提取功能
"""

import requests
import time
import json

def test_image_extent_extraction():
    """测试影像范围提取接口"""
    print("🧪 测试修复后的影像范围提取接口...")
    
    # 测试参数
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 使用实际存在的TIF文件路径
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    try:
        print(f"\n📁 测试文件: {test_image_path}")
        
        # 1. 启动影像范围提取任务
        print("\n1️⃣ 启动影像范围提取任务...")
        
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            print(f"📂 输出路径: {result['data']['output_path']}")
            
            task_id = result['data']['task_id']
            
            # 2. 监控任务状态
            print(f"\n2️⃣ 监控任务执行状态...")
            
            max_wait_time = 180  # 最大等待3分钟
            start_time = time.time()
            last_status = ""
            
            while time.time() - start_time < max_wait_time:
                try:
                    status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()['data']
                        task_status = status_data['task_status']
                        progress = status_data.get('progress', 0)
                        message = status_data.get('message', '')
                        
                        # 只在状态变化时打印
                        current_status = f"{task_status} ({progress}%)"
                        if current_status != last_status:
                            print(f"📊 任务状态: {current_status} - {message}")
                            last_status = current_status
                        
                        if task_status == '完成':
                            print("\n✅ 任务执行完成！")
                            
                            # 显示结果信息
                            if 'result' in status_data:
                                result_info = status_data['result']
                                print(f"📊 有效区域数量: {result_info.get('contour_count', 0)}")
                                print(f"📐 总面积: {result_info.get('valid_area', 0):.6f} 平方度")
                                print(f"🗺️ 坐标系: {result_info.get('coordinate_system', 'WGS84')}")
                                print(f"📂 输出文件: {result_info.get('output_path', '')}")
                            
                            return True
                            
                        elif task_status == '失败':
                            print(f"\n❌ 任务执行失败: {message}")
                            
                            # 获取详细日志
                            print("\n📋 获取详细日志...")
                            log_response = requests.get(f"{base_url}/logs/", params={'task_id': task_id, 'lines': 50}, timeout=10)
                            if log_response.status_code == 200:
                                log_data = log_response.json()['data']
                                logs = log_data.get('logs', [])
                                print("最近的日志信息:")
                                for log_line in logs[-10:]:  # 显示最后10行日志
                                    print(f"  {log_line}")
                            
                            return False
                        
                        time.sleep(3)  # 等待3秒后再次查询
                    else:
                        print(f"❌ 状态查询失败: {status_response.status_code}")
                        return False
                        
                except requests.exceptions.Timeout:
                    print("⏰ 状态查询超时，继续等待...")
                    continue
                except Exception as e:
                    print(f"❌ 状态查询异常: {e}")
                    time.sleep(5)
                    continue
            
            print("\n⏰ 任务执行超时")
            return False
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保Django服务正在运行")
        return False
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

def test_parameter_combinations():
    """测试不同参数组合"""
    print("\n🧪 测试不同参数组合...")
    
    base_url = "http://127.0.0.1:8091/api/analysis/image-extent-extraction/"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    test_cases = [
        {
            'name': '高精度提取',
            'params': {
                'image': test_image_path,
                'simplify_tolerance': 0.5,
                'min_area': 500.0
            }
        },
        {
            'name': '快速提取',
            'params': {
                'image': test_image_path,
                'simplify_tolerance': 5.0,
                'min_area': 5000.0
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ 测试 {test_case['name']}...")
        try:
            response = requests.get(base_url, params=test_case['params'], timeout=10)
            if response.status_code == 200:
                result = response.json()
                print(f"✅ {test_case['name']} 任务启动成功: {result['data']['task_id']}")
            else:
                print(f"❌ {test_case['name']} 任务启动失败: {response.status_code}")
        except Exception as e:
            print(f"❌ {test_case['name']} 测试异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试修复后的影像范围提取功能")
    
    # 测试基本功能
    success = test_image_extent_extraction()
    
    if success:
        print("\n🎉 基本功能测试通过！")
        
        # 测试不同参数组合
        test_parameter_combinations()
        
        print("\n🎉 所有测试完成！")
    else:
        print("\n❌ 基本功能测试失败，请检查日志")
    
    print("\n📖 修复说明:")
    print("1. 修复了min_area参数传递问题（平方米 → 像素数量转换）")
    print("2. 添加了像素分辨率计算和日志输出")
    print("3. 修复了未使用变量的警告")
    print("4. 改进了错误处理和日志记录")
