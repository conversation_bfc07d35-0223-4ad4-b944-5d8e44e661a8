#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试排队执行的合并分析接口
"""

import requests
import json
import time

def test_queued_combined_analysis():
    """测试排队执行的合并分析接口"""
    print("🧪 测试排队执行的合并分析接口...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    test_params = {
        'id': '20250705171601',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print(f"\n🚀 提交排队合并分析任务...")
        print(f"   影像ID: {test_params['id']}")
        print(f"   分析类别: arableLand")
        
        # 发送请求
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=test_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            queue_info = result['data']['queue_info']
            
            print(f"✅ 任务创建成功: {task_id}")
            print(f"📋 队列信息:")
            print(f"   队列位置: {queue_info['position']}")
            print(f"   正在处理: {'是' if queue_info['is_processing'] else '否'}")
            print(f"   当前任务: {queue_info['current_task'] or '无'}")
            
            return task_id
            
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def test_multiple_queued_tasks():
    """测试多个排队任务"""
    print(f"\n🧪 测试多个排队任务...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 创建多个测试任务
    test_tasks = [
        {
            'id': '20250705171601',
            'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
            'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
            'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus'
        },
        {
            'id': '20250705171602',
            'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.tif',
            'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
            'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus'
        },
        {
            'id': '20250705171603',
            'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171603/20250705171603_out.tif',
            'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
            'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus'
        }
    ]
    
    created_tasks = []
    
    for i, task_params in enumerate(test_tasks, 1):
        try:
            print(f"\n📤 提交第{i}个任务: {task_params['id']}")
            
            response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=task_params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                task_id = result['data']['task_id']
                queue_info = result['data']['queue_info']
                
                created_tasks.append(task_id)
                
                print(f"✅ 任务{i}创建成功: {task_id}")
                print(f"   队列位置: {queue_info['position']}")
                
            else:
                print(f"❌ 任务{i}创建失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 任务{i}创建异常: {e}")
    
    print(f"\n📊 成功创建 {len(created_tasks)} 个任务")
    return created_tasks

def test_queue_status():
    """测试队列状态查询"""
    print(f"\n🧪 测试队列状态查询...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            data = result['data']
            
            print(f"✅ 队列状态查询成功")
            print(f"📊 队列状态:")
            print(f"   正在处理: {'是' if data['is_processing'] else '否'}")
            print(f"   当前任务: {data['current_task_id'] or '无'}")
            print(f"   队列大小: {data['queue_size']}")
            
            if data['waiting_tasks']:
                print(f"📋 等待中的任务:")
                for i, task in enumerate(data['waiting_tasks'], 1):
                    print(f"   {i}. {task['task_id'][:8]}... (影像: {task['image_id']}, 类别: {task['analysis_category']})")
            else:
                print(f"📋 队列为空")
                
            return data
            
        else:
            print(f"❌ 队列状态查询失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 队列状态查询异常: {e}")
        return None

def test_cancel_waiting_task(task_id):
    """测试取消等待中的任务"""
    print(f"\n🧪 测试取消等待中的任务...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        data = {'task_id': task_id}
        
        print(f"📤 取消任务: {task_id}")
        
        response = requests.post(f"{base_url}/cancel-task/", json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务取消成功")
            print(f"   任务ID: {result['data']['task_id']}")
            return True
            
        else:
            result = response.json()
            print(f"❌ 任务取消失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 任务取消异常: {e}")
        return False

def test_cancel_non_waiting_task():
    """测试取消非等待中任务的错误处理"""
    print(f"\n🧪 测试取消非等待中任务的错误处理...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 使用一个不存在的任务ID
    fake_task_id = "fake-task-id-12345"
    
    try:
        data = {'task_id': fake_task_id}
        
        print(f"📤 尝试取消不存在的任务: {fake_task_id}")
        
        response = requests.post(f"{base_url}/cancel-task/", json=data, timeout=10)
        
        if response.status_code == 400:
            result = response.json()
            print(f"✅ 错误处理正确: {result['message']}")
            return True
        else:
            print(f"❌ 错误处理异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_queue_execution(max_wait=300):
    """监控队列执行情况"""
    print(f"\n📊 监控队列执行情况...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            queue_status = test_queue_status()
            
            if queue_status:
                if not queue_status['is_processing'] and queue_status['queue_size'] == 0:
                    print(f"🎉 所有任务执行完成！")
                    break
                    
                print(f"⏰ 继续监控... (剩余: {queue_status['queue_size']} 个任务)")
            
            time.sleep(30)  # 每30秒检查一次
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(30)
            continue
    
    print(f"📊 监控结束")

def main():
    """主函数"""
    print("🚀 开始测试排队执行的合并分析接口")
    
    print(f"\n📝 功能说明:")
    print(f"1. 🔄 任务排队执行，避免并发冲突")
    print(f"2. 📋 支持队列状态查询")
    print(f"3. ⏸️ 支持取消等待中的任务")
    print(f"4. 🛡️ 系统稳定性保障")
    
    # 测试1: 单个排队任务
    task_id = test_queued_combined_analysis()
    
    # 测试2: 队列状态查询
    test_queue_status()
    
    # 测试3: 多个排队任务
    created_tasks = test_multiple_queued_tasks()
    
    # 测试4: 队列状态查询（有多个任务时）
    test_queue_status()
    
    # 测试5: 取消等待中的任务
    if created_tasks:
        # 取消最后一个任务
        test_cancel_waiting_task(created_tasks[-1])
        
        # 再次查看队列状态
        test_queue_status()
    
    # 测试6: 错误处理
    test_cancel_non_waiting_task()
    
    # 测试7: 监控队列执行（可选）
    print(f"\n❓ 是否监控队列执行？(y/n)")
    choice = input().lower().strip()
    
    if choice == 'y':
        monitor_queue_execution()
    
    print(f"\n🎉 排队执行的合并分析接口测试完成！")
    
    print(f"\n📖 使用说明:")
    print(f"1. 使用 /combined-ai-spatial-analysis-queued/ 提交排队任务")
    print(f"2. 使用 /queue-status/ 查看队列状态")
    print(f"3. 使用 /cancel-task/ 取消等待中的任务")
    print(f"4. 任务会按提交顺序依次执行")
    print(f"5. 系统同时只执行一个合并分析任务")

if __name__ == "__main__":
    main()
