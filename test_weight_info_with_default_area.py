#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试权重信息接口的默认面积功能
"""

import requests
import json

def test_weight_info_with_default_area():
    """测试权重信息接口的默认面积功能"""
    print("🧪 测试权重信息接口的默认面积功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        # 1. 测试 weight-info 接口
        print("\n1️⃣ 测试 /api/analysis/weight-info/ 接口...")
        
        response = requests.get(f"{base_url}/weight-info/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            print(f"📋 状态: {result['status']}")
            print(f"📝 消息: {result['message']}")
            
            if result['status'] == 'success' and 'data' in result:
                data = result['data']
                print(f"\n📊 权重信息数据:")
                
                for land_type, info in data.items():
                    print(f"\n🏷️ 地物类型: {land_type}")
                    
                    # 检查显示名称
                    display_name = info.get('display_name', 'N/A')
                    print(f"  📛 显示名称: {display_name}")
                    
                    # 检查默认面积 - 新增功能
                    default_area = info.get('default_area', 'N/A')
                    if default_area != 'N/A':
                        print(f"  📐 默认面积: {default_area}")
                        
                        # 验证默认面积是否符合预期
                        expected_areas = {
                            'arableLand': 400.0,
                            'constructionLand': 200.0
                        }
                        
                        if land_type in expected_areas:
                            expected_area = expected_areas[land_type]
                            if abs(float(default_area) - expected_area) < 0.01:
                                print(f"    ✅ 默认面积正确: {default_area}")
                            else:
                                print(f"    ❌ 默认面积错误: {default_area} (期望: {expected_area})")
                        else:
                            print(f"    ℹ️ 未知地物类型的默认面积: {default_area}")
                    else:
                        print(f"  ❌ 缺少默认面积字段")
                    
                    # 检查其他字段
                    print(f"  🎯 默认权重: {info.get('default', 'N/A')}")
                    
                    models = info.get('models', {})
                    print(f"  🤖 模型类型数量: {len(models)}")
                    
                    shp_files = info.get('shp_files', [])
                    print(f"  📁 SHP文件数量: {len(shp_files)}")
                
                # 验证默认面积功能
                print(f"\n📈 默认面积功能验证:")
                expected_areas = {
                    'arableLand': 400.0,
                    'constructionLand': 200.0
                }
                
                area_correct = True
                for land_type, expected_area in expected_areas.items():
                    if land_type in data:
                        actual_area = data[land_type].get('default_area')
                        if actual_area is not None:
                            if abs(float(actual_area) - expected_area) < 0.01:
                                print(f"  ✅ {land_type}: {actual_area} (正确)")
                            else:
                                print(f"  ❌ {land_type}: {actual_area} (期望: {expected_area})")
                                area_correct = False
                        else:
                            print(f"  ❌ {land_type}: 缺少default_area字段")
                            area_correct = False
                    else:
                        print(f"  ⚠️ 缺少地物类型: {land_type}")
                
                if area_correct:
                    print(f"  🎉 默认面积功能正常！")
                else:
                    print(f"  ⚠️ 默认面积功能存在问题")
                
            else:
                print(f"❌ 接口返回数据格式异常")
                print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
        
        # 2. 测试 weight-config 接口
        print(f"\n2️⃣ 测试 /api/analysis/weight-config/ 接口...")
        
        response = requests.get(f"{base_url}/weight-config/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            print(f"📋 状态: {result['status']}")
            
            if result['status'] == 'success' and 'data' in result:
                data = result['data']
                print(f"\n📊 配置信息:")
                print(f"  📁 数据路径: {data.get('window_data_path', 'N/A')}")
                
                # 显示默认权重
                default_weights = data.get('default_weights', {})
                print(f"  🎯 默认权重配置:")
                for land_type, weight in default_weights.items():
                    print(f"    {land_type}: {weight}")
                
                # 显示字段映射
                field_mapping = data.get('field_mapping', {})
                print(f"  🏷️ 字段映射配置:")
                for land_type, display_name in field_mapping.items():
                    print(f"    {land_type}: {display_name}")
                
                # 显示默认面积 - 新增功能
                default_areas = data.get('default_areas', {})
                print(f"  📐 默认面积配置:")
                if default_areas:
                    for land_type, area in default_areas.items():
                        print(f"    {land_type}: {area}")
                    
                    # 验证默认面积配置
                    expected_areas = {
                        'arableLand': 400.0,
                        'constructionLand': 200.0
                    }
                    
                    config_correct = True
                    for land_type, expected_area in expected_areas.items():
                        actual_area = default_areas.get(land_type)
                        if actual_area is not None:
                            if abs(float(actual_area) - expected_area) < 0.01:
                                print(f"    ✅ {land_type}: {actual_area}")
                            else:
                                print(f"    ❌ {land_type}: {actual_area} (期望: {expected_area})")
                                config_correct = False
                        else:
                            print(f"    ❌ 缺少配置: {land_type}")
                            config_correct = False
                    
                    if config_correct:
                        print(f"  🎉 默认面积配置正确！")
                    else:
                        print(f"  ⚠️ 默认面积配置有误")
                else:
                    print(f"  ❌ 缺少默认面积配置")
                
            else:
                print(f"❌ 配置接口返回数据格式异常")
                
        else:
            print(f"❌ 配置接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_task_cfg_default_area():
    """测试Task.cfg文件中的默认面积配置"""
    print(f"\n3️⃣ 测试Task.cfg文件中的默认面积配置...")
    
    try:
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        response = requests.get(config_url, timeout=10)
        
        if response.status_code == 200:
            config_content = response.text
            print(f"✅ Task.cfg文件读取成功")
            
            # 检查是否包含Default_area节
            if '[Default_area]' in config_content:
                print(f"✅ 发现 [Default_area] 配置节")
                
                # 提取Default_area内容
                lines = config_content.split('\n')
                in_default_area = False
                default_areas = {}
                
                for line in lines:
                    line = line.strip()
                    if line == '[Default_area]':
                        in_default_area = True
                        continue
                    elif line.startswith('[') and line.endswith(']'):
                        in_default_area = False
                        continue
                    
                    if in_default_area and '=' in line:
                        key, value = line.split('=', 1)
                        default_areas[key.strip()] = value.strip()
                
                print(f"📋 Default_area 内容:")
                for key, value in default_areas.items():
                    print(f"  {key} = {value}")
                
                # 检查期望的配置
                expected_areas = {
                    'arableLand': '400',
                    'constructionLand': '200'
                }
                
                for key, expected_value in expected_areas.items():
                    if key in default_areas:
                        actual_value = default_areas[key]
                        if actual_value == expected_value:
                            print(f"  ✅ {key} = {actual_value}")
                        else:
                            print(f"  ⚠️ {key} = {actual_value} (期望: {expected_value})")
                    else:
                        print(f"  ❌ 缺少配置: {key}")
                
            else:
                print(f"⚠️ Task.cfg中未发现 [Default_area] 配置节")
                print(f"建议在Task.cfg中添加以下内容:")
                print(f"[Default_area]")
                print(f"arableLand = 400")
                print(f"constructionLand = 200")
                
        else:
            print(f"❌ 无法读取Task.cfg文件: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试Task.cfg默认面积配置异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试权重信息接口的默认面积功能")
    
    success = test_weight_info_with_default_area()
    test_task_cfg_default_area()
    
    if success:
        print(f"\n🎉 权重信息接口默认面积功能测试完成！")
    else:
        print(f"\n❌ 权重信息接口默认面积功能测试失败")
    
    print(f"\n📖 功能说明:")
    print(f"1. /api/analysis/weight-info/ 接口现在包含 default_area 字段")
    print(f"2. /api/analysis/weight-config/ 接口现在包含 default_areas 配置")
    print(f"3. 从Task.cfg的[Default_area]节读取默认面积配置")
    print(f"4. 如果配置文件中没有[Default_area]，使用默认配置")
    print(f"5. 支持的默认配置: arableLand->400.0, constructionLand->200.0")
    print(f"6. 面积值自动转换为浮点数类型")
    
    print(f"\n📝 Task.cfg配置示例:")
    print(f"[Default_area]")
    print(f"arableLand = 400")
    print(f"constructionLand = 200")
