#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试合并分析接口的修复
1. 验证status只有"进行中"、"完成"、"失败"三种状态
2. 验证影像范围文件放在与其他SHP文件相同的目录下
"""

import requests
import time
import json
import os

def test_combined_analysis_fixes():
    """测试合并分析接口的修复"""
    print("🧪 测试合并分析接口的修复...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    image_id = "20250705171601"
    params = {
        'id': image_id,
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print(f"\n🚀 启动合并分析任务...")
        print(f"   影像ID: {image_id}")
        
        # 启动任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功: {task_id}")
            
            # 监控任务执行和验证修复
            success = monitor_and_verify_fixes(base_url, task_id, image_id)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_and_verify_fixes(base_url, task_id, image_id, max_wait=1200):
    """监控任务执行并验证修复"""
    print(f"\n📊 监控任务执行并验证修复...")
    
    start_time = time.time()
    status_history = []
    file_path_checks = []
    
    # TaskInfo.json路径
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    while time.time() - start_time < max_wait:
        try:
            # 1. 检查API状态
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            api_status = None
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                api_status = status_data['task_status']
                
                print(f"🔄 API状态: {api_status}")
            
            # 2. 检查TaskInfo.json中的状态
            if os.path.exists(taskinfo_path):
                taskinfo_status = check_taskinfo_status(taskinfo_path, task_id, status_history)
                
                # 3. 检查文件路径
                check_file_paths(image_id, file_path_checks)
            
            # 4. 检查任务是否完成
            if api_status in ['完成', '失败']:
                print(f"\n🏁 任务结束: {api_status}")
                
                # 最终验证
                final_verification(taskinfo_path, task_id, image_id, status_history, file_path_checks)
                return api_status == '完成'
            
            time.sleep(15)  # 每15秒检查一次
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(15)
            continue
    
    print(f"\n⏰ 任务监控超时")
    return False

def check_taskinfo_status(taskinfo_path, task_id, status_history):
    """检查TaskInfo.json中的状态"""
    try:
        with open(taskinfo_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            for task in data:
                if task.get('task_id') == task_id:
                    current_status = task.get('status', 'Unknown')
                    
                    # 记录状态变化
                    if not status_history or status_history[-1] != current_status:
                        status_history.append(current_status)
                        print(f"📋 TaskInfo状态变化: {current_status}")
                        
                        # 验证状态是否合法
                        valid_statuses = ['等待中', '进行中', '完成', '失败']
                        if current_status not in valid_statuses:
                            print(f"❌ 发现非法状态: {current_status}")
                        else:
                            print(f"✅ 状态合法: {current_status}")
                    
                    return current_status
                    
    except Exception as e:
        print(f"❌ 检查TaskInfo状态失败: {e}")
    
    return None

def check_file_paths(image_id, file_path_checks):
    """检查文件路径"""
    try:
        # 检查分析目录
        analysis_dir = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/arableLand"
        
        if os.path.exists(analysis_dir):
            files = os.listdir(analysis_dir)
            
            # 查找范围文件
            area_files = [f for f in files if '_area_' in f and f.endswith('.shp')]
            
            if area_files and not any('area_file_found' in check for check in file_path_checks):
                area_file = area_files[0]
                area_file_path = os.path.join(analysis_dir, area_file)
                
                print(f"📁 发现范围文件: {area_file}")
                print(f"   路径: {area_file_path}")
                print(f"✅ 范围文件位置正确（在arableLand目录下）")
                
                file_path_checks.append({
                    'type': 'area_file_found',
                    'file': area_file,
                    'path': area_file_path,
                    'correct_location': True
                })
                
                # 检查文件大小
                if os.path.exists(area_file_path):
                    file_size = os.path.getsize(area_file_path)
                    print(f"   文件大小: {file_size} 字节")
                    
                    if file_size > 0:
                        print(f"✅ 范围文件不为空")
                    else:
                        print(f"⚠️ 范围文件为空")
            
            # 检查其他SHP文件
            other_shp_files = [f for f in files if f.endswith('.shp') and '_area_' not in f]
            if other_shp_files:
                print(f"📁 同目录下的其他SHP文件: {len(other_shp_files)} 个")
                for shp_file in other_shp_files:
                    print(f"   - {shp_file}")
                    
    except Exception as e:
        print(f"❌ 检查文件路径失败: {e}")

def final_verification(taskinfo_path, task_id, image_id, status_history, file_path_checks):
    """最终验证"""
    print(f"\n🔍 最终验证结果...")
    
    # 1. 验证状态历史
    print(f"📊 状态变化历史:")
    valid_statuses = ['等待中', '进行中', '完成', '失败']
    all_valid = True
    
    for i, status in enumerate(status_history):
        if status in valid_statuses:
            print(f"   {i+1}. ✅ {status}")
        else:
            print(f"   {i+1}. ❌ {status} (非法状态)")
            all_valid = False
    
    if all_valid:
        print(f"🎉 状态验证通过：所有状态都合法")
    else:
        print(f"❌ 状态验证失败：发现非法状态")
    
    # 2. 验证文件路径
    print(f"\n📁 文件路径验证:")
    area_file_found = any(check['type'] == 'area_file_found' for check in file_path_checks)
    
    if area_file_found:
        area_check = next(check for check in file_path_checks if check['type'] == 'area_file_found')
        print(f"✅ 范围文件位置正确")
        print(f"   文件: {area_check['file']}")
        print(f"   路径: {area_check['path']}")
        print(f"   位于arableLand目录: {area_check['correct_location']}")
    else:
        print(f"❌ 未找到范围文件或位置不正确")
    
    # 3. 验证最终TaskInfo内容
    print(f"\n📋 最终TaskInfo验证:")
    try:
        with open(taskinfo_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            for task in data:
                if task.get('task_id') == task_id:
                    final_status = task.get('status', 'Unknown')
                    progress = task.get('progress', 0)
                    
                    print(f"   最终状态: {final_status}")
                    print(f"   最终进度: {progress}%")
                    
                    # 检查结果字段
                    results = task.get('results', {})
                    if results:
                        ai_time = results.get('ai_processing_time')
                        spatial_stats = results.get('spatial_statistics', {})
                        success = results.get('success')
                        
                        print(f"   AI处理时间: {ai_time}")
                        print(f"   成功状态: {success}")
                        
                        if spatial_stats:
                            non_null_count = sum(1 for v in spatial_stats.values() if v is not None)
                            total_count = len(spatial_stats)
                            print(f"   空间统计: {non_null_count}/{total_count} 字段有值")
                        
                        if final_status == '完成' and success and spatial_stats:
                            print(f"🎉 TaskInfo内容验证通过")
                        else:
                            print(f"⚠️ TaskInfo内容可能不完整")
                    
                    break
                    
    except Exception as e:
        print(f"❌ 验证最终TaskInfo失败: {e}")
    
    # 4. 总结
    print(f"\n📝 修复验证总结:")
    print(f"1. 状态简化: {'✅ 通过' if all_valid else '❌ 失败'}")
    print(f"2. 文件路径: {'✅ 通过' if area_file_found else '❌ 失败'}")

def test_existing_taskinfo():
    """测试现有的TaskInfo文件"""
    print(f"\n🔍 检查现有TaskInfo文件...")
    
    image_id = "20250705171601"
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    if os.path.exists(taskinfo_path):
        try:
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📋 现有TaskInfo文件分析:")
            print(f"   文件路径: {taskinfo_path}")
            print(f"   任务数量: {len(data) if isinstance(data, list) else 1}")
            
            if isinstance(data, list):
                for i, task in enumerate(data):
                    status = task.get('status', 'Unknown')
                    task_id = task.get('task_id', 'Unknown')
                    
                    print(f"   任务 {i+1}:")
                    print(f"     ID: {task_id}")
                    print(f"     状态: {status}")
                    
                    # 检查状态是否合法
                    valid_statuses = ['等待中', '进行中', '完成', '失败']
                    if status in valid_statuses:
                        print(f"     ✅ 状态合法")
                    else:
                        print(f"     ❌ 状态非法: {status}")
                        
        except Exception as e:
            print(f"❌ 读取现有TaskInfo失败: {e}")
    else:
        print(f"ℹ️ 现有TaskInfo文件不存在: {taskinfo_path}")

if __name__ == "__main__":
    print("🚀 开始测试合并分析接口的修复")
    
    # 检查现有TaskInfo
    test_existing_taskinfo()
    
    # 测试新的合并分析
    success = test_combined_analysis_fixes()
    
    if success:
        print(f"\n🎉 合并分析接口修复测试通过！")
    else:
        print(f"\n❌ 合并分析接口修复测试失败")
    
    print(f"\n📖 修复说明:")
    print(f"1. ✅ TaskInfo中的status只有'进行中'、'完成'、'失败'三种状态")
    print(f"2. ✅ 影像范围文件放在与其他SHP文件相同的目录下")
    print(f"   - 原路径: D:/Drone_Project/nginxData/ODM/AI/{'{image_id}'}/")
    print(f"   - 新路径: D:/Drone_Project/nginxData/ODM/AI/{'{image_id}'}/arableLand/")
    print(f"3. ✅ 保持文件命名格式: {'{image_id}'}_area_{'{timestamp}'}.shp")
