#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地理空间分析执行器

功能说明:
- 异步执行空间数据变化分析任务
- 任务状态管理和监控
- 日志记录和管理
- 结果文件管理

开发者注意:
- 参考batch_executor、geo_executor的设计模式
- 支持异步任务执行，避免阻塞API响应
- 提供详细的任务状态跟踪和日志监控
- 支持任务取消和资源清理
"""

import os
import logging
import threading
import uuid
import json
from datetime import datetime
from typing import Dict, Any, Optional

# 导入分析处理器
from .analysis_processor import analysis_processor

# 导入AI模型处理器
try:
    from .ai_model_processor import AIModelConfig, ai_model_processor
    AI_AVAILABLE = True
except ImportError as e:
    AI_AVAILABLE = False
    print(f"AI模块不可用: {e}")

# 设置日志目录
LOGS_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
ANALYSIS_LOGS_DIR = os.path.join(LOGS_DIR, 'analysislog')
os.makedirs(ANALYSIS_LOGS_DIR, exist_ok=True)

# 状态文件路径
STATUS_FILE = os.path.join(ANALYSIS_LOGS_DIR, 'analysis_status.json')

# 导入统一的日志配置
from .logger_config import analysis_logger

class AnalysisExecutor:
    """地理空间分析执行器"""

    def __init__(self):
        """初始化执行器"""
        self.tasks = {}  # 任务状态存储
        self.result_files = {}  # 结果文件存储
        self.running_threads = {}  # 运行中的线程
        self.log_dir = ANALYSIS_LOGS_DIR  # 日志目录
        self._load_status()  # 加载已有状态

    def _load_status(self):
        """加载任务状态"""
        try:
            if os.path.exists(STATUS_FILE):
                with open(STATUS_FILE, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
                analysis_logger.info(f"加载了 {len(self.tasks)} 个任务状态")
        except Exception as e:
            analysis_logger.error(f"加载任务状态失败: {str(e)}")
            self.tasks = {}

    def _save_status(self):
        """保存任务状态"""
        try:
            with open(STATUS_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            analysis_logger.error(f"保存任务状态失败: {str(e)}")

    def _create_task_info_template(self, task_id: str, output_path: str, task_logger):
        """创建TaskInfo.json模板"""
        try:
            # 获取输出目录
            output_dir = os.path.dirname(output_path)
            task_info_path = os.path.join(output_dir, "TaskInfo.json")

            # 获取任务参数
            task_params = self.tasks[task_id]['parameters']

            # 创建新的任务信息
            new_task_info = {
                "task_id": task_id,
                "task_type": "ai_semantic_segmentation",
                "status": "进行中",
                "message": "AI语义分割任务已开始",
                "start_time": self.tasks[task_id]['start_time'],
                "end_time": None,
                "progress": 0,
                "input_files": {
                    "image_path": task_params['image_path'],
                    "model_path": task_params['model_path']
                },
                "output_files": {
                    "result_shapefile": output_path
                },
                "parameters": {
                    "model_type": task_params['model_type'],
                    "target_classes": task_params['target_classes'],
                    "window_size": task_params['window_size'],
                    "batch_size": task_params['batch_size'],
                    "simplify": task_params['simplify'],
                    "min_area": task_params['min_area'],
                    "num_classes": task_params['num_classes']
                },
                "statistics": {
                    "processing_time": None,
                    "feature_count": None,
                    "total_area": None
                },
                "error": None,
                "log_file": self.tasks[task_id]['log_file']
            }

            # 读取现有的TaskInfo.json（如果存在）
            task_info_list = []
            if os.path.exists(task_info_path):
                try:
                    with open(task_info_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        # 如果是数组格式，直接使用；如果是对象格式，转换为数组
                        if isinstance(existing_data, list):
                            task_info_list = existing_data
                        elif isinstance(existing_data, dict):
                            task_info_list = [existing_data]
                        else:
                            task_info_list = []
                except (json.JSONDecodeError, Exception) as e:
                    task_logger.warning(f"⚠️ 读取现有TaskInfo.json失败，将创建新文件: {e}")
                    task_info_list = []

            # 检查是否已存在相同task_id的记录，如果存在则更新，否则添加
            task_found = False
            for i, task in enumerate(task_info_list):
                if task.get('task_id') == task_id:
                    task_info_list[i] = new_task_info
                    task_found = True
                    task_logger.info(f"📝 更新现有任务记录: {task_id}")
                    break

            if not task_found:
                task_info_list.append(new_task_info)
                task_logger.info(f"➕ 添加新任务记录: {task_id}")

            # 保存更新后的TaskInfo.json
            with open(task_info_path, 'w', encoding='utf-8') as f:
                json.dump(task_info_list, f, ensure_ascii=False, indent=2)

            # 记录TaskInfo路径到任务状态
            self.tasks[task_id]['task_info_path'] = task_info_path

            task_logger.info(f"✅ TaskInfo.json已更新: {task_info_path}")
            task_logger.info(f"📊 当前任务总数: {len(task_info_list)}")

        except Exception as e:
            task_logger.error(f"❌ 创建TaskInfo.json模板失败: {str(e)}")

    def _update_task_info(self, task_id: str, updates: dict, task_logger=None):
        """更新TaskInfo.json文件（支持数组格式）"""
        try:
            if task_id not in self.tasks or 'task_info_path' not in self.tasks[task_id]:
                return

            task_info_path = self.tasks[task_id]['task_info_path']

            if not os.path.exists(task_info_path):
                return

            # 读取现有TaskInfo
            with open(task_info_path, 'r', encoding='utf-8') as f:
                task_info_data = json.load(f)

            # 确保是数组格式
            if isinstance(task_info_data, dict):
                task_info_list = [task_info_data]
            elif isinstance(task_info_data, list):
                task_info_list = task_info_data
            else:
                if task_logger:
                    task_logger.error(f"❌ TaskInfo.json格式不正确")
                return

            # 查找并更新对应的任务记录
            task_found = False
            for i, task_info in enumerate(task_info_list):
                if task_info.get('task_id') == task_id:
                    # 更新字段
                    for key, value in updates.items():
                        if '.' in key:
                            # 支持嵌套字段更新，如 'statistics.processing_time'
                            keys = key.split('.')
                            current = task_info
                            for k in keys[:-1]:
                                if k not in current:
                                    current[k] = {}
                                current = current[k]
                            current[keys[-1]] = value
                        else:
                            task_info[key] = value

                    task_info_list[i] = task_info
                    task_found = True
                    break

            if not task_found:
                if task_logger:
                    task_logger.warning(f"⚠️ 未找到task_id为{task_id}的记录")
                return

            # 保存更新后的TaskInfo
            with open(task_info_path, 'w', encoding='utf-8') as f:
                json.dump(task_info_list, f, ensure_ascii=False, indent=2)

            if task_logger:
                task_logger.info(f"📝 TaskInfo.json已更新: {list(updates.keys())}")

        except Exception as e:
            if task_logger:
                task_logger.error(f"❌ 更新TaskInfo.json失败: {str(e)}")
            else:
                analysis_logger.error(f"❌ 更新TaskInfo.json失败: {str(e)}")

    def _create_task_logger(self, task_id, task_log_file=None):
        """为任务创建独立的日志记录器"""
        if task_log_file is None:
            task_log_file = os.path.join(ANALYSIS_LOGS_DIR, f"{task_id}.log")

        # 创建任务专用的logger
        task_logger = logging.getLogger(f'analysis_task_{task_id}')
        task_logger.setLevel(logging.INFO)

        # 清除已有的处理器
        task_logger.handlers.clear()

        # 创建文件处理器
        task_file_handler = logging.FileHandler(task_log_file, encoding='utf-8')
        task_file_handler.setLevel(logging.INFO)

        # 创建简单格式器（不包含logger名称，因为文件名已经标识了任务）
        task_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        task_file_handler.setFormatter(task_formatter)

        task_logger.addHandler(task_file_handler)

        return task_logger
        
    def execute_spatial_changes_analysis(self, old_data_path: str, new_data_path: str,
                                       area_threshold: float = 200.0,
                                       output_dir: str = None,
                                       shp_filename: str = "changes_shapefile",
                                       clip_area_path: str = None) -> Dict[str, Any]:
        """
        执行空间数据变化分析

        参数:
            old_data_path: 老数据路径
            new_data_path: 新数据路径
            area_threshold: 面积阈值
            output_dir: 输出目录（可选，默认为新数据根目录）
            shp_filename: 输出文件名（可选，默认changes_shapefile）
            clip_area_path: 裁剪范围Shapefile路径（可选）

        返回:
            dict: 任务信息
        """
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 创建任务专用日志
            task_logger = self._create_task_logger(task_id)
            task_log_file = os.path.join(ANALYSIS_LOGS_DIR, f"{task_id}.log")

            analysis_logger.info(f"开始空间数据变化分析任务: {task_id}")
            task_logger.info(f"=== 空间数据变化分析任务开始 ===")
            task_logger.info(f"任务ID: {task_id}")
            task_logger.info(f"老数据路径: {old_data_path}")
            task_logger.info(f"新数据路径: {new_data_path}")
            task_logger.info(f"面积阈值: {area_threshold} 平方米")

            # 验证输入文件
            if not os.path.exists(old_data_path):
                error_msg = f"老数据文件不存在: {old_data_path}"
                task_logger.error(error_msg)
                raise FileNotFoundError(error_msg)
            if not os.path.exists(new_data_path):
                error_msg = f"新数据文件不存在: {new_data_path}"
                task_logger.error(error_msg)
                raise FileNotFoundError(error_msg)

            # 确定输出目录：如果未指定，使用新数据的根目录
            if output_dir is None:
                output_dir = os.path.dirname(new_data_path)
            task_logger.info(f"输出目录: {output_dir}")
            task_logger.info(f"输出文件名: {shp_filename}")
            if clip_area_path:
                task_logger.info(f"裁剪范围: {clip_area_path}")
            else:
                task_logger.info("裁剪范围: 使用新数据覆盖区域")

            # 初始化任务状态
            self.tasks[task_id] = {
                'task_id': task_id,
                'status': '正在运行',
                'message': '任务已排队，等待执行...',
                'start_time': datetime.now().isoformat(),
                'progress': 0,
                'log_file': task_log_file,
                'parameters': {
                    'old_data_path': old_data_path,
                    'new_data_path': new_data_path,
                    'area_threshold': area_threshold,
                    'output_dir': output_dir,
                    'shp_filename': shp_filename,
                    'clip_area_path': clip_area_path
                }
            }

            # 保存状态
            self._save_status()
            
            # 启动异步任务
            thread = threading.Thread(
                target=self._run_analysis_task,
                args=(task_id,),
                daemon=True
            )
            thread.start()
            self.running_threads[task_id] = thread
            
            analysis_logger.info(f"空间数据变化分析任务已启动: {task_id}")
            
            return {
                'success': True,
                'task_id': task_id,
                'message': '空间数据变化分析任务已启动',
                'status': '正在运行'
            }
            
        except Exception as e:
            error_msg = f"启动空间数据变化分析任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e)
            }
    
    def _run_analysis_task(self, task_id: str):
        """运行分析任务（在后台线程中执行）"""
        task_logger = None
        try:
            task_info = self.tasks[task_id]
            params = task_info['parameters']

            # 获取任务专用日志记录器
            task_logger = logging.getLogger(f'analysis_task_{task_id}')

            task_logger.info("=== 开始执行分析任务 ===")

            # 更新任务状态为处理中
            self._update_task_status(task_id, '正在运行', '正在进行空间数据变化分析...', 5)
            task_logger.info("任务状态: 正在运行")

            # 设置分析参数
            analysis_processor.area_threshold = params['area_threshold']
            task_logger.info(f"设置面积阈值: {params['area_threshold']} 平方米")

            # 更新进度
            self._update_task_status(task_id, 'processing', '正在读取输入数据...', 10)
            task_logger.info("开始读取输入数据")

            # 执行分析
            analysis_logger.info(f"开始执行分析任务: {task_id}")

            # 创建自定义的分析处理器实例，传入task_logger
            result = self._run_analysis_with_progress(
                task_id,
                task_logger,
                params['old_data_path'],
                params['new_data_path'],
                params['output_dir'],
                params['shp_filename'],
                params.get('clip_area_path')
            )

            if result['success']:
                # 更新任务状态为完成
                self._update_task_status(task_id, '运行成功', '空间数据变化分析完成', 100)
                self.tasks[task_id]['result'] = result

                task_logger.info("=== 分析任务完成 ===")
                task_logger.info(f"流出图斑数量: {result['statistics']['outflow_count']}")
                task_logger.info(f"流入图斑数量: {result['statistics']['inflow_count']}")
                task_logger.info(f"总图斑数量: {result['statistics']['total_count']}")
                task_logger.info(f"流出面积: {result['statistics']['outflow_area']:.2f} 平方米")
                task_logger.info(f"流入面积: {result['statistics']['inflow_area']:.2f} 平方米")
                task_logger.info(f"合并输出文件: {result['output_files']['combined_shapefile']}")

                analysis_logger.info(f"空间数据变化分析任务完成: {task_id}")

            else:
                # 分析失败
                self._update_task_status(task_id, '运行失败', result['message'])
                self.tasks[task_id]['error'] = result.get('error', '未知错误')

                task_logger.error(f"分析失败: {result['message']}")
                if 'error' in result:
                    task_logger.error(f"错误详情: {result['error']}")

                analysis_logger.error(f"空间数据变化分析任务失败: {task_id} - {result['message']}")

        except Exception as e:
            # 处理异常
            error_msg = f"分析过程中出错: {str(e)}"
            self._update_task_status(task_id, '运行失败', error_msg)
            self.tasks[task_id]['error'] = str(e)

            if task_logger:
                task_logger.error(f"任务执行异常: {error_msg}")
                import traceback
                task_logger.error("详细错误信息:")
                task_logger.error(traceback.format_exc())

            analysis_logger.error(f"空间数据变化分析任务异常: {task_id} - {error_msg}")

        finally:
            # 保存状态
            self._save_status()

            # 清理线程引用
            if task_id in self.running_threads:
                del self.running_threads[task_id]

    def _run_analysis_with_progress(self, task_id, task_logger, old_data_path, new_data_path,
                                   output_dir, shp_filename, clip_area_path=None):
        """带进度跟踪的分析执行"""
        try:
            # 更新进度
            self._update_task_status(task_id, '正在运行', '正在执行空间数据变化分析...', 20)
            task_logger.info("开始执行空间数据变化分析")
            if clip_area_path:
                task_logger.info(f"使用裁剪范围: {clip_area_path}")

            # 直接调用分析处理器的方法
            result = analysis_processor.analyze_farmland_changes(
                old_data_path=old_data_path,
                new_data_path=new_data_path,
                output_dir=output_dir,
                shp_filename=shp_filename,
                clip_area_path=clip_area_path
            )

            # 更新进度
            self._update_task_status(task_id, '正在运行', '分析完成，正在保存结果...', 90)
            task_logger.info("空间数据变化分析完成")

            return result

        except Exception as e:
            error_msg = f"分析执行失败: {str(e)}"
            task_logger.error(error_msg)
            import traceback
            task_logger.error("详细错误信息:")
            task_logger.error(traceback.format_exc())

            return {
                'success': False,
                'message': error_msg,
                'error': str(e)
            }
    

    
    def _update_task_status(self, task_id: str, status: str, message: str, progress: int = None):
        """更新任务状态"""
        if task_id in self.tasks:
            self.tasks[task_id]['status'] = status
            self.tasks[task_id]['message'] = message
            if progress is not None:
                self.tasks[task_id]['progress'] = progress
            if status in ['运行成功', '运行失败', 'cancelled']:
                self.tasks[task_id]['end_time'] = datetime.now().isoformat()

            # 保存状态到文件
            self._save_status()
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, Any]:
        """获取所有任务状态"""
        return self.tasks
    
    def get_result_file(self, file_id: str) -> Optional[Dict[str, Any]]:
        """获取结果文件信息"""
        return self.result_files.get(file_id)
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务"""
        try:
            if task_id not in self.tasks:
                return {
                    'success': False,
                    'message': f'任务不存在: {task_id}'
                }
            
            task_info = self.tasks[task_id]
            
            if task_info['status'] in ['completed', 'failed', 'cancelled']:
                return {
                    'success': False,
                    'message': f'任务已结束，无法取消: {task_info["status"]}'
                }
            
            # 更新任务状态
            self._update_task_status(task_id, 'cancelled', '任务已被取消')
            
            # 停止线程（注意：Python线程无法强制停止，只能标记状态）
            if task_id in self.running_threads:
                analysis_logger.info(f"标记任务为取消状态: {task_id}")
            
            analysis_logger.info(f"任务已取消: {task_id}")
            
            return {
                'success': True,
                'message': '任务已取消'
            }
            
        except Exception as e:
            error_msg = f"取消任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e)
            }

    def execute_ai_semantic_segmentation(self, image_path: str, model_path: str,
                                        output_path: str, model_type: str = 'segnext',
                                        target_classes: str = None, window_size: int = 512,
                                        batch_size: int = 16, simplify: float = 0.0,
                                        min_area: float = 0.0, num_classes: int = 2) -> Dict[str, Any]:
        """
        执行AI语义分割任务

        参数:
            image_path: 输入图像路径
            model_path: 模型权重文件路径
            output_path: 输出Shapefile路径
            model_type: 模型类型
            target_classes: 目标类别
            window_size: 滑动窗口大小
            batch_size: 批处理大小
            simplify: 简化阈值
            min_area: 最小面积阈值
            num_classes: 类别数量

        返回:
            dict: 任务信息
        """
        if not AI_AVAILABLE:
            return {
                'success': False,
                'message': 'AI模块不可用，请检查依赖安装',
                'task_id': None
            }

        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 创建任务日志文件
            task_log_file = os.path.join(self.log_dir, f"{task_id}.log")

            # 创建任务专用logger
            task_logger = self._create_task_logger(task_id, task_log_file)

            task_logger.info("=== AI语义分割任务开始 ===")
            task_logger.info(f"任务ID: {task_id}")
            task_logger.info(f"图像路径: {image_path}")
            task_logger.info(f"模型路径: {model_path}")
            task_logger.info(f"输出路径: {output_path}")
            task_logger.info(f"模型类型: {model_type}")
            task_logger.info(f"目标类别: {target_classes}")

            # 初始化任务状态
            self.tasks[task_id] = {
                'task_id': task_id,
                'status': '正在运行',
                'message': '任务已排队，等待执行...',
                'start_time': datetime.now().isoformat(),
                'progress': 0,
                'log_file': task_log_file,
                'parameters': {
                    'image_path': image_path,
                    'model_path': model_path,
                    'output_path': output_path,
                    'model_type': model_type,
                    'target_classes': target_classes,
                    'window_size': window_size,
                    'batch_size': batch_size,
                    'simplify': simplify,
                    'min_area': min_area,
                    'num_classes': num_classes
                }
            }

            # 创建TaskInfo.json模板
            self._create_task_info_template(task_id, output_path, task_logger)

            # 保存任务状态
            self._save_status()

            # 启动后台任务
            thread = threading.Thread(
                target=self._run_ai_task,
                args=(task_id, task_logger),
                daemon=True
            )
            thread.start()

            analysis_logger.info(f"AI语义分割任务已启动: {task_id}")

            return {
                'success': True,
                'message': 'AI语义分割任务已启动',
                'task_id': task_id,
                'log_file': task_log_file
            }

        except Exception as e:
            error_msg = f"启动AI语义分割任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e),
                'task_id': None
            }

    def execute_ai_batch_processing(self, input_dir: str, output_dir: str,
                                   model_path: str, model_type: str = 'segnext',
                                   target_classes: str = None, window_size: int = 512,
                                   batch_size: int = 16, num_classes: int = 2) -> Dict[str, Any]:
        """
        执行AI批量处理任务

        参数:
            input_dir: 输入图像目录
            output_dir: 输出目录
            model_path: 模型权重文件路径
            model_type: 模型类型
            target_classes: 目标类别
            window_size: 滑动窗口大小
            batch_size: 批处理大小
            num_classes: 类别数量

        返回:
            dict: 任务信息
        """
        if not AI_AVAILABLE:
            return {
                'success': False,
                'message': 'AI模块不可用，请检查依赖安装',
                'task_id': None
            }

        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())

            # 创建任务日志文件
            task_log_file = os.path.join(self.log_dir, f"{task_id}.log")

            # 创建任务专用logger
            task_logger = self._create_task_logger(task_id, task_log_file)

            task_logger.info("=== AI批量处理任务开始 ===")
            task_logger.info(f"任务ID: {task_id}")
            task_logger.info(f"输入目录: {input_dir}")
            task_logger.info(f"输出目录: {output_dir}")
            task_logger.info(f"模型路径: {model_path}")
            task_logger.info(f"模型类型: {model_type}")

            # 初始化任务状态
            self.tasks[task_id] = {
                'task_id': task_id,
                'status': '正在运行',
                'message': '任务已排队，等待执行...',
                'start_time': datetime.now().isoformat(),
                'progress': 0,
                'log_file': task_log_file,
                'parameters': {
                    'input_dir': input_dir,
                    'output_dir': output_dir,
                    'model_path': model_path,
                    'model_type': model_type,
                    'target_classes': target_classes,
                    'window_size': window_size,
                    'batch_size': batch_size,
                    'num_classes': num_classes
                }
            }

            # 启动后台任务
            thread = threading.Thread(
                target=self._run_ai_batch_task,
                args=(task_id, task_logger),
                daemon=True
            )
            thread.start()

            analysis_logger.info(f"AI批量处理任务已启动: {task_id}")

            return {
                'success': True,
                'message': 'AI批量处理任务已启动',
                'task_id': task_id,
                'log_file': task_log_file
            }

        except Exception as e:
            error_msg = f"启动AI批量处理任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e),
                'task_id': None
            }

    def execute_image_extent_extraction(self, image_path: str, output_path: str = None,
                                      simplify_tolerance: float = 1.0,
                                      min_area: float = 1000.0,
                                      keep_original_crs: bool = True) -> Dict[str, Any]:
        """
        执行影像有效范围提取任务

        参数:
            image_path: 输入TIF影像路径
            output_path: 输出Shapefile路径，默认自动生成
            simplify_tolerance: 简化容差，单位为米
            min_area: 最小面积阈值，单位为平方米
            keep_original_crs: 是否保持原始坐标系，默认True

        返回:
            dict: 任务信息
        """
        try:
            # 验证输入文件
            if not os.path.exists(image_path):
                return {
                    'success': False,
                    'message': f'输入影像文件不存在: {image_path}',
                    'task_id': None
                }

            # 生成输出路径
            if not output_path:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_dir = os.path.dirname(image_path)
                output_path = os.path.join(output_dir, f"{base_name}_extent.shp")

            # 创建任务ID
            task_id = str(uuid.uuid4())

            # 创建任务专用日志
            task_log_file = os.path.join(self.log_dir, f"{task_id}.log")
            task_logger = self._create_task_logger(task_id, task_log_file)

            # 初始化任务状态
            self._update_task_status(task_id, '等待中', '影像范围提取任务已创建，等待执行...', 0)

            # 保存任务参数到状态文件
            self.tasks[task_id] = {
                'task_id': task_id,
                'status': '等待中',
                'message': '影像范围提取任务已创建，等待执行...',
                'start_time': datetime.now().isoformat(),
                'progress': 0,
                'log_file': task_log_file,
                'parameters': {
                    'image_path': image_path,
                    'output_path': output_path,
                    'simplify_tolerance': simplify_tolerance,
                    'min_area': min_area,
                    'keep_original_crs': keep_original_crs
                }
            }
            self._save_status()

            # 启动异步任务
            thread = threading.Thread(
                target=self._run_image_extent_extraction,
                args=(task_id, image_path, output_path, simplify_tolerance, min_area, keep_original_crs, task_logger)
            )
            thread.daemon = True
            thread.start()

            # 保存线程引用
            self.running_threads[task_id] = thread

            analysis_logger.info(f"影像范围提取任务已启动: {task_id}")
            analysis_logger.info(f"输入影像: {image_path}")
            analysis_logger.info(f"输出路径: {output_path}")

            return {
                'success': True,
                'message': '影像范围提取任务已启动',
                'task_id': task_id,
                'log_file': task_log_file,
                'input_image': image_path,
                'output_path': output_path
            }

        except Exception as e:
            error_msg = f"启动影像范围提取任务失败: {str(e)}"
            analysis_logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'error': str(e),
                'task_id': None
            }

    def _run_ai_task(self, task_id: str, task_logger):
        """运行AI语义分割任务"""
        try:
            params = self.tasks[task_id]['parameters']

            # 更新任务状态
            self._update_task_status(task_id, '正在运行', '正在初始化AI模型...', 10)

            # 更新TaskInfo.json
            self._update_task_info(task_id, {
                'status': '进行中',
                'message': '正在初始化AI模型...',
                'progress': 10
            }, task_logger)

            task_logger.info("开始创建AI模型配置...")

            # 创建AI模型配置
            config = AIModelConfig(
                image=params['image_path'],
                model=params['model_path'],
                output=params['output_path'],
                model_type=params['model_type'],
                target_classes=params['target_classes'],
                window_size=params['window_size'],
                batch_size=params['batch_size'],
                simplify=params['simplify'],
                min_area=params['min_area'],
                num_classes=params['num_classes']
            )
            task_logger.info(f"AI模型配置创建完成: {config.model_type}")
            task_logger.info(f"配置参数: window_size={config.window_size}, batch_size={config.batch_size}")

            # 执行AI处理 - 切换到新的分析代码
            self._update_task_status(task_id, '正在运行', '正在执行AI语义分割...', 30)

            # 更新TaskInfo.json
            self._update_task_info(task_id, {
                'status': '进行中',
                'message': '正在执行AI语义分割...',
                'progress': 30
            }, task_logger)

            task_logger.info("开始AI模型处理...")

            # 使用新的pre_pytorch_new.py进行处理
            result = self._execute_new_ai_processing(config, task_logger)

            task_logger.info(f"AI模型处理完成，结果: {result.get('success', False) if isinstance(result, dict) else 'Unknown'}")

            if result['success']:
                # 任务成功完成
                self._update_task_status(task_id, '已完成', 'AI语义分割任务完成', 100)

                # 更新任务结果
                end_time = datetime.now().isoformat()
                self.tasks[task_id].update({
                    'end_time': end_time,
                    'result': result
                })

                # 更新TaskInfo.json - 成功完成
                task_info_updates = {
                    'status': '完成',
                    'message': 'AI语义分割任务完成',
                    'progress': 100,
                    'end_time': end_time
                }

                # 添加统计信息
                if 'performance' in result:
                    perf = result['performance']
                    task_info_updates['statistics.processing_time'] = perf.get('total_time', 0)

                if 'feature_count' in result:
                    task_info_updates['statistics.feature_count'] = result['feature_count']

                if 'total_area' in result:
                    task_info_updates['statistics.total_area'] = result['total_area']

                self._update_task_info(task_id, task_info_updates, task_logger)

                task_logger.info("=== AI语义分割任务完成 ===")
                task_logger.info(f"输出文件: {result['output_path']}")
                if 'performance' in result:
                    perf = result['performance']
                    task_logger.info(f"处理时间: {perf.get('total_time', 'N/A')} 秒")

            else:
                # 任务失败
                error_msg = result.get('error', '未知错误')
                self._update_task_status(task_id, '失败', f'AI语义分割失败: {error_msg}', 0)

                # 更新TaskInfo.json - 任务失败
                self._update_task_info(task_id, {
                    'status': '失败',
                    'message': f'AI语义分割失败: {error_msg}',
                    'progress': 0,
                    'end_time': datetime.now().isoformat(),
                    'error': error_msg
                }, task_logger)

                task_logger.error(f"AI语义分割失败: {error_msg}")

        except Exception as e:
            error_msg = f"AI任务执行异常: {str(e)}"
            self._update_task_status(task_id, '失败', error_msg, 0)

            # 更新TaskInfo.json - 异常失败
            self._update_task_info(task_id, {
                'status': '失败',
                'message': error_msg,
                'progress': 0,
                'end_time': datetime.now().isoformat(),
                'error': str(e)
            }, task_logger)

            task_logger.error(error_msg)
            task_logger.error(f"错误详情: {str(e)}")

    def _execute_new_ai_processing(self, config, task_logger):
        """
        使用新的pre_pytorch_new.py执行AI处理

        Args:
            config: AI模型配置对象
            task_logger: 任务专用logger

        Returns:
            dict: 处理结果
        """
        try:
            task_logger.info("🚀 使用新版AI分析引擎进行处理...")

            # 检查是否是合并分析任务，如果是，创建TaskInfo.json
            self._check_and_create_combined_taskinfo(config, task_logger)

            # 导入新的处理模块
            import sys
            import os
            new_ai_module_path = os.path.join(
                os.path.dirname(__file__),
                'AIChangeShp'
            )
            if new_ai_module_path not in sys.path:
                sys.path.insert(0, new_ai_module_path)

            # 动态导入新的处理模块
            import importlib.util
            spec = importlib.util.spec_from_file_location(
                "pre_pytorch_new",
                os.path.join(new_ai_module_path, "pre_pytorch_new.py")
            )
            pre_pytorch_new = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(pre_pytorch_new)

            task_logger.info("✅ 新版AI分析引擎加载成功")

            # 创建参数对象
            args = self._create_args_for_new_processor(config, task_logger)

            # 设置设备
            import torch
            gpu_id = getattr(config, 'gpu', 0)
            if torch.cuda.is_available() and gpu_id >= 0:
                device = torch.device(f'cuda:{gpu_id}')
                task_logger.info(f"🎮 使用GPU设备: {device}")
            else:
                device = torch.device('cpu')
                task_logger.info(f"💻 使用CPU设备: {device}")

            # 执行处理
            task_logger.info(f"📂 输入图像: {config.image}")
            task_logger.info(f"🤖 模型路径: {config.model}")
            task_logger.info(f"📁 输出路径: {config.output}")
            task_logger.info(f"🔧 模型类型: {config.model_type}")

            # 调用新的处理函数
            task_logger.info("🔄 开始AI模型推理...")
            success, testtime, processing_time = pre_pytorch_new.process_single_image(
                config.image,
                config.model,
                config.output,
                args,
                device,
                model=None,
                task_logger=task_logger
            )

            if success:
                task_logger.info(f"✅ AI处理成功完成，耗时: {processing_time:.2f}秒")

                # 更新TaskInfo.json - AI处理完成
                self._update_combined_taskinfo_on_completion(config, processing_time, True, task_logger)

                return {
                    'success': True,
                    'message': 'AI语义分割完成',
                    'processing_time': processing_time,
                    'output_path': config.output
                }
            else:
                task_logger.error("❌ AI处理失败")

                # 更新TaskInfo.json - AI处理失败
                self._update_combined_taskinfo_on_completion(config, processing_time if 'processing_time' in locals() else 0, False, task_logger)

                return {
                    'success': False,
                    'message': 'AI处理失败',
                    'processing_time': processing_time if 'processing_time' in locals() else 0
                }

        except Exception as e:
            task_logger.error(f"❌ 新版AI处理引擎执行失败: {str(e)}")
            import traceback
            task_logger.error(f"错误详情: {traceback.format_exc()}")
            return {
                'success': False,
                'message': f'新版AI处理失败: {str(e)}'
            }

    def _check_and_create_combined_taskinfo(self, config, task_logger):
        """检查是否是合并分析任务，如果是则创建TaskInfo.json"""
        try:
            # 检查输出路径是否包含合并分析的特征
            output_path = getattr(config, 'output', '')
            if not output_path:
                return

            # 检查路径是否符合合并分析的模式: .../ODM/AI/影像ID/分析类别/...
            import re
            pattern = r'.*[/\\]ODM[/\\]AI[/\\](\d+)[/\\]([^/\\]+)[/\\].*'
            match = re.match(pattern, output_path.replace('\\', '/'))

            if not match:
                return  # 不是合并分析任务

            image_id = match.group(1)
            analysis_category = match.group(2)

            task_logger.info(f"🔍 检测到合并分析任务: image_id={image_id}, category={analysis_category}")

            # 构建TaskInfo.json路径，统一使用正斜杠
            task_info_dir = output_path.replace('\\', '/').split(f'/{analysis_category}/')[0]
            task_info_path = os.path.join(task_info_dir, 'TaskInfo.json').replace('\\', '/')

            task_logger.info(f"📁 TaskInfo路径: {task_info_path}")

            # 获取真实的任务ID（如果有的话）
            real_task_id = getattr(config, 'combined_task_id', None)
            if not real_task_id:
                # 如果没有传递真实任务ID，生成临时任务ID
                import uuid
                real_task_id = str(uuid.uuid4())
                task_logger.warning(f"⚠️ 未获取到真实任务ID，使用临时ID: {real_task_id}")
            else:
                task_logger.info(f"✅ 使用真实任务ID: {real_task_id}")

            # 创建TaskInfo.json模板
            self._create_combined_taskinfo_template(
                task_info_path, real_task_id, image_id, analysis_category, config, task_logger
            )

        except Exception as e:
            task_logger.warning(f"⚠️ 检查合并分析TaskInfo失败: {str(e)}")

    def _create_combined_taskinfo_template(self, task_info_path, task_id, image_id, analysis_category, config, task_logger):
        """创建合并分析TaskInfo.json模板"""
        try:
            task_logger.info(f"🔧 创建合并分析TaskInfo.json模板...")

            # 确保目录存在
            task_dir = os.path.dirname(task_info_path)
            if not os.path.exists(task_dir):
                os.makedirs(task_dir, exist_ok=True)
                task_logger.info(f"📂 创建目录: {task_dir}")

            # 尝试从config中获取更多信息
            old_data_path = getattr(config, 'old_data_path', None)
            if not old_data_path:
                # 尝试从输出路径推断old_data_path的可能位置
                old_data_path = f"D:/Drone_Project/nginxData/ODM/AIOLDSHP/{analysis_category}/耕地2024_84.shp"

            # 生成最终输出路径
            ai_output_path = getattr(config, 'output', '').replace('\\', '/')
            if ai_output_path and '_1_' in ai_output_path:
                final_output_path = ai_output_path.replace('_1_', '_2_')
            else:
                final_output_path = ai_output_path.replace('.shp', '_final.shp') if ai_output_path else 'N/A'

            # 获取绝对路径的日志文件
            log_file_path = os.path.abspath(os.path.join('geoserver_api', 'logs', 'analysislog', f'{task_id}.log')).replace('\\', '/')

            # 提取模型名称
            model_path = getattr(config, 'model', '')
            model_name = os.path.basename(model_path) if model_path else 'unknown'

            # 创建新的任务信息模板
            new_task_info = {
                'task_id': task_id,
                'image_id': image_id,
                'analysis_category': analysis_category,
                'timestamp': int(datetime.now().timestamp()),
                'datetime': datetime.now().isoformat(),
                'input_files': {
                    'image_path': getattr(config, 'image', '').replace('\\', '/'),
                    'model_path': model_path.replace('\\', '/'),
                    'old_data_path': old_data_path.replace('\\', '/') if old_data_path else 'N/A'
                },
                'output_files': {
                    'ai_output_path': ai_output_path,
                    'final_output_path': final_output_path
                },
                'parameters': {
                    'model_type': getattr(config, 'model_type', 'unknown'),
                    'model_name': model_name,
                    'num_classes': getattr(config, 'num_classes', 2),
                    'area_threshold': getattr(config, 'area_threshold', 400.0)
                },
                'results': {
                    'ai_processing_time': None,
                    'spatial_statistics': {
                        'outflow_count': None,
                        'inflow_count': None,
                        'total_count': None,
                        'outflow_area': None,
                        'inflow_area': None,
                        'area_threshold': getattr(config, 'area_threshold', 400.0)
                    },
                    'success': None
                },
                'geoserver_publish': {
                    'ai_result': {'success': False, 'message': '待发布', 'layer_name': ''},
                    'final_result': {'success': False, 'message': '待发布', 'layer_name': ''},
                    'overall_success': False,
                    'workspace': analysis_category,
                    'epsg': '32648',
                    'publish_time': None
                },
                'status': '进行中',
                'log_file': log_file_path
            }

            # 读取现有的TaskInfo.json（如果存在）
            existing_tasks = []
            if os.path.exists(task_info_path):
                task_logger.info(f"📄 读取现有TaskInfo文件...")
                try:
                    with open(task_info_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        # 确保是数组格式
                        if isinstance(existing_data, list):
                            existing_tasks = existing_data
                            task_logger.info(f"📋 读取到数组格式，包含 {len(existing_tasks)} 个历史任务")
                        elif isinstance(existing_data, dict):
                            existing_tasks = [existing_data]
                            task_logger.info(f"📋 读取到对象格式，转换为数组格式")
                        else:
                            existing_tasks = []
                            task_logger.warning(f"⚠️ 未知格式，创建新数组")
                except Exception as e:
                    task_logger.warning(f"⚠️ 读取历史任务信息失败: {str(e)}")
                    existing_tasks = []
            else:
                task_logger.info(f"📝 TaskInfo文件不存在，将创建新文件")

            # 检查是否已存在相同task_id的记录
            task_found = False
            for i, task in enumerate(existing_tasks):
                if task.get('task_id') == task_id:
                    # 更新现有记录，合并信息
                    existing_task = existing_tasks[i]

                    # 保留原有的基础信息，但更新更详细的信息
                    if 'model_name' not in existing_task.get('parameters', {}):
                        existing_task['parameters']['model_name'] = new_task_info['parameters']['model_name']

                    if 'geoserver_publish' not in existing_task:
                        existing_task['geoserver_publish'] = new_task_info['geoserver_publish']

                    existing_tasks[i] = existing_task
                    task_logger.info(f"🔄 更新现有任务记录: {task_id}")
                    task_found = True
                    break

            if not task_found:
                # 添加新任务到列表
                existing_tasks.append(new_task_info)
                task_logger.info(f"➕ 添加新任务记录: {task_id}")

            # 保存更新后的TaskInfo.json
            with open(task_info_path, 'w', encoding='utf-8') as f:
                json.dump(existing_tasks, f, ensure_ascii=False, indent=2)

            task_logger.info(f"✅ TaskInfo.json文件已写入完成")

            # 验证文件是否正确保存
            if os.path.exists(task_info_path):
                file_size = os.path.getsize(task_info_path)
                task_logger.info(f"📊 文件验证: 大小={file_size}字节，任务总数: {len(existing_tasks)}")
            else:
                task_logger.error(f"❌ 文件保存失败，文件不存在")

        except Exception as e:
            task_logger.error(f"❌ 创建TaskInfo.json模板失败: {str(e)}")
            import traceback
            task_logger.error(f"错误详情: {traceback.format_exc()}")

    def _update_combined_taskinfo_on_completion(self, config, processing_time, success, task_logger):
        """AI处理完成后更新TaskInfo.json"""
        try:
            # 检查是否是合并分析任务
            output_path = getattr(config, 'output', '')
            if not output_path:
                return

            # 检查路径是否符合合并分析的模式
            import re
            pattern = r'.*[/\\]ODM[/\\]AI[/\\](\d+)[/\\]([^/\\]+)[/\\].*'
            match = re.match(pattern, output_path.replace('\\', '/'))

            if not match:
                return  # 不是合并分析任务

            image_id = match.group(1)
            analysis_category = match.group(2)

            # 构建TaskInfo.json路径
            task_info_dir = output_path.replace('\\', '/').split(f'/{analysis_category}/')[0]
            task_info_path = os.path.join(task_info_dir, 'TaskInfo.json').replace('\\', '/')

            if not os.path.exists(task_info_path):
                return

            # 获取任务ID
            task_id = getattr(config, 'combined_task_id', None)
            if not task_id:
                return

            task_logger.info(f"🔄 更新TaskInfo.json: AI处理完成，成功={success}")

            # 读取现有TaskInfo.json
            with open(task_info_path, 'r', encoding='utf-8') as f:
                task_data = json.load(f)

            if not isinstance(task_data, list):
                return

            # 查找并更新对应的任务记录
            for i, task in enumerate(task_data):
                if task.get('task_id') == task_id:
                    # 更新AI处理结果
                    task['results']['ai_processing_time'] = processing_time
                    task['results']['success'] = success

                    if success:
                        task['status'] = 'AI处理完成'
                    else:
                        task['status'] = 'AI处理失败'

                    task_data[i] = task
                    task_logger.info(f"✅ 找到并更新任务记录: {task_id}")
                    break
            else:
                task_logger.warning(f"⚠️ 未找到task_id为{task_id}的记录")
                return

            # 保存更新后的TaskInfo.json
            with open(task_info_path, 'w', encoding='utf-8') as f:
                json.dump(task_data, f, ensure_ascii=False, indent=2)

            task_logger.info(f"📝 TaskInfo.json已更新: AI处理时间={processing_time}秒")

        except Exception as e:
            task_logger.warning(f"⚠️ 更新TaskInfo.json失败: {str(e)}")

    def _create_args_for_new_processor(self, config, task_logger):
        """
        为新的AI处理器创建参数对象

        Args:
            config: AI模型配置对象
            task_logger: 任务专用logger

        Returns:
            argparse.Namespace: 参数对象
        """
        import argparse

        # 创建参数对象
        args = argparse.Namespace()

        # 基本参数
        args.image = config.image
        args.model = config.model
        args.output = config.output
        args.model_type = config.model_type or 'deeplabv3_plus'
        args.num_classes = config.num_classes or 2
        args.gpu = getattr(config, 'gpu', 0)

        # 处理参数
        args.overlap = 0.5  # 重叠区域百分比
        args.batch_size = 16  # 批处理大小
        args.window_size = 512  # 窗口大小
        args.save_memory = False  # 内存节省模式
        args.use_amp = False  # 混合精度
        args.num_workers = 0  # 数据加载器工作线程数（设为0避免多进程问题）

        # 模型特定参数
        args.backbone = getattr(config, 'backbone', 'resnet101')  # DeepLabV3+骨干网络
        args.segformer_type = getattr(config, 'segformer_type', 'b5')  # SegFormer类型
        args.segnext_type = getattr(config, 'segnext_type', 'base')  # SegNeXt类型
        args.segnext_improved_type = getattr(config, 'segnext_improved_type', 'base')  # 改进版SegNeXt类型
        args.vit_type = getattr(config, 'vit_type', 'vit_base')  # ViT类型
        args.vit_img_size = getattr(config, 'vit_img_size', 224)  # ViT图像尺寸
        args.vit_patch_size = getattr(config, 'vit_patch_size', 16)  # ViT补丁尺寸
        args.deep_supervision = getattr(config, 'deep_supervision', False)  # 深度监督

        # 批量处理参数
        args.batch_mode = False
        args.image_dirs = None
        args.output_dirs = None
        args.image_ext = '.tif'
        args.recursive = False

        # 掩码参数
        args.mask_shp = None
        args.ignore_nodata = True
        args.nodata_values = [0, -9999]

        # 目标类别参数
        if hasattr(config, 'target_classes') and config.target_classes:
            args.target_classes = config.target_classes
        else:
            args.target_classes = [1]  # 默认输出类别1

        # 矢量化参数
        args.simplify = 0.0  # 简化阈值
        args.min_area = 0.0  # 最小面积
        args.remove_holes = True  # 移除空洞
        args.hole_threshold = 1000  # 空洞阈值
        args.merge_adjacent = True  # 合并相邻
        args.merge_distance = 10  # 合并距离

        # 其他参数
        args.generate_report = False
        args.verbose = True

        # 添加更多模型需要的参数
        args.in_chans = 3  # 输入通道数
        args.input_channels = 3  # 输入通道数（另一种命名）
        args.in_channels = 3  # 输入通道数（第三种命名）

        task_logger.info(f"🔧 创建处理参数:")
        task_logger.info(f"  模型类型: {args.model_type}")
        task_logger.info(f"  类别数量: {args.num_classes}")
        task_logger.info(f"  目标类别: {args.target_classes}")
        task_logger.info(f"  窗口大小: {args.window_size}")
        task_logger.info(f"  批处理大小: {args.batch_size}")
        task_logger.info(f"  重叠比例: {args.overlap}")

        return args

    def _run_ai_batch_task(self, task_id: str, task_logger):
        """运行AI批量处理任务"""
        try:
            params = self.tasks[task_id]['parameters']

            # 更新任务状态
            self._update_task_status(task_id, '正在运行', '正在初始化AI批量处理...', 10)

            # 创建AI模型配置
            config = AIModelConfig(
                image="",  # 批量模式不需要单个图像路径
                model=params['model_path'],
                output="",  # 批量模式不需要单个输出路径
                model_type=params['model_type'],
                target_classes=params['target_classes'],
                window_size=params['window_size'],
                batch_size=params['batch_size'],
                num_classes=params['num_classes'],
                batch_mode=True,
                input_dir=params['input_dir'],
                output_dir=params['output_dir']
            )

            # 执行AI批量处理
            self._update_task_status(task_id, '正在运行', '正在执行AI批量处理...', 30)
            result = ai_model_processor.process_batch(config)

            if result['success']:
                # 任务成功完成
                self._update_task_status(task_id, '已完成', 'AI批量处理任务完成', 100)

                # 更新任务结果
                self.tasks[task_id].update({
                    'end_time': datetime.now().isoformat(),
                    'result': result
                })

                task_logger.info("=== AI批量处理任务完成 ===")
                task_logger.info(f"总文件数: {result['total_files']}")
                task_logger.info(f"成功处理: {result['success_count']}")
                task_logger.info(f"失败数量: {result['failed_count']}")

            else:
                # 任务失败
                error_msg = result.get('error', '未知错误')
                self._update_task_status(task_id, '失败', f'AI批量处理失败: {error_msg}', 0)
                task_logger.error(f"AI批量处理失败: {error_msg}")

        except Exception as e:
            error_msg = f"AI批量任务执行异常: {str(e)}"
            self._update_task_status(task_id, '失败', error_msg, 0)
            task_logger.error(error_msg)
            task_logger.error(f"错误详情: {str(e)}")

    def _run_image_extent_extraction(self, task_id: str, image_path: str, output_path: str,
                                   simplify_tolerance: float, min_area: float, keep_original_crs: bool, task_logger):
        """
        执行影像范围提取的内部方法
        """
        try:
            task_logger.info("🚀 开始影像有效范围提取任务")
            task_logger.info(f"📁 输入影像: {image_path}")
            task_logger.info(f"📂 输出路径: {output_path}")
            task_logger.info(f"🔧 简化容差: {simplify_tolerance}米")
            task_logger.info(f"📏 最小面积: {min_area}平方米")
            task_logger.info(f"🗺️ 保持原始坐标系: {'是' if keep_original_crs else '否，转换为WGS84'}")

            # 更新任务状态
            self._update_task_status(task_id, '正在运行', '正在分析影像数据...', 10)

            # 更新任务状态到字典
            if task_id in self.tasks:
                self.tasks[task_id]['status'] = '正在运行'
                self.tasks[task_id]['message'] = '正在分析影像数据...'
                self.tasks[task_id]['progress'] = 10
            self._save_status()

            # 执行范围提取
            task_logger.info("🔄 调用影像范围提取处理器...")
            try:
                result = analysis_processor.extract_image_valid_extent(
                    image_path=image_path,
                    output_path=output_path,
                    simplify_tolerance=simplify_tolerance,
                    min_area=min_area,
                    keep_original_crs=keep_original_crs
                )
                task_logger.info(f"🔄 处理器返回结果: {result.get('success', False)}")
            except Exception as processor_error:
                task_logger.error(f"❌ 处理器执行异常: {str(processor_error)}")
                task_logger.error(f"❌ 异常类型: {type(processor_error).__name__}")
                import traceback
                task_logger.error(f"❌ 完整错误堆栈:\n{traceback.format_exc()}")
                raise processor_error

            if result['success']:
                # 更新任务状态为完成
                self._update_task_status(task_id, '完成', '影像有效范围提取完成', 100)

                # 更新任务结果
                self.tasks[task_id]['result'] = result
                self.tasks[task_id]['end_time'] = datetime.now().isoformat()
                self.tasks[task_id]['status'] = '完成'
                self.tasks[task_id]['message'] = '影像有效范围提取完成'
                self.tasks[task_id]['progress'] = 100

                task_logger.info("✅ 影像范围提取任务完成")
                task_logger.info(f"📊 有效区域数量: {result.get('contour_count', 0)}")
                task_logger.info(f"📐 总面积: {result.get('valid_area', 0):.6f} 平方度")
                task_logger.info(f"🗺️ 坐标系: {result.get('coordinate_system', 'WGS84')}")

            else:
                # 更新任务状态为失败
                error_message = f"影像范围提取失败: {result.get('message', '未知错误')}"
                self._update_task_status(task_id, '失败', error_message, 0)

                # 更新任务状态到字典
                self.tasks[task_id]['status'] = '失败'
                self.tasks[task_id]['message'] = error_message
                self.tasks[task_id]['progress'] = 0
                self.tasks[task_id]['end_time'] = datetime.now().isoformat()
                if 'error' in result:
                    self.tasks[task_id]['error'] = result['error']

                task_logger.error(f"❌ {error_message}")

            # 保存任务状态
            self._save_status()

        except Exception as e:
            error_msg = f"影像范围提取任务执行失败: {str(e)}"
            task_logger.error(f"❌ {error_msg}")

            # 更新任务状态为失败
            self._update_task_status(task_id, '失败', error_msg, 0)

            # 更新任务状态到字典
            if task_id in self.tasks:
                self.tasks[task_id]['status'] = '失败'
                self.tasks[task_id]['message'] = error_msg
                self.tasks[task_id]['progress'] = 0
                self.tasks[task_id]['end_time'] = datetime.now().isoformat()
                self.tasks[task_id]['error'] = str(e)

            self._save_status()

        finally:
            # 清理线程引用
            if task_id in self.running_threads:
                del self.running_threads[task_id]


# 创建全局实例
analysis_executor = AnalysisExecutor()
