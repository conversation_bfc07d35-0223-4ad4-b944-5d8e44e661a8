2025-07-18 09:08:31,961 - geo_publisher - INFO - 加载了 0 个任务状态
2025-07-18 09:20:47,115 - geo_publisher - INFO - 加载了 0 个任务状态
2025-07-18 09:21:18,853 - geo_publisher - INFO - 启动GeoTIFF发布任务 b84f98a5-2fa7-4f28-8c75-a9bea5862fb3: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 09:27:29,259 - geo_publisher - INFO - 加载了 1 个任务状态
2025-07-18 09:28:58,394 - geo_publisher - INFO - 启动GeoTIFF发布任务 d0f79e8e-f0a4-45a5-bf93-0458b334cb27: D:/Drone_Project/dataset/nanning.tif, workspace=tttt
2025-07-18 11:02:19,371 - geo_publisher - INFO - 启动GeoTIFF发布任务 d72ea4b9-27ce-4b4d-89fc-216e69c0093b: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:02:21,881 - geo_publisher - ERROR - 执行任务 d72ea4b9-27ce-4b4d-89fc-216e69c0093b 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:02:22,043 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:05:25,231 - geo_publisher - INFO - 启动GeoTIFF发布任务 22d40ee0-c742-4857-b3c0-ceac20fb79e9: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:09:32,955 - geo_publisher - INFO - 启动GeoTIFF发布任务 9c5a0509-2017-44c3-9483-ec555fd7591d: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:14:41,596 - geo_publisher - INFO - 加载了 5 个任务状态
2025-07-18 11:14:42,492 - geo_publisher - INFO - 启动GeoTIFF发布任务 f104757d-4597-4d56-98a5-c7a0906b3e8d: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:14:42,803 - geo_publisher - ERROR - 执行任务 f104757d-4597-4d56-98a5-c7a0906b3e8d 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:14:42,810 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:21:33,360 - geo_publisher - INFO - 加载了 7 个任务状态
2025-07-18 11:21:45,672 - geo_publisher - INFO - 启动GeoTIFF发布任务 cc386f17-774b-42bd-bf93-151c3648b22f: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 11:21:55,983 - geo_publisher - INFO - 启动GeoTIFF发布任务 e737a7af-417b-4395-bb63-c72e5e48afb3: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:25:11,983 - geo_publisher - INFO - 启动GeoTIFF发布任务 84cf9165-a1c6-41e2-9bbf-ebddb19735e2: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 11:28:26,503 - geo_publisher - INFO - 加载了 10 个任务状态
2025-07-18 11:29:09,471 - geo_publisher - INFO - 启动GeoTIFF发布任务 08b8bd9a-bcb4-4080-84ad-2d7c0001c225: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace="tttt"
2025-07-18 11:29:10,295 - geo_publisher - ERROR - 执行任务 08b8bd9a-bcb4-4080-84ad-2d7c0001c225 时出错: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'
2025-07-18 11:29:10,306 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 352, in _run_with_logging
    result = func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 425, in publish_func
    return manager.publish_geotiff(path, ws, store, layer)
  File "D:\Drone_Project\geoserverapi\geoserver_manager.py", line 607, in publish_geotiff
    self.geo.create_workspace(workspace)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\geo\Geoserver.py", line 396, in create_workspace
    raise GeoserverException(r.status_code, r.content)
geo.Geoserver.GeoserverException: Status : 500 - b'Error persisting WorkspaceInfoImpl["tttt"] to workspaces/"tttt"/workspace.xml'

2025-07-18 11:30:34,971 - geo_publisher - INFO - 加载了 11 个任务状态
2025-07-18 11:30:46,071 - geo_publisher - INFO - 启动GeoTIFF发布任务 71d4f572-1811-4358-8356-1684f928fcaf: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=tttt
2025-07-18 12:03:16,810 - geo_publisher - INFO - 加载了 12 个任务状态
2025-07-18 12:04:24,727 - geo_publisher - INFO - 启动GeoTIFF发布任务 e8c063d9-1859-4e86-88aa-d9857bf38de6: D:/Drone_Project/dataset/nanning.tif, workspace=tttt
2025-07-18 12:06:53,272 - geo_publisher - INFO - 加载了 13 个任务状态
2025-07-18 12:54:08,690 - geo_publisher - INFO - 启动GeoTIFF发布任务 216b7e9c-d10d-43de-844b-d046e24c2205: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-07-18 13:07:37,440 - geo_publisher - INFO - 启动GeoTIFF发布任务 75c92676-e2f1-4faa-91b3-65087ac759e8: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-07-18 13:33:28,424 - geo_publisher - INFO - 启动GeoTIFF发布任务 ecbe12a5-d49f-4d85-abfe-5019a10a238e: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-07-21 10:29:25,356 - geo_publisher - INFO - 加载了 16 个任务状态
2025-07-21 11:18:31,821 - geo_publisher - INFO - 启动GeoTIFF发布任务 0606cfc0-cecb-480e-8ba3-da2988e8a32a: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-21 11:45:47,279 - geo_publisher - INFO - 启动GeoTIFF发布任务 34dc3714-3005-42b1-8857-7a1fd8b98aaa: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-24 08:28:14,252 - geo_publisher - INFO - 加载了 18 个任务状态
2025-07-24 08:52:58,355 - geo_publisher - INFO - 加载了 18 个任务状态
2025-07-24 09:13:17,930 - geo_publisher - INFO - 加载了 18 个任务状态
2025-07-24 10:29:50,545 - geo_publisher - INFO - 启动GeoTIFF发布任务 9d13d791-9a4a-41a2-9c1c-4d3b80011e28: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-24 11:25:28,829 - geo_publisher - INFO - 加载了 19 个任务状态
2025-07-24 15:18:42,138 - geo_publisher - INFO - 加载了 19 个任务状态
2025-07-24 15:28:14,044 - geo_publisher - INFO - 启动GeoTIFF发布任务 d61f6b3b-018a-4ebb-9152-d64b13a733e6: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-24 16:19:12,683 - geo_publisher - INFO - 启动GeoTIFF发布任务 c96109c4-298c-456f-b5bd-96940b503e94: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-24 17:00:46,672 - geo_publisher - INFO - 启动GeoTIFF发布任务 b0f48082-8d48-4388-bb85-be96f4d4f42a: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-25 17:19:35,028 - geo_publisher - INFO - 启动GeoTIFF发布任务 6a751591-8b66-4278-8098-990307409110: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-25 17:52:39,402 - geo_publisher - INFO - 启动GeoTIFF发布任务 d84cf149-f59c-428c-8cb7-3193edaa851e: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-28 08:37:15,070 - geo_publisher - INFO - 加载了 24 个任务状态
2025-07-28 09:14:49,622 - geo_publisher - INFO - 加载了 24 个任务状态
2025-07-28 09:32:43,583 - geo_publisher - INFO - 启动GeoTIFF发布任务 90c2c041-02ea-4446-8ced-a5683c09f88b: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-28 09:47:45,617 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 09:53:34,885 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 10:12:14,130 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 10:20:04,630 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 10:27:31,144 - geo_publisher - INFO - 加载了 25 个任务状态
2025-07-28 10:58:49,680 - geo_publisher - INFO - 启动GeoTIFF发布任务 fd7c85a1-5ebd-489a-845b-f95bb5449d04: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-28 11:31:49,551 - geo_publisher - INFO - 启动GeoTIFF发布任务 2f2cd194-6d18-437b-a91d-de467720b289: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-28 11:41:34,146 - geo_publisher - INFO - 启动GeoTIFF发布任务 73df475a-9777-404f-be70-a349026590b7: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-28 15:28:56,828 - geo_publisher - INFO - 启动GeoTIFF发布任务 48028fd8-20e6-4e09-82f4-e2149549087c: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-28 16:26:08,275 - geo_publisher - INFO - 加载了 29 个任务状态
2025-07-28 16:55:21,617 - geo_publisher - INFO - 启动GeoTIFF发布任务 e2a4d1e7-456c-442a-866e-978cdbe7d1a6: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-07-28 17:25:55,664 - geo_publisher - INFO - 启动GeoTIFF发布任务 32e86b97-4fd6-40e7-ae05-e2801e632d67: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-29 10:21:21,209 - geo_publisher - INFO - 加载了 31 个任务状态
2025-07-29 14:53:31,057 - geo_publisher - INFO - 加载了 31 个任务状态
2025-07-30 08:23:37,065 - geo_publisher - INFO - 加载了 31 个任务状态
2025-07-30 15:49:42,988 - geo_publisher - INFO - 启动GeoTIFF发布任务 f3b75e0d-deaf-43f2-bae9-ecd6a87db91b: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-07-30 17:37:15,407 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:47:17,327 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:51:06,572 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:54:41,232 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 16:58:15,037 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 17:08:34,852 - geo_publisher - INFO - 加载了 32 个任务状态
2025-07-31 17:11:33,760 - geo_publisher - INFO - 加载了 32 个任务状态
2025-08-01 09:20:45,447 - geo_publisher - INFO - 加载了 32 个任务状态
2025-08-01 10:23:41,511 - geo_publisher - INFO - 加载了 32 个任务状态
2025-08-04 08:45:20,599 - geo_publisher - INFO - 加载了 32 个任务状态
2025-08-04 10:04:51,354 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 bd6c42de-cfac-4dc4-b697-ad21c06f525c: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:04:51,426 - geo_publisher - ERROR - 执行任务 bd6c42de-cfac-4dc4-b697-ad21c06f525c 时出错: name 'task_logger' is not defined
2025-08-04 10:04:51,434 - geo_publisher - ERROR - Traceback (most recent call last):
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 410, in _run_with_logging
    result = wrapper_func(manager, *args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 408, in wrapper_func
    return func(*args, **kwargs)
  File "D:\Drone_Project\geoserverapi\geoserverRest\core\geo_publisher.py", line 587, in publish_func
    task_logger.info(f"开始扫描根目录: {root_dir}")
NameError: name 'task_logger' is not defined

2025-08-04 10:08:29,511 - geo_publisher - INFO - 加载了 33 个任务状态
2025-08-04 10:09:11,725 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 064b5c26-c1bf-4d01-a62b-5fedae28c716: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:15:01,958 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 5468daf0-2e0e-4686-9923-e412a135b1aa: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 10:31:25,210 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 18ab6896-04b6-4c2f-b9d9-3d515b582c9e: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-04 11:17:50,384 - geo_publisher - INFO - 加载了 36 个任务状态
2025-08-04 11:26:47,589 - geo_publisher - INFO - 启动GeoTIFF发布任务 2a879d67-a3ba-466a-b935-d7aff885c034: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-04 11:47:23,710 - geo_publisher - INFO - 启动GeoTIFF发布任务 b665c195-123c-4a77-b4f4-ee8f3235341b: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-08-04 15:06:19,155 - geo_publisher - INFO - 启动GeoTIFF发布任务 a16f3aff-8432-46d8-a4a5-940d84ed9f50: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-08-04 15:46:29,840 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-04 16:14:22,864 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-04 16:16:23,373 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-04 17:45:28,610 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-04 17:47:54,945 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-05 09:01:17,014 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-05 09:45:03,919 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-05 10:11:14,407 - geo_publisher - INFO - 加载了 39 个任务状态
2025-08-05 10:13:11,147 - geo_publisher - INFO - 启动GeoTIFF发布任务 5968c6d7-4bfc-4a5b-b525-31850241a34f: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif, workspace=testdj
2025-08-06 08:50:41,975 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 08:50:42,385 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 08:51:06,620 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 08:51:06,866 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 08:51:33,417 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 09:01:31,097 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 09:01:53,713 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 09:07:46,129 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 09:07:47,785 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 09:08:11,487 - geo_publisher - INFO - 加载了 40 个任务状态
2025-08-06 09:16:48,602 - geo_publisher - INFO - 启动GeoTIFF发布任务 f9c49afc-bceb-445e-8f6a-0c93e613ca24: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto_out.tif, workspace=testodm
2025-08-06 09:18:18,834 - geo_publisher - INFO - 启动GeoTIFF发布任务 753adcf9-8209-4039-a9fb-03a7f910c9f2: D:/Drone_Project/nginxData/ODM/Input/20250705171599/project/odm_orthophoto/odm_orthophoto.tif, workspace=testodm
2025-08-06 09:29:27,875 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 09:29:52,478 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 09:37:02,238 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 09:41:16,356 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 09:46:47,916 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:04:08,249 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:04:15,956 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:04:43,261 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:04:43,717 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:05:19,525 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:05:19,740 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:05:49,729 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:18:42,299 - geo_publisher - INFO - 加载了 42 个任务状态
2025-08-06 10:23:14,370 - geo_publisher - INFO - 启动GeoTIFF发布任务 8fd16e18-072b-49e6-a7d8-4ac932c1881c: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-06 10:29:12,523 - geo_publisher - INFO - 加载了 43 个任务状态
2025-08-06 11:38:25,031 - geo_publisher - INFO - 加载了 43 个任务状态
2025-08-06 11:41:06,464 - geo_publisher - INFO - 启动GeoTIFF发布任务 56060580-bc39-4865-87ef-d06b2c39576d: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-06 12:02:09,361 - geo_publisher - INFO - 启动GeoTIFF发布任务 e4962964-f486-497b-9c0b-17000927eb60: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-08-06 12:09:31,021 - geo_publisher - INFO - 启动GeoTIFF发布任务 0b96688a-b35d-47e6-a648-5046ec32b9e2: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-08-06 12:24:03,179 - geo_publisher - INFO - 启动GeoTIFF发布任务 6aad3825-c080-4d2e-948a-27b2bddbcb75: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-08-07 08:40:22,788 - geo_publisher - INFO - 加载了 47 个任务状态
2025-08-07 08:40:36,129 - geo_publisher - INFO - 加载了 47 个任务状态
2025-08-07 09:00:16,456 - geo_publisher - INFO - 启动GeoTIFF发布任务 6902a87c-3033-4a33-ae01-1b48507feb82: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-07 09:01:52,015 - geo_publisher - INFO - 加载了 48 个任务状态
2025-08-07 09:02:57,113 - geo_publisher - INFO - 加载了 48 个任务状态
2025-08-07 09:03:17,216 - geo_publisher - INFO - 加载了 48 个任务状态
2025-08-07 09:04:22,859 - geo_publisher - INFO - 加载了 48 个任务状态
2025-08-07 09:04:59,752 - geo_publisher - INFO - 加载了 48 个任务状态
2025-08-07 09:06:11,610 - geo_publisher - INFO - 启动GeoTIFF发布任务 8c59f326-d54d-4bb0-8b09-d13cd89ef659: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-07 09:24:52,336 - geo_publisher - INFO - 加载了 49 个任务状态
2025-08-07 09:25:26,613 - geo_publisher - INFO - 加载了 49 个任务状态
2025-08-07 09:25:48,177 - geo_publisher - INFO - 请求发布特定结构GeoTIFF: D:/Drone_Project/nginxData/ODM/Output -> testodm
2025-08-07 09:25:48,181 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 43d8cc67-ee1b-480a-bf78-c54987b083e9: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-07 09:36:21,046 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:37:11,477 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:37:30,893 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:37:45,574 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:38:04,670 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:38:21,143 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:38:35,959 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:38:58,126 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:39:26,640 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:39:55,554 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:40:59,381 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:41:29,189 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:44:12,741 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:44:43,021 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:45:01,656 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:46:06,667 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:46:32,708 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:48:04,172 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 09:48:30,280 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:12:53,057 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:36:08,031 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:38:44,474 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:39:26,828 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:40:23,275 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:41:15,959 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:42:05,730 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:42:35,745 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:43:57,017 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 11:45:25,994 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 12:23:56,032 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 12:24:12,952 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 12:25:10,206 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 12:26:58,520 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 14:54:16,094 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:00:26,919 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:00:39,303 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:10:23,846 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:11:04,147 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:12:13,385 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:14:05,622 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:14:37,098 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:18:08,253 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:18:23,394 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:18:41,023 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:19:07,738 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:19:29,545 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:19:45,561 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:23:47,713 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:24:05,095 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:24:55,204 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:25:19,787 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:28:42,917 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:30:19,797 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:30:19,821 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 15:31:45,809 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:42:31,148 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:43:46,805 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:45:42,924 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:48:17,019 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:48:51,119 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:50:05,133 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:50:52,065 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:51:47,204 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:52:28,457 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:55:13,661 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:55:27,186 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:57:20,533 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 17:59:48,752 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 18:00:02,185 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 18:01:30,400 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-07 18:05:30,689 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:21:07,242 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:23:09,755 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:26:04,344 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:26:59,791 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:27:19,879 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:27:34,907 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:31:07,353 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:31:34,924 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:31:34,929 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:32:05,311 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:32:05,712 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:32:36,162 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:32:36,166 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:37:59,571 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:40:37,061 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:41:36,149 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:41:36,184 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:45:34,423 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:46:36,664 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:46:36,772 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:47:30,571 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:49:35,752 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 08:49:36,637 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:13:39,727 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:13:39,850 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:14:35,493 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:14:35,504 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:14:53,686 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:24:30,659 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:40:10,951 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:40:36,146 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:40:36,166 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:46:49,747 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:46:50,863 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:47:14,697 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:47:14,896 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:47:34,686 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:47:34,704 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:48:15,717 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:48:15,742 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:48:31,415 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:48:31,436 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:51:37,239 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:51:37,243 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:53:45,133 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:53:45,699 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:54:14,418 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 09:54:14,943 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:03:39,480 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:03:39,774 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:03:56,507 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:03:56,874 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:16:33,458 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:16:33,484 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:27:03,949 - geo_publisher - INFO - 加载了 50 个任务状态
2025-08-08 10:32:37,782 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif -> testodm
2025-08-08 10:32:37,827 - geo_publisher - INFO - 启动GeoTIFF发布任务 9779849e-da59-41c4-8fa2-9807f3e9c99a: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-08 10:32:37,830 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: 9779849e-da59-41c4-8fa2-9807f3e9c99a
2025-08-08 10:32:37,899 - geo_publisher - INFO - 查询发布任务状态: 9779849e-da59-41c4-8fa2-9807f3e9c99a
2025-08-08 10:33:39,055 - geo_publisher - INFO - 查询发布任务状态: 9779849e-da59-41c4-8fa2-9807f3e9c99a
2025-08-08 10:52:53,540 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 10:54:31,596 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:08:52,121 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:09:12,853 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:09:50,406 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:10:12,415 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:11:18,248 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:11:42,382 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:18:47,899 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:21:41,542 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 11:21:41,547 - geo_publisher - ERROR - 同步发布Shapefile时出错: 'GeoServerManager' object has no attribute 'check_workspace_exists'
2025-08-08 11:23:17,138 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:23:42,555 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:24:12,627 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:24:34,977 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 11:24:35,001 - geo_publisher - WARNING - 工作区创建结果: 创建工作区失败，状态码: 404, 响应: {
"message":"Not Found",
"url":"/rest/workspaces",
"status":"404"
}
2025-08-08 11:24:35,007 - geo_publisher - ERROR - 创建数据存储失败: 创建数据存储失败，状态码: 404, 响应: {
"message":"Not Found",
"url":"/rest/workspaces/testodm/datastores",
"status":"404"
}
2025-08-08 11:26:14,215 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 11:26:14,240 - geo_publisher - WARNING - 工作区创建结果: 创建工作区失败，状态码: 404, 响应: {
"message":"Not Found",
"url":"/rest/workspaces",
"status":"404"
}
2025-08-08 11:26:14,271 - geo_publisher - ERROR - 创建数据存储失败: 创建数据存储失败，状态码: 404, 响应: {
"message":"Not Found",
"url":"/rest/workspaces/testodm/datastores",
"status":"404"
}
2025-08-08 11:28:09,795 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 11:28:09,878 - geo_publisher - WARNING - 工作区创建结果: 创建工作区失败，状态码: 404, 响应: {
"message":"Not Found",
"url":"/rest/workspaces",
"status":"404"
}
2025-08-08 11:28:09,901 - geo_publisher - ERROR - 创建数据存储失败: 创建数据存储失败，状态码: 404, 响应: {
"message":"Not Found",
"url":"/rest/workspaces/testodm/datastores",
"status":"404"
}
2025-08-08 11:32:28,262 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 11:36:34,537 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 11:36:36,946 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 11:59:12,498 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 11:59:12,656 - geo_publisher - ERROR - 创建数据存储失败: 创建数据存储失败，状态码: 500, 响应: Store '20250705171599_out' already exists in workspace 'testodm'
2025-08-08 12:00:30,135 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:01:13,316 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:01:43,462 - geo_publisher - ERROR - 创建数据存储失败: 删除数据存储时出错: HTTPConnectionPool(host='localhost', port=8083): Read timed out. (read timeout=30)
2025-08-08 12:02:18,825 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:03:10,160 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:03:10,455 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:03:38,704 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:04:39,320 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:05:07,165 - geo_publisher - INFO - 开始批量发布目录中的Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599
2025-08-08 12:05:07,265 - geo_publisher - INFO - 发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599\20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:06:07,778 - geo_publisher - INFO - 成功发布: 20250705171599_out
2025-08-08 12:06:07,780 - geo_publisher - INFO - 批量发布完成: 成功 1 个，失败 0 个
2025-08-08 12:06:51,099 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:07:51,614 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:09:31,230 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:10:31,792 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:11:13,332 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:11:33,049 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:11:33,142 - geo_publisher - INFO - 图层已存在，正在删除: testodm:20250705171599_out
2025-08-08 12:12:03,159 - geo_publisher - ERROR - 删除现有图层失败: 删除图层和数据存储时出错: HTTPConnectionPool(host='localhost', port=8083): Read timed out. (read timeout=30)
2025-08-08 12:12:39,945 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:13:44,554 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:13:44,687 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:13:44,776 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in store: '20250705171599_out'
2025-08-08 12:14:32,444 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:14:49,119 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:14:49,326 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:14:49,453 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in store: '20250705171599_out'
2025-08-08 12:15:14,279 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:15:30,840 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:15:31,058 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:15:31,121 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in namespace: 'testodm'
2025-08-08 12:15:54,472 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:16:11,001 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out_1754626571
2025-08-08 12:16:11,135 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out_1754626571
2025-08-08 12:16:11,319 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 400, 响应: {
"servlet":"dispatcher",
"message":"Trying to create new feature type inside the store, but no attributes were specified",
"url":"/geoserver/rest/workspaces/testodm/datastores/20250705171599_out_1754626571/featuretypes",
"status":"400"
}
2025-08-08 12:17:12,291 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:17:35,438 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out_1754626655
2025-08-08 12:17:35,541 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out_1754626655
2025-08-08 12:17:36,011 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out_1754626655
2025-08-08 12:18:17,500 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out_1754626697
2025-08-08 12:18:17,629 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out_1754626697
2025-08-08 12:18:17,944 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out_1754626697
2025-08-08 12:18:40,045 - geo_publisher - INFO - 开始批量发布目录中的Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599
2025-08-08 12:18:40,165 - geo_publisher - INFO - 发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599\20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:18:40,310 - geo_publisher - INFO - 批量发布完成: 成功 0 个，失败 1 个
2025-08-08 12:19:08,691 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:19:26,900 - geo_publisher - INFO - 开始批量发布目录中的Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599
2025-08-08 12:19:26,974 - geo_publisher - INFO - 发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599\20250705171599_out.shp -> testodm:20250705171599_out_1754626766
2025-08-08 12:19:27,152 - geo_publisher - INFO - 成功发布: 20250705171599_out
2025-08-08 12:19:27,157 - geo_publisher - INFO - 批量发布完成: 成功 1 个，失败 0 个
2025-08-08 12:21:16,001 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out_1754626876
2025-08-08 12:21:16,175 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out_1754626876
2025-08-08 12:21:16,352 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out_1754626876
2025-08-08 12:21:30,918 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out_1754626890
2025-08-08 12:21:31,046 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out_1754626890
2025-08-08 12:21:31,185 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out_1754626890
2025-08-08 12:22:55,025 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:23:20,029 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:23:41,660 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:24:04,137 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:24:27,485 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:25:06,602 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:25:36,651 - geo_publisher - WARNING - 工作区创建结果: 创建工作区时出错: HTTPConnectionPool(host='localhost', port=8083): Read timed out. (read timeout=30)
2025-08-08 12:25:46,684 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:26:16,703 - geo_publisher - ERROR - 创建数据存储失败: 创建数据存储时出错: HTTPConnectionPool(host='localhost', port=8083): Read timed out. (read timeout=30)
2025-08-08 12:26:42,628 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:27:40,389 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:27:40,541 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:27:40,666 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 400, 响应: {
"servlet":"dispatcher",
"message":"Trying to create new feature type inside the store, but no attributes were specified",
"url":"/geoserver/rest/workspaces/testodm/datastores/20250705171599_out_1754627260/featuretypes",
"status":"400"
}
2025-08-08 12:28:50,053 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:29:09,871 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:29:09,984 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:29:10,051 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in namespace: 'testodm'
2025-08-08 12:29:55,591 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:30:15,319 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:30:15,449 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:30:15,512 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in namespace: 'testodm'
2025-08-08 12:33:55,604 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:34:21,971 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:34:42,271 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:35:30,827 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:35:30,934 - geo_publisher - INFO - 图层不存在，直接发布: testodm:20250705171599_out
2025-08-08 12:35:31,331 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:36:06,673 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:36:06,839 - geo_publisher - INFO - 图层已存在，正在删除所有相关数据: testodm:20250705171599_out
2025-08-08 12:37:06,951 - geo_publisher - INFO - 删除结果: 删除完成: 成功删除 1 项，1 个错误
2025-08-08 12:37:06,959 - geo_publisher - INFO - 已删除: ['数据存储: 20250705171599_out_1754627730']
2025-08-08 12:37:06,963 - geo_publisher - WARNING - 删除错误: ['删除图层超时']
2025-08-08 12:37:06,965 - geo_publisher - INFO - 清理完成，开始重新发布
2025-08-08 12:37:07,282 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:37:36,263 - geo_publisher - INFO - 开始批量发布目录中的Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599
2025-08-08 12:37:36,339 - geo_publisher - INFO - 发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599\20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:37:36,367 - geo_publisher - INFO - 图层已存在，正在删除所有相关数据: testodm:20250705171599_out
2025-08-08 12:38:37,765 - geo_publisher - INFO - 删除结果: 删除完成: 成功删除 1 项，1 个错误
2025-08-08 12:38:37,771 - geo_publisher - WARNING - 删除时有错误: ['删除图层超时']
2025-08-08 12:38:37,774 - geo_publisher - INFO - 清理完成: 20250705171599_out
2025-08-08 12:38:38,193 - geo_publisher - INFO - 成功发布: 20250705171599_out
2025-08-08 12:38:38,213 - geo_publisher - INFO - 批量发布完成: 成功 1 个，失败 0 个
2025-08-08 12:40:25,540 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:40:42,104 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:40:59,468 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:41:15,194 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:41:30,825 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:43:18,337 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:43:47,506 - geo_publisher - INFO - 加载了 51 个任务状态
2025-08-08 12:44:01,444 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:44:01,637 - geo_publisher - INFO - 检测到冲突 - 图层存在: True, 存储存在: False
2025-08-08 12:44:01,644 - geo_publisher - INFO - 正在删除组合: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:45:01,752 - geo_publisher - INFO - 删除结果: 删除组合完成: 成功删除 0 项，1 个错误
2025-08-08 12:45:01,754 - geo_publisher - WARNING - 删除错误: ['删除图层超时']
2025-08-08 12:45:01,756 - geo_publisher - INFO - 清理完成，开始重新发布
2025-08-08 12:45:01,919 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in namespace: 'testodm'
2025-08-08 12:45:49,318 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:45:49,449 - geo_publisher - INFO - 无冲突，直接发布: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:45:49,493 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in namespace: 'testodm'
2025-08-08 12:50:36,822 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:51:06,850 - geo_publisher - WARNING - 工作区创建结果: 创建工作区时出错: HTTPConnectionPool(host='localhost', port=8083): Read timed out. (read timeout=30)
2025-08-08 12:51:08,607 - geo_publisher - INFO - 检测到冲突 - 图层存在: False, 存储存在: True
2025-08-08 12:51:08,628 - geo_publisher - INFO - 正在删除组合: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:51:08,768 - geo_publisher - INFO - 删除结果: 删除组合完成: 成功删除 1 项，0 个错误
2025-08-08 12:51:08,770 - geo_publisher - INFO - 已删除: ['数据存储: 20250705171599_out']
2025-08-08 12:51:08,773 - geo_publisher - INFO - 清理完成，开始重新发布
2025-08-08 12:51:08,859 - geo_publisher - ERROR - Shapefile同步发布失败: 发布图层失败，状态码: 500, 响应: Resource named '20250705171599_out' already exists in namespace: 'testodm'
2025-08-08 12:52:01,511 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:52:01,722 - geo_publisher - INFO - 无冲突，直接发布: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:52:01,879 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:52:35,165 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:52:35,415 - geo_publisher - INFO - 检测到冲突 - 图层存在: True, 存储存在: True
2025-08-08 12:52:35,423 - geo_publisher - INFO - 正在删除组合: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:53:35,554 - geo_publisher - INFO - 删除结果: 删除组合完成: 成功删除 2 项，1 个错误
2025-08-08 12:53:35,556 - geo_publisher - INFO - 已删除: ['FeatureType: 20250705171599_out', '数据存储: 20250705171599_out']
2025-08-08 12:53:35,567 - geo_publisher - WARNING - 删除错误: ['删除图层超时']
2025-08-08 12:53:35,574 - geo_publisher - INFO - 清理完成，开始重新发布
2025-08-08 12:53:35,945 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:54:05,733 - geo_publisher - INFO - 开始批量发布目录中的Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599
2025-08-08 12:54:06,079 - geo_publisher - INFO - 发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599\20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:54:06,143 - geo_publisher - INFO - 检测到冲突 - 图层存在: True, 存储存在: True
2025-08-08 12:54:06,146 - geo_publisher - INFO - 正在删除组合: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:55:06,231 - geo_publisher - INFO - 删除结果: 删除组合完成: 成功删除 2 项，1 个错误
2025-08-08 12:55:06,234 - geo_publisher - WARNING - 删除时有错误: ['删除图层超时']
2025-08-08 12:55:06,241 - geo_publisher - INFO - 清理完成: 20250705171599_out
2025-08-08 12:55:06,384 - geo_publisher - INFO - 成功发布: 20250705171599_out
2025-08-08 12:55:06,882 - geo_publisher - INFO - 批量发布完成: 成功 1 个，失败 0 个
2025-08-08 12:56:47,301 - geo_publisher - INFO - 开始同步发布Shapefile: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.shp -> testodm:20250705171599_out
2025-08-08 12:56:47,478 - geo_publisher - INFO - 检测到冲突 - 图层存在: True, 存储存在: True
2025-08-08 12:56:47,482 - geo_publisher - INFO - 正在删除组合: testodm:20250705171599_out:20250705171599_out
2025-08-08 12:57:48,273 - geo_publisher - INFO - 删除结果: 删除组合完成: 成功删除 2 项，1 个错误
2025-08-08 12:57:48,346 - geo_publisher - INFO - 已删除: ['FeatureType: 20250705171599_out', '数据存储: 20250705171599_out']
2025-08-08 12:57:48,408 - geo_publisher - WARNING - 删除错误: ['删除图层超时']
2025-08-08 12:57:48,460 - geo_publisher - INFO - 清理完成，开始重新发布
2025-08-08 12:57:53,280 - geo_publisher - INFO - Shapefile同步发布成功: testodm:20250705171599_out
2025-08-08 12:59:24,231 - geo_publisher - INFO - 开始异步发布结构化GeoTIFF: D:/Drone_Project/nginxData/ODM/Output -> testodm
2025-08-08 12:59:34,009 - geo_publisher - INFO - 启动特定结构GeoTIFF发布任务 518c24fd-b376-4da8-b9af-4f096b3bb5f7: D:/Drone_Project/nginxData/ODM/Output, workspace=testodm
2025-08-08 12:59:34,015 - geo_publisher - INFO - 结构化GeoTIFF发布任务已启动，任务ID: 518c24fd-b376-4da8-b9af-4f096b3bb5f7
2025-08-08 13:06:32,487 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:06:49,394 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:08:47,760 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:12:20,872 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:12:36,817 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:13:50,789 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:15:10,632 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:25:09,277 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:25:36,451 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:26:04,248 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:26:33,295 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:27:10,935 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:30:09,815 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:32:30,266 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:55:17,285 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:55:17,300 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 13:55:18,638 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:37:59,539 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:37:59,537 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:38:00,574 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:38:51,271 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:38:51,301 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:38:51,647 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:39:17,800 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:39:20,508 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:39:20,964 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:39:57,714 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:39:57,733 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:39:58,934 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:40:34,253 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:40:34,400 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:40:34,509 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:41:03,216 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:41:05,021 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:41:05,187 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:50:43,199 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:50:44,054 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:51:33,739 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:51:34,149 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:51:35,070 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:52:04,569 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:52:04,795 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:52:05,333 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:52:41,956 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:52:42,017 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:52:42,786 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:53:15,644 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:53:15,652 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 14:53:16,967 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:03:37,790 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:03:53,880 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:07:30,530 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:08:18,380 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:08:52,644 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:09:18,687 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:09:44,667 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 15:24:15,535 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 16:54:44,886 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-08 16:54:58,759 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 08:34:39,395 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 08:34:55,003 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:04:31,731 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:06:22,699 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:06:46,269 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:07:04,140 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:09:01,481 - geo_publisher - INFO - 开始自动切片图层: drone:招商引资区交种植区_小范围数据
2025-08-11 16:09:01,485 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=0-10, 格式=image/png
2025-08-11 16:09:01,543 - geo_publisher - ERROR - 自动切片任务启动失败: 切片任务启动失败: 启动切片任务时出错: 'latin-1' codec can't encode characters in position 69-77: Body ('招商引资区交种植区') is not valid Latin-1. Use body.encode('utf-8') if you want to send it encoded in UTF-8.
2025-08-11 16:09:06,572 - geo_publisher - INFO - 查询切片任务状态: workspace=drone, layer_name=招商引资区交种植区_小范围数据
2025-08-11 16:09:06,584 - geo_publisher - ERROR - 获取切片任务状态失败: 获取切片任务状态失败，状态码: 404, 响应: 
2025-08-11 16:09:33,538 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:14:45,751 - geo_publisher - INFO - 开始自动切片图层: drone:招商引资区交种植区_小范围数据
2025-08-11 16:14:45,752 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=0-10, 格式=image/png
2025-08-11 16:14:45,782 - geo_publisher - ERROR - 自动切片任务启动失败: 切片任务启动失败: 启动切片任务失败，状态码: 404, 响应: <html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=ISO-8859-1"/>
<title>Error 404 Not Found</title>
</head>
<body><h2>HTTP ERROR 404 Not Found</h2>
<table>
<tr><th>URI:</th><td>/geoserver/seed/drone:%E6%8B%9B%E5%95%86%E5%BC%95%E8%B5%84%E5%8C%BA%E4%BA%A4%E7%A7%8D%E6%A4%8D%E5%8C%BA_%E5%B0%8F%E8%8C%83%E5%9B%B4%E6%95%B0%E6%8D%AE.xml</td></tr>
<tr><th>STATUS:</th><td>404</td></tr>
<tr><th>MESSAGE:</th><td>Not Found</td></tr>
<tr><th>SERVLET:</th><td>dispatcher</td></tr>
</table>
<hr/><a href="https://jetty.org/">Powered by Jetty:// 9.4.56.v20240826</a><hr/>

</body>
</html>

2025-08-11 16:14:50,824 - geo_publisher - INFO - 查询切片任务状态: workspace=drone, layer_name=招商引资区交种植区_小范围数据
2025-08-11 16:14:50,857 - geo_publisher - ERROR - 获取切片任务状态失败: 获取切片任务状态失败，状态码: 404, 响应: 
2025-08-11 16:16:27,758 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:17:42,066 - geo_publisher - INFO - 开始自动切片图层: testodm:20250705171599
2025-08-11 16:17:42,069 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=0-5, 格式=image/png
2025-08-11 16:17:42,104 - geo_publisher - ERROR - 自动切片任务启动失败: 切片任务启动失败: 启动切片任务失败，状态码: 404, 响应: <html>
<head>
<meta http-equiv="Content-Type" content="text/html;charset=ISO-8859-1"/>
<title>Error 404 Not Found</title>
</head>
<body><h2>HTTP ERROR 404 Not Found</h2>
<table>
<tr><th>URI:</th><td>/geoserver/seed/testodm:20250705171599.xml</td></tr>
<tr><th>STATUS:</th><td>404</td></tr>
<tr><th>MESSAGE:</th><td>Not Found</td></tr>
<tr><th>SERVLET:</th><td>dispatcher</td></tr>
</table>
<hr/><a href="https://jetty.org/">Powered by Jetty:// 9.4.56.v20240826</a><hr/>

</body>
</html>

2025-08-11 16:18:47,214 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 16:20:05,344 - geo_publisher - INFO - 开始自动切片图层: testodm:20250705171599
2025-08-11 16:20:05,355 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=0-5, 格式=image/png
2025-08-11 16:20:05,472 - geo_publisher - INFO - 自动切片任务启动成功: testodm:20250705171599
2025-08-11 16:20:07,485 - geo_publisher - INFO - 查询切片任务状态: workspace=testodm, layer_name=20250705171599
2025-08-11 16:20:07,504 - geo_publisher - INFO - 成功获取切片任务状态，共 0 个任务
2025-08-11 16:21:07,743 - geo_publisher - INFO - 开始自动切片图层: testodm:20250705171599
2025-08-11 16:21:07,744 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=0-8, 格式=image/png
2025-08-11 16:21:07,860 - geo_publisher - INFO - 自动切片任务启动成功: testodm:20250705171599
2025-08-11 16:21:10,873 - geo_publisher - INFO - 查询切片任务状态: workspace=testodm, layer_name=20250705171599
2025-08-11 16:21:10,915 - geo_publisher - INFO - 成功获取切片任务状态，共 0 个任务
2025-08-11 16:21:10,921 - geo_publisher - INFO - 查询切片任务状态: workspace=None, layer_name=None
2025-08-11 16:21:10,930 - geo_publisher - ERROR - 获取切片任务状态失败: 获取切片任务状态失败，状态码: 405, 响应: 
2025-08-11 16:21:10,954 - geo_publisher - INFO - 开始自动切片图层: testodm:20250705171599
2025-08-11 16:21:10,973 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=0-5, 格式=image/png
2025-08-11 16:21:11,095 - geo_publisher - INFO - 自动切片任务启动成功: testodm:20250705171599
2025-08-11 16:21:11,107 - geo_publisher - INFO - 开始自动切片图层: testodm:20250705171599
2025-08-11 16:21:11,115 - geo_publisher - INFO - 切片参数: 网格集=EPSG:4326, 级别=6-10, 格式=image/jpeg
2025-08-11 16:21:11,193 - geo_publisher - INFO - 自动切片任务启动成功: testodm:20250705171599
2025-08-11 17:01:54,354 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:02:41,380 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:23:15,172 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:23:26,476 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:30:34,135 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:31:06,913 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:33:34,190 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:35:52,954 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-11 17:36:54,305 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:32:33,429 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:32:46,779 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:40:05,063 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:40:33,914 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:42:19,214 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:42:36,379 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:43:02,231 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:45:57,502 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:50:33,840 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 15:53:29,126 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 16:21:56,494 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 16:25:30,899 - geo_publisher - INFO - 开始批量上传样式文件，文件夹: D:/Drone_Project/nginxData/styles
2025-08-12 16:25:30,901 - geo_publisher - INFO - 工作区: 全局样式
2025-08-12 16:25:30,904 - geo_publisher - INFO - 覆盖模式: True
2025-08-12 16:25:30,927 - geo_publisher - INFO - 找到 40 个样式文件
2025-08-12 16:25:30,927 - geo_publisher - INFO - 正在上传样式: Aquamarine (Aquamarine.txt)
2025-08-12 16:25:31,032 - geo_publisher - ERROR - 样式 Aquamarine 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,033 - geo_publisher - INFO - 正在上传样式: Black (Black.txt)
2025-08-12 16:25:31,095 - geo_publisher - ERROR - 样式 Black 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,095 - geo_publisher - INFO - 正在上传样式: Blue (Blue.txt)
2025-08-12 16:25:31,250 - geo_publisher - ERROR - 样式 Blue 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,250 - geo_publisher - INFO - 正在上传样式: Brown (Brown.txt)
2025-08-12 16:25:31,293 - geo_publisher - ERROR - 样式 Brown 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,293 - geo_publisher - INFO - 正在上传样式: Chartreuse (Chartreuse.txt)
2025-08-12 16:25:31,359 - geo_publisher - ERROR - 样式 Chartreuse 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,360 - geo_publisher - INFO - 正在上传样式: Coral (Coral.txt)
2025-08-12 16:25:31,388 - geo_publisher - ERROR - 样式 Coral 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,388 - geo_publisher - INFO - 正在上传样式: Crimson (Crimson.txt)
2025-08-12 16:25:31,425 - geo_publisher - ERROR - 样式 Crimson 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,426 - geo_publisher - INFO - 正在上传样式: Cyan (Cyan.txt)
2025-08-12 16:25:31,484 - geo_publisher - ERROR - 样式 Cyan 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,487 - geo_publisher - INFO - 正在上传样式: DarkKhaki (DarkKhaki.txt)
2025-08-12 16:25:31,526 - geo_publisher - ERROR - 样式 DarkKhaki 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,526 - geo_publisher - INFO - 正在上传样式: DarkOrange (DarkOrange.txt)
2025-08-12 16:25:31,561 - geo_publisher - ERROR - 样式 DarkOrange 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,562 - geo_publisher - INFO - 正在上传样式: DarkTurquoise (DarkTurquoise.txt)
2025-08-12 16:25:31,591 - geo_publisher - ERROR - 样式 DarkTurquoise 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,593 - geo_publisher - INFO - 正在上传样式: DodgerBlue (DodgerBlue.txt)
2025-08-12 16:25:31,622 - geo_publisher - ERROR - 样式 DodgerBlue 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,622 - geo_publisher - INFO - 正在上传样式: Gold (Gold.txt)
2025-08-12 16:25:31,647 - geo_publisher - ERROR - 样式 Gold 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,649 - geo_publisher - INFO - 正在上传样式: Gray (Gray.txt)
2025-08-12 16:25:31,726 - geo_publisher - ERROR - 样式 Gray 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,726 - geo_publisher - INFO - 正在上传样式: Green (Green.txt)
2025-08-12 16:25:31,768 - geo_publisher - ERROR - 样式 Green 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,769 - geo_publisher - INFO - 正在上传样式: LightCoral (LightCoral.txt)
2025-08-12 16:25:31,798 - geo_publisher - ERROR - 样式 LightCoral 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,799 - geo_publisher - INFO - 正在上传样式: LightSeaGreen (LightSeaGreen.txt)
2025-08-12 16:25:31,831 - geo_publisher - ERROR - 样式 LightSeaGreen 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,832 - geo_publisher - INFO - 正在上传样式: Lime (Lime.txt)
2025-08-12 16:25:31,892 - geo_publisher - ERROR - 样式 Lime 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,893 - geo_publisher - INFO - 正在上传样式: Magenta (Magenta.txt)
2025-08-12 16:25:31,920 - geo_publisher - ERROR - 样式 Magenta 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,921 - geo_publisher - INFO - 正在上传样式: MediumAquamarine (MediumAquamarine.txt)
2025-08-12 16:25:31,950 - geo_publisher - ERROR - 样式 MediumAquamarine 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:31,951 - geo_publisher - INFO - 正在上传样式: MediumPurple (MediumPurple.txt)
2025-08-12 16:25:32,006 - geo_publisher - ERROR - 样式 MediumPurple 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,006 - geo_publisher - INFO - 正在上传样式: MediumSpringGreen (MediumSpringGreen.txt)
2025-08-12 16:25:32,046 - geo_publisher - ERROR - 样式 MediumSpringGreen 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,046 - geo_publisher - INFO - 正在上传样式: MediumVioletRed (MediumVioletRed.txt)
2025-08-12 16:25:32,096 - geo_publisher - ERROR - 样式 MediumVioletRed 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,097 - geo_publisher - INFO - 正在上传样式: Navy (Navy.txt)
2025-08-12 16:25:32,142 - geo_publisher - ERROR - 样式 Navy 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,143 - geo_publisher - INFO - 正在上传样式: Olive (Olive.txt)
2025-08-12 16:25:32,190 - geo_publisher - ERROR - 样式 Olive 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,191 - geo_publisher - INFO - 正在上传样式: Orange (Orange.txt)
2025-08-12 16:25:32,231 - geo_publisher - ERROR - 样式 Orange 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,232 - geo_publisher - INFO - 正在上传样式: Orchid (Orchid.txt)
2025-08-12 16:25:32,281 - geo_publisher - ERROR - 样式 Orchid 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,282 - geo_publisher - INFO - 正在上传样式: PaleGoldenrod (PaleGoldenrod.txt)
2025-08-12 16:25:32,330 - geo_publisher - ERROR - 样式 PaleGoldenrod 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,331 - geo_publisher - INFO - 正在上传样式: Pink (Pink.txt)
2025-08-12 16:25:32,375 - geo_publisher - ERROR - 样式 Pink 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,376 - geo_publisher - INFO - 正在上传样式: Purple (Purple.txt)
2025-08-12 16:25:32,421 - geo_publisher - ERROR - 样式 Purple 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,421 - geo_publisher - INFO - 正在上传样式: Red (Red.txt)
2025-08-12 16:25:32,449 - geo_publisher - ERROR - 样式 Red 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,451 - geo_publisher - INFO - 正在上传样式: Salmon (Salmon.txt)
2025-08-12 16:25:32,505 - geo_publisher - ERROR - 样式 Salmon 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,506 - geo_publisher - INFO - 正在上传样式: SkyBlue (SkyBlue.txt)
2025-08-12 16:25:32,548 - geo_publisher - ERROR - 样式 SkyBlue 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,550 - geo_publisher - INFO - 正在上传样式: SlateBlue (SlateBlue.txt)
2025-08-12 16:25:32,594 - geo_publisher - ERROR - 样式 SlateBlue 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,595 - geo_publisher - INFO - 正在上传样式: SteelBlue (SteelBlue.txt)
2025-08-12 16:25:32,643 - geo_publisher - ERROR - 样式 SteelBlue 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,644 - geo_publisher - INFO - 正在上传样式: Teal (Teal.txt)
2025-08-12 16:25:32,669 - geo_publisher - ERROR - 样式 Teal 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,669 - geo_publisher - INFO - 正在上传样式: Tomato (Tomato.txt)
2025-08-12 16:25:32,693 - geo_publisher - ERROR - 样式 Tomato 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,695 - geo_publisher - INFO - 正在上传样式: Violet (Violet.txt)
2025-08-12 16:25:32,722 - geo_publisher - ERROR - 样式 Violet 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,723 - geo_publisher - INFO - 正在上传样式: White (White.txt)
2025-08-12 16:25:32,753 - geo_publisher - ERROR - 样式 White 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,754 - geo_publisher - INFO - 正在上传样式: Yellow (Yellow.txt)
2025-08-12 16:25:32,794 - geo_publisher - ERROR - 样式 Yellow 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:25:32,795 - geo_publisher - INFO - 批量样式上传完成: 所有样式文件上传失败，共 40 个文件
2025-08-12 16:26:58,922 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 16:28:06,343 - geo_publisher - INFO - 开始批量上传样式文件，文件夹: D:/Drone_Project/nginxData/styles
2025-08-12 16:28:06,348 - geo_publisher - INFO - 工作区: 全局样式
2025-08-12 16:28:06,349 - geo_publisher - INFO - 覆盖模式: True
2025-08-12 16:28:06,380 - geo_publisher - INFO - 找到 40 个样式文件
2025-08-12 16:28:06,381 - geo_publisher - INFO - 正在上传样式: Aquamarine (Aquamarine.txt)
2025-08-12 16:28:06,974 - geo_publisher - INFO - 样式 Aquamarine 上传成功
2025-08-12 16:28:06,975 - geo_publisher - INFO - 正在上传样式: Black (Black.txt)
2025-08-12 16:28:07,036 - geo_publisher - ERROR - 样式 Black 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,037 - geo_publisher - INFO - 正在上传样式: Blue (Blue.txt)
2025-08-12 16:28:07,087 - geo_publisher - ERROR - 样式 Blue 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,088 - geo_publisher - INFO - 正在上传样式: Brown (Brown.txt)
2025-08-12 16:28:07,161 - geo_publisher - ERROR - 样式 Brown 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,162 - geo_publisher - INFO - 正在上传样式: Chartreuse (Chartreuse.txt)
2025-08-12 16:28:07,215 - geo_publisher - ERROR - 样式 Chartreuse 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,215 - geo_publisher - INFO - 正在上传样式: Coral (Coral.txt)
2025-08-12 16:28:07,255 - geo_publisher - ERROR - 样式 Coral 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,256 - geo_publisher - INFO - 正在上传样式: Crimson (Crimson.txt)
2025-08-12 16:28:07,330 - geo_publisher - INFO - 样式 Crimson 上传成功
2025-08-12 16:28:07,330 - geo_publisher - INFO - 正在上传样式: Cyan (Cyan.txt)
2025-08-12 16:28:07,359 - geo_publisher - ERROR - 样式 Cyan 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,360 - geo_publisher - INFO - 正在上传样式: DarkKhaki (DarkKhaki.txt)
2025-08-12 16:28:07,518 - geo_publisher - INFO - 样式 DarkKhaki 上传成功
2025-08-12 16:28:07,519 - geo_publisher - INFO - 正在上传样式: DarkOrange (DarkOrange.txt)
2025-08-12 16:28:07,599 - geo_publisher - INFO - 样式 DarkOrange 上传成功
2025-08-12 16:28:07,600 - geo_publisher - INFO - 正在上传样式: DarkTurquoise (DarkTurquoise.txt)
2025-08-12 16:28:07,732 - geo_publisher - INFO - 样式 DarkTurquoise 上传成功
2025-08-12 16:28:07,733 - geo_publisher - INFO - 正在上传样式: DodgerBlue (DodgerBlue.txt)
2025-08-12 16:28:07,817 - geo_publisher - INFO - 样式 DodgerBlue 上传成功
2025-08-12 16:28:07,818 - geo_publisher - INFO - 正在上传样式: Gold (Gold.txt)
2025-08-12 16:28:07,858 - geo_publisher - ERROR - 样式 Gold 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,859 - geo_publisher - INFO - 正在上传样式: Gray (Gray.txt)
2025-08-12 16:28:07,897 - geo_publisher - ERROR - 样式 Gray 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,898 - geo_publisher - INFO - 正在上传样式: Green (Green.txt)
2025-08-12 16:28:07,928 - geo_publisher - ERROR - 样式 Green 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:07,930 - geo_publisher - INFO - 正在上传样式: LightCoral (LightCoral.txt)
2025-08-12 16:28:08,004 - geo_publisher - INFO - 样式 LightCoral 上传成功
2025-08-12 16:28:08,005 - geo_publisher - INFO - 正在上传样式: LightSeaGreen (LightSeaGreen.txt)
2025-08-12 16:28:08,065 - geo_publisher - INFO - 样式 LightSeaGreen 上传成功
2025-08-12 16:28:08,066 - geo_publisher - INFO - 正在上传样式: Lime (Lime.txt)
2025-08-12 16:28:08,096 - geo_publisher - ERROR - 样式 Lime 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,097 - geo_publisher - INFO - 正在上传样式: Magenta (Magenta.txt)
2025-08-12 16:28:08,142 - geo_publisher - ERROR - 样式 Magenta 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,144 - geo_publisher - INFO - 正在上传样式: MediumAquamarine (MediumAquamarine.txt)
2025-08-12 16:28:08,205 - geo_publisher - INFO - 样式 MediumAquamarine 上传成功
2025-08-12 16:28:08,206 - geo_publisher - INFO - 正在上传样式: MediumPurple (MediumPurple.txt)
2025-08-12 16:28:08,271 - geo_publisher - INFO - 样式 MediumPurple 上传成功
2025-08-12 16:28:08,272 - geo_publisher - INFO - 正在上传样式: MediumSpringGreen (MediumSpringGreen.txt)
2025-08-12 16:28:08,339 - geo_publisher - INFO - 样式 MediumSpringGreen 上传成功
2025-08-12 16:28:08,341 - geo_publisher - INFO - 正在上传样式: MediumVioletRed (MediumVioletRed.txt)
2025-08-12 16:28:08,396 - geo_publisher - INFO - 样式 MediumVioletRed 上传成功
2025-08-12 16:28:08,397 - geo_publisher - INFO - 正在上传样式: Navy (Navy.txt)
2025-08-12 16:28:08,420 - geo_publisher - ERROR - 样式 Navy 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,422 - geo_publisher - INFO - 正在上传样式: Olive (Olive.txt)
2025-08-12 16:28:08,447 - geo_publisher - ERROR - 样式 Olive 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,447 - geo_publisher - INFO - 正在上传样式: Orange (Orange.txt)
2025-08-12 16:28:08,474 - geo_publisher - ERROR - 样式 Orange 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,475 - geo_publisher - INFO - 正在上传样式: Orchid (Orchid.txt)
2025-08-12 16:28:08,571 - geo_publisher - INFO - 样式 Orchid 上传成功
2025-08-12 16:28:08,572 - geo_publisher - INFO - 正在上传样式: PaleGoldenrod (PaleGoldenrod.txt)
2025-08-12 16:28:08,661 - geo_publisher - INFO - 样式 PaleGoldenrod 上传成功
2025-08-12 16:28:08,662 - geo_publisher - INFO - 正在上传样式: Pink (Pink.txt)
2025-08-12 16:28:08,702 - geo_publisher - ERROR - 样式 Pink 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,702 - geo_publisher - INFO - 正在上传样式: Purple (Purple.txt)
2025-08-12 16:28:08,736 - geo_publisher - ERROR - 样式 Purple 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,737 - geo_publisher - INFO - 正在上传样式: Red (Red.txt)
2025-08-12 16:28:08,769 - geo_publisher - ERROR - 样式 Red 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:08,770 - geo_publisher - INFO - 正在上传样式: Salmon (Salmon.txt)
2025-08-12 16:28:08,828 - geo_publisher - INFO - 样式 Salmon 上传成功
2025-08-12 16:28:08,829 - geo_publisher - INFO - 正在上传样式: SkyBlue (SkyBlue.txt)
2025-08-12 16:28:08,911 - geo_publisher - INFO - 样式 SkyBlue 上传成功
2025-08-12 16:28:08,911 - geo_publisher - INFO - 正在上传样式: SlateBlue (SlateBlue.txt)
2025-08-12 16:28:08,977 - geo_publisher - INFO - 样式 SlateBlue 上传成功
2025-08-12 16:28:08,978 - geo_publisher - INFO - 正在上传样式: SteelBlue (SteelBlue.txt)
2025-08-12 16:28:09,056 - geo_publisher - INFO - 样式 SteelBlue 上传成功
2025-08-12 16:28:09,056 - geo_publisher - INFO - 正在上传样式: Teal (Teal.txt)
2025-08-12 16:28:09,110 - geo_publisher - ERROR - 样式 Teal 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:09,111 - geo_publisher - INFO - 正在上传样式: Tomato (Tomato.txt)
2025-08-12 16:28:09,140 - geo_publisher - ERROR - 样式 Tomato 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:09,141 - geo_publisher - INFO - 正在上传样式: Violet (Violet.txt)
2025-08-12 16:28:09,197 - geo_publisher - INFO - 样式 Violet 上传成功
2025-08-12 16:28:09,198 - geo_publisher - INFO - 正在上传样式: White (White.txt)
2025-08-12 16:28:09,227 - geo_publisher - ERROR - 样式 White 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:09,227 - geo_publisher - INFO - 正在上传样式: Yellow (Yellow.txt)
2025-08-12 16:28:09,252 - geo_publisher - ERROR - 样式 Yellow 上传失败: 创建样式失败: HTTP 500
2025-08-12 16:28:09,252 - geo_publisher - INFO - 批量样式上传完成: 部分成功：成功上传 19 个，失败 21 个样式文件
2025-08-12 16:42:09,290 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-12 16:42:28,003 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 08:52:42,715 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 08:53:29,144 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:17:38,839 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:27:23,126 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:28:49,039 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:29:17,591 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:29:39,991 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:30:08,005 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:30:29,374 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:36:30,001 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:36:57,970 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:37:17,276 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:37:33,010 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:38:00,892 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:38:46,309 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:39:10,871 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:39:25,695 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:39:45,885 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:40:00,493 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:40:18,463 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:40:33,967 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:40:54,169 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:41:14,621 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:56:09,397 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:56:27,994 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:57:19,112 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:57:36,464 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:57:50,679 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:58:17,845 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:58:34,348 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:58:54,803 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 09:59:09,426 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:39:40,815 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:39:57,206 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:54:38,349 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:55:00,547 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:55:28,303 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:55:48,737 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:56:15,856 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:56:57,249 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:57:39,648 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:58:05,322 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 10:58:49,131 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:25:51,691 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:38:02,798 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:38:39,194 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:39:06,108 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:39:26,146 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:39:43,916 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:40:11,541 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:40:33,863 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:41:16,327 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:41:35,362 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 11:41:54,501 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:17:59,120 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:18:19,711 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:18:40,852 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:19:02,552 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:19:24,887 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:20:08,842 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:33:47,175 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:34:05,047 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:34:21,642 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-13 15:35:55,159 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-14 09:54:03,106 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-14 09:54:21,709 - geo_publisher - INFO - 加载了 52 个任务状态
2025-08-14 10:48:14,046 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif -> testodm
2025-08-14 10:48:14,209 - geo_publisher - INFO - 启动GeoTIFF发布任务 90bdd0db-8811-4b2d-8231-81566d80d540: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-14 10:48:14,210 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: 90bdd0db-8811-4b2d-8231-81566d80d540
2025-08-14 10:48:14,255 - geo_publisher - INFO - 查询发布任务状态: 90bdd0db-8811-4b2d-8231-81566d80d540
2025-08-14 10:49:14,008 - geo_publisher - INFO - 查询发布任务状态: 90bdd0db-8811-4b2d-8231-81566d80d540
2025-08-14 11:15:31,401 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif -> testodm
2025-08-14 11:15:31,713 - geo_publisher - INFO - 启动GeoTIFF发布任务 cf0942dd-85a8-491c-b71b-54a624ddf7e5: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-08-14 11:15:31,717 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: cf0942dd-85a8-491c-b71b-54a624ddf7e5
2025-08-14 11:15:32,055 - geo_publisher - INFO - 查询发布任务状态: cf0942dd-85a8-491c-b71b-54a624ddf7e5
2025-08-14 11:16:32,196 - geo_publisher - INFO - 查询发布任务状态: cf0942dd-85a8-491c-b71b-54a624ddf7e5
2025-08-14 16:31:44,598 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif -> testodm
2025-08-14 16:31:44,605 - geo_publisher - INFO - 启动GeoTIFF发布任务 0f1b5078-cfab-4e06-9ec5-0e31506d25ed: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-14 16:31:44,607 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: 0f1b5078-cfab-4e06-9ec5-0e31506d25ed
2025-08-14 16:31:44,661 - geo_publisher - INFO - 查询发布任务状态: 0f1b5078-cfab-4e06-9ec5-0e31506d25ed
2025-08-14 16:32:47,547 - geo_publisher - INFO - 查询发布任务状态: 0f1b5078-cfab-4e06-9ec5-0e31506d25ed
2025-08-14 16:54:08,002 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif -> testodm
2025-08-14 16:54:08,006 - geo_publisher - INFO - 启动GeoTIFF发布任务 63294102-23da-4e90-86f7-4442b49b70bf: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-14 16:54:08,007 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: 63294102-23da-4e90-86f7-4442b49b70bf
2025-08-14 16:54:08,047 - geo_publisher - INFO - 查询发布任务状态: 63294102-23da-4e90-86f7-4442b49b70bf
2025-08-14 16:55:08,437 - geo_publisher - INFO - 查询发布任务状态: 63294102-23da-4e90-86f7-4442b49b70bf
2025-08-14 17:03:27,148 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:05:26,860 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:05:46,674 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:09:11,767 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:17:08,754 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:17:52,771 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:18:28,255 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:22:48,756 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:23:13,045 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:26:54,491 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:31:40,292 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:32:11,231 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:35:53,443 - geo_publisher - INFO - 加载了 56 个任务状态
2025-08-14 17:39:19,592 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif -> testodm
2025-08-14 17:39:19,716 - geo_publisher - INFO - 启动GeoTIFF发布任务 c33eb809-9115-48d3-a0a4-5c8f31db614e: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599.tif, workspace=testodm
2025-08-14 17:39:19,718 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: c33eb809-9115-48d3-a0a4-5c8f31db614e
2025-08-14 17:39:20,219 - geo_publisher - INFO - 查询发布任务状态: c33eb809-9115-48d3-a0a4-5c8f31db614e
2025-08-14 17:40:21,918 - geo_publisher - INFO - 查询发布任务状态: c33eb809-9115-48d3-a0a4-5c8f31db614e
2025-08-14 17:54:12,165 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 17:55:59,944 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 17:56:21,391 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 17:56:42,464 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 17:57:09,895 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 17:57:27,518 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 17:57:57,191 - geo_publisher - INFO - 加载了 57 个任务状态
2025-08-14 18:36:35,971 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif -> testodm
2025-08-14 18:36:36,324 - geo_publisher - INFO - 启动GeoTIFF发布任务 c1081560-6b00-44bc-a4a9-b2b5daf82d8b: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif, workspace=testodm
2025-08-14 18:36:36,351 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: c1081560-6b00-44bc-a4a9-b2b5daf82d8b
2025-08-14 18:36:41,618 - geo_publisher - INFO - 查询发布任务状态: c1081560-6b00-44bc-a4a9-b2b5daf82d8b
2025-08-14 18:37:41,701 - geo_publisher - INFO - 查询发布任务状态: c1081560-6b00-44bc-a4a9-b2b5daf82d8b
2025-08-14 18:52:35,002 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif -> testodm
2025-08-14 18:52:35,008 - geo_publisher - INFO - 启动GeoTIFF发布任务 a6ee4684-c10c-4fb8-89aa-430af5cef942: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601.tif, workspace=testodm
2025-08-14 18:52:35,008 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: a6ee4684-c10c-4fb8-89aa-430af5cef942
2025-08-14 18:52:35,048 - geo_publisher - INFO - 查询发布任务状态: a6ee4684-c10c-4fb8-89aa-430af5cef942
2025-08-14 18:53:35,099 - geo_publisher - INFO - 查询发布任务状态: a6ee4684-c10c-4fb8-89aa-430af5cef942
2025-08-14 19:08:30,740 - geo_publisher - INFO - 开始异步发布GeoTIFF: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif -> testodm
2025-08-14 19:08:30,777 - geo_publisher - INFO - 启动GeoTIFF发布任务 ba6127d0-464d-43cb-9024-29f225965e68: D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602.tif, workspace=testodm
2025-08-14 19:08:30,779 - geo_publisher - INFO - GeoTIFF发布任务已启动，任务ID: ba6127d0-464d-43cb-9024-29f225965e68
2025-08-14 19:08:30,835 - geo_publisher - INFO - 查询发布任务状态: ba6127d0-464d-43cb-9024-29f225965e68
2025-08-14 19:09:30,873 - geo_publisher - INFO - 查询发布任务状态: ba6127d0-464d-43cb-9024-29f225965e68
2025-08-15 08:35:31,907 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:35:47,047 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:42:27,058 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:42:38,819 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:43:08,150 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:43:44,103 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:47:29,297 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:47:43,110 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:48:33,846 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:48:48,700 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:57:32,921 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:59:05,012 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:59:30,985 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 08:59:43,619 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 09:01:13,891 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 09:01:26,772 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 09:01:51,522 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 09:03:13,101 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:04:31,449 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:04:55,644 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:06:17,806 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:07:03,734 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:07:22,178 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:07:39,040 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:08:31,035 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:15:59,294 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:16:46,271 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:22:37,448 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 11:22:49,584 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 14:56:04,309 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 14:56:21,732 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 14:56:38,287 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 14:57:02,310 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 14:58:02,890 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:10:32,717 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:11:20,969 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:12:40,575 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:13:02,098 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:13:42,273 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:14:25,349 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 15:14:50,139 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 17:39:53,276 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 17:41:07,714 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-15 17:41:45,592 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 08:37:56,732 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 08:38:58,536 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 08:43:51,934 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 08:44:39,754 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 09:29:44,631 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 09:34:26,105 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 09:34:45,463 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 10:57:57,560 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:03:43,555 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:05:07,213 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:20:49,650 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:21:07,814 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:21:49,970 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:22:18,588 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:23:57,090 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:24:27,506 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:25:01,200 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:25:20,057 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:25:53,976 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:26:13,128 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:26:33,229 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 11:27:17,510 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:00:36,301 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:01:15,805 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:01:38,475 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:02:43,901 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:03:16,365 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:06:28,716 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:06:49,961 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:07:28,456 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:08:07,020 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 15:49:57,318 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 16:55:51,136 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 16:56:21,429 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 16:56:39,898 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:05:55,891 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:06:27,030 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:07:11,687 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:07:45,879 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:08:29,631 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:10:15,078 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:14:30,201 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:19:22,028 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:20:10,218 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:20:43,416 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:26:05,451 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:26:48,465 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:27:53,092 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:32:07,351 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:32:35,858 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:32:55,856 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:33:16,411 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:34:10,244 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:34:31,404 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:34:52,518 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:35:29,827 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:36:09,708 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:53:03,448 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:53:25,303 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:53:46,441 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:54:07,543 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:54:53,299 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:55:56,305 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-18 17:56:49,210 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 08:57:50,638 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 08:58:10,174 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:02:21,938 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:02:50,563 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:03:12,009 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:03:36,495 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:03:53,348 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:04:22,257 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:04:38,882 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:05:02,295 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:05:31,098 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:06:35,788 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:10:49,548 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:11:08,123 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:11:27,909 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:11:51,938 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:12:15,693 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:12:34,736 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:12:57,410 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:13:22,793 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:14:13,659 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:14:48,553 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:15:21,060 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:15:55,412 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:16:13,867 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:17:01,671 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:20:17,801 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:20:56,628 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:21:15,268 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:21:53,889 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:32:36,347 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:34:07,668 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:38:56,784 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:39:25,726 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:39:47,502 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:40:15,567 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:40:40,863 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:41:07,082 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:41:30,335 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:41:48,796 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:42:44,878 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:43:01,081 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:43:37,883 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:43:56,064 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:44:15,155 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:44:54,162 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:45:31,144 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:45:53,450 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:46:10,192 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 09:46:29,918 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:41:36,754 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:42:28,779 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:42:47,776 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:43:06,844 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:57:58,666 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:58:21,680 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:58:44,451 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:59:05,822 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:59:30,269 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 10:59:53,148 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:26:33,883 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:29:15,121 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:30:00,666 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:30:31,924 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:38:55,636 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:39:21,264 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:40:31,409 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:42:07,747 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:43:47,482 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:50:28,636 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:50:51,681 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:51:52,962 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:52:36,270 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:52:55,981 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:56:58,515 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 15:57:58,770 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 16:02:25,222 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 16:16:46,150 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 17:04:38,321 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 17:05:23,692 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 17:27:42,063 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 17:28:16,990 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 17:28:35,390 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-19 17:29:06,999 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:43:38,694 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:43:52,698 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:45:38,500 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:46:38,112 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:46:56,072 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:47:16,613 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:47:33,200 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:47:57,437 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:48:50,339 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:49:28,751 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:49:42,250 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:49:55,768 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:59:18,457 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 08:59:34,906 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:00:07,779 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:02:11,016 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:02:26,673 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:02:45,762 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:03:00,901 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:03:30,202 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:03:54,627 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:04:12,815 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:06:28,432 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:06:48,564 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:07:02,911 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:07:21,111 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:12:58,269 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:13:37,034 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:13:53,747 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:14:17,528 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:14:32,821 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:14:45,265 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:15:10,895 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:15:26,555 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:15:46,356 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:16:00,331 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:16:17,983 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:16:30,185 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:16:42,989 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:29:18,192 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:29:30,328 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:29:45,603 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:30:17,823 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:30:51,431 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:31:14,123 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:31:44,586 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:32:06,848 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:32:20,322 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:33:04,190 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:33:39,560 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:36:02,157 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:39:18,249 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:41:43,373 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:44:39,706 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:45:16,622 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:45:31,229 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:46:01,481 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:47:34,813 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:48:07,601 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:48:37,644 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:48:50,585 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:49:11,148 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:02:14,871 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:02:32,761 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:05:20,873 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:05:40,007 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:05:55,549 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:06:27,572 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:07:02,237 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:07:15,310 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:07:28,984 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:07:59,928 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:12:46,079 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:13:17,255 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:13:50,721 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:18:38,365 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:19:00,613 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:19:21,856 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:19:51,285 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:20:20,499 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:20:44,970 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:20:59,037 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:24:55,642 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:25:08,028 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:26:38,542 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:27:01,595 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:27:45,120 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:31:31,131 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:32:05,548 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:33:51,130 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:34:04,358 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:35:45,800 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:37:12,921 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:38:12,468 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:38:23,650 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:44:36,917 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 10:45:29,483 - geo_publisher - INFO - 加载了 60 个任务状态
