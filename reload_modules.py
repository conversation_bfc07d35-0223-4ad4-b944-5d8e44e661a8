#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
强制重新加载Django模块
"""

import os
import sys
import importlib

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_api.settings')

import django
django.setup()

def reload_combined_analysis_modules():
    """重新加载合并分析相关模块"""
    print("🔄 开始重新加载合并分析模块...")
    
    modules_to_reload = [
        'geoserver_api.core.combined_analysis_executor',
        'geoserver_api.views.analysis.combined_analysis_views',
    ]
    
    for module_name in modules_to_reload:
        try:
            if module_name in sys.modules:
                print(f"🔄 重新加载模块: {module_name}")
                importlib.reload(sys.modules[module_name])
            else:
                print(f"📦 首次导入模块: {module_name}")
                importlib.import_module(module_name)
            print(f"✅ 模块加载成功: {module_name}")
        except Exception as e:
            print(f"❌ 模块加载失败: {module_name} - {e}")
    
    print("✅ 模块重新加载完成")

def test_module_loading():
    """测试模块是否正确加载"""
    print("\n🧪 测试模块加载...")
    
    try:
        from geoserver_api.core.combined_analysis_executor import combined_analysis_executor
        print("✅ combined_analysis_executor 导入成功")
        
        # 测试是否有我们添加的方法
        if hasattr(combined_analysis_executor, '_create_combined_task_info_template'):
            print("✅ _create_combined_task_info_template 方法存在")
        else:
            print("❌ _create_combined_task_info_template 方法不存在")
        
        if hasattr(combined_analysis_executor, '_update_combined_task_info'):
            print("✅ _update_combined_task_info 方法存在")
        else:
            print("❌ _update_combined_task_info 方法不存在")
            
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")

if __name__ == "__main__":
    print("🚀 开始强制重新加载Django模块")
    
    reload_combined_analysis_modules()
    test_module_loading()
    
    print("\n📖 说明:")
    print("1. 如果模块重新加载成功，Django服务器应该会使用新的代码")
    print("2. 如果仍然有问题，请重启Django服务器")
    print("3. 确保Django服务器在开发模式下运行（DEBUG=True）")
