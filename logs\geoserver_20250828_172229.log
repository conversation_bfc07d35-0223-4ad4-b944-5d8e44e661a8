2025-08-28 17:22:29,120 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_172229.log
2025-08-28 17:22:29,123 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:22:29,124 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:22:29,143 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:22:29,146 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:22:29,146 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:22:29,158 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:22:29,159 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:22:29,160 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:22:29,169 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:22:29,171 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:22:29,172 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:22:29,183 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:22:29,186 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:22:29,186 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:22:29,197 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:22:29,199 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:22:29,200 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:22:29,210 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:22:29,230 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:22:29,241 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:22:29,435 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:22:29,452 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:22:29,457 - analysis_executor - INFO - 加载了 28 个任务状态
