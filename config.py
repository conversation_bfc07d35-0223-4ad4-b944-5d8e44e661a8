"""
Author: 吴博文 <EMAIL>
Date: 2025-07-01 10:15:27
LastEditors: 吴博文 <EMAIL>
LastEditTime: 2025-07-01 10:18:05
FilePath: \geoserverapi\config.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
"""

import os
import sys
import socket

# 尝试导入dotenv，如果不存在则跳过
try:
    from dotenv import load_dotenv

    # 从.env文件加载环境变量（如果存在）
    load_dotenv()
    print("成功加载.env文件")
except ImportError:
    print("警告：未找到python-dotenv模块，将使用默认配置")


# 定义一个函数来替代os.getenv，以便在没有环境变量时提供默认值
def get_env(key, default=None):
    # 尝试从.env文件中读取（如果文件存在且未使用dotenv模块）
    env_value = os.getenv(key)
    if env_value is None:
        env_path = os.path.join(os.path.dirname(__file__), ".env")
        if os.path.exists(env_path):
            try:
                with open(env_path, "r") as env_file:
                    for line in env_file:
                        line = line.strip()
                        # 跳过注释行和空行
                        if line.startswith("#") or not line:
                            continue
                        if "=" in line:
                            env_key, env_value = line.split("=", 1)
                            if env_key == key:
                                return env_value
            except Exception:
                pass
        return default
    return env_value


def get_django_port():
    """
    动态获取Django运行的端口号

    优先级:
    1. 环境变量 DJANGO_PORT
    2. 命令行参数中的端口号
    3. Django settings中的配置
    4. 默认端口 8091

    Returns:
        int: Django运行的端口号
    """
    # 1. 检查环境变量
    env_port = get_env("DJANGO_PORT")
    if env_port:
        try:
            return int(env_port)
        except ValueError:
            pass

    # 2. 检查命令行参数
    try:
        for i, arg in enumerate(sys.argv):
            if arg == "runserver" and i + 1 < len(sys.argv):
                # 格式: python manage.py runserver 0.0.0.0:8091
                server_arg = sys.argv[i + 1]
                if ":" in server_arg:
                    port_str = server_arg.split(":")[-1]
                    try:
                        return int(port_str)
                    except ValueError:
                        pass
                else:
                    # 只有端口号: python manage.py runserver 8091
                    try:
                        return int(server_arg)
                    except ValueError:
                        pass
    except:
        pass

    # 3. 尝试从Django settings获取
    try:
        # 只有在Django环境中才能导入settings
        if 'django' in sys.modules or 'DJANGO_SETTINGS_MODULE' in os.environ:
            from django.conf import settings
            if hasattr(settings, 'DJANGO_PORT'):
                return int(settings.DJANGO_PORT)
    except:
        pass

    # 4. 返回默认端口
    return 8085


def get_django_host():
    """
    获取Django运行的主机地址

    Returns:
        str: Django运行的主机地址
    """
    # 检查环境变量
    env_host = get_env("DJANGO_HOST", "127.0.0.1")

    # 检查命令行参数
    try:
        for i, arg in enumerate(sys.argv):
            if arg == "runserver" and i + 1 < len(sys.argv):
                server_arg = sys.argv[i + 1]
                if ":" in server_arg:
                    host = server_arg.split(":")[0]
                    if host and host != "0.0.0.0":
                        return host
                    # 如果是0.0.0.0，返回localhost用于内部调用
                    return "127.0.0.1"
    except:
        pass

    return env_host


def get_django_base_url():
    """
    获取Django API的基础URL

    Returns:
        str: Django API的基础URL，格式: http://host:port
    """
    host = get_django_host()
    port = get_django_port()
    return f"http://{host}:{port}"


# Django API配置
DJANGO_HOST = get_django_host()
DJANGO_PORT = get_django_port()
DJANGO_BASE_URL = get_django_base_url()

# GeoServer连接设置
GEOSERVER_URL = get_env("GEOSERVER_URL", "http://localhost:8083/geoserver")
GEOSERVER_USER = get_env("GEOSERVER_USER", "admin")
GEOSERVER_PASSWORD = get_env("GEOSERVER_PASSWORD", "geoserver")

# 默认工作区和数据存储设置
DEFAULT_WORKSPACE = get_env("DEFAULT_WORKSPACE", "default_workspace")
DEFAULT_DATASTORE = get_env("DEFAULT_DATASTORE", "default_datastore")

# 日志设置
LOG_LEVEL = get_env("LOG_LEVEL", "INFO")
LOG_FILE = get_env("LOG_FILE", "geoserver_api.log")

# ODM相关设置
ODM_BASE_URL = get_env("ODM_BASE_URL", "http://127.0.0.1:81")
ODM_TIMEOUT = int(get_env("ODM_TIMEOUT", "10"))  # 请求超时时间(秒)
ODM_DEFAULT_WINDOW_DATA_PATH = get_env("ODM_DEFAULT_WINDOW_DATA_PATH", "D:/Drone_Project/nginxData")
ODM_DEFAULT_AIRFLOW_DATA_PATH = get_env("ODM_DEFAULT_AIRFLOW_DATA_PATH", "/opt/airflow/data")
ODM_DEFAULT_WORKSPACE = get_env("ODM_DEFAULT_WORKSPACE", "testodm")
ODM_GEOSERVER_HOST = get_env("ODM_GEOSERVER_HOST", "host.docker.internal")
ODM_GEOSERVER_HD_PORT = get_env("ODM_GEOSERVER_HD_PORT", "5083")
ODM_BATCH_PATH = get_env("ODM_BATCH_PATH", "D:/Drone_Project/ODM/ODM/run.bat")
