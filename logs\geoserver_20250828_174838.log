2025-08-28 17:48:38,724 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_174838.log
2025-08-28 17:48:38,726 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:38,727 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:38,748 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:38,753 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:38,753 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:38,764 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:38,766 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:38,766 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:38,780 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:38,782 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:38,783 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:38,793 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:38,795 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:38,796 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:38,808 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:38,810 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:38,810 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:38,820 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:38,837 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:48:38,842 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:48:43,840 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:48:43,906 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:48:43,924 - analysis_executor - INFO - 加载了 28 个任务状态
