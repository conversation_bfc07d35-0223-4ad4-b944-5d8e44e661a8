2025-08-28 17:17:57,526 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_171757.log
2025-08-28 17:17:57,528 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:57,529 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:57,550 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:57,553 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:57,554 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:57,567 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:57,569 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:57,570 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:57,584 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:57,586 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:57,586 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:57,597 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:57,599 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:57,599 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:57,612 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:57,615 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:17:57,615 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:17:57,625 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:17:57,644 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:17:57,650 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:17:57,814 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:17:57,830 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:17:57,833 - analysis_executor - INFO - 加载了 28 个任务状态
