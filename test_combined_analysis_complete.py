#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试合并分析的完整TaskInfo.json更新
"""

import requests
import time
import json
import os

def test_combined_analysis_complete():
    """测试合并分析的完整TaskInfo.json更新"""
    print("🧪 测试合并分析的完整TaskInfo.json更新...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    image_id = "20250705171601"
    image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    model_path = "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
    old_data_path = "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
    
    # 预期的TaskInfo.json路径
    expected_taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  影像ID: {image_id}")
        print(f"  TaskInfo路径: {expected_taskinfo_path}")
        
        # 记录启动前状态
        before_count = 0
        if os.path.exists(expected_taskinfo_path):
            try:
                with open(expected_taskinfo_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                if isinstance(data, list):
                    before_count = len(data)
                print(f"📋 启动前: 包含 {before_count} 个任务")
            except:
                print(f"📋 启动前: TaskInfo.json格式错误")
        else:
            print(f"📋 启动前: TaskInfo.json不存在")
        
        # 启动合并分析任务
        print(f"\n🚀 启动合并分析任务...")
        
        params = {
            'id': image_id,
            'image': image_path,
            'model': model_path,
            'old_data_path': old_data_path,
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus'
        }
        
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功，ID: {task_id}")
            
            # 监控TaskInfo.json的完整更新过程
            success = monitor_complete_taskinfo_updates(base_url, task_id, expected_taskinfo_path)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_complete_taskinfo_updates(base_url, task_id, task_info_path, max_wait_time=1200):
    """监控TaskInfo.json的完整更新过程"""
    print(f"\n📊 监控TaskInfo.json完整更新过程...")
    
    start_time = time.time()
    last_status = None
    last_progress = -1
    update_history = []
    
    while time.time() - start_time < max_wait_time:
        try:
            # 1. 通过API查询任务状态
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            api_status = None
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                api_status = {
                    'task_status': status_data['task_status'],
                    'message': status_data.get('message', ''),
                    'progress': status_data.get('progress', 0)
                }
            
            # 2. 检查TaskInfo.json中的对应任务
            taskinfo_status = None
            if os.path.exists(task_info_path):
                try:
                    with open(task_info_path, 'r', encoding='utf-8') as f:
                        taskinfo_data = json.load(f)
                    
                    if isinstance(taskinfo_data, list):
                        # 查找对应的任务
                        for task in taskinfo_data:
                            if task.get('task_id') == task_id:
                                taskinfo_status = {
                                    'status': task.get('status', 'Unknown'),
                                    'message': task.get('message', ''),
                                    'progress': task.get('progress', 0),
                                    'results': task.get('results', {}),
                                    'parameters': task.get('parameters', {})
                                }
                                break
                        
                        # 检查是否有更新
                        if taskinfo_status and taskinfo_status != last_status:
                            print(f"\n🔄 TaskInfo更新:")
                            print(f"  状态: {taskinfo_status['status']}")
                            print(f"  消息: {taskinfo_status['message']}")
                            print(f"  进度: {taskinfo_status['progress']}%")
                            
                            # 检查parameters中的model_name
                            parameters = taskinfo_status.get('parameters', {})
                            if 'model_name' in parameters:
                                print(f"  模型名称: {parameters['model_name']}")
                            else:
                                print(f"  ⚠️ 缺少模型名称")
                            
                            # 检查results
                            results = taskinfo_status.get('results', {})
                            if results:
                                print(f"  AI处理时间: {results.get('ai_processing_time', 'N/A')}")
                                print(f"  成功状态: {results.get('success', 'N/A')}")
                                
                                # 检查空间统计
                                spatial_stats = results.get('spatial_statistics', {})
                                if spatial_stats:
                                    print(f"  空间统计:")
                                    for key, value in spatial_stats.items():
                                        if value is not None:
                                            print(f"    {key}: {value}")
                                else:
                                    print(f"  空间统计: 暂无数据")
                            
                            # 记录更新历史
                            update_history.append({
                                'time': time.time() - start_time,
                                'status': taskinfo_status['status'],
                                'progress': taskinfo_status['progress'],
                                'has_spatial_stats': bool(results.get('spatial_statistics', {}))
                            })
                            
                            last_status = taskinfo_status.copy()
                
                except Exception as e:
                    print(f"❌ 读取TaskInfo.json失败: {e}")
            
            # 3. 检查任务是否完成
            if api_status and api_status['task_status'] in ['完成', '失败']:
                print(f"\n✅ 任务结束，最终状态: {api_status['task_status']}")
                
                # 显示更新历史
                print(f"\n📈 TaskInfo.json更新历史:")
                for i, update in enumerate(update_history):
                    print(f"  {i+1}. {update['time']:.1f}s - {update['status']} ({update['progress']}%) - 空间统计: {'有' if update['has_spatial_stats'] else '无'}")
                
                # 验证最终结果
                final_validation = validate_final_taskinfo(task_info_path, task_id)
                return api_status['task_status'] == '完成' and final_validation
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(10)
            continue
    
    print(f"\n⏰ 任务监控超时")
    return False

def validate_final_taskinfo(task_info_path, task_id):
    """验证最终的TaskInfo.json内容"""
    print(f"\n🔍 验证最终TaskInfo.json内容...")
    
    try:
        if not os.path.exists(task_info_path):
            print(f"❌ TaskInfo.json文件不存在")
            return False
        
        with open(task_info_path, 'r', encoding='utf-8') as f:
            taskinfo_data = json.load(f)
        
        if not isinstance(taskinfo_data, list):
            print(f"❌ TaskInfo.json格式不正确")
            return False
        
        # 查找目标任务
        target_task = None
        for task in taskinfo_data:
            if task.get('task_id') == task_id:
                target_task = task
                break
        
        if not target_task:
            print(f"❌ 未找到目标任务")
            return False
        
        print(f"📋 最终任务信息验证:")
        
        # 验证基本信息
        print(f"  任务ID: {target_task.get('task_id', 'N/A')}")
        print(f"  状态: {target_task.get('status', 'N/A')}")
        print(f"  进度: {target_task.get('progress', 'N/A')}%")
        
        # 验证parameters中的model_name
        parameters = target_task.get('parameters', {})
        model_name = parameters.get('model_name', 'N/A')
        print(f"  模型名称: {model_name}")
        
        if model_name == 'N/A':
            print(f"  ❌ 缺少模型名称")
            return False
        else:
            print(f"  ✅ 模型名称正确")
        
        # 验证results
        results = target_task.get('results', {})
        if not results:
            print(f"  ❌ 缺少results字段")
            return False
        
        ai_processing_time = results.get('ai_processing_time')
        success = results.get('success')
        
        print(f"  AI处理时间: {ai_processing_time}")
        print(f"  成功状态: {success}")
        
        # 验证空间统计
        spatial_stats = results.get('spatial_statistics', {})
        if not spatial_stats:
            print(f"  ❌ 缺少空间统计信息")
            return False
        
        required_stats = ['outflow_count', 'inflow_count', 'total_count', 'outflow_area', 'inflow_area']
        missing_stats = []
        
        for stat in required_stats:
            value = spatial_stats.get(stat)
            if value is None:
                missing_stats.append(stat)
            else:
                print(f"  {stat}: {value}")
        
        if missing_stats:
            print(f"  ❌ 缺少空间统计字段: {missing_stats}")
            return False
        else:
            print(f"  ✅ 空间统计信息完整")
        
        print(f"🎉 TaskInfo.json验证通过！")
        return True
        
    except Exception as e:
        print(f"❌ 验证TaskInfo.json失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试合并分析的完整TaskInfo.json更新")
    
    success = test_combined_analysis_complete()
    
    if success:
        print(f"\n🎉 合并分析TaskInfo.json完整更新测试通过！")
    else:
        print(f"\n❌ 合并分析TaskInfo.json完整更新测试失败")
    
    print(f"\n📖 功能说明:")
    print(f"1. TaskInfo.json在任务开始时立即创建")
    print(f"2. parameters中包含model_name字段")
    print(f"3. AI处理完成后更新ai_processing_time和success")
    print(f"4. 空间变化分析完成后更新spatial_statistics")
    print(f"5. 任务完成时状态更新为'完成'")
    print(f"6. 全程动态更新，确保信息完整性")
