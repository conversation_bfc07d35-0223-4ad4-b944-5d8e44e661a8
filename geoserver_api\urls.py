#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer API URL配置
"""

from django.urls import path, include
from .views.base_views import health_check, query_coordinate, get_layer_intersections, query_intersecting_layers

# 基础API路由
urlpatterns = [
    # 健康检查
    path('health/', health_check, name='health_check'),

    # 坐标查询API
    path('api/query/', query_coordinate, name='query_coordinate'),

    # 图层相交查询API
    path('api/layer_intersections/', get_layer_intersections, name='get_layer_intersections'),

    # 坐标点与图层相交查询API
    path('api/query_intersecting_layers/', query_intersecting_layers, name='query_intersecting_layers'),

    # 管理API路由
    path('api/management/', include('geoserver_api.management_urls')),

    # 批处理API路由
    path('api/batch/', include('geoserver_api.batch_urls')),

    # TIF处理API路由
    path('api/tif/', include('geoserver_api.tif_urls')),

    # GeoServer发布API路由
    path('api/geo/', include('geoserver_api.geo_urls')),

    # 地图API路由
    path('api/map/', include('geoserver_api.map_urls')),

    # 地理空间分析API路由
    path('api/analysis/', include('geoserver_api.analysis_urls')),

    # Other模块API路由
    path('api/other/', include('geoserver_api.views.other.other_urls')),
]
