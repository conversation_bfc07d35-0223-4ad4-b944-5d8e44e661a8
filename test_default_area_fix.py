#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试默认面积配置修复
"""

import requests
import json

def test_default_area_fix():
    """测试默认面积配置修复"""
    print("🧪 测试默认面积配置修复...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        # 1. 测试 weight-info 接口
        print("\n1️⃣ 测试 /api/analysis/weight-info/ 接口...")
        
        response = requests.get(f"{base_url}/weight-info/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            print(f"📋 状态: {result['status']}")
            
            if result['status'] == 'success' and 'data' in result:
                data = result['data']
                print(f"\n📊 默认面积验证:")
                
                # 期望的默认面积
                expected_areas = {
                    'arableLand': 400.0,
                    'constructionLand': 200.0
                }
                
                all_correct = True
                
                for land_type, expected_area in expected_areas.items():
                    if land_type in data:
                        actual_area = data[land_type].get('default_area')
                        
                        if actual_area is not None:
                            if abs(float(actual_area) - expected_area) < 0.01:
                                print(f"  ✅ {land_type}: {actual_area} (正确)")
                            else:
                                print(f"  ❌ {land_type}: {actual_area} (期望: {expected_area})")
                                all_correct = False
                        else:
                            print(f"  ❌ {land_type}: 缺少default_area字段")
                            all_correct = False
                    else:
                        print(f"  ❌ 缺少地物类型: {land_type}")
                        all_correct = False
                
                if all_correct:
                    print(f"\n🎉 默认面积配置修复成功！")
                else:
                    print(f"\n❌ 默认面积配置仍有问题")
                
                # 显示完整的返回数据
                print(f"\n📋 完整返回数据:")
                for land_type, info in data.items():
                    print(f"  {land_type}:")
                    print(f"    display_name: {info.get('display_name', 'N/A')}")
                    print(f"    default_area: {info.get('default_area', 'N/A')}")
                    print(f"    default: {info.get('default', 'N/A')}")
                    print(f"    models: {len(info.get('models', {}))} 种类型")
                    print(f"    shp_files: {len(info.get('shp_files', []))} 个文件")
                
                return all_correct
                
            else:
                print(f"❌ 接口返回数据格式异常")
                return False
                
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_weight_config_fix():
    """测试权重配置接口修复"""
    print(f"\n2️⃣ 测试 /api/analysis/weight-config/ 接口...")
    
    try:
        base_url = "http://127.0.0.1:8091/api/analysis"
        response = requests.get(f"{base_url}/weight-config/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            
            if result['status'] == 'success' and 'data' in result:
                data = result['data']
                
                # 检查默认面积配置
                default_areas = data.get('default_areas', {})
                print(f"\n📊 默认面积配置验证:")
                
                if default_areas:
                    expected_areas = {
                        'arableLand': 400.0,
                        'constructionLand': 200.0
                    }
                    
                    config_correct = True
                    
                    for land_type, expected_area in expected_areas.items():
                        actual_area = default_areas.get(land_type)
                        
                        if actual_area is not None:
                            if abs(float(actual_area) - expected_area) < 0.01:
                                print(f"  ✅ {land_type}: {actual_area}")
                            else:
                                print(f"  ❌ {land_type}: {actual_area} (期望: {expected_area})")
                                config_correct = False
                        else:
                            print(f"  ❌ 缺少配置: {land_type}")
                            config_correct = False
                    
                    if config_correct:
                        print(f"  🎉 权重配置接口修复成功！")
                    else:
                        print(f"  ❌ 权重配置接口仍有问题")
                    
                    return config_correct
                else:
                    print(f"  ❌ 缺少default_areas配置")
                    return False
                    
            else:
                print(f"❌ 配置接口返回数据格式异常")
                return False
                
        else:
            print(f"❌ 配置接口调用失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试权重配置接口异常: {e}")
        return False

def compare_before_after():
    """对比修复前后的效果"""
    print(f"\n3️⃣ 修复前后对比:")
    
    print(f"\n修复前的问题:")
    print(f"  ❌ constructionLand的default_area返回400.0 (错误)")
    print(f"  ❌ 原因: configparser将键名转换为小写")
    print(f"  ❌ 'constructionLand' -> 'constructionland'")
    print(f"  ❌ 代码查找'constructionLand'时找不到，使用默认值400.0")
    
    print(f"\n修复方案:")
    print(f"  ✅ 添加键名映射处理configparser的小写转换")
    print(f"  ✅ 'arableland' -> 'arableLand'")
    print(f"  ✅ 'constructionland' -> 'constructionLand'")
    print(f"  ✅ 正确读取配置文件中的数值")
    
    print(f"\n修复后的期望结果:")
    print(f"  ✅ arableLand的default_area: 400.0")
    print(f"  ✅ constructionLand的default_area: 200.0")

def test_edge_cases():
    """测试边界情况"""
    print(f"\n4️⃣ 测试边界情况:")
    
    print(f"\n测试场景:")
    print(f"1. 配置文件中有正确的[Default_area]节")
    print(f"2. arableLand = 400, constructionLand = 200")
    print(f"3. configparser自动转换键名为小写")
    print(f"4. 代码正确处理键名映射")
    print(f"5. 数值转换正常")
    
    print(f"\n可能的其他问题:")
    print(f"- 如果配置文件中有其他地物类型，会使用原始键名")
    print(f"- 如果数值转换失败，会使用默认值400.0")
    print(f"- 如果整个[Default_area]节缺失，会使用硬编码默认值")

def main():
    """主函数"""
    print("🚀 开始测试默认面积配置修复")
    
    # 对比修复前后
    compare_before_after()
    
    # 测试权重信息接口
    weight_info_ok = test_default_area_fix()
    
    # 测试权重配置接口
    weight_config_ok = test_weight_config_fix()
    
    # 测试边界情况
    test_edge_cases()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"  权重信息接口: {'✅ 通过' if weight_info_ok else '❌ 失败'}")
    print(f"  权重配置接口: {'✅ 通过' if weight_config_ok else '❌ 失败'}")
    
    if weight_info_ok and weight_config_ok:
        print(f"\n🎉 默认面积配置修复成功！")
        print(f"\n📝 修复总结:")
        print(f"1. ✅ 识别了configparser的键名小写转换问题")
        print(f"2. ✅ 添加了键名映射机制")
        print(f"3. ✅ 正确处理了arableLand和constructionLand的配置")
        print(f"4. ✅ 保持了向后兼容性")
        print(f"5. ✅ 两个接口都返回正确的默认面积值")
    else:
        print(f"\n❌ 默认面积配置修复失败")
        print(f"请检查:")
        print(f"1. 服务器是否重启以加载新代码")
        print(f"2. 配置文件格式是否正确")
        print(f"3. 日志中是否有错误信息")

if __name__ == "__main__":
    main()
