2025-08-25 17:20:12,039 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250825_172012.log
2025-08-25 17:20:12,040 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-25 17:20:12,041 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-25 17:20:12,058 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-25 17:20:12,061 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-25 17:20:12,062 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-25 17:20:12,071 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-25 17:20:12,073 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-25 17:20:12,073 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-25 17:20:12,083 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-25 17:20:12,084 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-25 17:20:12,085 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-25 17:20:12,094 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-25 17:20:12,096 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-25 17:20:12,096 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-25 17:20:12,106 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-25 17:20:12,110 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-25 17:20:12,111 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-25 17:20:12,121 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-25 17:20:12,136 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-25 17:20:12,152 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-25 17:20:16,609 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-25 17:20:16,624 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-25 17:20:16,627 - analysis_executor - INFO - 加载了 28 个任务状态
