#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试启动脚本 - 捕获所有启动过程中的错误和环境信息
"""

import os
import sys
import traceback
import platform
import subprocess
from datetime import datetime
from pathlib import Path

def write_debug_info(content, filename="startup_debug.txt"):
    """写入调试信息到文件"""
    try:
        with open(filename, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*80}\n")
            f.write(content)
            f.write(f"\n{'='*80}\n\n")
    except Exception as e:
        print(f"写入调试文件失败: {e}")

def collect_environment_info():
    """收集环境信息"""
    info = []
    
    try:
        info.append("=== 系统环境信息 ===")
        info.append(f"操作系统: {platform.system()} {platform.release()}")
        info.append(f"Python版本: {sys.version}")
        info.append(f"Python路径: {sys.executable}")
        info.append(f"当前工作目录: {os.getcwd()}")
        info.append(f"脚本路径: {os.path.abspath(__file__)}")
        
        info.append("\n=== 环境变量 ===")
        important_vars = ['PATH', 'PYTHONPATH', 'PYTHONIOENCODING', 'PYTHONUTF8', 'DJANGO_SETTINGS_MODULE']
        for var in important_vars:
            value = os.environ.get(var, '未设置')
            info.append(f"{var}: {value}")
        
        info.append("\n=== 项目文件检查 ===")
        important_files = [
            'manage.py',
            'geoserver_django/settings.py',
            'geoserver_api/__init__.py',
            'geoserver_api/core/task_queue_manager.py',
            'geoserver_api/views/analysis/analysis_views.py',
            'requirements.txt'
        ]
        
        for file_path in important_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                info.append(f"✓ {file_path} (大小: {size} 字节)")
            else:
                info.append(f"✗ {file_path} (文件不存在)")
        
        info.append("\n=== Python包检查 ===")
        required_packages = ['django', 'djangorestframework', 'requests', 'numpy']
        for package in required_packages:
            try:
                __import__(package)
                info.append(f"✓ {package}")
            except ImportError as e:
                info.append(f"✗ {package}: {e}")
        
        info.append("\n=== 目录结构检查 ===")
        important_dirs = [
            'geoserver_api',
            'geoserver_api/core',
            'geoserver_api/views',
            'geoserver_api/views/analysis',
            'logs',
            'static',
            'media'
        ]
        
        for dir_path in important_dirs:
            if os.path.exists(dir_path) and os.path.isdir(dir_path):
                files_count = len(os.listdir(dir_path)) if os.path.exists(dir_path) else 0
                info.append(f"✓ {dir_path}/ (包含 {files_count} 个文件)")
            else:
                info.append(f"✗ {dir_path}/ (目录不存在)")
        
    except Exception as e:
        info.append(f"收集环境信息时出错: {e}")
        info.append(f"错误详情: {traceback.format_exc()}")
    
    return "\n".join(info)

def test_django_import():
    """测试Django导入"""
    info = []
    info.append("=== Django导入测试 ===")
    
    try:
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_django.settings')
        
        import django
        info.append(f"✓ Django版本: {django.get_version()}")
        
        django.setup()
        info.append("✓ Django setup完成")
        
        # 测试导入关键模块
        from geoserver_api.core.task_queue_manager import TaskQueueManager
        info.append("✓ TaskQueueManager导入成功")
        
        from geoserver_api.views.analysis.analysis_views import queued_combined_ai_spatial_analysis
        info.append("✓ analysis_views导入成功")
        
    except Exception as e:
        info.append(f"✗ Django导入失败: {e}")
        info.append(f"错误详情: {traceback.format_exc()}")
    
    return "\n".join(info)

def test_database_connection():
    """测试数据库连接"""
    info = []
    info.append("=== 数据库连接测试 ===")
    
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            info.append(f"✓ 数据库连接成功: {result}")
    except Exception as e:
        info.append(f"✗ 数据库连接失败: {e}")
        info.append(f"错误详情: {traceback.format_exc()}")
    
    return "\n".join(info)

def main():
    """主函数"""
    print("开始收集调试信息...")
    
    # 清空之前的调试文件
    debug_file = "startup_debug.txt"
    if os.path.exists(debug_file):
        os.remove(debug_file)
    
    # 收集环境信息
    env_info = collect_environment_info()
    write_debug_info(env_info, debug_file)
    print("✓ 环境信息已收集")
    
    # 测试Django导入
    django_info = test_django_import()
    write_debug_info(django_info, debug_file)
    print("✓ Django导入测试完成")
    
    # 测试数据库连接
    db_info = test_database_connection()
    write_debug_info(db_info, debug_file)
    print("✓ 数据库连接测试完成")
    
    print(f"\n调试信息已保存到: {os.path.abspath(debug_file)}")
    print("请运行以下命令启动服务器:")
    print("python manage.py runserver 0.0.0.0:8091")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        error_info = f"调试脚本执行失败: {e}\n错误详情: {traceback.format_exc()}"
        write_debug_info(error_info)
        print(error_info)
