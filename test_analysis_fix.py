#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试分析任务修复
验证time模块修复后分析任务是否正常工作
"""

import requests
import time
from config import DJANGO_BASE_URL

def test_analysis_task():
    """测试分析任务"""
    print("🧪 测试分析任务修复")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    # 提交测试任务
    task_params = {
        'id': '20250705171599',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print("📤 提交测试任务...")
        print(f"   API URL: {base_url}/queued-combined-ai-spatial-analysis/")
        
        response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                              params=task_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                task_id = result['data']['task_id']
                queue_position = result['data']['queue_position']
                
                print(f"✅ 任务提交成功:")
                print(f"   任务ID: {task_id}")
                print(f"   队列位置: {queue_position}")
                
                # 监控任务执行
                monitor_task(task_id, task_params['id'])
                
            else:
                print(f"❌ 任务提交失败: {result['message']}")
        else:
            print(f"❌ 任务提交HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def monitor_task(task_id, image_id, max_wait=300):
    """监控任务执行"""
    print(f"\n👀 监控任务执行...")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            # 检查队列状态
            response = requests.get(f"{base_url}/queue-status/", 
                                  params={'task_id': task_id}, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    status = data.get('status', 'unknown')
                    message = data.get('message', '')
                    
                    # 只在状态变化时打印
                    if status != last_status:
                        timestamp = time.strftime('%H:%M:%S')
                        print(f"   [{timestamp}] 状态: {status}")
                        if message:
                            print(f"   [{timestamp}] 消息: {message}")
                        last_status = status
                    
                    if status in ['完成', '失败']:
                        print(f"   任务结束: {status}")
                        
                        # 检查详细结果
                        check_task_details(image_id, task_id)
                        return status == '完成'
            
            time.sleep(5)  # 每5秒检查一次
            
        except Exception as e:
            print(f"   检查状态异常: {e}")
            time.sleep(5)
    
    print(f"   ⚠️ 监控超时")
    return False

def check_task_details(image_id, task_id):
    """检查任务详细结果"""
    print(f"\n🔍 检查任务详细结果...")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    try:
        # 获取TaskInfo
        response = requests.get(f"{base_url}/taskinfo/", 
                              params={'id': image_id}, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success' and result['data']:
                tasks = result['data']
                
                # 查找指定的任务
                target_task = None
                for task in tasks:
                    if task.get('task_id') == task_id:
                        target_task = task
                        break
                
                if target_task:
                    print(f"   📋 任务详情:")
                    print(f"     任务ID: {task_id[:8]}...")
                    print(f"     状态: {target_task.get('status', 'N/A')}")
                    print(f"     消息: {target_task.get('message', 'N/A')}")
                    
                    # 检查是否有time相关错误
                    message = target_task.get('message', '')
                    if 'time' in message.lower() and 'referenced before assignment' in message:
                        print(f"   ❌ 仍然存在time模块错误!")
                        return False
                    
                    # 检查GeoServer发布结果
                    geoserver_publish = target_task.get('geoserver_publish', {})
                    if geoserver_publish:
                        print(f"   🌐 GeoServer发布结果:")
                        
                        ai_result = geoserver_publish.get('ai_result', {})
                        final_result = geoserver_publish.get('final_result', {})
                        overall_success = geoserver_publish.get('overall_success', False)
                        
                        ai_status = "✅" if ai_result.get('success') else "❌"
                        final_status = "✅" if final_result.get('success') else "❌"
                        overall_status = "✅" if overall_success else "❌"
                        
                        print(f"     AI结果: {ai_status} {ai_result.get('message', 'N/A')}")
                        print(f"     最终结果: {final_status} {final_result.get('message', 'N/A')}")
                        print(f"     总体状态: {overall_status}")
                        
                        # 检查发布时间
                        publish_time = geoserver_publish.get('publish_time')
                        if publish_time:
                            print(f"     发布时间: {publish_time}")
                            print(f"   ✅ time.time()调用成功!")
                        else:
                            print(f"   ⚠️ 未找到发布时间")
                    
                    return True
                    
                else:
                    print(f"   ❌ 未找到指定任务: {task_id}")
                    return False
            else:
                print(f"   ❌ 获取TaskInfo失败")
                return False
        else:
            print(f"   ❌ 获取TaskInfo HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查任务详情异常: {e}")
        return False

def main():
    """主函数"""
    print("🧪 分析任务修复测试")
    print("=" * 50)
    
    print("📝 修复内容:")
    print("- 移除局部'import time'语句")
    print("- 保留全局time模块导入")
    print("- 修复time.time()调用错误")
    
    print("\n🎯 测试目标:")
    print("- 验证任务提交成功")
    print("- 验证任务执行不出现time错误")
    print("- 验证GeoServer发布正常")
    print("- 验证publish_time字段正常生成")
    
    # 执行测试
    test_analysis_task()
    
    print(f"\n🎉 测试完成！")
    
    print(f"\n📚 修复说明:")
    print(f"- 问题: local variable 'time' referenced before assignment")
    print(f"- 原因: 局部import time覆盖全局time模块")
    print(f"- 修复: 移除第1343行的'import time'")
    print(f"- 结果: time.time()和time.sleep()正常工作")

if __name__ == "__main__":
    main()
