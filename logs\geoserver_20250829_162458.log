2025-08-29 16:24:58,768 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_162458.log
2025-08-29 16:24:58,770 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:24:58,771 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:24:58,843 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:24:58,846 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:24:58,847 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:24:58,862 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:24:58,865 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:24:58,866 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:24:58,881 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:24:58,883 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:24:58,884 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:24:58,897 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:24:58,899 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:24:58,899 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:24:58,912 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:24:58,914 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:24:58,914 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:24:58,926 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:24:58,941 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 16:24:58,946 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 16:25:03,293 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 16:25:03,306 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 16:25:03,309 - analysis_executor - INFO - 加载了 28 个任务状态
