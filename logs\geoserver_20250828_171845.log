2025-08-28 17:18:45,431 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_171845.log
2025-08-28 17:18:45,433 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:45,434 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:45,453 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:45,457 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:45,457 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:45,468 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:45,470 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:45,470 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:45,482 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:45,485 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:45,485 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:45,500 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:45,502 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:45,503 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:45,518 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:45,520 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:45,520 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:45,539 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:45,561 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:18:45,568 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:18:45,729 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:18:45,759 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:18:45,763 - analysis_executor - INFO - 加载了 28 个任务状态
