# 地理空间分析API文档

## 概述

地理空间分析API是一个功能强大的遥感数据分析平台，集成了AI语义分割、空间变化分析、权重管理等多种功能，为地理空间数据处理提供完整的解决方案。

## 功能模块概览

### 🔍 核心功能
- **空间数据变化分析**：分析两个时期空间数据的变化，计算流入流出区域
- **AI语义分割**：使用深度学习模型对遥感图像进行语义分割，直接输出Shapefile
- **合并分析**：将AI语义分割和空间变化分析合并为一个完整流程
- **权重信息管理**：管理AI模型权重文件，按地物类型分类
- **任务状态监控**：异步任务执行，支持状态查询和日志监控

### 📋 接口列表

| 接口路径 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/analysis/spatial-changes/` | GET | 空间数据变化分析 |
| `/api/analysis/queued-combined-ai-spatial-analysis/` | GET | 队列化合并AI分析和空间变化分析 |
| `/api/analysis/cancel-queued-task/` | GET | 取消等待中的队列任务 |
| `/api/analysis/queue-status/` | GET | 获取任务队列状态 |
| `/api/analysis/ai-semantic-segmentation/` | GET | AI语义分割 |
| `/api/analysis/ai-batch-processing/` | GET | AI批量处理 |
| `/api/analysis/combined-ai-spatial-analysis/` | GET | 合并AI分析和空间变化分析 |
| `/api/analysis/weight-info/` | GET | 获取AI模型权重信息 |
| `/api/analysis/weight-config/` | GET | 获取权重配置信息 |
| `/api/analysis/ai-models-info/` | GET | 获取AI模型信息 |
| `/api/analysis/image-extent-extraction/` | GET | 影像有效范围提取 |
| `/api/analysis/taskinfo/` | GET | 根据ID获取TaskInfo.json内容 |
| `/api/analysis/status/` | GET | 查询分析任务状态 |
| `/api/analysis/logs/` | GET | 获取分析日志 |
| `/api/analysis/download-data/` | POST | 批量下载分析数据 |

### 🤖 支持的AI模型
- **DeepLabV3+**：经典语义分割模型及其改进版
- **U-Net系列**：UNet、UNet++、L-UNet、UNet-GAI
- **Transformer系列**：SegFormer、SegNeXt、Vision Transformer
- **其他模型**：共支持13种主流深度学习模型

### 🌍 支持的地物类型
- **耕地（arableLand）**：农田、耕地区域识别
- **建设用地（constructionLand）**：建筑物、道路等人工建设区域
- **其他地物类型**：可根据配置扩展

---

## 详细接口说明

### 1. 空间数据变化分析

**接口地址**: `GET /api/analysis/spatial-changes/`

**功能说明**: 分析两个时期空间数据的变化，计算流入流出区域。支持指定裁剪范围进行精确对比分析，或使用新数据覆盖区域进行全覆盖分析。支持自定义面积阈值过滤，异步任务执行，输出单个shapefile文件，使用flowtype属性区分流入和流出。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| old_data_path | string | 是 | 老数据Shapefile路径 | D:/data/farmland_2024.shp |
| new_data_path | string | 是 | 新数据Shapefile路径 | D:/data/farmland_2025.shp |
| area_threshold | number | 否 | 面积阈值(平方米)，默认200 | 400.0 |
| output_dir | string | 否 | 输出目录，默认为新数据根目录 | D:/output/ |
| shp_filename | string | 否 | 输出文件名，默认changes_shapefile | farmland_changes |
| clip_area_path | string | 否 | 裁剪范围Shapefile路径，指定分析区域 | D:/data/study_area.shp |

**输出结果**:

输出的shapefile文件包含以下字段：
- **area**: 图斑面积(平方米)
- **flowtype**: 变化类型("in"=流入，"out"=流出)
- **change_type**: 变化描述("inflow"或"outflow")
- **其他字段**: 保留原始数据的属性字段

**功能特性**:

1. **裁剪范围支持**:
   - 提供`clip_area_path`参数：在指定范围内进行精确对比分析
   - 不提供`clip_area_path`参数：使用新数据覆盖区域进行全覆盖分析
   - 确保新老数据在相同范围内进行对比，避免无关区域干扰

2. **分析逻辑**:
   - 读取新老数据的Shapefile文件
   - 根据裁剪范围或新数据边界确定分析区域
   - 裁剪老数据和新数据到分析区域
   - 计算流出数据（老数据 - 新数据）和流入数据（新数据 - 老数据）
   - 按面积阈值过滤小图斑
   - 合并结果并添加变化类型标识

3. **输出优势**:
   - 单个Shapefile文件包含所有变化信息
   - flowtype字段区分流入流出，便于符号化显示
   - 保留原始属性信息，支持进一步分析

**返回数据**:
```json
{
  "status": "success",
  "message": "空间数据变化分析任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "queued",
    "message": "任务已排队，等待执行..."
  }
}
```

**调用示例**:
```
# 基础调用（使用新数据覆盖区域）
GET http://127.0.0.1:8091/api/analysis/spatial-changes/?old_data_path=D:/data/farmland_2024.shp&new_data_path=D:/data/farmland_2025.shp&area_threshold=400.0

# 使用裁剪范围进行精确分析
GET http://127.0.0.1:8091/api/analysis/spatial-changes/?old_data_path=D:/data/farmland_2024.shp&new_data_path=D:/data/farmland_2025.shp&clip_area_path=D:/data/study_area.shp&area_threshold=400.0
```

### 2. AI语义分割

**接口地址**: `GET /api/analysis/ai-semantic-segmentation/`

**功能说明**: 使用深度学习模型对遥感图像进行语义分割，直接输出Shapefile矢量文件。支持13种主流深度学习模型，支持GPU加速推理，支持大图像滑窗处理。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| image | string | 是 | 输入遥感图像路径 | D:/data/satellite.tif |
| model | string | 是 | 模型权重文件路径 | D:/models/farmland_model.pth |
| output | string | 否 | 输出Shapefile路径，默认自动生成 | D:/results/result.shp |
| model_type | string | 否 | 模型类型，默认segnext | deeplabv3_plus |
| target_classes | string | 否 | 目标类别，如"1,2,3" | 1,2 |
| window_size | integer | 否 | 滑动窗口大小，默认512 | 512 |
| batch_size | integer | 否 | 批处理大小，默认16 | 16 |
| simplify | float | 否 | 简化阈值，默认0.0 | 1.0 |
| min_area | float | 否 | 最小面积阈值，默认0.0 | 100.0 |
| num_classes | integer | 否 | 类别数量，默认2 | 3 |

**支持的模型类型**:
- **deeplabv3_plus**: DeepLabV3+ 经典语义分割模型
- **unet**: U-Net 医学图像分割经典模型
- **segformer**: SegFormer Transformer语义分割模型
- **segnext**: SegNeXt 下一代语义分割模型
- **unetplusplus**: UNet++ 嵌套U-Net架构
- **其他**: lunet、unetgai、vision_transformer等

**输出结果**:
生成标准的Shapefile文件，包含分割后的矢量多边形，每个多边形包含类别信息和几何属性。

**返回数据**:
```json
{
  "status": "success",
  "message": "AI语义分割任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "log_file": "D:/logs/550e8400-e29b-41d4-a716-446655440000.log"
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/ai-semantic-segmentation/?image=D:/data/satellite.tif&model=D:/models/farmland_model.pth&model_type=deeplabv3_plus&target_classes=1,2&window_size=512
```

### 3. AI批量处理

**接口地址**: `GET /api/analysis/ai-batch-processing/`

**功能说明**: 批量处理目录下的所有遥感图像，使用AI模型进行语义分割。自动扫描输入目录中的所有图像文件，逐一进行AI分析处理。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| input_dir | string | 是 | 输入图像目录 | D:/data/images/ |
| output_dir | string | 是 | 输出目录 | D:/results/ |
| model | string | 是 | 模型权重文件路径 | D:/models/model.pth |
| model_type | string | 否 | 模型类型，默认segnext | deeplabv3_plus |
| target_classes | string | 否 | 目标类别，如"1,2,3" | 1,2 |
| window_size | integer | 否 | 滑动窗口大小，默认512 | 512 |
| batch_size | integer | 否 | 批处理大小，默认16 | 16 |
| num_classes | integer | 否 | 类别数量，默认2 | 3 |

**输出结果**:
为每个输入图像生成对应的Shapefile文件，保存在指定的输出目录中。

**返回数据**:
```json
{
  "status": "success",
  "message": "AI批量处理任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "input_dir": "D:/data/images/",
    "output_dir": "D:/results/",
    "estimated_files": 15
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/ai-batch-processing/?input_dir=D:/data/images/&output_dir=D:/results/&model=D:/models/model.pth&model_type=deeplabv3_plus
```

### 4. 队列化合并AI分析和空间变化分析

**接口地址**: `GET /api/analysis/queued-combined-ai-spatial-analysis/`

**功能说明**: 将合并分析任务添加到队列中按顺序执行，避免并发执行导致的系统崩溃。采用生产者-消费者模型，确保任务按提交顺序依次执行，同时只有一个任务在运行。

**核心特性**:
- 🔄 **队列管理**: 任务按提交顺序排队执行
- 🚫 **并发控制**: 同时只执行一个任务，避免系统资源冲突
- 📊 **状态跟踪**: 实时跟踪任务状态和队列位置
- ⏰ **等待时间估算**: 提供预估等待时间
- ❌ **任务取消**: 支持取消等待中的任务
- 📝 **即时反馈**: 立即创建TaskInfo.json模板，提示前端任务已创建

**执行流程**:
1. **接收参数** → 验证参数有效性
2. **创建TaskInfo模板** → 状态为"等待中"，立即返回task_id
3. **加入队列** → 任务进入执行队列
4. **队列执行** → 按顺序执行，状态变为"进行中"
5. **完整分析** → AI语义分割 → 空间数据变化分析 → 发布GeoServer
6. **执行下一个** → 当前任务完成后自动执行队列中的下一个任务

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 影像ID | 20250705171599 |
| image | string | 是 | TIF图像路径 | D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif |
| model | string | 是 | AI模型文件路径 | D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth |
| old_data_path | string | 是 | 历史数据文件路径 | D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp |
| area_threshold | float | 否 | 面积阈值，默认400.0 | 200.0 |
| model_type | string | 否 | 模型类型，默认deeplabv3_plus | deeplabv3_plus |

**返回数据**:
```json
{
  "success": true,
  "message": "任务已添加到队列",
  "data": {
    "task_id": "71d765d8-dfdc-4c4e-bf9c-cb826069c708",
    "status": "等待中",
    "queue_position": 1,
    "estimated_wait_time": "约 10 分钟"
  }
}
```

**调用示例**:
```bash
curl -X GET "http://localhost:8091/api/analysis/queued-combined-ai-spatial-analysis/" \
  -G \
  -d "id=20250705171599" \
  -d "image=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif" \
  -d "model=D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth" \
  -d "old_data_path=D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp" \
  -d "area_threshold=200.0"
```

### 5. 取消等待中的队列任务

**接口地址**: `GET /api/analysis/cancel-queued-task/`

**功能说明**: 取消状态为"等待中"的任务，已执行或完成的任务无法取消。

**输入参数**:
- `task_id`: 任务ID (必选，URL参数)

**任务状态限制**:
- ✅ **等待中**: 可以取消
- ❌ **进行中**: 无法取消（任务正在执行）
- ❌ **完成**: 无法取消（任务已完成）
- ❌ **失败**: 无法取消（任务已结束）

**请求示例**:
```bash
curl -X GET "http://localhost:8091/api/analysis/cancel-queued-task/?task_id=71d765d8-dfdc-4c4e-bf9c-cb826069c708"
```

**成功响应**:
```json
{
  "success": true,
  "message": "任务已取消"
}
```

**失败响应**:
```json
{
  "success": false,
  "message": "任务状态为\"进行中\"，无法取消"
}
```

### 6. 获取任务队列状态

**接口地址**: `GET /api/analysis/queue-status/`

**功能说明**: 查看当前队列中的任务数量、执行状态等信息。

**输入参数**:
- `task_id`: 任务ID (可选，如果提供则返回特定任务状态)

**队列状态响应**:
```json
{
  "success": true,
  "data": {
    "queue_size": 3,
    "current_task": "71d765d8-dfdc-4c4e-bf9c-cb826069c708",
    "total_tasks": 10,
    "waiting_tasks": 3,
    "completed_tasks": 7
  }
}
```

**特定任务状态响应**:
```json
{
  "success": true,
  "data": {
    "task_id": "71d765d8-dfdc-4c4e-bf9c-cb826069c708",
    "status": "等待中",
    "message": "任务已创建，等待执行",
    "created_time": "2025-08-25T17:33:30.910068",
    "queue_position": 2,
    "estimated_wait_time": "约 20 分钟"
  }
}
```

**请求示例**:
```bash
# 获取队列整体状态
curl -X GET "http://localhost:8091/api/analysis/queue-status/"

# 获取特定任务状态
curl -X GET "http://localhost:8091/api/analysis/queue-status/?task_id=71d765d8-dfdc-4c4e-bf9c-cb826069c708"
```

### 7. 合并AI分析和空间变化分析（直接执行）

**接口地址**: `GET /api/analysis/combined-ai-spatial-analysis/`

**功能说明**: 将AI语义分割、影像范围提取和空间变化分析合并为一个统一的接口，提供完整的影像分析解决方案。执行流程：1) 对输入TIF图像进行AI预测生成SHP结果；2) 提取TIF影像的有效范围；3) 使用影像范围作为裁剪区域，将AI结果与历史SHP数据进行精确的空间变化分析。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 影像ID | 20250705171601 |
| image | string | 是 | TIF图像路径 | D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601_out.tif |
| model | string | 是 | 权重路径 | D:\Drone_Project\nginxData\ODM\AIWeight\arableLand\deeplabv3_plus\deeplabv3_plus_best_20250807-111949.pth |
| old_data_path | string | 是 | 历史SHP路径 | D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp |
| area_threshold | float | 否 | 面积阈值（平方米），默认400 | 400 |
| model_type | string | 否 | 模型类型，默认deeplabv3_plus | deeplabv3_plus |
| num_classes | int | 否 | 类别数量，默认2 | 2 |

**输出结果**:
生成完整的分析结果文件结构，包括：
- **AI分析结果**: AI语义分割生成的SHP文件
- **影像范围文件**: 自动提取的影像有效范围SHP文件（命名格式：`{image_id}_area_{timestamp}.shp`，与其他SHP文件位于同一目录）
- **空间变化分析结果**: 在影像范围内进行的精确变化分析SHP文件
- **GeoServer自动发布**: 自动将AI分析结果和最终分析结果发布到GeoServer（工作区：analysis_category，坐标系：EPSG:32648）
- **任务信息文件**: 包含完整分析过程信息的JSON文件，包括GeoServer发布状态

**分析流程**:
1. **AI语义分割** (0-40%): 对TIF影像进行AI预测，生成分类结果
2. **影像范围提取** (40-60%): 提取TIF影像的有效数据范围
3. **空间变化分析** (60-90%): 使用影像范围作为裁剪区域，进行精确的变化对比
4. **结果保存** (90-100%): 保存所有分析结果和任务信息

**返回数据**:
```json
{
  "status": "success",
  "message": "合并分析任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "等待中",
    "message": "任务已创建，等待执行",
    "image_id": "20250705171601",
    "estimated_time": "预计15-20分钟完成"
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/combined-ai-spatial-analysis/?id=20250705171601&image=D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601_out.tif&model=D:\Drone_Project\nginxData\ODM\AIWeight\arableLand\deeplabv3_plus\deeplabv3_plus_best_20250807-111949.pth&old_data_path=D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp&area_threshold=400
```

### 5. 获取AI模型权重信息

**接口地址**: `GET /api/analysis/weight-info/`

**功能说明**: 获取AI模型权重信息，按地物类型分类返回所有可用的权重文件。自动扫描权重目录结构，按地物类型和模型类型分类组织，提供完整的文件路径和HTTP访问URL。

**输入参数**: 无

**输出结果**:
返回按地物类型分类的权重信息，包含默认权重配置、可用模型类型、权重文件列表和SHP文件信息。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取权重信息",
  "data": {
    "arableLand": {
      "default": "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
      "display_name": "耕地",
      "default_area": 400.0,
      "models": {
        "deeplabv3_plus": [
          {
            "name": "deeplabv3_plus_best_20250807-111949.pth",
            "path": "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
            "url": "http://127.0.0.1:81/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
          }
        ]
      },
      "shp_files": [
        "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
      ]
    },
    "constructionLand": {
      "default": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth",
      "display_name": "建设用地",
      "default_area": 200.0,
      "models": {
        "deeplabv3_plus": [
          {
            "name": "deeplabv3_plus_best_20250811-162215.pth",
            "path": "D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth"
          }
        ]
      }
    }
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/weight-info/
```

### 6. 获取权重配置信息

**接口地址**: `GET /api/analysis/weight-config/`

**功能说明**: 获取权重配置信息，返回配置文件中的基础配置，包括数据路径和默认权重设置。

**输入参数**: 无

**输出结果**:
返回配置文件中的基础配置信息，包括数据路径和各地物类型的默认权重。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取配置信息",
  "data": {
    "window_data_path": "D:/Drone_Project/nginxData",
    "default_weights": {
      "arableLand": "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
      "constructionLand": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth"
    },
    "field_mapping": {
      "arableLand": "耕地",
      "constructionLand": "建设用地"
    },
    "default_areas": {
      "arableLand": 400.0,
      "constructionLand": 200.0
    }
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/weight-config/
```

### 7. 根据ID获取TaskInfo.json内容

**接口地址**: `GET /api/analysis/taskinfo/`

**功能说明**: 根据影像ID获取对应的TaskInfo.json文件内容，返回该ID下所有分析任务的详细信息。TaskInfo.json文件记录了完整的分析过程，包括任务参数、执行状态、处理时间、结果统计等信息。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 影像ID | 20250705171601 |

**文件路径规则**:
接口会自动构建TaskInfo.json文件路径：
- 从ODM配置文件读取`window_data_path`
- 构建路径：`{window_data_path}/ODM/AI/{id}/TaskInfo.json`
- 示例：`D:/Drone_Project/nginxData/ODM/AI/20250705171601/TaskInfo.json`

**输出结果**:
返回TaskInfo.json文件的完整内容，包含该影像ID下所有分析任务的详细信息。如果文件不存在，返回空列表。

**返回数据（文件存在）**:
```json
{
  "status": "success",
  "message": "成功获取TaskInfo信息，共 2 个任务",
  "data": [
    {
      "task_id": "62435a75-3482-4608-b585-f0d8506098e1",
      "image_id": "20250705171601",
      "analysis_category": "arableLand",
      "timestamp": 1755743087,
      "datetime": "2025-08-21T10:24:47.923977",
      "input_files": {
        "image_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif",
        "model_path": "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
        "old_data_path": "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
      },
      "output_files": {
        "ai_output_path": "D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand/20250705171601_1_1755743087.shp",
        "final_output_path": "D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand/20250705171601_2_1755743087.shp"
      },
      "parameters": {
        "model_type": "deeplabv3_plus",
        "model_name": "deeplabv3_plus_best_20250807-111949.pth",
        "num_classes": 2,
        "area_threshold": 400.0
      },
      "results": {
        "ai_processing_time": 426,
        "spatial_statistics": {
          "outflow_count": 0,
          "inflow_count": 15,
          "total_count": 15,
          "outflow_area": 0.0,
          "inflow_area": 398307.4,
          "area_threshold": 400.0
        },
        "success": true
      },
      "geoserver_publish": {
        "ai_result": {
          "success": true,
          "message": "Shapefile发布成功 (EPSG:32648)",
          "layer_name": "20250705171601_1_1755743087",
          "store_name": "20250705171601_1_1755743087_store"
        },
        "final_result": {
          "success": true,
          "message": "Shapefile发布成功 (EPSG:32648)",
          "layer_name": "20250705171601_2_1755743087",
          "store_name": "20250705171601_2_1755743087_store"
        },
        "overall_success": true,
        "workspace": "arableLand",
        "epsg": "32648",
        "publish_time": 1755743200.123
      },
      "status": "完成",
      "progress": 100,
      "log_file": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/logs/analysislog/62435a75-3482-4608-b585-f0d8506098e1.log"
    }
  ]
}
```

**返回数据（文件不存在）**:
```json
{
  "status": "success",
  "message": "TaskInfo文件不存在: 20241231999999",
  "data": []
}
```

**返回数据（参数错误）**:
```json
{
  "status": "error",
  "message": "缺少必需参数: id",
  "data": []
}
```

**TaskInfo字段说明**:

| 字段名 | 类型 | 说明 |
|--------|------|------|
| task_id | string | 唯一任务标识符 |
| image_id | string | 影像ID |
| analysis_category | string | 分析类别（如arableLand） |
| timestamp | number | Unix时间戳 |
| datetime | string | ISO格式时间 |
| input_files | object | 输入文件路径信息 |
| output_files | object | 输出文件路径信息 |
| parameters | object | 分析参数配置 |
| parameters.model_name | string | 使用的模型文件名 |
| results | object | 分析结果统计 |
| results.ai_processing_time | number | AI处理耗时（秒） |
| results.spatial_statistics | object | 空间变化统计信息 |
| results.success | boolean | 任务是否成功 |
| geoserver_publish | object | GeoServer发布信息 |
| geoserver_publish.ai_result | object | AI结果发布状态 |
| geoserver_publish.final_result | object | 最终结果发布状态 |
| geoserver_publish.overall_success | boolean | 总体发布是否成功 |
| geoserver_publish.workspace | string | GeoServer工作区名称 |
| geoserver_publish.epsg | string | 发布使用的坐标系 |
| status | string | 任务状态（等待中/进行中/完成/失败，仅这4种状态） |
| progress | number | 执行进度（0-100） |
| log_file | string | 日志文件路径 |

**调用示例**:
```
# 获取存在的TaskInfo
GET http://127.0.0.1:8091/api/analysis/taskinfo/?id=20250705171601

# 获取不存在的TaskInfo（返回空列表）
GET http://127.0.0.1:8091/api/analysis/taskinfo/?id=20241231999999
```

**GeoServer自动发布**:
合并分析完成后，系统会自动将分析结果发布到GeoServer：

- **发布内容**: AI分析结果和最终分析结果两个SHP文件
- **工作区**: 使用 `analysis_category` 参数作为工作区名称（如：arableLand）
- **坐标系**: 统一使用 EPSG:32648 坐标系
- **图层命名**: 使用SHP文件名作为图层名称
- **存储命名**: 图层名称 + "_store" 后缀
- **发布状态**: 在TaskInfo.json中记录详细的发布状态和结果

**发布示例**:
```
AI结果: arableLand:20250705171601_1_1755743087
最终结果: arableLand:20250705171601_2_1755743087
访问URL: http://geoserver:8080/geoserver/arableLand/wms
```

**使用场景**:
1. **任务监控**: 查看分析任务的完整执行历史
2. **结果查询**: 获取分析结果的详细统计信息
3. **参数回溯**: 查看历史任务使用的参数配置
4. **性能分析**: 分析任务执行时间和效率
5. **错误排查**: 通过日志文件路径定位问题
6. **GeoServer集成**: 自动发布结果，便于Web地图服务使用

### 8. 获取AI模型信息

**接口地址**: `GET /api/analysis/ai-models-info/`

**功能说明**: 获取系统支持的AI模型信息，返回所有可用的深度学习模型类型和描述。

**输入参数**: 无

**输出结果**:
返回系统支持的所有AI模型类型列表和相应的描述信息。

**返回数据**:
```json
{
  "status": "success",
  "message": "获取AI模型信息成功",
  "data": {
    "supported_models": [
      {
        "model_type": "deeplabv3_plus",
        "description": "DeepLabV3+ - 经典语义分割模型"
      },
      {
        "model_type": "unet",
        "description": "U-Net - 医学图像分割经典模型"
      },
      {
        "model_type": "segformer",
        "description": "SegFormer - Transformer语义分割模型"
      }
    ],
    "total_count": 13
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/ai-models-info/
```

### 9. 影像有效范围提取

**接口地址**: `GET /api/analysis/image-extent-extraction/`

**功能说明**: 提取TIF影像中的有效数据范围，排除NoData区域和无效像素，输出Shapefile。自动识别影像中的有效数据区域，生成精确的矢量边界。默认保持与输入影像相同的坐标系。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| image | string | 是 | 输入TIF影像路径 | D:/data/satellite.tif |
| output | string | 否 | 输出Shapefile路径，默认自动生成 | D:/results/extent.shp |
| simplify_tolerance | float | 否 | 简化容差，单位为米，默认1.0 | 2.0 |
| min_area | float | 否 | 最小面积阈值，单位为平方米，默认1000.0 | 500.0 |
| keep_original_crs | boolean | 否 | 是否保持原始坐标系，默认true | false |

**输出结果**:
生成Shapefile文件，包含影像的有效数据范围多边形，每个多边形包含ID、面积、周长等属性信息。坐标系默认与输入影像保持一致，也可选择转换为WGS84。

**返回数据**:
```json
{
  "status": "success",
  "message": "影像范围提取任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "log_file": "D:/logs/550e8400-e29b-41d4-a716-446655440000.log",
    "input_image": "D:/data/satellite.tif",
    "output_path": "D:/data/satellite_extent.shp"
  }
}
```

**调用示例**:
```
# 保持原始坐标系（默认）
GET http://127.0.0.1:8091/api/analysis/image-extent-extraction/?image=D:/data/satellite.tif&simplify_tolerance=1.0&min_area=1000.0

# 转换为WGS84坐标系
GET http://127.0.0.1:8091/api/analysis/image-extent-extraction/?image=D:/data/satellite.tif&keep_original_crs=false
```

### 10. 查询分析任务状态

**接口地址**: `GET /api/analysis/status/`

**功能说明**: 查询分析任务的执行状态和结果，支持查询所有类型分析任务的状态信息。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| task_id | string | 是 | 任务ID | 550e8400-e29b-41d4-a716-446655440000 |

**输出结果**:
返回任务的详细状态信息，包括执行进度、结果统计、输出文件等。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取任务状态",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "completed",
    "message": "分析任务完成",
    "start_time": "2025-08-12T10:30:00",
    "end_time": "2025-08-12T10:35:00",
    "progress": 100,
    "result": {
      "success": true,
      "message": "分析完成",
      "statistics": {
        "outflow_count": 15,
        "inflow_count": 8,
        "total_count": 23,
        "outflow_area": 5420.5,
        "inflow_area": 3280.2
      },
      "output_files": {
        "combined_shapefile": "D:/data/changes_shapefile.shp"
      }
    }
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/status/?task_id=550e8400-e29b-41d4-a716-446655440000
```

### 11. 获取分析日志

**接口地址**: `GET /api/analysis/logs/`

**功能说明**: 获取分析任务的执行日志，支持按任务ID过滤和限制返回行数。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| lines | number | 否 | 返回的日志行数，默认全部 | 200 |
| task_id | string | 否 | 获取特定任务的日志 | 550e8400-e29b-41d4-a716-446655440000 |
| log_type | string | 否 | 日志类型：main或task，默认task | task |

**输出结果**:
返回指定任务或全局的日志信息，包含详细的执行过程记录。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取分析日志",
  "data": {
    "log_file": "/path/to/analysis.log",
    "total_lines": 1500,
    "returned_lines": 100,
    "task_id_filter": "550e8400-e29b-41d4-a716-446655440000",
    "logs": [
      "2025-08-12 10:30:00 - analysis_executor - INFO - 开始分析任务",
      "2025-08-12 10:30:01 - analysis_executor - INFO - 读取数据文件",
      "2025-08-12 10:30:02 - analysis_executor - INFO - 执行空间分析"
    ]
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/logs/?task_id=550e8400-e29b-41d4-a716-446655440000&lines=100
```

---

## 技术说明

### 分析算法原理

**空间变化分析算法**:
1. 数据预处理：读取新旧两期Shapefile数据，验证数据完整性和坐标系
2. 分析区域确定：计算新数据的凸包边界，将老数据裁剪到该边界内
3. 流出计算：合并新数据几何体，对每个老数据图斑计算差集
4. 流入计算：合并老数据几何体，对每个新数据图斑计算差集
5. 结果输出：生成包含flowtype属性的合并Shapefile文件

**AI语义分割算法**:
1. 模型加载：动态加载指定的深度学习模型权重
2. 图像预处理：对输入遥感图像进行标准化和分块处理
3. 滑窗推理：使用滑动窗口对大图像进行分块预测
4. 结果融合：将分块预测结果进行拼接和融合
5. 矢量化：将预测结果转换为Shapefile矢量格式

### 错误处理

| 错误类型 | HTTP状态码 | 说明 |
|----------|------------|------|
| 参数缺失 | 400 | 缺少必需的请求参数 |
| 文件不存在 | 404 | 指定的文件不存在 |
| 文件格式错误 | 400 | 不是有效的文件格式 |
| 坐标系不匹配 | 400 | 数据集的坐标系不一致 |
| 任务不存在 | 404 | 指定的任务ID不存在 |
| 内存不足 | 500 | 数据量过大导致内存不足 |
| AI模型错误 | 500 | AI模型加载或推理失败 |

### 系统要求

**技术依赖**:
- Python 3.8+
- GDAL 3.0+
- PyTorch 1.8+（AI功能）
- CUDA 11.0+（GPU加速，可选）

**数据要求**:
- Shapefile格式：标准ESRI Shapefile，包含.shp、.shx、.dbf等文件
- 遥感图像：支持GeoTIFF格式，建议使用投影坐标系
- 坐标系：建议新旧数据使用相同的投影坐标系，确保面积计算准确
- 编码格式：建议使用UTF-8编码

**性能建议**:
- 单次分析建议数据量不超过500MB
- AI分析建议使用GPU加速，提高处理速度
- 大数据量分析建议分块处理
- 及时清理临时文件和中间结果

### 注意事项

1. **数据准备**：确保输入数据的完整性和格式正确性
2. **坐标系统一**：新旧数据必须使用相同的坐标系
3. **面积阈值**：根据实际需求设置合适的面积阈值
4. **任务监控**：大数据量分析可能需要较长时间，建议使用状态查询接口监控进度
5. **结果备份**：分析结果文件会定期清理，请及时备份重要结果

### 输出文件结构

**空间变化分析输出**:
```
output_dir/
├── changes_shapefile.shp    # 合并的变化结果
├── changes_shapefile.shx
├── changes_shapefile.dbf    # 包含flowtype字段
└── changes_shapefile.prj
```

**AI分析输出**:
```
output_dir/
├── result.shp              # AI分割结果
├── result.shx
├── result.dbf
└── result.prj
```

**合并分析输出**:
```
{window_data_path}/ODM/AI/{影像ID}/
├── {分析类别}/
│   ├── {影像ID}_1_{时间戳}.shp    # AI分析结果
│   ├── {影像ID}_area_{时间戳}.shp # 影像范围文件（与其他SHP文件同目录）
│   └── {影像ID}_2_{时间戳}.shp    # 最终分析结果
└── TaskInfo.json                   # 任务信息文件
```

## 🎯 裁剪范围功能说明

### 功能概述

空间数据变化分析现在支持使用指定的裁剪范围进行精确对比分析，确保新老数据在相同的空间范围内进行比较，避免无关区域的干扰。

### 使用场景

1. **指定研究区域**: 当只需要分析特定区域的变化时
2. **影像有效范围**: 使用影像的有效数据范围进行分析
3. **行政边界**: 在特定行政区划内进行变化分析
4. **项目边界**: 在项目范围内进行精确分析

### 实现方式

#### 1. 手动指定裁剪范围
```bash
GET /api/analysis/spatial-changes/?old_data_path=old.shp&new_data_path=new.shp&clip_area_path=study_area.shp
```

#### 2. 自动影像范围（合并分析）
```bash
GET /api/analysis/combined-ai-spatial-analysis/?id=20250705171601&image=image.tif&model=model.pth&old_data_path=old.shp
```
合并分析会自动：
1. 提取TIF影像的有效范围
2. 生成范围文件：`{image_id}_area_{timestamp}.shp`
3. 使用该范围进行空间变化分析

### 技术优势

- **精确对比**: 确保新老数据在相同范围内比较
- **减少干扰**: 排除无关区域的影响
- **提高效率**: 减少计算量，提升分析速度
- **结果可靠**: 避免边界效应，提高分析准确性

### 文件命名规则

影像范围文件命名格式：`{image_id}_area_{timestamp}.shp`

示例：
- `20250705171601_area_1755596377.shp`
- `20250705171602_area_1755596378.shp`

---

## 12. 分析数据下载

### 接口地址
`POST /api/analysis/download-data/`

### 接口说明
批量下载分析结果数据，支持多个SHP文件的打包下载。系统会自动将本地文件路径转换为HTTP URL，下载完整的SHP文件组（包括.shp、.shx、.dbf、.prj等），并打包为ZIP文件供下载。

### 请求参数

**请求体参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| file_paths | array | 是 | 文件路径列表 | 见下方示例 |

### 请求示例

```json
{
  "file_paths": [
    "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp",
    "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp"
  ]
}
```

### 处理流程

#### 第一步：路径转换
将本地文件路径转换为HTTP URL：

**转换规则**：
- 动态识别ODM目录位置
- 将ODM之前的路径替换为 `http://127.0.0.1:81`
- 保留ODM及其后的相对路径

**转换示例**：
```
原始路径: D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp
转换后URL: http://127.0.0.1:81/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp
```

#### 第二步：文件下载
通过HTTP URL下载完整的SHP文件组：

**SHP文件组包括**：
- `.shp` - 几何数据（必需）
- `.shx` - 几何索引（必需）
- `.dbf` - 属性数据（必需）
- `.prj` - 投影信息（可选）
- `.cpg` - 编码信息（可选）
- `.sbn/.sbx` - 空间索引（可选）
- `.shp.xml` - 元数据（可选）

#### 第三步：数据打包
将所有下载的文件打包为ZIP文件：

**打包规则**：
- 使用时间戳创建文件夹：`analysis_data_{timestamp}`
- 所有文件放在同一个文件夹中
- ZIP文件命名：`analysis_data_{timestamp}.zip`

### 返回数据

**成功响应**：
- **Content-Type**: `application/zip`
- **Content-Disposition**: `attachment; filename="analysis_data_1756085988.zip"`
- **响应体**: ZIP文件二进制数据

**错误响应**：
```json
{
  "status": "error",
  "message": "错误描述"
}
```

### 错误码说明

| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | 参数错误 | 缺少file_paths参数或格式错误 |
| 400 | 路径无效 | 没有有效的文件路径 |
| 500 | 打包失败 | 数据打包过程中出现错误 |
| 500 | 下载失败 | 文件下载过程中出现错误 |

### 调用示例

```bash
curl -X POST http://127.0.0.1:8091/api/analysis/download-data/ \
  -H "Content-Type: application/json" \
  -d '{
    "file_paths": [
      "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp",
      "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp"
    ]
  }' \
  --output analysis_data.zip
```

### 使用场景

1. **批量数据导出**: 一次性下载多个分析结果
2. **数据备份**: 将重要的分析结果进行备份
3. **数据共享**: 将分析结果打包分享给其他用户
4. **离线分析**: 下载数据进行离线的进一步分析
5. **数据迁移**: 将分析结果迁移到其他系统

### 注意事项

1. **文件完整性**: 系统会自动下载SHP文件的所有相关文件，确保数据完整性
2. **路径格式**: 支持Windows和Linux路径格式，自动进行标准化处理
3. **网络依赖**: 需要确保127.0.0.1:81服务正常运行
4. **临时文件**: 系统会自动清理临时文件，无需手动处理
5. **并发限制**: 建议避免同时发起大量下载请求

### 技术特点

- **智能路径转换**: 动态识别ODM目录，适应不同的部署环境
- **完整文件支持**: 自动下载SHP文件的所有相关文件
- **错误处理**: 完善的错误处理机制，确保下载稳定性
- **日志记录**: 详细的操作日志，便于问题排查
- **内存优化**: 使用流式处理，避免大文件占用过多内存

---

## 空SHP文件处理机制

### 功能说明

当分析结果为空（没有检测到任何目标要素）时，系统会自动创建**有效的空SHP文件**，确保后续流程的完整性和一致性。

### 适用场景

1. **AI语义分割无结果**: 模型未检测到目标地物类别
2. **空间变化分析无变化**: 新老数据对比无显著变化
3. **面积阈值过滤**: 所有检测结果都小于设定的面积阈值
4. **数据范围限制**: 在指定分析范围内无有效数据

### 空SHP文件特征

#### **文件结构**
- ✅ **完整的SHP文件组**: 包含.shp、.shx、.dbf、.prj等文件
- ✅ **正确的字段结构**: 包含所有必需的属性字段
- ✅ **有效的坐标系**: 使用与输入数据一致的坐标系
- ✅ **零要素数量**: 不包含任何几何要素

#### **字段结构示例**
```
AI分析结果空文件:
- class: Real (分类值)
- area: Real (面积)

空间变化分析空文件:
- area: Real (面积)
- flowtype: String (流向类型: "in"/"out")
- change_type: String (变化类型: "inflow"/"outflow")
```

### 处理流程

#### **AI语义分割**
1. **模型推理**: 执行深度学习模型推理
2. **结果检查**: 检查是否有非背景类像素
3. **矢量转换**: 即使无有效像素也执行栅格到矢量转换
4. **空文件创建**: 创建包含正确字段结构的空SHP文件

#### **空间变化分析**
1. **数据对比**: 执行新老数据的空间对比分析
2. **变化检测**: 计算流入流出区域
3. **阈值过滤**: 应用面积阈值过滤
4. **结果输出**: 即使无变化也创建空的结果文件

### 技术实现

#### **核心修改**
```python
# AI分析 - 栅格到矢量转换
def raster2vector(raster_path, vector_path, field_name="class", ignore_values=None):
    # 检查栅格数据是否有有效值
    has_valid_data = check_valid_data(band_array)

    if has_valid_data:
        # 执行正常的矢量转换
        gdal.FPolygonize(band, None, poly_layer, 0)
    else:
        # 创建空文件但保持字段结构
        print("⚠️ 创建空的SHP文件")

    # 无论是否有数据都保存文件
    polygon.SyncToDisk()

# 空间分析 - 合并结果输出
def _write_combined_shapefile(self, features, output_path):
    # 创建数据源和图层结构
    create_layer_structure()

    if not features:
        # 创建空文件
        analysis_logger.warning("⚠️ 没有变化数据，创建空的Shapefile")
    else:
        # 写入要素数据
        write_features()

    # 保存文件
    datasource.SyncToDisk()
```

### 使用优势

1. **流程完整性**: 确保分析流程不会因为空结果而中断
2. **数据一致性**: 保持输出文件的结构和格式一致
3. **GIS兼容性**: 空SHP文件可以在所有GIS软件中正常打开
4. **后续处理**: 支持进一步的数据处理和分析操作
5. **自动化友好**: 适合自动化批处理流程

### 日志示例

#### **AI分析空结果**
```
⚠️ 栅格数据中没有有效的分类结果（只有背景类），创建空的SHP文件
✅ 空的矢量文件已保存到: output.shp
   这是一个有效的SHP文件，只是不包含任何要素
```

#### **空间分析无变化**
```
⚠️ 没有变化数据，创建空的Shapefile
✅ 空的合并数据文件已保存到: changes.shp
   这是一个有效的SHP文件，只是不包含任何要素
```

### 验证方法

#### **文件验证**
```python
# 使用GDAL/OGR验证空SHP文件
datasource = ogr.Open("empty_result.shp")
layer = datasource.GetLayer()
feature_count = layer.GetFeatureCount()  # 应该为0
field_count = layer.GetLayerDefn().GetFieldCount()  # 应该>0
```

#### **GIS软件验证**
- **QGIS**: 可以正常加载，显示为空图层
- **ArcGIS**: 可以正常打开，属性表为空
- **其他GIS软件**: 兼容性良好

### 注意事项

1. **文件大小**: 空SHP文件仍有基本的文件头信息，不是0字节
2. **坐标系**: 保持与输入数据相同的坐标系信息
3. **字段类型**: 字段定义与有数据的文件完全一致
4. **索引文件**: .shx文件正确记录0个要素的索引信息
