# 地理空间分析API文档

## 概述

地理空间分析API是一个功能强大的遥感数据分析平台，集成了AI语义分割、空间变化分析、权重管理等多种功能，为地理空间数据处理提供完整的解决方案。

## 功能模块概览

### 🔍 核心功能
- **空间数据变化分析**：分析两个时期空间数据的变化，计算流入流出区域
- **AI语义分割**：使用深度学习模型对遥感图像进行语义分割，直接输出Shapefile
- **合并分析**：将AI语义分割和空间变化分析合并为一个完整流程
- **权重信息管理**：管理AI模型权重文件，按地物类型分类
- **任务状态监控**：异步任务执行，支持状态查询和日志监控

### 📋 接口列表

| 接口路径 | 方法 | 功能描述 |
|---------|------|----------|
| `/api/analysis/spatial-changes/` | GET | 空间数据变化分析 |
| `/api/analysis/ai-semantic-segmentation/` | GET | AI语义分割 |
| `/api/analysis/ai-batch-processing/` | GET | AI批量处理 |
| `/api/analysis/combined-ai-spatial-analysis/` | GET | 合并AI分析和空间变化分析 |
| `/api/analysis/weight-info/` | GET | 获取AI模型权重信息 |
| `/api/analysis/weight-config/` | GET | 获取权重配置信息 |
| `/api/analysis/ai-models-info/` | GET | 获取AI模型信息 |
| `/api/analysis/status/` | GET | 查询分析任务状态 |
| `/api/analysis/logs/` | GET | 获取分析日志 |

### 🤖 支持的AI模型
- **DeepLabV3+**：经典语义分割模型及其改进版
- **U-Net系列**：UNet、UNet++、L-UNet、UNet-GAI
- **Transformer系列**：SegFormer、SegNeXt、Vision Transformer
- **其他模型**：共支持13种主流深度学习模型

### 🌍 支持的地物类型
- **耕地（arableLand）**：农田、耕地区域识别
- **建设用地（constructionLand）**：建筑物、道路等人工建设区域
- **其他地物类型**：可根据配置扩展

---

## 详细接口说明

### 1. 空间数据变化分析

**接口地址**: `GET /api/analysis/spatial-changes/`

**功能说明**: 分析两个时期空间数据的变化，计算流入流出区域。自动计算新数据覆盖区域，裁剪老数据进行对比分析，支持自定义面积阈值过滤，异步任务执行，输出单个shapefile文件，使用flowtype属性区分流入和流出。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| old_data_path | string | 是 | 老数据Shapefile路径 | D:/data/farmland_2024.shp |
| new_data_path | string | 是 | 新数据Shapefile路径 | D:/data/farmland_2025.shp |
| area_threshold | number | 否 | 面积阈值(平方米)，默认200 | 400.0 |
| output_dir | string | 否 | 输出目录，默认为新数据根目录 | D:/output/ |
| shp_filename | string | 否 | 输出文件名，默认changes_shapefile | farmland_changes |

**输出结果**:

输出的shapefile文件包含以下字段：
- **area**: 图斑面积(平方米)
- **flowtype**: 变化类型("in"=流入，"out"=流出)
- **change_type**: 变化描述("inflow"或"outflow")
- **其他字段**: 保留原始数据的属性字段

**返回数据**:
```json
{
  "status": "success",
  "message": "空间数据变化分析任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "queued",
    "message": "任务已排队，等待执行..."
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/spatial-changes/?old_data_path=D:/data/farmland_2024.shp&new_data_path=D:/data/farmland_2025.shp&area_threshold=400.0
```

### 2. AI语义分割

**接口地址**: `GET /api/analysis/ai-semantic-segmentation/`

**功能说明**: 使用深度学习模型对遥感图像进行语义分割，直接输出Shapefile矢量文件。支持13种主流深度学习模型，支持GPU加速推理，支持大图像滑窗处理。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| image | string | 是 | 输入遥感图像路径 | D:/data/satellite.tif |
| model | string | 是 | 模型权重文件路径 | D:/models/farmland_model.pth |
| output | string | 否 | 输出Shapefile路径，默认自动生成 | D:/results/result.shp |
| model_type | string | 否 | 模型类型，默认segnext | deeplabv3_plus |
| target_classes | string | 否 | 目标类别，如"1,2,3" | 1,2 |
| window_size | integer | 否 | 滑动窗口大小，默认512 | 512 |
| batch_size | integer | 否 | 批处理大小，默认16 | 16 |
| simplify | float | 否 | 简化阈值，默认0.0 | 1.0 |
| min_area | float | 否 | 最小面积阈值，默认0.0 | 100.0 |
| num_classes | integer | 否 | 类别数量，默认2 | 3 |

**支持的模型类型**:
- **deeplabv3_plus**: DeepLabV3+ 经典语义分割模型
- **unet**: U-Net 医学图像分割经典模型
- **segformer**: SegFormer Transformer语义分割模型
- **segnext**: SegNeXt 下一代语义分割模型
- **unetplusplus**: UNet++ 嵌套U-Net架构
- **其他**: lunet、unetgai、vision_transformer等

**输出结果**:
生成标准的Shapefile文件，包含分割后的矢量多边形，每个多边形包含类别信息和几何属性。

**返回数据**:
```json
{
  "status": "success",
  "message": "AI语义分割任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "log_file": "D:/logs/550e8400-e29b-41d4-a716-446655440000.log"
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/ai-semantic-segmentation/?image=D:/data/satellite.tif&model=D:/models/farmland_model.pth&model_type=deeplabv3_plus&target_classes=1,2&window_size=512
```

### 3. AI批量处理

**接口地址**: `GET /api/analysis/ai-batch-processing/`

**功能说明**: 批量处理目录下的所有遥感图像，使用AI模型进行语义分割。自动扫描输入目录中的所有图像文件，逐一进行AI分析处理。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| input_dir | string | 是 | 输入图像目录 | D:/data/images/ |
| output_dir | string | 是 | 输出目录 | D:/results/ |
| model | string | 是 | 模型权重文件路径 | D:/models/model.pth |
| model_type | string | 否 | 模型类型，默认segnext | deeplabv3_plus |
| target_classes | string | 否 | 目标类别，如"1,2,3" | 1,2 |
| window_size | integer | 否 | 滑动窗口大小，默认512 | 512 |
| batch_size | integer | 否 | 批处理大小，默认16 | 16 |
| num_classes | integer | 否 | 类别数量，默认2 | 3 |

**输出结果**:
为每个输入图像生成对应的Shapefile文件，保存在指定的输出目录中。

**返回数据**:
```json
{
  "status": "success",
  "message": "AI批量处理任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "input_dir": "D:/data/images/",
    "output_dir": "D:/results/",
    "estimated_files": 15
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/ai-batch-processing/?input_dir=D:/data/images/&output_dir=D:/results/&model=D:/models/model.pth&model_type=deeplabv3_plus
```

### 4. 合并AI分析和空间变化分析

**接口地址**: `GET /api/analysis/combined-ai-spatial-analysis/`

**功能说明**: 将AI语义分割和空间变化分析合并为一个统一的接口，提供完整的影像分析解决方案。首先对输入TIF图像进行AI预测生成SHP结果，然后将AI结果与历史SHP数据进行空间变化分析。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 影像ID | 20250705171601 |
| image | string | 是 | TIF图像路径 | D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601_out.tif |
| model | string | 是 | 权重路径 | D:\Drone_Project\nginxData\ODM\AIWeight\arableLand\deeplabv3_plus\deeplabv3_plus_best_20250807-111949.pth |
| old_data_path | string | 是 | 历史SHP路径 | D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp |
| area_threshold | float | 否 | 面积阈值（平方米），默认400 | 400 |
| model_type | string | 否 | 模型类型，默认deeplabv3_plus | deeplabv3_plus |
| num_classes | int | 否 | 类别数量，默认2 | 2 |

**输出结果**:
生成完整的分析结果文件结构，包括AI分析结果、最终变化分析结果和任务信息JSON文件。

**返回数据**:
```json
{
  "status": "success",
  "message": "合并分析任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "等待中",
    "message": "任务已创建，等待执行",
    "image_id": "20250705171601",
    "estimated_time": "预计15-20分钟完成"
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/combined-ai-spatial-analysis/?id=20250705171601&image=D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601_out.tif&model=D:\Drone_Project\nginxData\ODM\AIWeight\arableLand\deeplabv3_plus\deeplabv3_plus_best_20250807-111949.pth&old_data_path=D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp&area_threshold=400
```

### 5. 获取AI模型权重信息

**接口地址**: `GET /api/analysis/weight-info/`

**功能说明**: 获取AI模型权重信息，按地物类型分类返回所有可用的权重文件。自动扫描权重目录结构，按地物类型和模型类型分类组织，提供完整的文件路径和HTTP访问URL。

**输入参数**: 无

**输出结果**:
返回按地物类型分类的权重信息，包含默认权重配置、可用模型类型、权重文件列表和SHP文件信息。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取权重信息",
  "data": {
    "arableLand": {
      "default": "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
      "models": {
        "deeplabv3_plus": [
          {
            "name": "deeplabv3_plus_best_20250807-111949.pth",
            "path": "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
            "url": "http://127.0.0.1:81/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
          }
        ]
      },
      "shp_files": [
        "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
      ]
    },
    "constructionLand": {
      "default": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth",
      "models": {
        "deeplabv3_plus": [
          {
            "name": "deeplabv3_plus_best_20250811-162215.pth",
            "path": "D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth"
          }
        ]
      }
    }
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/weight-info/
```

### 6. 获取权重配置信息

**接口地址**: `GET /api/analysis/weight-config/`

**功能说明**: 获取权重配置信息，返回配置文件中的基础配置，包括数据路径和默认权重设置。

**输入参数**: 无

**输出结果**:
返回配置文件中的基础配置信息，包括数据路径和各地物类型的默认权重。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取配置信息",
  "data": {
    "window_data_path": "D:/Drone_Project/nginxData",
    "default_weights": {
      "arableLand": "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
      "constructionLand": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth"
    }
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/weight-config/
```

### 7. 获取AI模型信息

**接口地址**: `GET /api/analysis/ai-models-info/`

**功能说明**: 获取系统支持的AI模型信息，返回所有可用的深度学习模型类型和描述。

**输入参数**: 无

**输出结果**:
返回系统支持的所有AI模型类型列表和相应的描述信息。

**返回数据**:
```json
{
  "status": "success",
  "message": "获取AI模型信息成功",
  "data": {
    "supported_models": [
      {
        "model_type": "deeplabv3_plus",
        "description": "DeepLabV3+ - 经典语义分割模型"
      },
      {
        "model_type": "unet",
        "description": "U-Net - 医学图像分割经典模型"
      },
      {
        "model_type": "segformer",
        "description": "SegFormer - Transformer语义分割模型"
      }
    ],
    "total_count": 13
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/ai-models-info/
```

### 8. 查询分析任务状态

**接口地址**: `GET /api/analysis/status/`

**功能说明**: 查询分析任务的执行状态和结果，支持查询所有类型分析任务的状态信息。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| task_id | string | 是 | 任务ID | 550e8400-e29b-41d4-a716-446655440000 |

**输出结果**:
返回任务的详细状态信息，包括执行进度、结果统计、输出文件等。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取任务状态",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "completed",
    "message": "分析任务完成",
    "start_time": "2025-08-12T10:30:00",
    "end_time": "2025-08-12T10:35:00",
    "progress": 100,
    "result": {
      "success": true,
      "message": "分析完成",
      "statistics": {
        "outflow_count": 15,
        "inflow_count": 8,
        "total_count": 23,
        "outflow_area": 5420.5,
        "inflow_area": 3280.2
      },
      "output_files": {
        "combined_shapefile": "D:/data/changes_shapefile.shp"
      }
    }
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/status/?task_id=550e8400-e29b-41d4-a716-446655440000
```

### 9. 获取分析日志

**接口地址**: `GET /api/analysis/logs/`

**功能说明**: 获取分析任务的执行日志，支持按任务ID过滤和限制返回行数。

**输入参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| lines | number | 否 | 返回的日志行数，默认全部 | 200 |
| task_id | string | 否 | 获取特定任务的日志 | 550e8400-e29b-41d4-a716-446655440000 |
| log_type | string | 否 | 日志类型：main或task，默认task | task |

**输出结果**:
返回指定任务或全局的日志信息，包含详细的执行过程记录。

**返回数据**:
```json
{
  "status": "success",
  "message": "成功获取分析日志",
  "data": {
    "log_file": "/path/to/analysis.log",
    "total_lines": 1500,
    "returned_lines": 100,
    "task_id_filter": "550e8400-e29b-41d4-a716-446655440000",
    "logs": [
      "2025-08-12 10:30:00 - analysis_executor - INFO - 开始分析任务",
      "2025-08-12 10:30:01 - analysis_executor - INFO - 读取数据文件",
      "2025-08-12 10:30:02 - analysis_executor - INFO - 执行空间分析"
    ]
  }
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/analysis/logs/?task_id=550e8400-e29b-41d4-a716-446655440000&lines=100
```

---

## 技术说明

### 分析算法原理

**空间变化分析算法**:
1. 数据预处理：读取新旧两期Shapefile数据，验证数据完整性和坐标系
2. 分析区域确定：计算新数据的凸包边界，将老数据裁剪到该边界内
3. 流出计算：合并新数据几何体，对每个老数据图斑计算差集
4. 流入计算：合并老数据几何体，对每个新数据图斑计算差集
5. 结果输出：生成包含flowtype属性的合并Shapefile文件

**AI语义分割算法**:
1. 模型加载：动态加载指定的深度学习模型权重
2. 图像预处理：对输入遥感图像进行标准化和分块处理
3. 滑窗推理：使用滑动窗口对大图像进行分块预测
4. 结果融合：将分块预测结果进行拼接和融合
5. 矢量化：将预测结果转换为Shapefile矢量格式

### 错误处理

| 错误类型 | HTTP状态码 | 说明 |
|----------|------------|------|
| 参数缺失 | 400 | 缺少必需的请求参数 |
| 文件不存在 | 404 | 指定的文件不存在 |
| 文件格式错误 | 400 | 不是有效的文件格式 |
| 坐标系不匹配 | 400 | 数据集的坐标系不一致 |
| 任务不存在 | 404 | 指定的任务ID不存在 |
| 内存不足 | 500 | 数据量过大导致内存不足 |
| AI模型错误 | 500 | AI模型加载或推理失败 |

### 系统要求

**技术依赖**:
- Python 3.8+
- GDAL 3.0+
- PyTorch 1.8+（AI功能）
- CUDA 11.0+（GPU加速，可选）

**数据要求**:
- Shapefile格式：标准ESRI Shapefile，包含.shp、.shx、.dbf等文件
- 遥感图像：支持GeoTIFF格式，建议使用投影坐标系
- 坐标系：建议新旧数据使用相同的投影坐标系，确保面积计算准确
- 编码格式：建议使用UTF-8编码

**性能建议**:
- 单次分析建议数据量不超过500MB
- AI分析建议使用GPU加速，提高处理速度
- 大数据量分析建议分块处理
- 及时清理临时文件和中间结果

### 注意事项

1. **数据准备**：确保输入数据的完整性和格式正确性
2. **坐标系统一**：新旧数据必须使用相同的坐标系
3. **面积阈值**：根据实际需求设置合适的面积阈值
4. **任务监控**：大数据量分析可能需要较长时间，建议使用状态查询接口监控进度
5. **结果备份**：分析结果文件会定期清理，请及时备份重要结果

### 输出文件结构

**空间变化分析输出**:
```
output_dir/
├── changes_shapefile.shp    # 合并的变化结果
├── changes_shapefile.shx
├── changes_shapefile.dbf    # 包含flowtype字段
└── changes_shapefile.prj
```

**AI分析输出**:
```
output_dir/
├── result.shp              # AI分割结果
├── result.shx
├── result.dbf
└── result.prj
```

**合并分析输出**:
```
{window_data_path}/ODM/AI/{影像ID}/
├── {分析类别}/
│   ├── {影像ID}_1_{时间戳}.shp    # AI分析结果
│   └── {影像ID}_2_{时间戳}.shp    # 最终分析结果
└── TaskInfo.json                   # 任务信息文件
```
