#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地理空间分析API视图

功能说明:
- 空间数据变化分析接口
- 空间数据处理和分析
- 结果文件生成和下载
- 异步任务执行和状态监控

接口列表:
- GET /api/analysis/spatial-changes/ - 空间数据变化分析
- GET /api/analysis/status/<task_id>/ - 查询分析任务状态
- GET /api/analysis/download/<file_id>/ - 下载分析结果文件
- GET /api/analysis/logs/ - 获取分析日志
"""

import os
import json
import logging
import requests
import zipfile
import tempfile
import time
from pathlib import Path
from urllib.parse import urlparse
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status
from django.http import JsonResponse, HttpResponse, FileResponse

# 导入核心模块
from ...core.analysis_executor import analysis_executor, analysis_logger

# 尝试导入合并分析执行器
try:
    from ...core.combined_analysis_executor import combined_analysis_executor
    COMBINED_EXECUTOR_AVAILABLE = True
except ImportError:
    COMBINED_EXECUTOR_AVAILABLE = False

@api_view(['GET'])
def spatial_changes_analysis(request):
    """
    空间数据变化分析接口

    功能: 分析两个时期空间数据的变化，生成流入流出数据

    查询参数:
        old_data_path: 老数据路径 (必选)
        new_data_path: 新数据路径 (必选)
        area_threshold: 面积阈值，单位平方米 (可选，默认200)
        output_dir: 输出目录 (可选，默认为新数据根目录)
        shp_filename: 输出文件名 (可选，默认changes_shapefile)
        clip_area_path: 裁剪范围Shapefile路径 (可选)

    返回: 分析任务ID和初始状态
    """
    try:
        # 获取参数
        old_data_path = request.GET.get("old_data_path")
        new_data_path = request.GET.get("new_data_path")
        area_threshold = float(request.GET.get("area_threshold", 200.0))
        output_dir = request.GET.get("output_dir")  # 可选，None表示使用默认
        shp_filename = request.GET.get("shp_filename", "changes_shapefile")
        clip_area_path = request.GET.get("clip_area_path")  # 可选，裁剪范围

        # 参数验证
        if not old_data_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: old_data_path'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not new_data_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: new_data_path'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件是否存在
        if not os.path.exists(old_data_path):
            return Response({
                'status': 'error',
                'message': f'老数据文件不存在: {old_data_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        if not os.path.exists(new_data_path):
            return Response({
                'status': 'error',
                'message': f'新数据文件不存在: {new_data_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        analysis_logger.info(f"收到空间数据变化分析请求")
        analysis_logger.info(f"老数据: {old_data_path}")
        analysis_logger.info(f"新数据: {new_data_path}")
        analysis_logger.info(f"面积阈值: {area_threshold} 平方米")
        if clip_area_path:
            analysis_logger.info(f"裁剪范围: {clip_area_path}")
        else:
            analysis_logger.info("裁剪范围: 使用新数据覆盖区域")

        # 执行分析任务
        result = analysis_executor.execute_spatial_changes_analysis(
            old_data_path=old_data_path,
            new_data_path=new_data_path,
            area_threshold=area_threshold,
            output_dir=output_dir,
            shp_filename=shp_filename,
            clip_area_path=clip_area_path
        )

        if result['success']:
            return Response({
                'status': 'success',
                'message': '空间数据变化分析任务已启动',
                'data': {
                    'task_id': result['task_id'],
                    'task_status': result['status'],
                    'message': result['message']
                }
            })
        else:
            return Response({
                'status': 'error',
                'message': result['message'],
                'error': result.get('error')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    except Exception as e:
        analysis_logger.error(f"耕地流入流出分析API错误: {str(e)}")
        import traceback
        analysis_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_analysis_status(request):
    """
    查询分析任务状态

    查询参数:
        task_id: 任务ID

    返回: 任务状态信息
    """
    try:
        task_id = request.GET.get('task_id')

        if not task_id:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: task_id'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 首先尝试从普通分析执行器获取任务状态
        task_info = analysis_executor.get_task_status(task_id)

        # 如果普通分析执行器中没有找到，尝试从合并分析执行器获取
        if not task_info and COMBINED_EXECUTOR_AVAILABLE:
            combined_result = combined_analysis_executor.get_task_status(task_id)
            if combined_result['success']:
                task_info = combined_result['data']

        if not task_info:
            return Response({
                'status': 'error',
                'message': f'任务不存在: {task_id}'
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'status': 'success',
            'message': '成功获取任务状态',
            'data': task_info
        })

    except Exception as e:
        analysis_logger.error(f"查询任务状态API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





@api_view(['GET'])
def get_analysis_logs(request):
    """
    获取分析日志

    查询参数:
        lines: 返回的日志行数，默认全部
        task_id: 获取特定任务的日志 (可选)
        log_type: 日志类型，main(主日志)或task(任务日志)，默认task

    返回: 日志内容
    """
    try:
        lines_param = request.GET.get('lines')
        lines = int(lines_param) if lines_param else None  # None表示全部
        task_id = request.GET.get('task_id')
        log_type = request.GET.get('log_type', 'task')

        # 确定日志目录
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs', 'analysislog')

        if not os.path.exists(logs_dir):
            return Response({
                'status': 'error',
                'message': '分析日志目录不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 根据参数确定要读取的日志文件
        if task_id and log_type == 'task':
            # 读取特定任务的日志
            log_file = os.path.join(logs_dir, f"{task_id}.log")

            if not os.path.exists(log_file):
                return Response({
                    'status': 'error',
                    'message': f'任务 {task_id} 的日志文件不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 读取任务日志
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()

            # 获取指定行数或全部
            if lines is None:
                recent_lines = all_lines
            else:
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

            return Response({
                'status': 'success',
                'message': f'成功获取任务 {task_id} 的日志',
                'data': {
                    'log_file': log_file,
                    'log_type': 'task',
                    'task_id': task_id,
                    'total_lines': len(all_lines),
                    'returned_lines': len(recent_lines),
                    'logs': recent_lines
                }
            })

        else:
            # 读取主执行器日志
            log_file = os.path.join(logs_dir, 'analysis_executor.log')

            if not os.path.exists(log_file):
                return Response({
                    'status': 'error',
                    'message': '主日志文件不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 读取主日志
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()

            # 获取指定行数或全部
            if lines is None:
                recent_lines = all_lines
            else:
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

            # 如果指定了task_id，过滤相关日志
            if task_id:
                filtered_lines = [line for line in recent_lines if task_id in line]
                recent_lines = filtered_lines

            return Response({
                'status': 'success',
                'message': '成功获取分析主日志',
                'data': {
                    'log_file': log_file,
                    'log_type': 'main',
                    'task_id_filter': task_id,
                    'total_lines': len(all_lines),
                    'returned_lines': len(recent_lines),
                    'logs': recent_lines
                }
            })

    except Exception as e:
        analysis_logger.error(f"获取分析日志API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _get_taskinfo_file_path(image_id):
    """
    获取TaskInfo.json文件路径

    参数:
        image_id (str): 影像ID

    返回:
        dict: {
            'success': bool,
            'path': str,
            'message': str
        }
    """
    try:
        # 从ODM配置获取window_data_path
        config_result = _get_odm_config()

        if not config_result['success']:
            return {
                'success': False,
                'path': '',
                'message': f'获取ODM配置失败: {config_result["message"]}'
            }

        window_data_path = config_result['config'].get('PATHS', {}).get('window_data_path')

        if not window_data_path:
            # 使用默认路径
            window_data_path = 'D:/Drone_Project/nginxData'
            analysis_logger.warning(f"使用默认window_data_path: {window_data_path}")

        # 构建TaskInfo.json路径
        taskinfo_path = os.path.join(window_data_path, 'ODM', 'AI', image_id, 'TaskInfo.json')

        return {
            'success': True,
            'path': taskinfo_path,
            'message': 'TaskInfo路径构建成功'
        }

    except Exception as e:
        return {
            'success': False,
            'path': '',
            'message': f'构建TaskInfo路径失败: {str(e)}'
        }


def _get_odm_config():
    """
    获取ODM配置文件

    返回:
        dict: {
            'success': bool,
            'config': dict,
            'message': str
        }
    """
    try:
        # 从ODM服务器获取配置文件
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"

        response = requests.get(config_url, timeout=10)
        response.raise_for_status()

        # 解析配置文件内容
        config_content = response.text
        config = {}
        current_section = None

        for line in config_content.splitlines():
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            # 解析节名
            if line.startswith("[") and line.endswith("]"):
                current_section = line[1:-1]
                config[current_section] = {}
                continue

            # 解析键值对
            if "=" in line and current_section:
                key, value = line.split("=", 1)
                config[current_section][key.strip()] = value.strip()

        return {
            'success': True,
            'config': config,
            'message': '配置文件读取成功'
        }

    except requests.RequestException as e:
        return {
            'success': False,
            'config': {},
            'message': f'无法读取ODM配置文件: {str(e)}'
        }
    except Exception as e:
        return {
            'success': False,
            'config': {},
            'message': f'解析ODM配置文件失败: {str(e)}'
        }


@api_view(['GET'])
def get_taskinfo_by_id(request):
    """
    根据ID获取TaskInfo.json内容

    参数:
        id (str): 影像ID，如 20250705171601

    返回格式:
    {
        "status": "success",
        "message": "成功获取TaskInfo信息",
        "data": [
            {
                "task_id": "uuid",
                "image_id": "20250705171601",
                "analysis_category": "arableLand",
                "status": "完成",
                "parameters": {...},
                "results": {...}
            }
        ]
    }

    如果文件不存在，返回空列表:
    {
        "status": "success",
        "message": "TaskInfo文件不存在",
        "data": []
    }
    """
    try:
        # 获取参数
        image_id = request.GET.get('id')

        if not image_id:
            analysis_logger.warning("获取TaskInfo请求缺少id参数")
            return Response({
                'status': 'error',
                'message': '缺少必需参数: id',
                'data': []
            }, status=status.HTTP_400_BAD_REQUEST)

        analysis_logger.info(f"获取TaskInfo请求: image_id={image_id}")

        # 获取TaskInfo.json文件路径
        taskinfo_result = _get_taskinfo_file_path(image_id)

        if not taskinfo_result['success']:
            analysis_logger.warning(f"获取TaskInfo文件路径失败: {taskinfo_result['message']}")
            return Response({
                'status': 'success',
                'message': taskinfo_result['message'],
                'data': []
            }, status=status.HTTP_200_OK)

        taskinfo_path = taskinfo_result['path']
        analysis_logger.info(f"TaskInfo文件路径: {taskinfo_path}")

        # 检查文件是否存在
        if not os.path.exists(taskinfo_path):
            analysis_logger.info(f"TaskInfo文件不存在: {taskinfo_path}")
            return Response({
                'status': 'success',
                'message': f'TaskInfo文件不存在: {image_id}',
                'data': []
            }, status=status.HTTP_200_OK)

        # 读取TaskInfo.json文件
        try:
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                taskinfo_data = json.load(f)

            # 确保返回列表格式
            if isinstance(taskinfo_data, dict):
                taskinfo_data = [taskinfo_data]
            elif not isinstance(taskinfo_data, list):
                taskinfo_data = []

            analysis_logger.info(f"成功读取TaskInfo文件，包含 {len(taskinfo_data)} 个任务")

            return Response({
                'status': 'success',
                'message': f'成功获取TaskInfo信息，共 {len(taskinfo_data)} 个任务',
                'data': taskinfo_data
            }, status=status.HTTP_200_OK)

        except json.JSONDecodeError as e:
            analysis_logger.error(f"TaskInfo文件JSON格式错误: {str(e)}")
            return Response({
                'status': 'error',
                'message': f'TaskInfo文件格式错误: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            analysis_logger.error(f"读取TaskInfo文件失败: {str(e)}")
            return Response({
                'status': 'error',
                'message': f'读取TaskInfo文件失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        analysis_logger.error(f"获取TaskInfo API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def download_analysis_data(request):
    """
    下载分析数据接口

    功能: 批量下载SHP文件并打包为ZIP文件

    请求体参数:
        file_paths: 文件路径列表 (必选)

    示例请求体:
    {
        "file_paths": [
            "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp",
            "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp"
        ]
    }

    返回: ZIP文件下载
    """
    try:
        # 获取请求参数
        data = request.data
        file_paths = data.get('file_paths', [])

        if not file_paths:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: file_paths'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not isinstance(file_paths, list):
            return Response({
                'status': 'error',
                'message': 'file_paths必须是数组格式'
            }, status=status.HTTP_400_BAD_REQUEST)

        analysis_logger.info(f"开始处理数据下载请求，文件数量: {len(file_paths)}")

        # 第一步：重新拼接地址
        converted_urls = []
        for file_path in file_paths:
            converted_url = _convert_path_to_url(file_path)
            if converted_url:
                converted_urls.append({
                    'original_path': file_path,
                    'url': converted_url,
                    'filename': os.path.basename(file_path)
                })
                analysis_logger.info(f"路径转换: {file_path} -> {converted_url}")
            else:
                analysis_logger.warning(f"路径转换失败: {file_path}")

        if not converted_urls:
            return Response({
                'status': 'error',
                'message': '没有有效的文件路径'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 第二步：下载并打包文件
        timestamp = str(int(time.time()))
        zip_filename = f"analysis_data_{timestamp}.zip"

        # 创建临时ZIP文件
        temp_zip_path = _create_data_package(converted_urls, timestamp)

        if not temp_zip_path or not os.path.exists(temp_zip_path):
            return Response({
                'status': 'error',
                'message': '数据打包失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        analysis_logger.info(f"数据打包完成: {temp_zip_path}")

        # 第三步：返回ZIP文件
        try:
            response = FileResponse(
                open(temp_zip_path, 'rb'),
                as_attachment=True,
                filename=zip_filename,
                content_type='application/zip'
            )

            # 设置响应头
            response['Content-Length'] = os.path.getsize(temp_zip_path)
            response['Content-Disposition'] = f'attachment; filename="{zip_filename}"'

            analysis_logger.info(f"开始下载文件: {zip_filename}")
            return response

        except Exception as e:
            analysis_logger.error(f"文件下载失败: {str(e)}")
            return Response({
                'status': 'error',
                'message': f'文件下载失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        analysis_logger.error(f"下载数据接口异常: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _convert_path_to_url(file_path):
    """
    将本地文件路径转换为HTTP URL

    参数:
        file_path: 本地文件路径

    返回:
        str: HTTP URL或None
    """
    try:
        # 标准化路径
        normalized_path = os.path.normpath(file_path).replace('\\', '/')

        # 查找ODM目录的位置
        odm_index = normalized_path.find('/ODM/')
        if odm_index == -1:
            # 尝试查找ODM目录（不区分大小写）
            odm_index = normalized_path.lower().find('/odm/')
            if odm_index == -1:
                analysis_logger.warning(f"路径中未找到ODM目录: {file_path}")
                return None

        # 提取ODM之后的路径
        relative_path = normalized_path[odm_index + 1:]  # +1 是为了包含ODM

        # 构建HTTP URL
        base_url = "http://127.0.0.1:81"
        full_url = f"{base_url}/{relative_path}"

        return full_url

    except Exception as e:
        analysis_logger.error(f"路径转换异常: {file_path}, 错误: {str(e)}")
        return None


def _create_data_package(converted_urls, timestamp):
    """
    创建数据包

    参数:
        converted_urls: 转换后的URL列表
        timestamp: 时间戳

    返回:
        str: 临时ZIP文件路径
    """
    try:
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, f"analysis_data_{timestamp}.zip")

        analysis_logger.info(f"创建数据包: {zip_path}")

        # 收集下载信息用于生成readme
        download_info = []

        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # 创建以时间戳命名的文件夹
            folder_name = f"analysis_data_{timestamp}"

            for url_info in converted_urls:
                url = url_info['url']
                original_path = url_info['original_path']
                original_filename = url_info['filename']

                try:
                    # 下载SHP文件及其相关文件
                    shp_files = _download_shapefile_complete(url, folder_name, zipf)

                    if shp_files:
                        analysis_logger.info(f"成功下载SHP文件组: {original_filename} ({len(shp_files)} 个文件)")

                        # 收集下载信息
                        download_info.append({
                            'original_path': original_path,
                            'filename': original_filename,
                            'files': shp_files,
                            'status': 'success'
                        })
                    else:
                        analysis_logger.warning(f"SHP文件下载失败: {url}")
                        download_info.append({
                            'original_path': original_path,
                            'filename': original_filename,
                            'files': [],
                            'status': 'failed'
                        })

                except Exception as e:
                    analysis_logger.error(f"下载文件失败: {url}, 错误: {str(e)}")
                    download_info.append({
                        'original_path': original_path,
                        'filename': original_filename,
                        'files': [],
                        'status': 'error',
                        'error': str(e)
                    })
                    continue

            # 生成readme.txt文件（包含详细任务信息）
            readme_content = _generate_readme_content(download_info, timestamp)
            zipf.writestr(f"{folder_name}/readme.txt", readme_content)
            analysis_logger.info(f"已添加readme.txt文件到压缩包（包含任务详细信息）")

        return zip_path

    except Exception as e:
        analysis_logger.error(f"创建数据包失败: {str(e)}")
        return None


def _generate_readme_content(download_info, timestamp):
    """
    生成readme.txt文件内容（包含详细任务信息）

    参数:
        download_info: 下载信息列表
        timestamp: 时间戳

    返回:
        str: readme.txt文件内容
    """
    from datetime import datetime

    # 生成时间信息
    download_time = datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')

    readme_lines = []
    readme_lines.append("=" * 80)
    readme_lines.append("                    分析数据下载包说明文档")
    readme_lines.append("=" * 80)
    readme_lines.append("")
    readme_lines.append(f"下载时间: {download_time}")
    readme_lines.append(f"数据包名称: analysis_data_{timestamp}")
    readme_lines.append(f"文件总数: {len(download_info)}")
    readme_lines.append("")

    # 统计信息
    success_count = len([info for info in download_info if info['status'] == 'success'])
    failed_count = len([info for info in download_info if info['status'] in ['failed', 'error']])

    readme_lines.append("[STATS] 下载统计:")
    readme_lines.append(f"  [SUCCESS] 成功下载: {success_count} 个文件")
    readme_lines.append(f"  [ERROR] 下载失败: {failed_count} 个文件")
    readme_lines.append("")

    # 获取任务详细信息
    task_details = _get_task_details_from_files(download_info)

    # 任务概览
    if task_details:
        readme_lines.append("[TARGET] 任务概览:")
        unique_tasks = {}
        for detail in task_details:
            image_id = detail.get('image_id')
            if image_id and image_id not in unique_tasks:
                unique_tasks[image_id] = detail

        for image_id, detail in unique_tasks.items():
            readme_lines.append(f"  [QUEUE] 影像ID: {image_id}")
            if detail.get('task_type'):
                readme_lines.append(f"     任务类型: {detail['task_type']}")
            if detail.get('ai_model'):
                readme_lines.append(f"     AI模型: {detail['ai_model']}")
            if detail.get('comparison_layer'):
                readme_lines.append(f"     对比图层: {detail['comparison_layer']}")
            if detail.get('analysis_category'):
                readme_lines.append(f"     分析类别: {detail['analysis_category']}")
            readme_lines.append("")

    # 数据说明
    readme_lines.append("[QUEUE] 数据说明:")
    readme_lines.append("  本压缩包包含AI分析和空间变化分析的结果数据")
    readme_lines.append("  每个SHP文件都包含完整的文件组(.shp, .shx, .dbf, .prj等)")
    readme_lines.append("  文件命名规则: {影像ID}_{类型}_{时间戳}.shp")
    readme_lines.append("    - 类型1: AI分析结果")
    readme_lines.append("    - 类型2: 空间变化分析结果")
    readme_lines.append("")

    # 详细文件列表
    readme_lines.append("[FOLDER] 文件详细信息:")
    readme_lines.append("-" * 80)

    for i, info in enumerate(download_info, 1):
        readme_lines.append(f"\n{i}. 文件: {info['filename']}")
        readme_lines.append(f"   原始路径: {info['original_path']}")

        # 解析文件名获取任务信息
        task_info = _parse_filename_info(info['filename'])
        if task_info:
            readme_lines.append(f"   影像ID: {task_info['image_id']}")
            readme_lines.append(f"   分析类型: {task_info['analysis_type']}")
            readme_lines.append(f"   时间戳: {task_info['timestamp']}")
            readme_lines.append(f"   分析时间: {task_info['analysis_time']}")

        # 获取详细任务信息
        detailed_info = _get_detailed_task_info(info['original_path'], task_info)
        if detailed_info:
            readme_lines.append(f"   [QUEUE] 任务详情:")
            if detailed_info.get('task_type'):
                readme_lines.append(f"     任务类型: {detailed_info['task_type']}")
            if detailed_info.get('ai_model_info'):
                model_info = detailed_info['ai_model_info']
                readme_lines.append(f"     AI模型: {model_info.get('model_name', 'N/A')}")
                readme_lines.append(f"     模型类型: {model_info.get('model_type', 'N/A')}")
                readme_lines.append(f"     模型路径: {model_info.get('model_path', 'N/A')}")
            if detailed_info.get('comparison_layer'):
                readme_lines.append(f"     对比图层: {detailed_info['comparison_layer']}")
            if detailed_info.get('analysis_category'):
                readme_lines.append(f"     分析类别: {detailed_info['analysis_category']}")
            if detailed_info.get('analysis_results'):
                results = detailed_info['analysis_results']
                readme_lines.append(f"     [STATS] 分析结果:")
                if results.get('total_area'):
                    readme_lines.append(f"       总面积: {results['total_area']:.2f} 平方米")
                if results.get('change_area'):
                    readme_lines.append(f"       变化面积: {results['change_area']:.2f} 平方米")
                if results.get('inflow_area'):
                    readme_lines.append(f"       流入面积: {results['inflow_area']:.2f} 平方米")
                if results.get('outflow_area'):
                    readme_lines.append(f"       流出面积: {results['outflow_area']:.2f} 平方米")
                if results.get('feature_count'):
                    readme_lines.append(f"       要素数量: {results['feature_count']} 个")

        readme_lines.append(f"   下载状态: {_get_status_text(info['status'])}")

        if info['status'] == 'success':
            readme_lines.append(f"   包含文件: {', '.join(info['files'])}")
        elif info['status'] == 'error':
            readme_lines.append(f"   错误信息: {info.get('error', '未知错误')}")

        readme_lines.append("")

    # 使用说明
    readme_lines.append("-" * 80)
    readme_lines.append("📖 使用说明:")
    readme_lines.append("")
    readme_lines.append("1. SHP文件格式:")
    readme_lines.append("   - .shp: 主要的几何数据文件")
    readme_lines.append("   - .shx: 索引文件")
    readme_lines.append("   - .dbf: 属性数据文件")
    readme_lines.append("   - .prj: 坐标系统信息文件")
    readme_lines.append("   - .cpg: 编码信息文件(可选)")
    readme_lines.append("")
    readme_lines.append("2. 数据导入:")
    readme_lines.append("   - 可以直接导入到GIS软件(如QGIS、ArcGIS等)")
    readme_lines.append("   - 导入时请选择.shp文件，其他文件会自动关联")
    readme_lines.append("   - 坐标系统通常为UTM投影(EPSG:32648等)")
    readme_lines.append("")
    readme_lines.append("3. 数据内容:")
    readme_lines.append("   - AI分析结果(类型1): 包含AI模型识别出的地物要素")
    readme_lines.append("     * 几何类型: 多边形(Polygon)")
    readme_lines.append("     * 属性字段: 类别、置信度、面积等")
    readme_lines.append("     * 数据来源: 深度学习模型语义分割")
    readme_lines.append("   - 空间变化分析结果(类型2): 包含变化区域的统计信息")
    readme_lines.append("     * 几何类型: 多边形(Polygon)")
    readme_lines.append("     * 属性字段: 变化类型(流入/流出)、面积、时间等")
    readme_lines.append("     * 数据来源: 新旧数据空间叠加分析")
    readme_lines.append("")
    readme_lines.append("4. 任务类型说明:")
    readme_lines.append("   - 合并分析任务: 同时执行AI分析和空间变化分析")
    readme_lines.append("   - AI分析: 使用深度学习模型对遥感影像进行语义分割")
    readme_lines.append("   - 空间分析: 将AI分析结果与历史数据进行空间对比")
    readme_lines.append("   - 结果输出: 生成两个SHP文件，分别对应AI结果和变化分析")
    readme_lines.append("")
    readme_lines.append("5. 模型信息:")
    readme_lines.append("   - 支持多种深度学习模型(DeepLabV3+、U-Net、SegFormer等)")
    readme_lines.append("   - 模型训练针对特定地物类别(如耕地、建筑物、水体等)")
    readme_lines.append("   - 输出结果包含置信度和类别信息")
    readme_lines.append("")

    # 技术支持
    readme_lines.append("-" * 80)
    readme_lines.append("[CONFIG] 技术支持:")
    readme_lines.append("")
    readme_lines.append("如有问题，请联系技术支持团队")
    readme_lines.append("提供此readme.txt文件以便快速定位问题")
    readme_lines.append("")
    readme_lines.append("=" * 80)

    return "\n".join(readme_lines)


def _parse_filename_info(filename):
    """
    解析文件名获取任务信息

    参数:
        filename: 文件名，格式如 20250705171599_1_1756085988.shp

    返回:
        dict: 解析出的信息
    """
    try:
        # 移除扩展名
        base_name = filename.replace('.shp', '')
        parts = base_name.split('_')

        if len(parts) >= 3:
            image_id = parts[0]
            analysis_type_num = parts[1]
            timestamp = parts[2]

            # 分析类型映射
            analysis_type_map = {
                '1': 'AI分析结果',
                '2': '空间变化分析结果'
            }

            analysis_type = analysis_type_map.get(analysis_type_num, f'类型{analysis_type_num}')

            # 转换时间戳
            try:
                from datetime import datetime
                analysis_time = datetime.fromtimestamp(int(timestamp)).strftime('%Y-%m-%d %H:%M:%S')
            except:
                analysis_time = '时间解析失败'

            return {
                'image_id': image_id,
                'analysis_type': analysis_type,
                'timestamp': timestamp,
                'analysis_time': analysis_time
            }
    except:
        pass

    return None


def _get_status_text(status):
    """
    获取状态文本

    参数:
        status: 状态代码

    返回:
        str: 状态文本
    """
    status_map = {
        'success': '[SUCCESS] 下载成功',
        'failed': '[ERROR] 下载失败',
        'error': '[ERROR] 下载错误'
    }

    return status_map.get(status, f'未知状态: {status}')


def _get_task_details_from_files(download_info):
    """
    从文件信息中获取任务详细信息

    参数:
        download_info: 下载信息列表

    返回:
        list: 任务详细信息列表
    """
    task_details = []

    for info in download_info:
        try:
            detailed_info = _get_detailed_task_info(info['original_path'], None)
            if detailed_info:
                task_details.append(detailed_info)
        except:
            continue

    return task_details


def _get_detailed_task_info(file_path, task_info):
    """
    获取详细的任务信息

    参数:
        file_path: 文件路径
        task_info: 基本任务信息

    返回:
        dict: 详细任务信息
    """
    try:
        # 从文件路径中提取信息
        path_parts = file_path.replace('\\', '/').split('/')

        # 查找ODM目录和相关信息
        odm_index = -1
        for i, part in enumerate(path_parts):
            if part == 'ODM':
                odm_index = i
                break

        if odm_index == -1:
            return None

        # 提取影像ID和分析类别
        image_id = None
        analysis_category = None

        if odm_index + 3 < len(path_parts):
            image_id = path_parts[odm_index + 2]  # ODM/AI/{image_id}
            analysis_category = path_parts[odm_index + 3]  # ODM/AI/{image_id}/{category}

        # 尝试读取TaskInfo.json获取详细信息
        task_info_path = None
        if image_id:
            # 构建TaskInfo.json路径
            base_path = '/'.join(path_parts[:odm_index])
            task_info_path = f"{base_path}/ODM/Input/{image_id}/TaskInfo.json"

            # 尝试不同的可能路径
            possible_paths = [
                task_info_path,
                f"{base_path}/ODM/AI/{image_id}/TaskInfo.json",
                f"{base_path}/ODM/AI/{image_id}/{analysis_category}/TaskInfo.json"
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    task_info_path = path
                    break

        detailed_info = {
            'image_id': image_id,
            'analysis_category': analysis_category,
            'task_type': '合并分析任务（AI分析 + 空间变化分析）',
            'ai_model_info': None,
            'comparison_layer': None,
            'analysis_results': None
        }

        # 读取TaskInfo.json文件
        if task_info_path and os.path.exists(task_info_path):
            try:
                with open(task_info_path, 'r', encoding='utf-8') as f:
                    task_data = json.load(f)

                # 查找最新的任务信息
                if isinstance(task_data, list) and task_data:
                    latest_task = task_data[-1]  # 获取最新的任务

                    # 提取AI模型信息
                    if 'ai_analysis' in latest_task:
                        ai_info = latest_task['ai_analysis']
                        model_path = ai_info.get('model_path', '')
                        model_type = ai_info.get('model_type', '')

                        detailed_info['ai_model_info'] = {
                            'model_name': os.path.basename(model_path) if model_path else 'N/A',
                            'model_type': model_type or 'N/A',
                            'model_path': model_path or 'N/A'
                        }

                    # 提取对比图层信息
                    if 'spatial_analysis' in latest_task:
                        spatial_info = latest_task['spatial_analysis']
                        old_data_path = spatial_info.get('old_data_path', '')
                        detailed_info['comparison_layer'] = os.path.basename(old_data_path) if old_data_path else 'N/A'

                    # 提取分析结果
                    if 'results' in latest_task:
                        results = latest_task['results']
                        detailed_info['analysis_results'] = {
                            'total_area': results.get('total_area', 0),
                            'change_area': results.get('change_area', 0),
                            'inflow_area': results.get('inflow_area', 0),
                            'outflow_area': results.get('outflow_area', 0),
                            'feature_count': results.get('total_count', 0)
                        }

                    # 提取空间统计信息
                    if 'spatial_statistics' in latest_task:
                        spatial_stats = latest_task['spatial_statistics']
                        if not detailed_info['analysis_results']:
                            detailed_info['analysis_results'] = {}

                        detailed_info['analysis_results'].update({
                            'inflow_area': spatial_stats.get('inflow_area', 0),
                            'outflow_area': spatial_stats.get('outflow_area', 0),
                            'inflow_count': spatial_stats.get('inflow_count', 0),
                            'outflow_count': spatial_stats.get('outflow_count', 0),
                            'total_count': spatial_stats.get('total_count', 0)
                        })

            except Exception as e:
                analysis_logger.debug(f"读取TaskInfo.json失败: {e}")

        return detailed_info

    except Exception as e:
        analysis_logger.debug(f"获取详细任务信息失败: {e}")
        return None


def _download_shapefile_complete(shp_url, folder_name, zipf):
    """
    下载完整的SHP文件组（包括.shp, .shx, .dbf, .prj等）

    参数:
        shp_url: SHP文件的HTTP URL
        folder_name: ZIP中的文件夹名称
        zipf: ZipFile对象

    返回:
        list: 成功下载的文件列表
    """
    try:
        # SHP文件的相关扩展名
        shp_extensions = ['.shp', '.shx', '.dbf',  ".shx",'.prj', '.cpg', '.sbn', '.sbx', '.shp.xml']

        # 获取基础文件名（不含扩展名）
        base_url = shp_url.rsplit('.', 1)[0]  # 移除.shp扩展名
        base_filename = os.path.basename(base_url)

        downloaded_files = []

        for ext in shp_extensions:
            file_url = base_url + ext
            file_name = base_filename + ext

            try:
                # 尝试下载文件
                response = requests.get(file_url, timeout=30)

                if response.status_code == 200:
                    # 将文件添加到ZIP中
                    zip_path = f"{folder_name}/{file_name}"
                    zipf.writestr(zip_path, response.content)
                    downloaded_files.append(file_name)
                    analysis_logger.debug(f"下载成功: {file_url}")
                else:
                    # .shp, .shx, .dbf是必需的，其他文件可选
                    if ext in ['.shp', '.shx', '.dbf']:
                        analysis_logger.warning(f"必需文件下载失败: {file_url} (HTTP {response.status_code})")
                    else:
                        analysis_logger.debug(f"可选文件不存在: {file_url}")

            except requests.exceptions.RequestException as e:
                if ext in ['.shp', '.shx', '.dbf']:
                    analysis_logger.error(f"必需文件下载异常: {file_url}, 错误: {str(e)}")
                else:
                    analysis_logger.debug(f"可选文件下载异常: {file_url}")

        # 检查是否下载了必需的文件
        required_files = [f"{base_filename}.shp", f"{base_filename}.shx", f"{base_filename}.dbf"]
        missing_required = [f for f in required_files if f not in downloaded_files]

        if missing_required:
            analysis_logger.error(f"缺少必需的SHP文件: {missing_required}")
            return []

        return downloaded_files

    except Exception as e:
        analysis_logger.error(f"下载完整SHP文件失败: {shp_url}, 错误: {str(e)}")
        return []


@api_view(['GET'])
def queued_combined_ai_spatial_analysis(request):
    """
    队列化合并AI分析和空间变化分析接口

    功能: 将合并分析任务添加到队列中按顺序执行，避免并发执行导致的系统崩溃

    参数:
        id: 影像ID (必选)
        image: 影像文件路径 (必选)
        model: AI模型文件路径 (必选)
        old_data_path: 历史数据文件路径 (必选)
        area_threshold: 面积阈值，默认400.0 (可选)
        model_type: 模型类型，默认deeplabv3_plus (可选)

    返回:
        成功: {'success': True, 'data': {'task_id': 'xxx', 'status': '等待中', 'queue_position': 1}}
        失败: {'success': False, 'message': '错误信息'}
    """
    try:
        # 参数验证
        required_params = ['id', 'image', 'model', 'old_data_path']
        missing_params = []

        for param in required_params:
            if not request.GET.get(param):
                missing_params.append(param)

        if missing_params:
            return Response({
                'success': False,
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取参数
        task_params = {
            'id': request.GET.get('id'),
            'image': request.GET.get('image'),
            'model': request.GET.get('model'),
            'old_data_path': request.GET.get('old_data_path'),
            'area_threshold': float(request.GET.get('area_threshold', 400.0)),
            'model_type': request.GET.get('model_type', 'deeplabv3_plus')
        }

        # 参数验证
        if not os.path.exists(task_params['image']):
            return Response({
                'success': False,
                'message': f'影像文件不存在: {task_params["image"]}'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not os.path.exists(task_params['model']):
            return Response({
                'success': False,
                'message': f'模型文件不存在: {task_params["model"]}'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not os.path.exists(task_params['old_data_path']):
            return Response({
                'success': False,
                'message': f'历史数据文件不存在: {task_params["old_data_path"]}'
            }, status=status.HTTP_400_BAD_REQUEST)

        analysis_logger.info(f"[QUEUE] 收到队列化合并分析请求: {task_params['id']}")

        # 添加任务到队列
        from ...core.task_queue_manager import task_queue_manager
        result = task_queue_manager.add_task(task_params)

        if result['success']:
            analysis_logger.info(f"[SUCCESS] 任务已添加到队列: {result['data']['task_id']}")
            return Response(result, status=status.HTTP_200_OK)
        else:
            analysis_logger.error(f"[ERROR] 添加任务到队列失败: {result['message']}")
            return Response(result, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        error_msg = f"队列化合并分析接口异常: {str(e)}"
        analysis_logger.error(error_msg)
        return Response({
            'success': False,
            'message': error_msg
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def cancel_queued_task(request):
    """
    取消等待中的队列任务

    功能: 取消状态为"等待中"的任务，已执行或完成的任务无法取消

    URL参数:
        task_id: 任务ID (必选)

    返回:
        成功: {'success': True, 'message': '任务已取消'}
        失败: {'success': False, 'message': '错误信息'}
    """
    try:
        # 获取URL参数
        task_id = request.GET.get('task_id')

        if not task_id:
            return Response({
                'success': False,
                'message': '缺少必要参数: task_id'
            }, status=status.HTTP_400_BAD_REQUEST)

        analysis_logger.info(f"[RECEIVE] 收到取消任务请求: {task_id}")

        # 取消任务
        from ...core.task_queue_manager import task_queue_manager
        result = task_queue_manager.cancel_task(task_id)

        if result['success']:
            analysis_logger.info(f"[SUCCESS] 任务已取消: {task_id}")
            return Response(result, status=status.HTTP_200_OK)
        else:
            analysis_logger.warning(f"[WARNING] 取消任务失败: {result['message']}")
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        error_msg = f"取消任务接口异常: {str(e)}"
        analysis_logger.error(error_msg)
        return Response({
            'success': False,
            'message': error_msg
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_queue_status(request):
    """
    获取任务队列状态

    功能: 查看当前队列中的任务数量、执行状态等信息

    参数:
        task_id: 任务ID (可选，如果提供则返回特定任务状态)

    返回:
        队列状态或特定任务状态
    """
    try:
        task_id = request.GET.get('task_id')

        from ...core.task_queue_manager import task_queue_manager

        if task_id:
            # 获取特定任务状态
            result = task_queue_manager.get_task_status(task_id)
        else:
            # 获取队列状态
            result = task_queue_manager.get_queue_status()

        return Response(result, status=status.HTTP_200_OK)

    except Exception as e:
        error_msg = f"获取队列状态接口异常: {str(e)}"
        analysis_logger.error(error_msg)
        return Response({
            'success': False,
            'message': error_msg
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def delete_analysis_task(request):
    """
    删除分析任务

    功能: 根据任务状态删除任务信息、GeoServer图层和相关文件

    参数:
        id: 影像ID (必选)
        task_id: 任务ID (必选)

    返回:
        成功: {'success': True, 'message': '任务删除成功', 'details': {...}}
        失败: {'success': False, 'message': '错误信息'}
    """
    try:
        # 获取参数
        image_id = request.GET.get('id')
        task_id = request.GET.get('task_id')

        if not image_id or not task_id:
            return Response({
                'success': False,
                'message': '缺少必要参数: id 和 task_id'
            }, status=status.HTTP_400_BAD_REQUEST)

        analysis_logger.info(f"[DELETE] 收到删除任务请求: image_id={image_id}, task_id={task_id}")

        # 执行删除操作
        from ...core.task_deletion_manager import TaskDeletionManager
        deletion_manager = TaskDeletionManager()

        result = deletion_manager.delete_task(image_id, task_id)

        if result['success']:
            analysis_logger.info(f"[SUCCESS] 任务删除成功: {task_id}")
            return Response(result, status=status.HTTP_200_OK)
        else:
            analysis_logger.error(f"[ERROR] 任务删除失败: {result['message']}")
            return Response(result, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        error_msg = f"删除任务接口异常: {str(e)}"
        analysis_logger.error(error_msg)
        return Response({
            'success': False,
            'message': error_msg
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
