#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地理空间分析API视图

功能说明:
- 空间数据变化分析接口
- 空间数据处理和分析
- 结果文件生成和下载
- 异步任务执行和状态监控

接口列表:
- GET /api/analysis/spatial-changes/ - 空间数据变化分析
- GET /api/analysis/status/<task_id>/ - 查询分析任务状态
- GET /api/analysis/download/<file_id>/ - 下载分析结果文件
- GET /api/analysis/logs/ - 获取分析日志
"""

import os
import json
import logging
import requests
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

# 导入核心模块
from ...core.analysis_executor import analysis_executor, analysis_logger

# 尝试导入合并分析执行器
try:
    from ...core.combined_analysis_executor import combined_analysis_executor
    COMBINED_EXECUTOR_AVAILABLE = True
except ImportError:
    COMBINED_EXECUTOR_AVAILABLE = False

@api_view(['GET'])
def spatial_changes_analysis(request):
    """
    空间数据变化分析接口

    功能: 分析两个时期空间数据的变化，生成流入流出数据

    查询参数:
        old_data_path: 老数据路径 (必选)
        new_data_path: 新数据路径 (必选)
        area_threshold: 面积阈值，单位平方米 (可选，默认200)
        output_dir: 输出目录 (可选，默认为新数据根目录)
        shp_filename: 输出文件名 (可选，默认changes_shapefile)
        clip_area_path: 裁剪范围Shapefile路径 (可选)

    返回: 分析任务ID和初始状态
    """
    try:
        # 获取参数
        old_data_path = request.GET.get("old_data_path")
        new_data_path = request.GET.get("new_data_path")
        area_threshold = float(request.GET.get("area_threshold", 200.0))
        output_dir = request.GET.get("output_dir")  # 可选，None表示使用默认
        shp_filename = request.GET.get("shp_filename", "changes_shapefile")
        clip_area_path = request.GET.get("clip_area_path")  # 可选，裁剪范围

        # 参数验证
        if not old_data_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: old_data_path'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not new_data_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: new_data_path'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件是否存在
        if not os.path.exists(old_data_path):
            return Response({
                'status': 'error',
                'message': f'老数据文件不存在: {old_data_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        if not os.path.exists(new_data_path):
            return Response({
                'status': 'error',
                'message': f'新数据文件不存在: {new_data_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        analysis_logger.info(f"收到空间数据变化分析请求")
        analysis_logger.info(f"老数据: {old_data_path}")
        analysis_logger.info(f"新数据: {new_data_path}")
        analysis_logger.info(f"面积阈值: {area_threshold} 平方米")
        if clip_area_path:
            analysis_logger.info(f"裁剪范围: {clip_area_path}")
        else:
            analysis_logger.info("裁剪范围: 使用新数据覆盖区域")

        # 执行分析任务
        result = analysis_executor.execute_spatial_changes_analysis(
            old_data_path=old_data_path,
            new_data_path=new_data_path,
            area_threshold=area_threshold,
            output_dir=output_dir,
            shp_filename=shp_filename,
            clip_area_path=clip_area_path
        )

        if result['success']:
            return Response({
                'status': 'success',
                'message': '空间数据变化分析任务已启动',
                'data': {
                    'task_id': result['task_id'],
                    'task_status': result['status'],
                    'message': result['message']
                }
            })
        else:
            return Response({
                'status': 'error',
                'message': result['message'],
                'error': result.get('error')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    except Exception as e:
        analysis_logger.error(f"耕地流入流出分析API错误: {str(e)}")
        import traceback
        analysis_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_analysis_status(request):
    """
    查询分析任务状态

    查询参数:
        task_id: 任务ID

    返回: 任务状态信息
    """
    try:
        task_id = request.GET.get('task_id')

        if not task_id:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: task_id'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 首先尝试从普通分析执行器获取任务状态
        task_info = analysis_executor.get_task_status(task_id)

        # 如果普通分析执行器中没有找到，尝试从合并分析执行器获取
        if not task_info and COMBINED_EXECUTOR_AVAILABLE:
            combined_result = combined_analysis_executor.get_task_status(task_id)
            if combined_result['success']:
                task_info = combined_result['data']

        if not task_info:
            return Response({
                'status': 'error',
                'message': f'任务不存在: {task_id}'
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'status': 'success',
            'message': '成功获取任务状态',
            'data': task_info
        })

    except Exception as e:
        analysis_logger.error(f"查询任务状态API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)





@api_view(['GET'])
def get_analysis_logs(request):
    """
    获取分析日志

    查询参数:
        lines: 返回的日志行数，默认全部
        task_id: 获取特定任务的日志 (可选)
        log_type: 日志类型，main(主日志)或task(任务日志)，默认task

    返回: 日志内容
    """
    try:
        lines_param = request.GET.get('lines')
        lines = int(lines_param) if lines_param else None  # None表示全部
        task_id = request.GET.get('task_id')
        log_type = request.GET.get('log_type', 'task')

        # 确定日志目录
        logs_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs', 'analysislog')

        if not os.path.exists(logs_dir):
            return Response({
                'status': 'error',
                'message': '分析日志目录不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 根据参数确定要读取的日志文件
        if task_id and log_type == 'task':
            # 读取特定任务的日志
            log_file = os.path.join(logs_dir, f"{task_id}.log")

            if not os.path.exists(log_file):
                return Response({
                    'status': 'error',
                    'message': f'任务 {task_id} 的日志文件不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 读取任务日志
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()

            # 获取指定行数或全部
            if lines is None:
                recent_lines = all_lines
            else:
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

            return Response({
                'status': 'success',
                'message': f'成功获取任务 {task_id} 的日志',
                'data': {
                    'log_file': log_file,
                    'log_type': 'task',
                    'task_id': task_id,
                    'total_lines': len(all_lines),
                    'returned_lines': len(recent_lines),
                    'logs': recent_lines
                }
            })

        else:
            # 读取主执行器日志
            log_file = os.path.join(logs_dir, 'analysis_executor.log')

            if not os.path.exists(log_file):
                return Response({
                    'status': 'error',
                    'message': '主日志文件不存在'
                }, status=status.HTTP_404_NOT_FOUND)

            # 读取主日志
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()

            # 获取指定行数或全部
            if lines is None:
                recent_lines = all_lines
            else:
                recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines

            # 如果指定了task_id，过滤相关日志
            if task_id:
                filtered_lines = [line for line in recent_lines if task_id in line]
                recent_lines = filtered_lines

            return Response({
                'status': 'success',
                'message': '成功获取分析主日志',
                'data': {
                    'log_file': log_file,
                    'log_type': 'main',
                    'task_id_filter': task_id,
                    'total_lines': len(all_lines),
                    'returned_lines': len(recent_lines),
                    'logs': recent_lines
                }
            })

    except Exception as e:
        analysis_logger.error(f"获取分析日志API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def _get_taskinfo_file_path(image_id):
    """
    获取TaskInfo.json文件路径

    参数:
        image_id (str): 影像ID

    返回:
        dict: {
            'success': bool,
            'path': str,
            'message': str
        }
    """
    try:
        # 从ODM配置获取window_data_path
        config_result = _get_odm_config()

        if not config_result['success']:
            return {
                'success': False,
                'path': '',
                'message': f'获取ODM配置失败: {config_result["message"]}'
            }

        window_data_path = config_result['config'].get('PATHS', {}).get('window_data_path')

        if not window_data_path:
            # 使用默认路径
            window_data_path = 'D:/Drone_Project/nginxData'
            analysis_logger.warning(f"使用默认window_data_path: {window_data_path}")

        # 构建TaskInfo.json路径
        taskinfo_path = os.path.join(window_data_path, 'ODM', 'AI', image_id, 'TaskInfo.json')

        return {
            'success': True,
            'path': taskinfo_path,
            'message': 'TaskInfo路径构建成功'
        }

    except Exception as e:
        return {
            'success': False,
            'path': '',
            'message': f'构建TaskInfo路径失败: {str(e)}'
        }


def _get_odm_config():
    """
    获取ODM配置文件

    返回:
        dict: {
            'success': bool,
            'config': dict,
            'message': str
        }
    """
    try:
        # 从ODM服务器获取配置文件
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"

        response = requests.get(config_url, timeout=10)
        response.raise_for_status()

        # 解析配置文件内容
        config_content = response.text
        config = {}
        current_section = None

        for line in config_content.splitlines():
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            # 解析节名
            if line.startswith("[") and line.endswith("]"):
                current_section = line[1:-1]
                config[current_section] = {}
                continue

            # 解析键值对
            if "=" in line and current_section:
                key, value = line.split("=", 1)
                config[current_section][key.strip()] = value.strip()

        return {
            'success': True,
            'config': config,
            'message': '配置文件读取成功'
        }

    except requests.RequestException as e:
        return {
            'success': False,
            'config': {},
            'message': f'无法读取ODM配置文件: {str(e)}'
        }
    except Exception as e:
        return {
            'success': False,
            'config': {},
            'message': f'解析ODM配置文件失败: {str(e)}'
        }


@api_view(['GET'])
def get_taskinfo_by_id(request):
    """
    根据ID获取TaskInfo.json内容

    参数:
        id (str): 影像ID，如 20250705171601

    返回格式:
    {
        "status": "success",
        "message": "成功获取TaskInfo信息",
        "data": [
            {
                "task_id": "uuid",
                "image_id": "20250705171601",
                "analysis_category": "arableLand",
                "status": "完成",
                "parameters": {...},
                "results": {...}
            }
        ]
    }

    如果文件不存在，返回空列表:
    {
        "status": "success",
        "message": "TaskInfo文件不存在",
        "data": []
    }
    """
    try:
        # 获取参数
        image_id = request.GET.get('id')

        if not image_id:
            analysis_logger.warning("获取TaskInfo请求缺少id参数")
            return Response({
                'status': 'error',
                'message': '缺少必需参数: id',
                'data': []
            }, status=status.HTTP_400_BAD_REQUEST)

        analysis_logger.info(f"获取TaskInfo请求: image_id={image_id}")

        # 获取TaskInfo.json文件路径
        taskinfo_result = _get_taskinfo_file_path(image_id)

        if not taskinfo_result['success']:
            analysis_logger.warning(f"获取TaskInfo文件路径失败: {taskinfo_result['message']}")
            return Response({
                'status': 'success',
                'message': taskinfo_result['message'],
                'data': []
            }, status=status.HTTP_200_OK)

        taskinfo_path = taskinfo_result['path']
        analysis_logger.info(f"TaskInfo文件路径: {taskinfo_path}")

        # 检查文件是否存在
        if not os.path.exists(taskinfo_path):
            analysis_logger.info(f"TaskInfo文件不存在: {taskinfo_path}")
            return Response({
                'status': 'success',
                'message': f'TaskInfo文件不存在: {image_id}',
                'data': []
            }, status=status.HTTP_200_OK)

        # 读取TaskInfo.json文件
        try:
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                taskinfo_data = json.load(f)

            # 确保返回列表格式
            if isinstance(taskinfo_data, dict):
                taskinfo_data = [taskinfo_data]
            elif not isinstance(taskinfo_data, list):
                taskinfo_data = []

            analysis_logger.info(f"成功读取TaskInfo文件，包含 {len(taskinfo_data)} 个任务")

            return Response({
                'status': 'success',
                'message': f'成功获取TaskInfo信息，共 {len(taskinfo_data)} 个任务',
                'data': taskinfo_data
            }, status=status.HTTP_200_OK)

        except json.JSONDecodeError as e:
            analysis_logger.error(f"TaskInfo文件JSON格式错误: {str(e)}")
            return Response({
                'status': 'error',
                'message': f'TaskInfo文件格式错误: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        except Exception as e:
            analysis_logger.error(f"读取TaskInfo文件失败: {str(e)}")
            return Response({
                'status': 'error',
                'message': f'读取TaskInfo文件失败: {str(e)}',
                'data': []
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        analysis_logger.error(f"获取TaskInfo API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}',
            'data': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
