#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试时间戳一致性修复
验证output_files和geoserver_publish中的文件名时间戳一致
"""

import requests
import json
import time
from config import DJANGO_BASE_URL

def test_timestamp_consistency():
    """测试时间戳一致性"""
    print("🧪 测试时间戳一致性修复")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    # 提交测试任务
    task_params = {
        'id': '20250705171599',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print("📤 提交测试任务...")
        response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                              params=task_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                task_id = result['data']['task_id']
                print(f"✅ 任务提交成功: {task_id[:8]}...")
                
                # 等待任务完成
                print("⏰ 等待任务完成...")
                wait_for_task_completion(task_id, task_params['id'])
                
                # 检查时间戳一致性
                check_timestamp_consistency(task_params['id'], task_id)
                
            else:
                print(f"❌ 任务提交失败: {result['message']}")
        else:
            print(f"❌ 任务提交HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def wait_for_task_completion(task_id, image_id, max_wait=300):
    """等待任务完成"""
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            # 检查队列状态
            response = requests.get(f"{base_url}/queue-status/", 
                                  params={'task_id': task_id}, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    status = data.get('status', 'unknown')
                    message = data.get('message', '')
                    
                    print(f"   状态: {status} - {message}")
                    
                    if status in ['完成', '失败']:
                        print(f"   任务已结束: {status}")
                        return status == '完成'
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"   检查状态异常: {e}")
            time.sleep(10)
    
    print(f"   ⚠️ 等待超时")
    return False

def check_timestamp_consistency(image_id, task_id):
    """检查时间戳一致性"""
    print(f"\n🔍 检查时间戳一致性...")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    try:
        # 获取TaskInfo
        response = requests.get(f"{base_url}/taskinfo/", 
                              params={'id': image_id}, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success' and result['data']:
                tasks = result['data']
                
                # 查找指定的任务
                target_task = None
                for task in tasks:
                    if task.get('task_id') == task_id:
                        target_task = task
                        break
                
                if target_task:
                    print(f"   📋 找到任务: {task_id[:8]}...")
                    
                    # 提取时间戳信息
                    task_timestamp = target_task.get('timestamp')
                    output_files = target_task.get('output_files', {})
                    geoserver_publish = target_task.get('geoserver_publish', {})
                    
                    print(f"   📊 TaskInfo时间戳: {task_timestamp}")
                    
                    # 检查output_files中的时间戳
                    ai_output_path = output_files.get('ai_output_path', '')
                    final_output_path = output_files.get('final_output_path', '')
                    
                    ai_timestamp = extract_timestamp_from_path(ai_output_path)
                    final_timestamp = extract_timestamp_from_path(final_output_path)
                    
                    print(f"   📁 output_files时间戳:")
                    print(f"     AI输出: {ai_timestamp} (从 {ai_output_path})")
                    print(f"     最终输出: {final_timestamp} (从 {final_output_path})")
                    
                    # 检查geoserver_publish中的时间戳
                    ai_result = geoserver_publish.get('ai_result', {})
                    final_result = geoserver_publish.get('final_result', {})
                    
                    ai_layer_name = ai_result.get('layer_name', '')
                    final_layer_name = final_result.get('layer_name', '')
                    
                    ai_geo_timestamp = extract_timestamp_from_layer_name(ai_layer_name)
                    final_geo_timestamp = extract_timestamp_from_layer_name(final_layer_name)
                    
                    print(f"   🌐 geoserver_publish时间戳:")
                    print(f"     AI图层: {ai_geo_timestamp} (从 {ai_layer_name})")
                    print(f"     最终图层: {final_geo_timestamp} (从 {final_layer_name})")
                    
                    # 验证一致性
                    all_timestamps = [task_timestamp, ai_timestamp, final_timestamp, ai_geo_timestamp, final_geo_timestamp]
                    unique_timestamps = set(filter(None, all_timestamps))
                    
                    print(f"\n   📊 时间戳一致性检查:")
                    print(f"     所有时间戳: {all_timestamps}")
                    print(f"     唯一时间戳: {list(unique_timestamps)}")
                    
                    if len(unique_timestamps) == 1:
                        print(f"   ✅ 时间戳完全一致: {list(unique_timestamps)[0]}")
                        return True
                    else:
                        print(f"   ❌ 时间戳不一致，发现 {len(unique_timestamps)} 个不同的时间戳")
                        return False
                        
                else:
                    print(f"   ❌ 未找到指定任务: {task_id}")
                    return False
            else:
                print(f"   ❌ 获取TaskInfo失败: {result.get('message', 'unknown')}")
                return False
        else:
            print(f"   ❌ 获取TaskInfo HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 检查时间戳一致性异常: {e}")
        return False

def extract_timestamp_from_path(file_path):
    """从文件路径中提取时间戳"""
    try:
        if not file_path:
            return None
        
        # 文件名格式: 20250705171599_1_1756167978.shp
        filename = file_path.split('/')[-1]  # 获取文件名
        parts = filename.split('_')
        
        if len(parts) >= 3:
            # 最后一个部分应该是时间戳.shp
            timestamp_part = parts[-1].split('.')[0]  # 去掉.shp
            return int(timestamp_part)
        
        return None
    except:
        return None

def extract_timestamp_from_layer_name(layer_name):
    """从图层名称中提取时间戳"""
    try:
        if not layer_name:
            return None
        
        # 图层名格式: 20250705171599_1_1756167978
        parts = layer_name.split('_')
        
        if len(parts) >= 3:
            # 最后一个部分应该是时间戳
            return int(parts[-1])
        
        return None
    except:
        return None

def test_multiple_tasks():
    """测试多个任务的时间戳一致性"""
    print(f"\n🔄 测试多个任务的时间戳一致性...")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    # 测试不同的影像ID
    test_cases = [
        {
            'id': '20250705171599',
            'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        },
        {
            'id': '20250705171600',
            'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600_out.tif',
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n   测试任务 {i+1}: {test_case['id']}")
        
        task_params = {
            **test_case,
            'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
            'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus'
        }
        
        try:
            response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                                  params=task_params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    task_id = result['data']['task_id']
                    print(f"     ✅ 任务提交成功: {task_id[:8]}...")
                    results.append({
                        'image_id': test_case['id'],
                        'task_id': task_id,
                        'submitted': True
                    })
                else:
                    print(f"     ❌ 任务提交失败: {result['message']}")
                    results.append({
                        'image_id': test_case['id'],
                        'task_id': None,
                        'submitted': False
                    })
            else:
                print(f"     ❌ HTTP错误: {response.status_code}")
                results.append({
                    'image_id': test_case['id'],
                    'task_id': None,
                    'submitted': False
                })
                
        except Exception as e:
            print(f"     ❌ 异常: {e}")
            results.append({
                'image_id': test_case['id'],
                'task_id': None,
                'submitted': False
            })
    
    print(f"\n   📊 提交结果:")
    for result in results:
        status = "✅ 成功" if result['submitted'] else "❌ 失败"
        print(f"     {result['image_id']}: {status}")
    
    return results

def main():
    """主函数"""
    print("🧪 时间戳一致性修复测试")
    
    print(f"\n📝 修复内容:")
    print(f"1. 统一使用TaskInfo中的timestamp字段")
    print(f"2. 避免在不同时间点生成时间戳")
    print(f"3. 确保output_files和geoserver_publish使用相同时间戳")
    
    print(f"\n🎯 测试目标:")
    print(f"- output_files中的文件名时间戳一致")
    print(f"- geoserver_publish中的图层名时间戳一致")
    print(f"- 所有时间戳都与TaskInfo.timestamp一致")
    
    # 选择测试模式
    print(f"\n🔧 测试模式:")
    print(f"1. 单个任务测试")
    print(f"2. 多个任务测试")
    
    choice = input("请选择测试模式 (1/2): ").strip()
    
    if choice == "1":
        test_timestamp_consistency()
    elif choice == "2":
        test_multiple_tasks()
    else:
        print("❌ 无效选择，执行单个任务测试")
        test_timestamp_consistency()
    
    print(f"\n🎉 测试完成！")
    
    print(f"\n📚 修复说明:")
    print(f"- 添加了_get_unified_timestamp方法")
    print(f"- 优先从TaskInfo.json中获取已存在的timestamp")
    print(f"- 所有文件名生成都使用统一的时间戳")
    print(f"- 确保时间戳在整个任务生命周期中保持一致")

if __name__ == "__main__":
    main()
