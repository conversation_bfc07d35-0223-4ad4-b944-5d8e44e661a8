#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试TaskInfo.json实时更新功能
"""

import requests
import time
import json
import os

def test_ai_semantic_segmentation_taskinfo():
    """测试AI语义分割的TaskInfo.json实时更新"""
    print("🧪 测试AI语义分割TaskInfo.json实时更新功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    model_path = "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
    output_path = "D:/Drone_Project/test_output/ai_result.shp"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  输入影像: {image_path}")
        print(f"  模型路径: {model_path}")
        print(f"  输出路径: {output_path}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✅ 创建输出目录: {output_dir}")
        
        # 启动AI语义分割任务
        print("\n🚀 启动AI语义分割任务...")
        params = {
            'image': image_path,
            'model': model_path,
            'output': output_path,
            'model_type': 'deeplabv3_plus',
            'window_size': 512,
            'batch_size': 16,
            'num_classes': 2
        }
        
        response = requests.get(f"{base_url}/ai-semantic-segmentation/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务执行和TaskInfo.json更新
            success = monitor_task_with_taskinfo(base_url, task_id, output_dir)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_task_with_taskinfo(base_url, task_id, output_dir, max_wait_time=300):
    """监控任务执行并检查TaskInfo.json更新"""
    print(f"\n📊 监控任务执行和TaskInfo.json更新...")
    
    task_info_path = os.path.join(output_dir, "TaskInfo.json")
    start_time = time.time()
    last_progress = -1
    last_taskinfo_status = None
    
    # 检查TaskInfo.json是否已创建
    print(f"\n🔍 检查TaskInfo.json文件: {task_info_path}")
    
    while time.time() - start_time < max_wait_time:
        try:
            # 1. 通过API查询任务状态
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            api_status = None
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                api_status = {
                    'task_status': status_data['task_status'],
                    'message': status_data.get('message', ''),
                    'progress': status_data.get('progress', 0)
                }
                
                # 只在进度变化时输出API状态
                if api_status['progress'] != last_progress:
                    print(f"\n📈 API状态: {api_status['task_status']} ({api_status['progress']}%) - {api_status['message']}")
                    last_progress = api_status['progress']
            
            # 2. 检查TaskInfo.json文件
            taskinfo_status = None
            if os.path.exists(task_info_path):
                try:
                    with open(task_info_path, 'r', encoding='utf-8') as f:
                        taskinfo_data = json.load(f)
                    
                    taskinfo_status = {
                        'status': taskinfo_data.get('status', 'Unknown'),
                        'message': taskinfo_data.get('message', ''),
                        'progress': taskinfo_data.get('progress', 0),
                        'start_time': taskinfo_data.get('start_time'),
                        'end_time': taskinfo_data.get('end_time')
                    }
                    
                    # 只在状态变化时输出TaskInfo状态
                    if taskinfo_status != last_taskinfo_status:
                        print(f"📄 TaskInfo: {taskinfo_status['status']} ({taskinfo_status['progress']}%) - {taskinfo_status['message']}")
                        if taskinfo_status['start_time']:
                            print(f"   开始时间: {taskinfo_status['start_time']}")
                        if taskinfo_status['end_time']:
                            print(f"   结束时间: {taskinfo_status['end_time']}")
                        last_taskinfo_status = taskinfo_status.copy()
                        
                except json.JSONDecodeError as e:
                    print(f"❌ TaskInfo.json格式错误: {e}")
                except Exception as e:
                    print(f"❌ 读取TaskInfo.json失败: {e}")
            else:
                if time.time() - start_time > 10:  # 10秒后还没有TaskInfo文件就提示
                    print(f"⚠️ TaskInfo.json文件尚未创建")
            
            # 3. 比较API状态和TaskInfo状态
            if api_status and taskinfo_status:
                if api_status['progress'] != taskinfo_status['progress']:
                    print(f"⚠️ 状态不一致: API进度={api_status['progress']}%, TaskInfo进度={taskinfo_status['progress']}%")
            
            # 4. 检查任务是否完成
            if api_status and api_status['task_status'] in ['完成', '失败']:
                print(f"\n✅ 任务结束，最终状态: {api_status['task_status']}")
                
                # 显示最终的TaskInfo内容
                if os.path.exists(task_info_path):
                    print(f"\n📋 最终TaskInfo.json内容:")
                    show_final_taskinfo(task_info_path)
                else:
                    print(f"❌ 最终TaskInfo.json文件不存在")
                
                return api_status['task_status'] == '完成'
            
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(5)
            continue
    
    print(f"\n⏰ 任务监控超时")
    return False

def show_final_taskinfo(task_info_path):
    """显示最终的TaskInfo.json内容"""
    try:
        with open(task_info_path, 'r', encoding='utf-8') as f:
            taskinfo_data = json.load(f)
        
        print(f"  任务ID: {taskinfo_data.get('task_id', 'N/A')}")
        print(f"  任务类型: {taskinfo_data.get('task_type', 'N/A')}")
        print(f"  状态: {taskinfo_data.get('status', 'N/A')}")
        print(f"  消息: {taskinfo_data.get('message', 'N/A')}")
        print(f"  进度: {taskinfo_data.get('progress', 0)}%")
        print(f"  开始时间: {taskinfo_data.get('start_time', 'N/A')}")
        print(f"  结束时间: {taskinfo_data.get('end_time', 'N/A')}")
        
        # 显示输入输出文件
        if 'input_files' in taskinfo_data:
            input_files = taskinfo_data['input_files']
            print(f"\n📂 输入文件:")
            for key, path in input_files.items():
                print(f"    {key}: {path}")
        
        if 'output_files' in taskinfo_data:
            output_files = taskinfo_data['output_files']
            print(f"\n📁 输出文件:")
            for key, path in output_files.items():
                print(f"    {key}: {path}")
        
        # 显示参数
        if 'parameters' in taskinfo_data:
            params = taskinfo_data['parameters']
            print(f"\n⚙️ 参数:")
            for key, value in params.items():
                print(f"    {key}: {value}")
        
        # 显示统计信息
        if 'statistics' in taskinfo_data:
            stats = taskinfo_data['statistics']
            print(f"\n📊 统计信息:")
            for key, value in stats.items():
                if value is not None:
                    print(f"    {key}: {value}")
        
        # 显示错误信息
        if 'error' in taskinfo_data and taskinfo_data['error']:
            print(f"\n❌ 错误信息: {taskinfo_data['error']}")
            
    except Exception as e:
        print(f"❌ 显示TaskInfo内容失败: {e}")

def check_taskinfo_template():
    """检查TaskInfo.json模板创建功能"""
    print("\n🔍 检查TaskInfo.json模板创建功能...")
    
    # 这里可以添加一些基本的模板检查逻辑
    print("✅ TaskInfo.json模板功能已集成到AI语义分割流程中")
    print("📝 模板包含以下字段:")
    print("  - task_id: 任务ID")
    print("  - task_type: 任务类型")
    print("  - status: 状态（进行中/完成/失败）")
    print("  - message: 状态消息")
    print("  - start_time/end_time: 开始/结束时间")
    print("  - progress: 进度百分比")
    print("  - input_files: 输入文件信息")
    print("  - output_files: 输出文件信息")
    print("  - parameters: 任务参数")
    print("  - statistics: 统计信息")
    print("  - error: 错误信息")
    print("  - log_file: 日志文件路径")

if __name__ == "__main__":
    print("🚀 开始测试TaskInfo.json实时更新功能")
    
    # 检查模板功能
    check_taskinfo_template()
    
    # 测试AI语义分割的TaskInfo实时更新
    print("\n" + "="*60)
    print("测试AI语义分割TaskInfo.json实时更新")
    print("="*60)
    
    success = test_ai_semantic_segmentation_taskinfo()
    
    if success:
        print("\n🎉 TaskInfo.json实时更新功能测试通过！")
    else:
        print("\n❌ TaskInfo.json实时更新功能测试失败")
    
    print("\n📖 功能说明:")
    print("1. TaskInfo.json在任务开始时立即创建模板")
    print("2. 任务执行过程中实时更新状态、进度、消息")
    print("3. 任务完成时更新最终状态、统计信息、结束时间")
    print("4. 任务失败时记录错误信息和失败状态")
    print("5. 支持嵌套字段更新（如statistics.processing_time）")
    print("6. 与API状态查询保持同步")
