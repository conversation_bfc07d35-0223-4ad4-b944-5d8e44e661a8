"""
GeoServer管理器
用于发布Shapefile到GeoServer
"""

import requests
import os
import sys
import logging
from urllib.parse import urljoin

# 导入配置
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..", "..")))
try:
    from config import GEOSERVER_URL, GEOSERVER_USER, GEOSERVER_PASSWORD
except ImportError:
    # 如果无法导入配置，使用默认值
    GEOSERVER_URL = "http://localhost:8083/geoserver"
    GEOSERVER_USER = "admin"
    GEOSERVER_PASSWORD = "geoserver"

logger = logging.getLogger(__name__)

class GeoServerManager:
    """
    GeoServer管理器类
    """
    
    def __init__(self, base_url=None, username=None, password=None):
        """
        初始化GeoServer管理器

        参数:
            base_url: GeoServer基础URL
            username: 用户名
            password: 密码
        """
        self.base_url = base_url or GEOSERVER_URL
        self.username = username or GEOSERVER_USER
        self.password = password or GEOSERVER_PASSWORD

        # 确保base_url以/结尾，然后正确构建rest_url
        if not self.base_url.endswith('/'):
            self.base_url += '/'
        self.rest_url = urljoin(self.base_url, 'rest/')

        # 设置认证
        self.auth = (self.username, self.password)

        # 设置请求头
        self.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }

        logger.info(f"初始化GeoServer管理器: {self.base_url}, 用户: {self.username}")
    
    def create_workspace(self, workspace_name):
        """
        创建工作区
        
        参数:
            workspace_name: 工作区名称
            
        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            url = urljoin(self.rest_url, 'workspaces')
            
            data = {
                "workspace": {
                    "name": workspace_name
                }
            }
            
            response = requests.post(
                url,
                json=data,
                auth=self.auth,
                headers=self.headers,
                timeout=30
            )
            
            if response.status_code == 201:
                logger.info(f"成功创建工作区: {workspace_name}")
                return {'success': True, 'message': f'工作区 {workspace_name} 创建成功'}
            elif response.status_code == 409:
                logger.info(f"工作区已存在: {workspace_name}")
                return {'success': True, 'message': f'工作区 {workspace_name} 已存在'}
            else:
                error_msg = f"创建工作区失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}
                
        except Exception as e:
            error_msg = f"创建工作区时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}
    
    def create_shapefile_datastore(self, workspace, store_name, shapefile_path):
        """
        创建Shapefile数据存储

        参数:
            workspace: 工作区名称
            store_name: 存储名称
            shapefile_path: Shapefile文件路径

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # URL编码处理工作区名称
            from urllib.parse import quote
            encoded_workspace = quote(workspace, safe='')

            url = urljoin(self.rest_url, f'workspaces/{encoded_workspace}/datastores')

            # 确保路径使用正斜杠，并处理中文路径
            import os
            normalized_path = os.path.abspath(shapefile_path).replace('\\', '/')

            # 检查文件是否存在
            if not os.path.exists(shapefile_path):
                error_msg = f"Shapefile文件不存在: {shapefile_path}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

            # 检查是否有必要的辅助文件
            base_path = os.path.splitext(shapefile_path)[0]
            required_files = ['.shp', '.shx', '.dbf']
            missing_files = []
            file_sizes = {}

            for ext in required_files:
                file_path = base_path + ext
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    file_sizes[ext] = file_size
                    logger.info(f"Shapefile组件 {ext}: 存在 ({file_size} bytes)")

                    # 检查文件是否为空
                    if file_size == 0:
                        logger.warning(f"Shapefile组件 {ext} 文件为空")
                else:
                    missing_files.append(ext)
                    logger.error(f"Shapefile组件 {ext}: 缺失")

            if missing_files:
                error_msg = f"缺少必要的Shapefile文件: {missing_files}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

            # 检查.shp文件是否有效（至少应该有文件头）
            shp_file = base_path + '.shp'
            if file_sizes.get('.shp', 0) < 100:  # SHP文件头至少100字节
                logger.warning(f"SHP文件可能损坏，文件大小过小: {file_sizes.get('.shp', 0)} bytes")

            # 尝试读取Shapefile的基本信息
            try:
                import struct
                with open(shp_file, 'rb') as f:
                    # 读取SHP文件头
                    header = f.read(100)
                    if len(header) >= 32:
                        # 检查文件类型码
                        file_code = struct.unpack('>I', header[0:4])[0]
                        shape_type = struct.unpack('<I', header[32:36])[0]
                        logger.info(f"SHP文件信息: 文件码={file_code}, 形状类型={shape_type}")

                        if file_code != 9994:
                            logger.warning(f"SHP文件头不正确，文件码应为9994，实际为{file_code}")
                    else:
                        logger.error("SHP文件头不完整")

            except Exception as shp_error:
                logger.warning(f"无法读取SHP文件信息: {str(shp_error)}")

            # 检查DBF文件（属性表）
            dbf_file = base_path + '.dbf'
            try:
                with open(dbf_file, 'rb') as f:
                    dbf_header = f.read(32)
                    if len(dbf_header) >= 32:
                        # 读取记录数和字段数
                        record_count = struct.unpack('<I', dbf_header[4:8])[0]
                        header_length = struct.unpack('<H', dbf_header[8:10])[0]
                        record_length = struct.unpack('<H', dbf_header[10:12])[0]

                        field_count = (header_length - 33) // 32

                        logger.info(f"DBF文件信息: 记录数={record_count}, 字段数={field_count}, 记录长度={record_length}")

                        if field_count == 0:
                            logger.error("DBF文件没有字段定义，这可能导致'no attributes were specified'错误")
                            return {'success': False, 'message': 'DBF文件没有字段定义，无法创建要素类型'}

                        if record_count == 0:
                            logger.warning("DBF文件没有数据记录")

                    else:
                        logger.error("DBF文件头不完整")

            except Exception as dbf_error:
                logger.warning(f"无法读取DBF文件信息: {str(dbf_error)}")

            data = {
                "dataStore": {
                    "name": store_name,
                    "type": "Shapefile",
                    "connectionParameters": {
                        "url": f"file:{normalized_path}",
                        "charset": "UTF-8"
                    }
                }
            }

            # 在创建数据存储之前进行详细验证
            validation_result = self.validate_shapefile_detailed(shapefile_path)
            logger.info(f"Shapefile验证结果: {validation_result['message']}")

            if not validation_result['success']:
                logger.error(f"Shapefile验证失败: {validation_result['message']}")
                logger.error(f"验证详情: {validation_result['details']}")
                return validation_result

            # 记录验证详情
            details = validation_result['details']
            if 'dbf_info' in details:
                dbf_info = details['dbf_info']
                logger.info(f"DBF字段信息: {len(dbf_info.get('fields', []))} 个字段")
                for field in dbf_info.get('fields', []):
                    logger.info(f"  字段: {field['name']} ({field['type']}, {field['length']})")

            logger.info(f"创建Shapefile数据存储: {workspace}:{store_name}")
            logger.info(f"Shapefile路径: {normalized_path}")
            logger.info(f"数据存储配置: {data}")

            response = requests.post(
                url,
                json=data,
                auth=self.auth,
                headers=self.headers,
                timeout=60  # 增加超时时间
            )
            
            if response.status_code == 201:
                logger.info(f"成功创建数据存储: {workspace}:{store_name}")
                return {'success': True, 'message': f'数据存储 {store_name} 创建成功'}
            elif response.status_code == 409 or (response.status_code == 500 and 'already exists' in response.text):
                # 数据存储已存在，这种情况不应该发生，因为我们在调用前已经检查并删除了
                logger.warning(f"数据存储意外存在: {workspace}:{store_name}")
                return {'success': True, 'message': f'数据存储 {store_name} 已存在'}
            else:
                error_msg = f"创建数据存储失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}
                
        except Exception as e:
            error_msg = f"创建数据存储时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def delete_datastore(self, workspace, store_name):
        """
        删除数据存储

        参数:
            workspace: 工作区名称
            store_name: 存储名称

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # URL编码处理，避免中文字符问题
            from urllib.parse import quote
            encoded_workspace = quote(workspace, safe='')
            encoded_store_name = quote(store_name, safe='')

            url = urljoin(self.rest_url, f'workspaces/{encoded_workspace}/datastores/{encoded_store_name}')

            # 添加recurse=true参数以删除相关的图层
            params = {'recurse': 'true'}

            logger.info(f"删除数据存储: {workspace}:{store_name}")
            logger.info(f"删除URL: {url}")

            response = requests.delete(
                url,
                auth=self.auth,
                params=params,
                timeout=60  # 增加超时时间到60秒
            )

            if response.status_code == 200:
                logger.info(f"成功删除数据存储: {workspace}:{store_name}")
                return {'success': True, 'message': f'数据存储 {store_name} 删除成功'}
            elif response.status_code == 404:
                logger.info(f"数据存储不存在: {workspace}:{store_name}")
                return {'success': True, 'message': f'数据存储 {store_name} 不存在'}
            else:
                error_msg = f"删除数据存储失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

        except requests.exceptions.Timeout:
            # 超时时，我们假设删除可能成功了，继续尝试创建
            logger.warning(f"删除数据存储超时，但继续尝试创建: {workspace}:{store_name}")
            return {'success': True, 'message': f'数据存储 {store_name} 删除超时，但继续尝试'}
        except Exception as e:
            error_msg = f"删除数据存储时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def create_shapefile_datastore_with_crs(self, workspace, store_name, shapefile_path, crs="EPSG:4326"):
        """
        创建Shapefile数据存储（支持指定坐标系）

        参数:
            workspace: 工作区名称
            store_name: 存储名称
            shapefile_path: Shapefile文件路径
            crs: 坐标系，如 "EPSG:4326"

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # URL编码处理工作区名称
            from urllib.parse import quote
            encoded_workspace = quote(workspace, safe='')

            url = urljoin(self.rest_url, f'workspaces/{encoded_workspace}/datastores')

            # 确保路径使用正斜杠，并处理中文路径
            import os
            normalized_path = os.path.abspath(shapefile_path).replace('\\', '/')

            # 检查文件是否存在
            if not os.path.exists(shapefile_path):
                error_msg = f"Shapefile文件不存在: {shapefile_path}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

            # 验证坐标系格式
            if not crs.upper().startswith('EPSG:'):
                error_msg = f"无效的坐标系格式: {crs}，应为 EPSG:XXXX 格式"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

            # 进行Shapefile验证
            validation_result = self.validate_shapefile_detailed(shapefile_path)
            logger.info(f"Shapefile验证结果: {validation_result['message']}")

            if not validation_result['success']:
                logger.error(f"Shapefile验证失败: {validation_result['message']}")
                return validation_result

            # 构建数据存储配置（包含坐标系信息）
            data = {
                "dataStore": {
                    "name": store_name,
                    "type": "Shapefile",
                    "connectionParameters": {
                        "url": f"file:{normalized_path}",
                        "charset": "UTF-8"
                    }
                }
            }

            logger.info(f"创建Shapefile数据存储 (CRS: {crs}): {workspace}:{store_name}")
            logger.info(f"Shapefile路径: {normalized_path}")

            response = requests.post(
                url,
                json=data,
                auth=self.auth,
                headers=self.headers,
                timeout=60
            )

            if response.status_code == 201:
                logger.info(f"成功创建数据存储: {workspace}:{store_name}")

                # 如果指定了非默认坐标系，需要在发布图层时设置
                if crs.upper() != "EPSG:4326":
                    logger.info(f"将在图层发布时设置坐标系为: {crs}")

                return {
                    'success': True,
                    'message': f'数据存储 {store_name} 创建成功',
                    'crs': crs
                }
            else:
                error_msg = f"创建数据存储失败: HTTP {response.status_code}"
                if response.text:
                    error_msg += f" - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

        except Exception as e:
            error_msg = f"创建Shapefile数据存储时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def validate_shapefile_detailed(self, shapefile_path):
        """
        详细验证Shapefile文件

        参数:
            shapefile_path: Shapefile文件路径

        返回:
            dict: {'success': bool, 'message': str, 'details': dict}
        """
        try:
            import struct

            logger.info(f"开始详细验证Shapefile: {shapefile_path}")

            base_path = os.path.splitext(shapefile_path)[0]
            validation_details = {
                'files_checked': {},
                'shp_info': {},
                'dbf_info': {},
                'issues': []
            }

            # 1. 检查所有必要文件
            required_files = {'.shp': 'Shape文件', '.shx': '索引文件', '.dbf': '属性文件'}
            optional_files = {'.prj': '投影文件', '.cpg': '编码文件'}

            for ext, desc in {**required_files, **optional_files}.items():
                file_path = base_path + ext
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    validation_details['files_checked'][ext] = {
                        'exists': True,
                        'size': file_size,
                        'description': desc
                    }
                    logger.info(f"{desc} ({ext}): 存在, {file_size} bytes")
                else:
                    validation_details['files_checked'][ext] = {
                        'exists': False,
                        'size': 0,
                        'description': desc
                    }
                    if ext in required_files:
                        validation_details['issues'].append(f"缺少必要文件: {desc} ({ext})")
                        logger.error(f"{desc} ({ext}): 缺失")
                    else:
                        logger.info(f"{desc} ({ext}): 不存在（可选）")

            # 2. 验证SHP文件
            shp_file = base_path + '.shp'
            if os.path.exists(shp_file):
                try:
                    with open(shp_file, 'rb') as f:
                        header = f.read(100)
                        if len(header) >= 36:
                            file_code = struct.unpack('>I', header[0:4])[0]
                            file_length = struct.unpack('>I', header[24:28])[0] * 2  # 以字节为单位
                            shape_type = struct.unpack('<I', header[32:36])[0]

                            validation_details['shp_info'] = {
                                'file_code': file_code,
                                'file_length': file_length,
                                'shape_type': shape_type,
                                'shape_type_name': self._get_shape_type_name(shape_type)
                            }

                            logger.info(f"SHP文件信息: 文件码={file_code}, 长度={file_length}, 形状类型={shape_type}")

                            if file_code != 9994:
                                validation_details['issues'].append(f"SHP文件头错误，文件码应为9994，实际为{file_code}")
                        else:
                            validation_details['issues'].append("SHP文件头不完整")
                except Exception as e:
                    validation_details['issues'].append(f"读取SHP文件失败: {str(e)}")

            # 3. 验证DBF文件（最重要）
            dbf_file = base_path + '.dbf'
            if os.path.exists(dbf_file):
                try:
                    with open(dbf_file, 'rb') as f:
                        dbf_header = f.read(32)
                        if len(dbf_header) >= 32:
                            record_count = struct.unpack('<I', dbf_header[4:8])[0]
                            header_length = struct.unpack('<H', dbf_header[8:10])[0]
                            record_length = struct.unpack('<H', dbf_header[10:12])[0]

                            # 计算字段数
                            field_count = max(0, (header_length - 33) // 32)

                            validation_details['dbf_info'] = {
                                'record_count': record_count,
                                'field_count': field_count,
                                'header_length': header_length,
                                'record_length': record_length,
                                'fields': []
                            }

                            logger.info(f"DBF文件信息: 记录数={record_count}, 字段数={field_count}")

                            # 读取字段定义
                            if field_count > 0:
                                f.seek(32)  # 跳过文件头
                                for i in range(field_count):
                                    field_header = f.read(32)
                                    if len(field_header) == 32:
                                        field_name = field_header[0:11].rstrip(b'\x00').decode('utf-8', errors='ignore')
                                        field_type = chr(field_header[11])
                                        field_length = field_header[16]
                                        field_decimal = field_header[17]

                                        field_info = {
                                            'name': field_name,
                                            'type': field_type,
                                            'length': field_length,
                                            'decimal': field_decimal
                                        }
                                        validation_details['dbf_info']['fields'].append(field_info)
                                        logger.info(f"字段 {i+1}: {field_name} ({field_type}, {field_length})")

                            if field_count == 0:
                                validation_details['issues'].append("DBF文件没有字段定义")
                                logger.error("DBF文件没有字段定义 - 这是导致'no attributes were specified'错误的主要原因")

                        else:
                            validation_details['issues'].append("DBF文件头不完整")
                except Exception as e:
                    validation_details['issues'].append(f"读取DBF文件失败: {str(e)}")

            # 4. 总结验证结果
            has_critical_issues = any('DBF文件没有字段定义' in issue for issue in validation_details['issues'])
            missing_required = any('缺少必要文件' in issue for issue in validation_details['issues'])

            if has_critical_issues or missing_required:
                return {
                    'success': False,
                    'message': f"Shapefile验证失败: {'; '.join(validation_details['issues'])}",
                    'details': validation_details
                }
            elif validation_details['issues']:
                return {
                    'success': True,
                    'message': f"Shapefile验证通过，但有警告: {'; '.join(validation_details['issues'])}",
                    'details': validation_details
                }
            else:
                return {
                    'success': True,
                    'message': "Shapefile验证通过",
                    'details': validation_details
                }

        except Exception as e:
            error_msg = f"Shapefile验证过程出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg, 'details': {}}

    def _get_shape_type_name(self, shape_type):
        """获取形状类型名称"""
        shape_types = {
            0: 'Null Shape',
            1: 'Point',
            3: 'Polyline',
            5: 'Polygon',
            8: 'MultiPoint',
            11: 'PointZ',
            13: 'PolylineZ',
            15: 'PolygonZ',
            18: 'MultiPointZ',
            21: 'PointM',
            23: 'PolylineM',
            25: 'PolygonM',
            28: 'MultiPointM',
            31: 'MultiPatch'
        }
        return shape_types.get(shape_type, f'Unknown ({shape_type})')

    def get_datastore_featuretypes(self, workspace, store_name):
        """
        获取数据存储中的要素类型列表

        参数:
            workspace: 工作区名称
            store_name: 存储名称

        返回:
            dict: {'success': bool, 'featuretypes': list, 'message': str}
        """
        try:
            from urllib.parse import quote
            encoded_workspace = quote(workspace, safe='')
            encoded_store_name = quote(store_name, safe='')

            url = urljoin(self.rest_url, f'workspaces/{encoded_workspace}/datastores/{encoded_store_name}/featuretypes')

            response = requests.get(
                url,
                auth=self.auth,
                headers={'Accept': 'application/json'},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                featuretypes = []

                if 'featureTypes' in data and 'featureType' in data['featureTypes']:
                    ft_list = data['featureTypes']['featureType']
                    if isinstance(ft_list, list):
                        featuretypes = [ft['name'] for ft in ft_list]
                    else:
                        featuretypes = [ft_list['name']]

                logger.info(f"获取到要素类型: {featuretypes}")
                return {'success': True, 'featuretypes': featuretypes, 'message': '获取成功'}
            else:
                error_msg = f"获取要素类型失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'featuretypes': [], 'message': error_msg}

        except Exception as e:
            error_msg = f"获取要素类型时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'featuretypes': [], 'message': error_msg}

    def publish_layer(self, workspace, store_name, layer_name, shapefile_path=None):
        """
        发布图层

        参数:
            workspace: 工作区名称
            store_name: 存储名称
            layer_name: 图层名称
            shapefile_path: Shapefile文件路径（用于获取原始文件名）

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # URL编码处理，避免中文字符问题
            from urllib.parse import quote
            encoded_workspace = quote(workspace, safe='')
            encoded_store_name = quote(store_name, safe='')

            url = urljoin(self.rest_url, f'workspaces/{encoded_workspace}/datastores/{encoded_store_name}/featuretypes')

            # 首先获取数据存储中可用的要素类型
            featuretypes_result = self.get_datastore_featuretypes(workspace, store_name)

            logger.info(f"要素类型查询结果: {featuretypes_result}")

            if not featuretypes_result['success']:
                logger.error(f"无法获取数据存储的要素类型: {featuretypes_result['message']}")
                # 不要因为这个失败就停止，继续尝试发布
                logger.warning("继续尝试发布图层，即使无法获取要素类型列表")
                available_featuretypes = []
            else:
                available_featuretypes = featuretypes_result['featuretypes']

            logger.info(f"数据存储中可用的要素类型: {available_featuretypes}")

            # 如果没有找到要素类型，可能需要等待或强制刷新数据存储
            if not available_featuretypes:
                logger.warning("数据存储中没有找到要素类型，尝试强制刷新...")

                # 等待一下让GeoServer处理文件
                import time
                time.sleep(2)

                # 再次尝试获取要素类型
                featuretypes_result = self.get_datastore_featuretypes(workspace, store_name)
                available_featuretypes = featuretypes_result.get('featuretypes', [])
                logger.info(f"刷新后的要素类型: {available_featuretypes}")

                # 如果还是没有，尝试使用文件名作为要素类型名
                if not available_featuretypes and shapefile_path:
                    from pathlib import Path
                    potential_featuretype = Path(shapefile_path).stem
                    logger.info(f"尝试使用文件名作为要素类型: {potential_featuretype}")
                    available_featuretypes = [potential_featuretype]

            # 获取正确的nativeName
            if shapefile_path:
                from pathlib import Path
                native_name = Path(shapefile_path).stem
                logger.info(f"使用shapefile路径获取nativeName: {native_name}")
            else:
                # 如果没有shapefile路径，尝试从可用的要素类型中选择第一个
                if available_featuretypes:
                    native_name = available_featuretypes[0]
                    logger.info(f"使用第一个可用要素类型作为nativeName: {native_name}")
                else:
                    # 最后使用store_name
                    native_name = store_name
                    logger.info(f"使用store_name作为nativeName: {native_name}")

            # 验证nativeName是否在可用的要素类型中
            if available_featuretypes and native_name not in available_featuretypes:
                logger.warning(f"nativeName '{native_name}' 不在可用要素类型中: {available_featuretypes}")
                # 使用第一个可用的要素类型
                if available_featuretypes:
                    native_name = available_featuretypes[0]
                    logger.info(f"改用第一个可用要素类型: {native_name}")

            # 如果仍然没有要素类型，尝试强制创建
            if not available_featuretypes:
                logger.warning("数据存储中没有要素类型，尝试强制创建要素类型...")

                # 构建要素类型创建请求
                featuretype_data = {
                    "featureType": {
                        "name": native_name,
                        "nativeName": native_name,
                        "title": layer_name,
                        "srs": "EPSG:4326",  # 默认坐标系
                        "enabled": True,
                        "store": {
                            "@class": "dataStore",
                            "name": store_name,
                            "href": f"{self.rest_url}workspaces/{workspace}/datastores/{store_name}.json"
                        }
                    }
                }

                # 尝试创建要素类型
                featuretype_url = f"{self.rest_url}workspaces/{workspace}/datastores/{store_name}/featuretypes"

                try:
                    logger.info(f"尝试创建要素类型: {featuretype_url}")
                    logger.info(f"要素类型数据: {featuretype_data}")

                    response = requests.post(
                        featuretype_url,
                        json=featuretype_data,
                        auth=self.auth,
                        headers=self.headers,
                        timeout=30
                    )

                    logger.info(f"创建要素类型响应状态码: {response.status_code}")

                    if response.status_code in [200, 201]:
                        logger.info("✅ 成功创建要素类型")
                        available_featuretypes = [native_name]
                    else:
                        logger.warning(f"创建要素类型失败: {response.status_code} - {response.text}")

                except Exception as ft_error:
                    logger.warning(f"创建要素类型时出错: {str(ft_error)}")

            # 记录调试信息
            logger.info(f"发布图层参数:")
            logger.info(f"  workspace: {workspace}")
            logger.info(f"  store_name: {store_name}")
            logger.info(f"  layer_name: {layer_name}")
            logger.info(f"  native_name: {native_name}")
            logger.info(f"  shapefile_path: {shapefile_path}")
            logger.info(f"  available_featuretypes: {available_featuretypes}")

            data = {
                "featureType": {
                    "name": layer_name,  # 图层名称
                    "nativeName": native_name,  # 使用正确的nativeName
                    "title": layer_name,  # 显示标题
                    "enabled": True
                }
            }
            
            response = requests.post(
                url,
                json=data,
                auth=self.auth,
                headers=self.headers,
                timeout=60  # 增加超时时间
            )
            
            if response.status_code == 201:
                logger.info(f"成功发布图层: {workspace}:{layer_name}")
                return {'success': True, 'message': f'图层 {layer_name} 发布成功'}
            elif response.status_code == 409:
                logger.info(f"图层已存在: {workspace}:{layer_name}")
                return {'success': True, 'message': f'图层 {layer_name} 已存在'}
            else:
                error_msg = f"发布图层失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}
                
        except Exception as e:
            error_msg = f"发布图层时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}
    
    def test_connection(self):
        """
        测试GeoServer连接

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # 测试工作区列表端点
            url = urljoin(self.rest_url, 'workspaces')

            response = requests.get(
                url,
                auth=self.auth,
                headers={'Accept': 'application/json'},
                timeout=10
            )

            if response.status_code == 200:
                logger.info(f"GeoServer连接成功: {self.base_url}")
                return {'success': True, 'message': 'GeoServer连接成功'}
            else:
                error_msg = f'GeoServer连接失败，状态码: {response.status_code}, 响应: {response.text[:200]}'
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

        except Exception as e:
            error_msg = f'GeoServer连接测试失败: {str(e)}'
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def publish_layer_with_crs(self, workspace, store_name, layer_name, crs="EPSG:4326", shapefile_path=None):
        """
        发布图层（支持指定坐标系）

        参数:
            workspace: 工作区名称
            store_name: 存储名称
            layer_name: 图层名称
            crs: 坐标系，如 "EPSG:4326"
            shapefile_path: Shapefile文件路径（用于获取原始文件名）

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # URL编码处理，避免中文字符问题
            from urllib.parse import quote
            encoded_workspace = quote(workspace, safe='')
            encoded_store_name = quote(store_name, safe='')

            url = urljoin(self.rest_url, f'workspaces/{encoded_workspace}/datastores/{encoded_store_name}/featuretypes')

            # 首先获取数据存储中可用的要素类型
            featuretypes_result = self.get_datastore_featuretypes(workspace, store_name)
            logger.info(f"要素类型查询结果: {featuretypes_result}")

            if not featuretypes_result['success']:
                logger.error(f"无法获取数据存储的要素类型: {featuretypes_result['message']}")
                available_featuretypes = []
            else:
                available_featuretypes = featuretypes_result['featuretypes']

            logger.info(f"数据存储中可用的要素类型: {available_featuretypes}")

            # 获取正确的nativeName
            if shapefile_path:
                from pathlib import Path
                native_name = Path(shapefile_path).stem
                logger.info(f"使用shapefile路径获取nativeName: {native_name}")
            else:
                if available_featuretypes:
                    native_name = available_featuretypes[0]
                    logger.info(f"使用第一个可用要素类型作为nativeName: {native_name}")
                else:
                    native_name = store_name
                    logger.info(f"使用store_name作为nativeName: {native_name}")

            # 构建要素类型创建请求（使用指定的坐标系）
            featuretype_data = {
                "featureType": {
                    "name": native_name,
                    "nativeName": native_name,
                    "title": layer_name,
                    "srs": crs,  # 使用指定的坐标系
                    "enabled": True,
                    "store": {
                        "@class": "dataStore",
                        "name": store_name,
                        "href": f"{self.rest_url}workspaces/{workspace}/datastores/{store_name}.json"
                    }
                }
            }

            logger.info(f"发布图层 (CRS: {crs}): {workspace}:{layer_name}")
            logger.info(f"要素类型数据: {featuretype_data}")

            response = requests.post(
                url,
                json=featuretype_data,
                auth=self.auth,
                headers=self.headers,
                timeout=60
            )

            if response.status_code == 201:
                logger.info(f"成功发布图层: {workspace}:{layer_name} (CRS: {crs})")
                return {
                    'success': True,
                    'message': f'图层 {layer_name} 发布成功',
                    'crs': crs
                }
            else:
                error_msg = f"发布图层失败: HTTP {response.status_code}"
                if response.text:
                    error_msg += f" - {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

        except Exception as e:
            error_msg = f"发布图层时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def check_layer_exists(self, workspace, layer_name):
        """
        检查图层是否存在

        参数:
            workspace: 工作区名称
            layer_name: 图层名称

        返回:
            bool: 图层是否存在
        """
        try:
            url = urljoin(self.rest_url, f'workspaces/{workspace}/layers/{layer_name}')

            response = requests.get(
                url,
                auth=self.auth,
                headers={'Accept': 'application/json'},
                timeout=10
            )

            exists = response.status_code == 200
            logger.info(f"图层 {workspace}:{layer_name} {'存在' if exists else '不存在'}")
            return exists

        except Exception as e:
            logger.error(f"检查图层存在性时出错: {str(e)}")
            return False

    def check_datastore_exists(self, workspace, store_name):
        """
        检查数据存储是否存在

        参数:
            workspace: 工作区名称
            store_name: 存储名称

        返回:
            bool: 数据存储是否存在
        """
        try:
            url = urljoin(self.rest_url, f'workspaces/{workspace}/datastores/{store_name}')

            response = requests.get(
                url,
                auth=self.auth,
                headers={'Accept': 'application/json'},
                timeout=10
            )

            exists = response.status_code == 200
            logger.info(f"数据存储 {workspace}:{store_name} {'存在' if exists else '不存在'}")
            return exists

        except Exception as e:
            logger.error(f"检查数据存储存在性时出错: {str(e)}")
            return False

    def delete_workspace_store_layer(self, workspace, store_name, layer_name):
        """
        删除指定的工作区-存储仓库-图层组合

        参数:
            workspace: 工作区名称
            store_name: 存储名称
            layer_name: 图层名称

        返回:
            dict: {'success': bool, 'message': str, 'details': list}
        """
        try:
            deleted_items = []
            errors = []

            logger.info(f"开始删除组合: {workspace}:{store_name}:{layer_name}")

            # 1. 删除图层定义
            try:
                layer_url = urljoin(self.rest_url, f'workspaces/{workspace}/layers/{layer_name}')
                layer_response = requests.delete(
                    layer_url,
                    auth=self.auth,
                    timeout=60
                )

                if layer_response.status_code == 200:
                    deleted_items.append(f"图层: {layer_name}")
                    logger.info(f"成功删除图层: {workspace}:{layer_name}")
                elif layer_response.status_code == 404:
                    logger.info(f"图层不存在: {workspace}:{layer_name}")
                else:
                    errors.append(f"删除图层失败: {layer_response.status_code}")
                    logger.warning(f"删除图层失败: {layer_response.status_code}")

            except requests.exceptions.Timeout:
                errors.append("删除图层超时")
                logger.warning(f"删除图层超时: {workspace}:{layer_name}")
            except Exception as e:
                errors.append(f"删除图层出错: {str(e)}")
                logger.error(f"删除图层出错: {str(e)}")

            # 2. 删除featuretype
            try:
                ft_url = urljoin(self.rest_url, f'workspaces/{workspace}/datastores/{store_name}/featuretypes/{layer_name}')
                ft_response = requests.delete(
                    ft_url,
                    auth=self.auth,
                    timeout=60
                )

                if ft_response.status_code == 200:
                    deleted_items.append(f"FeatureType: {layer_name}")
                    logger.info(f"成功删除FeatureType: {workspace}:{store_name}:{layer_name}")
                elif ft_response.status_code == 404:
                    logger.info(f"FeatureType不存在: {workspace}:{store_name}:{layer_name}")
                else:
                    errors.append(f"删除FeatureType失败: {ft_response.status_code}")
                    logger.warning(f"删除FeatureType失败: {ft_response.status_code}")

            except requests.exceptions.Timeout:
                errors.append("删除FeatureType超时")
                logger.warning(f"删除FeatureType超时: {workspace}:{store_name}:{layer_name}")
            except Exception as e:
                errors.append(f"删除FeatureType出错: {str(e)}")
                logger.error(f"删除FeatureType出错: {str(e)}")

            # 3. 删除数据存储
            try:
                ds_url = urljoin(self.rest_url, f'workspaces/{workspace}/datastores/{store_name}')
                ds_response = requests.delete(
                    ds_url,
                    auth=self.auth,
                    params={'recurse': 'true'},
                    timeout=60
                )

                if ds_response.status_code == 200:
                    deleted_items.append(f"数据存储: {store_name}")
                    logger.info(f"成功删除数据存储: {workspace}:{store_name}")
                elif ds_response.status_code == 404:
                    logger.info(f"数据存储不存在: {workspace}:{store_name}")
                else:
                    errors.append(f"删除数据存储失败: {ds_response.status_code}")
                    logger.warning(f"删除数据存储失败: {ds_response.status_code}")

            except requests.exceptions.Timeout:
                errors.append("删除数据存储超时")
                logger.warning(f"删除数据存储超时: {workspace}:{store_name}")
            except Exception as e:
                errors.append(f"删除数据存储出错: {str(e)}")
                logger.error(f"删除数据存储出错: {str(e)}")

            # 4. 返回结果
            success = len(deleted_items) > 0 or len(errors) == 0
            message = f"删除组合完成: 成功删除 {len(deleted_items)} 项，{len(errors)} 个错误"

            logger.info(f"删除组合操作完成: {workspace}:{store_name}:{layer_name} - {message}")

            return {
                'success': success,
                'message': message,
                'details': {
                    'deleted': deleted_items,
                    'errors': errors
                }
            }

        except Exception as e:
            error_msg = f"删除组合时出错: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'details': {
                    'deleted': [],
                    'errors': [error_msg]
                }
            }

    def delete_layer_and_datastore(self, workspace, layer_name, store_name=None):
        """
        删除图层和相关的数据存储

        参数:
            workspace: 工作区名称
            layer_name: 图层名称
            store_name: 存储名称（可选，如果不提供则尝试删除所有相关的数据存储）

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            # 1. 删除图层（这会删除图层定义）
            layer_url = urljoin(self.rest_url, f'workspaces/{workspace}/layers/{layer_name}')

            try:
                layer_response = requests.delete(
                    layer_url,
                    auth=self.auth,
                    params={'recurse': 'true'},  # 递归删除相关资源
                    timeout=60
                )

                if layer_response.status_code in [200, 404]:
                    logger.info(f"图层删除成功或不存在: {workspace}:{layer_name}")
                else:
                    logger.warning(f"删除图层失败，状态码: {layer_response.status_code}")
            except requests.exceptions.Timeout:
                logger.warning(f"删除图层超时: {workspace}:{layer_name}")

            # 2. 如果提供了store_name，删除特定的数据存储
            if store_name:
                datastore_result = self.delete_datastore(workspace, store_name)
                logger.info(f"删除数据存储结果: {datastore_result.get('message', '')}")

            # 3. 尝试删除所有可能相关的数据存储（以layer_name开头的）
            try:
                # 获取工作区中的所有数据存储
                datastores_url = urljoin(self.rest_url, f'workspaces/{workspace}/datastores')
                ds_response = requests.get(
                    datastores_url,
                    auth=self.auth,
                    headers={'Accept': 'application/json'},
                    timeout=30
                )

                if ds_response.status_code == 200:
                    datastores_data = ds_response.json()
                    if 'dataStores' in datastores_data and 'dataStore' in datastores_data['dataStores']:
                        datastores = datastores_data['dataStores']['dataStore']
                        if not isinstance(datastores, list):
                            datastores = [datastores]

                        # 删除所有以layer_name开头的数据存储
                        for ds in datastores:
                            ds_name = ds.get('name', '')
                            if ds_name.startswith(layer_name):
                                logger.info(f"删除相关数据存储: {ds_name}")
                                self.delete_datastore(workspace, ds_name)

            except Exception as e:
                logger.warning(f"清理相关数据存储时出错: {str(e)}")

            logger.info(f"完成删除操作: {workspace}:{layer_name}")
            return {'success': True, 'message': f'图层 {layer_name} 删除完成'}

        except Exception as e:
            error_msg = f"删除图层和数据存储时出错: {str(e)}"
            logger.error(error_msg)
            # 即使删除出错，我们也尝试继续发布
            return {'success': True, 'message': f'删除操作遇到问题但继续发布: {str(e)}'}

    def delete_layer_by_name(self, workspace, layer_name):
        """
        根据图层名称删除工作区下的所有相关数据

        参数:
            workspace: 工作区名称
            layer_name: 图层名称

        返回:
            dict: {'success': bool, 'message': str, 'details': list}
        """
        try:
            deleted_items = []
            errors = []

            logger.info(f"开始删除工作区 {workspace} 中图层 {layer_name} 的所有相关数据")

            # 1. 删除图层定义
            try:
                layer_url = urljoin(self.rest_url, f'workspaces/{workspace}/layers/{layer_name}')
                layer_response = requests.delete(
                    layer_url,
                    auth=self.auth,
                    params={'recurse': 'true'},
                    timeout=60
                )

                if layer_response.status_code == 200:
                    deleted_items.append(f"图层: {layer_name}")
                    logger.info(f"成功删除图层: {workspace}:{layer_name}")
                elif layer_response.status_code == 404:
                    logger.info(f"图层不存在: {workspace}:{layer_name}")
                else:
                    errors.append(f"删除图层失败: {layer_response.status_code}")
                    logger.warning(f"删除图层失败: {layer_response.status_code}")

            except requests.exceptions.Timeout:
                errors.append("删除图层超时")
                logger.warning(f"删除图层超时: {workspace}:{layer_name}")
            except Exception as e:
                errors.append(f"删除图层出错: {str(e)}")
                logger.error(f"删除图层出错: {str(e)}")

            # 2. 获取并删除所有相关的数据存储
            try:
                datastores_url = urljoin(self.rest_url, f'workspaces/{workspace}/datastores')
                ds_response = requests.get(
                    datastores_url,
                    auth=self.auth,
                    headers={'Accept': 'application/json'},
                    timeout=30
                )

                if ds_response.status_code == 200:
                    datastores_data = ds_response.json()
                    if 'dataStores' in datastores_data and 'dataStore' in datastores_data['dataStores']:
                        datastores = datastores_data['dataStores']['dataStore']
                        if not isinstance(datastores, list):
                            datastores = [datastores]

                        # 删除所有以layer_name开头的数据存储
                        for ds in datastores:
                            ds_name = ds.get('name', '')
                            if ds_name == layer_name or ds_name.startswith(f"{layer_name}_"):
                                try:
                                    ds_url = urljoin(self.rest_url, f'workspaces/{workspace}/datastores/{ds_name}')
                                    ds_delete_response = requests.delete(
                                        ds_url,
                                        auth=self.auth,
                                        params={'recurse': 'true'},
                                        timeout=60
                                    )

                                    if ds_delete_response.status_code == 200:
                                        deleted_items.append(f"数据存储: {ds_name}")
                                        logger.info(f"成功删除数据存储: {ds_name}")
                                    elif ds_delete_response.status_code == 404:
                                        logger.info(f"数据存储不存在: {ds_name}")
                                    else:
                                        errors.append(f"删除数据存储 {ds_name} 失败: {ds_delete_response.status_code}")
                                        logger.warning(f"删除数据存储 {ds_name} 失败: {ds_delete_response.status_code}")

                                except requests.exceptions.Timeout:
                                    errors.append(f"删除数据存储 {ds_name} 超时")
                                    logger.warning(f"删除数据存储 {ds_name} 超时")
                                except Exception as e:
                                    errors.append(f"删除数据存储 {ds_name} 出错: {str(e)}")
                                    logger.error(f"删除数据存储 {ds_name} 出错: {str(e)}")

                else:
                    errors.append(f"获取数据存储列表失败: {ds_response.status_code}")
                    logger.warning(f"获取数据存储列表失败: {ds_response.status_code}")

            except Exception as e:
                errors.append(f"处理数据存储时出错: {str(e)}")
                logger.error(f"处理数据存储时出错: {str(e)}")

            # 3. 返回结果
            success = len(deleted_items) > 0 or len(errors) == 0
            message = f"删除完成: 成功删除 {len(deleted_items)} 项，{len(errors)} 个错误"

            logger.info(f"删除操作完成: {workspace}:{layer_name} - {message}")

            return {
                'success': success,
                'message': message,
                'details': {
                    'deleted': deleted_items,
                    'errors': errors
                }
            }

        except Exception as e:
            error_msg = f"删除图层数据时出错: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message': error_msg,
                'details': {
                    'deleted': [],
                    'errors': [error_msg]
                }
            }

    def create_tile_layer(self, workspace, layer_name, gridset='EPSG:4326', image_format='image/png'):
        """
        创建瓦片图层（GWC缓存层）

        参数:
            workspace: 工作区名称
            layer_name: 图层名称
            gridset: 网格集名称，默认EPSG:4326
            image_format: 图像格式，默认image/png

        返回:
            dict: {'success': bool, 'message': str}
        """
        try:
            from urllib.parse import quote

            # URL编码处理
            encoded_workspace = quote(workspace, safe='')
            encoded_layer_name = quote(layer_name, safe='')

            # GWC REST API URL - 修复路径
            # 使用base_url而不是rest_url来构建GWC URL
            gwc_base = self.base_url.rstrip('/')
            gwc_url = f"{gwc_base}/gwc/rest/layers/{encoded_workspace}:{encoded_layer_name}.xml"

            # 构建瓦片层配置
            tile_layer_config = f'''<?xml version="1.0" encoding="UTF-8"?>
<GeoServerLayer>
    <name>{workspace}:{layer_name}</name>
    <enabled>true</enabled>
    <mimeFormats>
        <string>{image_format}</string>
    </mimeFormats>
    <gridSubsets>
        <gridSubset>
            <gridSetName>{gridset}</gridSetName>
            <zoomStart>0</zoomStart>
            <zoomStop>20</zoomStop>
        </gridSubset>
    </gridSubsets>
    <metaWidthHeight>
        <int>4</int>
        <int>4</int>
    </metaWidthHeight>
    <expireCache>0</expireCache>
    <expireClients>0</expireClients>
    <parameterFilters/>
    <gutter>0</gutter>
</GeoServerLayer>'''

            logger.info(f"创建瓦片图层: {workspace}:{layer_name}")
            logger.info(f"网格集: {gridset}, 格式: {image_format}")
            logger.info(f"GWC URL: {gwc_url}")
            logger.info(f"GWC Base: {gwc_base}")

            response = requests.put(
                gwc_url,
                data=tile_layer_config.encode('utf-8'),
                auth=self.auth,
                headers={'Content-Type': 'text/xml; charset=utf-8'},
                timeout=60
            )

            if response.status_code in [200, 201]:
                logger.info(f"成功创建瓦片图层: {workspace}:{layer_name}")
                return {'success': True, 'message': f'瓦片图层 {workspace}:{layer_name} 创建成功'}
            else:
                error_msg = f"创建瓦片图层失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

        except Exception as e:
            error_msg = f"创建瓦片图层时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def seed_tile_layer(self, workspace, layer_name, gridset='EPSG:4326',
                       zoom_start=0, zoom_stop=20, image_format='image/png',
                       operation='seed', thread_count=4):
        """
        对瓦片图层进行切片种子操作

        参数:
            workspace: 工作区名称
            layer_name: 图层名称
            gridset: 网格集名称，默认EPSG:4326
            zoom_start: 起始缩放级别，默认0
            zoom_stop: 结束缩放级别，默认20
            image_format: 图像格式，默认image/png
            operation: 操作类型，seed(生成)/reseed(重新生成)/truncate(清除)
            thread_count: 线程数，默认4

        返回:
            dict: {'success': bool, 'message': str, 'task_id': str}
        """
        try:
            from urllib.parse import quote

            # URL编码处理
            encoded_workspace = quote(workspace, safe='')
            encoded_layer_name = quote(layer_name, safe='')

            # GWC种子REST API URL - 修复路径
            # 使用base_url而不是rest_url来构建GWC URL
            gwc_base = self.base_url.rstrip('/')
            seed_url = f"{gwc_base}/gwc/rest/seed/{encoded_workspace}:{encoded_layer_name}.xml"

            # 构建种子任务配置
            seed_config = f'''<?xml version="1.0" encoding="UTF-8"?>
<seedRequest>
    <name>{workspace}:{layer_name}</name>
    <gridSetId>{gridset}</gridSetId>
    <zoomStart>{zoom_start}</zoomStart>
    <zoomStop>{zoom_stop}</zoomStop>
    <format>{image_format}</format>
    <type>{operation}</type>
    <threadCount>{thread_count}</threadCount>
</seedRequest>'''

            logger.info(f"开始切片操作: {workspace}:{layer_name}")
            logger.info(f"操作类型: {operation}, 级别: {zoom_start}-{zoom_stop}")
            logger.info(f"网格集: {gridset}, 格式: {image_format}")
            logger.info(f"线程数: {thread_count}")
            logger.info(f"种子URL: {seed_url}")

            response = requests.post(
                seed_url,
                data=seed_config.encode('utf-8'),
                auth=self.auth,
                headers={'Content-Type': 'text/xml; charset=utf-8'},
                timeout=60
            )

            if response.status_code in [200, 201]:
                # 尝试从响应中获取任务ID
                task_id = None
                try:
                    # GeoServer可能在响应头或响应体中返回任务ID
                    if 'Location' in response.headers:
                        task_id = response.headers['Location'].split('/')[-1]
                    elif response.text:
                        # 简单解析XML响应获取任务ID
                        import re
                        task_match = re.search(r'<taskId>(\d+)</taskId>', response.text)
                        if task_match:
                            task_id = task_match.group(1)
                except:
                    pass

                logger.info(f"成功启动切片任务: {workspace}:{layer_name}")
                return {
                    'success': True,
                    'message': f'切片任务已启动: {workspace}:{layer_name}',
                    'task_id': task_id or 'unknown'
                }
            else:
                error_msg = f"启动切片任务失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'message': error_msg}

        except Exception as e:
            error_msg = f"启动切片任务时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def get_seed_status(self, workspace=None, layer_name=None):
        """
        获取切片任务状态

        参数:
            workspace: 工作区名称（可选，不提供则获取所有任务）
            layer_name: 图层名称（可选）

        返回:
            dict: {'success': bool, 'tasks': list, 'message': str}
        """
        try:
            # GWC种子状态REST API URL - 修复路径
            # 使用base_url而不是rest_url来构建GWC URL
            gwc_base = self.base_url.rstrip('/')

            if workspace and layer_name:
                from urllib.parse import quote
                encoded_workspace = quote(workspace, safe='')
                encoded_layer_name = quote(layer_name, safe='')
                status_url = f"{gwc_base}/gwc/rest/seed/{encoded_workspace}:{encoded_layer_name}.xml"
            else:
                status_url = f"{gwc_base}/gwc/rest/seed.xml"

            logger.info(f"查询切片任务状态: {status_url}")

            response = requests.get(
                status_url,
                auth=self.auth,
                headers={'Accept': 'application/xml'},
                timeout=30
            )

            if response.status_code == 200:
                # 解析XML响应
                tasks = []
                try:
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(response.text)

                    # 解析任务信息
                    for task in root.findall('.//task'):
                        task_info = {
                            'layer': task.find('layerName').text if task.find('layerName') is not None else 'unknown',
                            'type': task.find('type').text if task.find('type') is not None else 'unknown',
                            'status': task.find('state').text if task.find('state') is not None else 'unknown',
                            'tiles_processed': task.find('tilesProcessed').text if task.find('tilesProcessed') is not None else '0',
                            'tiles_total': task.find('tilesTotal').text if task.find('tilesTotal') is not None else '0',
                            'time_remaining': task.find('timeRemaining').text if task.find('timeRemaining') is not None else 'unknown'
                        }
                        tasks.append(task_info)

                except Exception as parse_error:
                    logger.warning(f"解析切片任务状态XML失败: {str(parse_error)}")
                    # 返回原始响应
                    tasks = [{'raw_response': response.text}]

                logger.info(f"获取到 {len(tasks)} 个切片任务")
                return {'success': True, 'tasks': tasks, 'message': '获取切片任务状态成功'}
            else:
                error_msg = f"获取切片任务状态失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return {'success': False, 'tasks': [], 'message': error_msg}

        except Exception as e:
            error_msg = f"获取切片任务状态时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'tasks': [], 'message': error_msg}

    def auto_tile_layer(self, workspace, layer_name, gridset='EPSG:4326',
                       zoom_start=0, zoom_stop=20, image_format='image/png'):
        """
        自动切片图层（创建瓦片层 + 启动切片任务）

        参数:
            workspace: 工作区名称
            layer_name: 图层名称
            gridset: 网格集名称，默认EPSG:4326
            zoom_start: 起始缩放级别，默认0
            zoom_stop: 结束缩放级别，默认20
            image_format: 图像格式，默认image/png

        返回:
            dict: {'success': bool, 'message': str, 'details': dict}
        """
        try:
            logger.info(f"开始自动切片图层: {workspace}:{layer_name}")

            # 1. 创建瓦片图层
            tile_result = self.create_tile_layer(workspace, layer_name, gridset, image_format)
            if not tile_result['success']:
                # 如果创建失败，可能是已经存在，继续尝试切片
                logger.warning(f"瓦片图层创建失败，可能已存在: {tile_result['message']}")

            # 2. 启动切片任务
            seed_result = self.seed_tile_layer(
                workspace, layer_name, gridset,
                zoom_start, zoom_stop, image_format
            )

            if seed_result['success']:
                logger.info(f"自动切片完成: {workspace}:{layer_name}")
                return {
                    'success': True,
                    'message': f'自动切片任务已启动: {workspace}:{layer_name}',
                    'details': {
                        'tile_layer_result': tile_result,
                        'seed_result': seed_result,
                        'gridset': gridset,
                        'zoom_levels': f'{zoom_start}-{zoom_stop}',
                        'format': image_format
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f'切片任务启动失败: {seed_result["message"]}',
                    'details': {
                        'tile_layer_result': tile_result,
                        'seed_result': seed_result
                    }
                }

        except Exception as e:
            error_msg = f"自动切片时出错: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'message': error_msg}

    def create_style(self, style_name, style_content, workspace=None, overwrite=True):
        """
        创建或更新GeoServer样式

        参数:
            style_name: 样式名称
            style_content: 样式内容（SLD格式的XML）
            workspace: 工作区名称，None表示全局样式
            overwrite: 是否覆盖已存在的样式

        返回:
            dict: {'success': bool, 'message': str, 'details': dict}
        """
        try:
            logger.info(f"开始创建样式: {style_name}")
            logger.info(f"工作区: {workspace if workspace else '全局样式'}")
            logger.info(f"覆盖模式: {overwrite}")

            # 构建样式URL
            if workspace:
                # 工作区样式
                style_url = f"{self.rest_url}workspaces/{workspace}/styles"
                style_detail_url = f"{self.rest_url}workspaces/{workspace}/styles/{style_name}"
            else:
                # 全局样式
                style_url = f"{self.rest_url}styles"
                style_detail_url = f"{self.rest_url}styles/{style_name}"

            logger.info(f"样式URL: {style_url}")
            logger.info(f"样式详细URL: {style_detail_url}")

            # 检查样式是否已存在
            style_exists = False
            try:
                check_response = requests.get(
                    style_detail_url,
                    auth=self.auth,
                    timeout=30
                )
                style_exists = check_response.status_code == 200
                logger.info(f"样式 {style_name} 存在性检查: {style_exists}")
            except Exception as check_error:
                logger.warning(f"检查样式存在性时出错: {str(check_error)}")

            # 如果样式已存在且不允许覆盖
            if style_exists and not overwrite:
                return {
                    'success': False,
                    'message': f'样式 {style_name} 已存在，且未启用覆盖模式',
                    'details': {
                        'style_name': style_name,
                        'workspace': workspace,
                        'exists': True,
                        'overwrite': False
                    }
                }

            # 准备样式数据 - 使用XML格式
            style_xml = f'''<?xml version="1.0" encoding="UTF-8"?>
<style>
    <name>{style_name}</name>
    <filename>{style_name}.sld</filename>
</style>'''

            # 如果样式已存在，使用PUT更新；否则使用POST创建
            if style_exists:
                logger.info(f"更新已存在的样式: {style_name}")

                # 先更新样式元数据
                response = requests.put(
                    style_detail_url,
                    data=style_xml.encode('utf-8'),
                    auth=self.auth,
                    headers={'Content-Type': 'text/xml; charset=utf-8'},
                    timeout=30
                )

                if response.status_code not in [200, 201]:
                    logger.error(f"更新样式元数据失败: {response.status_code} - {response.text}")
                    return {
                        'success': False,
                        'message': f'更新样式元数据失败: HTTP {response.status_code}',
                        'details': {
                            'style_name': style_name,
                            'workspace': workspace,
                            'response_code': response.status_code,
                            'response_text': response.text[:500]
                        }
                    }

                # 更新样式内容
                content_url = f"{style_detail_url}.sld"
                content_response = requests.put(
                    content_url,
                    data=style_content.encode('utf-8'),
                    auth=self.auth,
                    headers={'Content-Type': 'application/vnd.ogc.sld+xml; charset=utf-8'},
                    timeout=30
                )

                if content_response.status_code not in [200, 201]:
                    logger.error(f"更新样式内容失败: {content_response.status_code} - {content_response.text}")
                    return {
                        'success': False,
                        'message': f'更新样式内容失败: HTTP {content_response.status_code}',
                        'details': {
                            'style_name': style_name,
                            'workspace': workspace,
                            'response_code': content_response.status_code,
                            'response_text': content_response.text[:500]
                        }
                    }

                logger.info(f"样式 {style_name} 更新成功")

            else:
                logger.info(f"创建新样式: {style_name}")

                # 创建新样式
                response = requests.post(
                    style_url,
                    data=style_xml.encode('utf-8'),
                    auth=self.auth,
                    headers={'Content-Type': 'text/xml; charset=utf-8'},
                    timeout=30
                )

                if response.status_code not in [200, 201]:
                    logger.error(f"创建样式失败: {response.status_code} - {response.text}")
                    return {
                        'success': False,
                        'message': f'创建样式失败: HTTP {response.status_code}',
                        'details': {
                            'style_name': style_name,
                            'workspace': workspace,
                            'response_code': response.status_code,
                            'response_text': response.text[:500]
                        }
                    }

                # 上传样式内容
                content_url = f"{style_detail_url}.sld"
                content_response = requests.put(
                    content_url,
                    data=style_content.encode('utf-8'),
                    auth=self.auth,
                    headers={'Content-Type': 'application/vnd.ogc.sld+xml; charset=utf-8'},
                    timeout=30
                )

                if content_response.status_code not in [200, 201]:
                    logger.error(f"上传样式内容失败: {content_response.status_code} - {content_response.text}")
                    return {
                        'success': False,
                        'message': f'上传样式内容失败: HTTP {content_response.status_code}',
                        'details': {
                            'style_name': style_name,
                            'workspace': workspace,
                            'response_code': content_response.status_code,
                            'response_text': content_response.text[:500]
                        }
                    }

                logger.info(f"样式 {style_name} 创建成功")

            return {
                'success': True,
                'message': f'样式 {style_name} {"更新" if style_exists else "创建"}成功',
                'details': {
                    'style_name': style_name,
                    'workspace': workspace if workspace else 'global',
                    'action': 'updated' if style_exists else 'created',
                    'overwrite': overwrite,
                    'style_url': style_detail_url
                }
            }

        except Exception as e:
            error_msg = f"创建样式 {style_name} 时出错: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': error_msg,
                'details': {
                    'style_name': style_name,
                    'workspace': workspace,
                    'error': str(e)
                }
            }


# 创建全局实例
geoserver_manager = GeoServerManager()
