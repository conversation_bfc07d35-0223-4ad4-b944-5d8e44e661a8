#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的队列测试
使用你提供的确切参数进行测试
"""

import requests
import json
import time

def test_exact_params():
    """使用确切参数测试"""
    print("🧪 使用确切参数测试排队接口...")
    
    # 你提供的确切URL
    url = "http://192.168.43.148:8091/api/analysis/combined-ai-spatial-analysis-queued/?id=20250705171599&image=D%3A%2FDrone_Project%2FnginxData%2FODM%2FOutput%2F20250705171599%2F20250705171599_out.tif&model=D%3A%2FDrone_Project%2FnginxData%2FODM%2FAIWeight%2FconstructionLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250811-162215.pth&old_data_path=D%3A%2FDrone_Project%2FnginxData%2FODM%2FAIOLDSHP%2FconstructionLand%2F%E5%BB%BA%E8%AE%BE2024_84.shp&area_threshold=200"
    
    print(f"📞 第一次调用...")
    print(f"URL: {url}")
    
    try:
        response1 = requests.get(url, timeout=30)
        print(f"第一次调用 - 状态码: {response1.status_code}")
        
        if response1.status_code == 200:
            try:
                result1 = response1.json()
                print(f"第一次调用 - 响应: {json.dumps(result1, indent=2, ensure_ascii=False)}")
                
                if result1.get('status') == 'success':
                    task_id_1 = result1['data']['task_id']
                    print(f"✅ 第一个任务提交成功: {task_id_1[:8]}...")
                else:
                    print(f"❌ 第一个任务提交失败: {result1.get('message')}")
                    return
                    
            except json.JSONDecodeError:
                print(f"❌ 第一次调用JSON解析失败")
                print(f"原始响应: {response1.text}")
                return
        else:
            print(f"❌ 第一次调用失败: {response1.status_code}")
            print(f"响应内容: {response1.text}")
            return
    
    except Exception as e:
        print(f"❌ 第一次调用异常: {e}")
        return
    
    # 等待2秒
    print(f"\n⏰ 等待2秒...")
    time.sleep(2)
    
    # 修改ID进行第二次调用
    url2 = url.replace('20250705171599', '20250705171598')
    
    print(f"\n📞 第二次调用...")
    print(f"URL: {url2}")
    
    try:
        response2 = requests.get(url2, timeout=30)
        print(f"第二次调用 - 状态码: {response2.status_code}")
        
        if response2.status_code == 200:
            try:
                result2 = response2.json()
                print(f"第二次调用 - 响应: {json.dumps(result2, indent=2, ensure_ascii=False)}")
                
                if result2.get('status') == 'success':
                    task_id_2 = result2['data']['task_id']
                    print(f"✅ 第二个任务提交成功: {task_id_2[:8]}...")
                else:
                    print(f"❌ 第二个任务提交失败: {result2.get('message')}")
                    return
                    
            except json.JSONDecodeError:
                print(f"❌ 第二次调用JSON解析失败")
                print(f"原始响应: {response2.text}")
                return
        else:
            print(f"❌ 第二次调用失败: {response2.status_code}")
            print(f"响应内容: {response2.text}")
            return
    
    except Exception as e:
        print(f"❌ 第二次调用异常: {e}")
        return
    
    # 检查队列状态
    print(f"\n📊 检查队列状态...")
    check_queue_status()

def check_queue_status():
    """检查队列状态"""
    try:
        url = "http://192.168.43.148:8091/api/analysis/queue-status/"
        response = requests.get(url, timeout=10)
        
        print(f"队列状态查询 - 状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"队列状态 - 响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('status') == 'success':
                    data = result['data']
                    executing_task = data.get('executing_task')
                    queue_size = data.get('queue_size', 0)
                    waiting_tasks = data.get('waiting_tasks', 0)
                    
                    print(f"\n🔍 队列分析:")
                    print(f"   执行任务: {executing_task or '无'}")
                    print(f"   队列大小: {queue_size}")
                    print(f"   等待任务: {waiting_tasks}")
                    
                    if executing_task and (queue_size > 0 or waiting_tasks > 0):
                        print(f"   ✅ 排队机制正常工作！")
                    elif executing_task and queue_size == 0 and waiting_tasks == 0:
                        print(f"   ⚠️ 只有一个任务在执行，可能第二个任务没有排队")
                    elif not executing_task:
                        print(f"   ❌ 没有任务在执行，排队机制可能有问题")
                    
                else:
                    print(f"❌ 队列状态查询失败: {result.get('message')}")
                    
            except json.JSONDecodeError:
                print(f"❌ 队列状态JSON解析失败")
                print(f"原始响应: {response.text}")
        else:
            print(f"❌ 队列状态查询失败: {response.status_code}")
            print(f"响应内容: {response.text}")
    
    except Exception as e:
        print(f"❌ 队列状态查询异常: {e}")

def test_interface_exists():
    """测试接口是否存在"""
    print("🔍 测试接口是否存在...")
    
    # 测试基础URL
    base_url = "http://192.168.43.148:8091"
    
    # 测试健康检查
    try:
        health_url = f"{base_url}/health/"
        response = requests.get(health_url, timeout=10)
        print(f"健康检查 - 状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"✅ 服务器正常运行")
        else:
            print(f"⚠️ 服务器可能有问题")
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False
    
    # 测试排队接口（不带参数）
    try:
        queue_url = f"{base_url}/api/analysis/combined-ai-spatial-analysis-queued/"
        response = requests.get(queue_url, timeout=10)
        print(f"排队接口（无参数） - 状态码: {response.status_code}")
        
        if response.status_code == 400:
            print(f"✅ 排队接口存在（返回参数错误是正常的）")
            return True
        elif response.status_code == 404:
            print(f"❌ 排队接口不存在")
            return False
        else:
            print(f"⚠️ 排队接口响应异常: {response.status_code}")
            print(f"响应内容: {response.text}")
            return True
            
    except Exception as e:
        print(f"❌ 排队接口测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始简化队列测试")
    
    print(f"\n📝 测试步骤:")
    print(f"1. 检查服务器和接口是否存在")
    print(f"2. 使用你提供的确切参数进行两次调用")
    print(f"3. 检查队列状态验证排队效果")
    
    # 1. 测试接口是否存在
    if not test_interface_exists():
        print(f"\n❌ 接口不存在，请检查:")
        print(f"1. 服务器是否正常运行")
        print(f"2. URL路由是否正确配置")
        print(f"3. 视图函数是否正确导入")
        return
    
    # 2. 使用确切参数测试
    test_exact_params()
    
    print(f"\n📖 调试提示:")
    print(f"1. 查看服务器日志，寻找这些标记:")
    print(f"   - 🚨🚨🚨 QUEUED_INTERFACE_CALLED 🚨🚨🚨")
    print(f"   - 📞 排队合并分析接口被调用")
    print(f"   - 🏗️ 创建全局任务队列管理器实例")
    print(f"2. 如果没有看到这些日志，说明接口没有被正确调用")
    print(f"3. 如果看到日志但排队不工作，说明队列逻辑有问题")

if __name__ == "__main__":
    main()
