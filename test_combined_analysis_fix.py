#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的合并分析功能
"""

import requests
import time
import json

def test_combined_analysis():
    """测试合并分析功能"""
    print("🧪 测试修复后的合并分析功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    image_id = "20250705171601"
    image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    model_path = "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
    old_data_path = "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  影像ID: {image_id}")
        print(f"  影像路径: {image_path}")
        print(f"  模型路径: {model_path}")
        print(f"  历史数据: {old_data_path}")
        
        # 启动合并分析任务
        print("\n🚀 启动合并分析任务...")
        params = {
            'image_id': image_id,
            'image': image_path,
            'model': model_path,
            'old_data': old_data_path,
            'area_threshold': 400.0
        }
        
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务执行
            success = monitor_task(base_url, task_id)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_task(base_url, task_id, max_wait_time=600):
    """监控任务执行"""
    print(f"\n📊 监控合并分析任务执行...")
    
    start_time = time.time()
    last_progress = -1
    
    while time.time() - start_time < max_wait_time:
        try:
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                task_status = status_data['task_status']
                message = status_data.get('message', '')
                progress = status_data.get('progress', 0)
                
                # 只在进度变化时输出
                if progress != last_progress:
                    print(f"📈 进度: {progress}% - {task_status} - {message}")
                    last_progress = progress
                
                if task_status == '完成':
                    print(f"\n✅ 合并分析任务完成！")
                    
                    # 显示详细结果信息
                    show_task_results(status_data)
                    return True
                    
                elif task_status == '失败':
                    print(f"\n❌ 合并分析任务失败: {message}")
                    
                    # 获取详细日志
                    get_task_logs(base_url, task_id)
                    return False
                
                time.sleep(5)
            else:
                print(f"❌ 状态查询失败: {status_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
            time.sleep(5)
            continue
    
    print(f"\n⏰ 合并分析任务超时")
    return False

def show_task_results(status_data):
    """显示任务结果详情"""
    print(f"\n📊 任务结果详情:")
    print(f"  任务ID: {status_data.get('task_id', 'N/A')}")
    print(f"  影像ID: {status_data.get('image_id', 'N/A')}")
    print(f"  开始时间: {status_data.get('start_time', 'N/A')}")
    print(f"  更新时间: {status_data.get('update_time', 'N/A')}")
    
    # 显示参数信息
    if 'parameters' in status_data:
        params = status_data['parameters']
        print(f"\n📋 任务参数:")
        print(f"  分析类别: {params.get('analysis_category', 'N/A')}")
        print(f"  面积阈值: {params.get('area_threshold', 'N/A')} 平方米")
        print(f"  模型类型: {params.get('model_type', 'N/A')}")
        print(f"  类别数量: {params.get('num_classes', 'N/A')}")
        
        print(f"\n📂 输出文件:")
        print(f"  AI结果: {params.get('ai_output_path', 'N/A')}")
        print(f"  最终结果: {params.get('final_output_path', 'N/A')}")
        print(f"  任务目录: {params.get('task_dir', 'N/A')}")
    
    # 显示结果统计
    if 'result' in status_data:
        result_info = status_data['result']
        print(f"\n📈 分析结果:")
        if 'statistics' in result_info:
            stats = result_info['statistics']
            print(f"  流出图斑: {stats.get('outflow_count', 0)}")
            print(f"  流入图斑: {stats.get('inflow_count', 0)}")
            print(f"  总图斑数: {stats.get('total_count', 0)}")
            print(f"  流出面积: {stats.get('outflow_area', 0)} 平方米")
            print(f"  流入面积: {stats.get('inflow_area', 0)} 平方米")

def get_task_logs(base_url, task_id):
    """获取任务日志"""
    try:
        print(f"\n📋 获取任务日志...")
        log_response = requests.get(f"{base_url}/logs/", params={'task_id': task_id, 'lines': 30}, timeout=10)
        
        if log_response.status_code == 200:
            log_data = log_response.json()['data']
            logs = log_data.get('logs', [])
            print(f"\n📋 最近的日志 (最后30行):")
            for log_line in logs[-30:]:
                print(f"  {log_line}")
        else:
            print(f"❌ 获取日志失败: {log_response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取日志异常: {e}")

def test_image_extent_extraction():
    """单独测试影像范围提取功能"""
    print("\n🧪 单独测试影像范围提取功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    try:
        print(f"\n📁 测试影像: {test_image_path}")
        
        # 启动影像范围提取任务
        print("\n🗺️ 启动影像范围提取任务...")
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0,
            'keep_original_crs': 'true'
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务执行
            success = monitor_simple_task(base_url, task_id, "影像范围提取")
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_simple_task(base_url, task_id, task_name, max_wait_time=120):
    """监控简单任务执行"""
    print(f"\n📊 监控 {task_name} 任务执行...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                task_status = status_data['task_status']
                message = status_data.get('message', '')
                progress = status_data.get('progress', 0)
                
                print(f"📈 {task_name}: {task_status} ({progress}%) - {message}")
                
                if task_status == '完成':
                    print(f"\n✅ {task_name} 任务完成！")
                    
                    # 显示结果信息
                    if 'result' in status_data:
                        result_info = status_data['result']
                        print(f"📊 结果信息:")
                        print(f"  有效区域数量: {result_info.get('contour_count', 0)}")
                        print(f"  总面积: {result_info.get('valid_area', 0):.6f}")
                        print(f"  坐标系: {result_info.get('coordinate_system', 'Unknown')}")
                        print(f"  输出文件: {result_info.get('output_path', '')}")
                    
                    return True
                    
                elif task_status == '失败':
                    print(f"\n❌ {task_name} 任务失败: {message}")
                    return False
                
                time.sleep(3)
            else:
                print(f"❌ 状态查询失败: {status_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
            time.sleep(5)
            continue
    
    print(f"\n⏰ {task_name} 任务超时")
    return False

if __name__ == "__main__":
    print("🚀 开始测试修复后的合并分析功能")
    
    # 先测试影像范围提取功能
    print("="*60)
    print("第一步：测试影像范围提取功能")
    print("="*60)
    
    extent_success = test_image_extent_extraction()
    
    if extent_success:
        print("\n⏳ 等待5秒后进行合并分析测试...")
        time.sleep(5)
        
        # 测试合并分析功能
        print("="*60)
        print("第二步：测试合并分析功能")
        print("="*60)
        
        combined_success = test_combined_analysis()
        
        if extent_success and combined_success:
            print("\n🎉 所有测试通过！")
        else:
            print("\n❌ 部分测试失败")
    else:
        print("\n❌ 影像范围提取测试失败，跳过合并分析测试")
    
    print("\n📖 修复说明:")
    print("1. 修复了合并分析中影像范围提取的task_dir获取问题")
    print("2. 改为直接调用analysis_processor进行同步处理")
    print("3. 添加了详细的日志输出和错误处理")
    print("4. 确保影像范围文件正确生成并用于空间变化分析")
