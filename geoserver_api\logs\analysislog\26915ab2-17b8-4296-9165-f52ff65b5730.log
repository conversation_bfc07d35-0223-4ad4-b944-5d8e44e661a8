2025-08-26 12:09:22 - INFO - === 合并分析任务开始 ===
2025-08-26 12:09:22 - INFO - 任务ID: 26915ab2-17b8-4296-9165-f52ff65b5730
2025-08-26 12:09:22 - INFO - 📝 TaskInfo.json已由队列管理器创建，跳过重复创建
2025-08-26 12:09:22 - INFO - 影像ID: 20250705171599
2025-08-26 12:09:22 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 12:09:22 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-26 12:09:22 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-26 12:09:22 - INFO - 分析类别: constructionLand
2025-08-26 12:09:22 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:09:22 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_2_1756181016.shp
2025-08-26 12:09:22 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171599
2025-08-26 12:09:22 - INFO - === 开始执行合并分析任务 ===
2025-08-26 12:09:22 - WARNING - ⚠️ TaskInfo路径丢失，尝试重新构建...
2025-08-26 12:09:22 - INFO - 📍 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 12:09:22 - INFO - 📝 更新字段: status = 进行中
2025-08-26 12:09:22 - INFO - 📝 更新字段: message = 开始AI语义分割...
2025-08-26 12:09:22 - INFO - 📝 更新字段: progress = 5
2025-08-26 12:09:22 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress']
2025-08-26 12:09:22 - INFO - 🔍 验证更新结果:
2025-08-26 12:09:22 - INFO -   状态: 进行中
2025-08-26 12:09:22 - INFO -   进度: 5%
2025-08-26 12:09:22 - INFO -   AI处理时间: None
2025-08-26 12:09:22 - INFO -   成功状态: None
2025-08-26 12:09:22 - INFO -   空间统计:
2025-08-26 12:09:22 - INFO -     area_threshold: 200.0
2025-08-26 12:09:22 - INFO - 🤖 开始AI语义分割...
2025-08-26 12:09:22 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 12:09:22 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-26 12:09:22 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:09:22 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-26 12:09:22 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-26 12:09:22 - INFO - 🔍 检测到合并分析任务: image_id=20250705171599, category=constructionLand
2025-08-26 12:09:22 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171599/TaskInfo.json
2025-08-26 12:09:22 - INFO - ✅ 使用真实任务ID: 26915ab2-17b8-4296-9165-f52ff65b5730
2025-08-26 12:09:22 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-26 12:09:22 - INFO - 📄 读取现有TaskInfo文件...
2025-08-26 12:09:22 - INFO - 📋 读取到数组格式，包含 5 个历史任务
2025-08-26 12:09:22 - INFO - 🔄 更新现有任务记录: 26915ab2-17b8-4296-9165-f52ff65b5730
2025-08-26 12:09:22 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-26 12:09:22 - INFO - 📊 文件验证: 大小=13827字节，任务总数: 5
2025-08-26 12:09:22 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-26 12:09:22 - INFO - 🔧 创建处理参数:
2025-08-26 12:09:22 - INFO -   模型类型: deeplabv3_plus
2025-08-26 12:09:22 - INFO -   类别数量: 2
2025-08-26 12:09:22 - INFO -   目标类别: [1]
2025-08-26 12:09:22 - INFO -   窗口大小: 512
2025-08-26 12:09:22 - INFO -   批处理大小: 16
2025-08-26 12:09:22 - INFO -   重叠比例: 0.5
2025-08-26 12:09:22 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-26 12:09:22 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 12:09:22 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-26 12:09:22 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:09:22 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-26 12:09:22 - INFO - 🔄 开始AI模型推理...
2025-08-26 12:09:22 - INFO - 
==================================================
2025-08-26 12:09:22 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 12:09:22 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:09:22 - INFO - ==================================================
2025-08-26 12:09:22 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 12:09:22 - INFO - 📏 图像大小: 5683x5165, 波段数: 3
2025-08-26 12:09:22 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-26 12:09:22 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-26 12:09:22 - INFO - 🔢 类别数量: 2
2025-08-26 12:09:22 - INFO - 🔄 重叠区域: 37 像素
2025-08-26 12:09:22 - INFO - 📦 批处理大小: 16
2025-08-26 12:09:22 - INFO - 💻 计算设备: cuda:0
2025-08-26 12:09:26 - INFO - 读取tif并裁剪预处理完毕,目前耗时间: 3s
2025-08-26 12:09:26 - INFO - 🔧 创建模型...
2025-08-26 12:09:29 - INFO - ✅ 模型创建完成
2025-08-26 12:09:29 - INFO - 🧠 开始模型预测...
2025-08-26 12:09:44 - INFO - ✅ AI处理成功完成，耗时: 21.00秒
2025-08-26 12:09:44 - INFO - 🔄 更新TaskInfo.json: AI处理完成，成功=True
2025-08-26 12:09:44 - INFO - ✅ 找到并更新任务记录: 26915ab2-17b8-4296-9165-f52ff65b5730
2025-08-26 12:09:44 - INFO - 📝 TaskInfo.json已更新: AI处理时间=21秒
2025-08-26 12:09:44 - INFO - AI语义分割完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:09:44 - INFO - 🔄 更新TaskInfo.json - AI分割完成
2025-08-26 12:09:44 - INFO - 📝 更新字段: status = 进行中
2025-08-26 12:09:44 - INFO - 📝 更新字段: message = AI语义分割完成，开始后续处理
2025-08-26 12:09:44 - INFO - 📝 更新字段: progress = 40
2025-08-26 12:09:44 - INFO - 📝 更新嵌套字段: results.ai_processing_time = 21
2025-08-26 12:09:44 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress', 'results.ai_processing_time']
2025-08-26 12:09:44 - INFO - 🔍 验证更新结果:
2025-08-26 12:09:44 - INFO -   状态: 进行中
2025-08-26 12:09:44 - INFO -   进度: 40%
2025-08-26 12:09:44 - INFO -   AI处理时间: 21
2025-08-26 12:09:44 - INFO -   成功状态: True
2025-08-26 12:09:44 - INFO -   空间统计:
2025-08-26 12:09:44 - INFO -     area_threshold: 200.0
2025-08-26 12:09:44 - INFO - ✅ TaskInfo.json更新完成 - AI分割阶段
2025-08-26 12:09:44 - INFO - 📝 更新字段: message = 提取影像有效范围...
2025-08-26 12:09:44 - INFO - 📝 更新字段: progress = 45
2025-08-26 12:09:44 - INFO - 📝 TaskInfo.json已更新: ['message', 'progress']
2025-08-26 12:09:44 - INFO - 🔍 验证更新结果:
2025-08-26 12:09:44 - INFO -   状态: 进行中
2025-08-26 12:09:44 - INFO -   进度: 45%
2025-08-26 12:09:44 - INFO -   AI处理时间: 21
2025-08-26 12:09:44 - INFO -   成功状态: True
2025-08-26 12:09:44 - INFO -   空间统计:
2025-08-26 12:09:44 - INFO -     area_threshold: 200.0
2025-08-26 12:09:44 - INFO - 🗺️ 开始提取影像有效范围...
2025-08-26 12:09:44 - INFO - 范围文件输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_area_1756181016.shp
2025-08-26 12:09:44 - INFO - 分析类别目录: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand
2025-08-26 12:09:44 - INFO - 调用影像范围提取: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif -> D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_area_1756181016.shp
2025-08-26 12:09:46 - INFO - ✅ 影像范围提取成功
2025-08-26 12:09:46 - INFO - 输出文件: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_area_1756181016.shp
2025-08-26 12:09:46 - INFO - 有效区域数量: 1
2025-08-26 12:09:46 - INFO - 坐标系: WGS 84 / UTM zone 48N (EPSG:32648)
2025-08-26 12:09:46 - INFO - 影像范围提取完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_area_1756181016.shp
2025-08-26 12:09:46 - INFO - 📝 更新字段: message = 开始空间变化分析...
2025-08-26 12:09:46 - INFO - 📝 更新字段: progress = 60
2025-08-26 12:09:46 - INFO - 📝 TaskInfo.json已更新: ['message', 'progress']
2025-08-26 12:09:46 - INFO - 🔍 验证更新结果:
2025-08-26 12:09:46 - INFO -   状态: 进行中
2025-08-26 12:09:46 - INFO -   进度: 60%
2025-08-26 12:09:46 - INFO -   AI处理时间: 21
2025-08-26 12:09:46 - INFO -   成功状态: True
2025-08-26 12:09:46 - INFO -   空间统计:
2025-08-26 12:09:46 - INFO -     area_threshold: 200.0
2025-08-26 12:09:46 - INFO - 📊 开始空间变化分析...
2025-08-26 12:09:46 - INFO - 📁 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-26 12:09:46 - INFO - 📁 新数据: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:09:46 - INFO - 📏 面积阈值: 200.0 平方米
2025-08-26 12:09:46 - INFO - 使用裁剪范围: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_area_1756181016.shp
2025-08-26 12:11:02 - INFO - ✅ 空间变化分析完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_2_1756181016.shp
2025-08-26 12:11:02 - INFO - 📊 获取到的空间分析统计信息: {'outflow_count': 0, 'inflow_count': 0, 'total_count': 0, 'outflow_area': 0, 'inflow_area': 0, 'area_threshold': 200.0}
2025-08-26 12:11:02 - INFO - 🔄 开始更新空间统计字段...
2025-08-26 12:11:02 - INFO -   添加字段: results.spatial_statistics.outflow_count = 0
2025-08-26 12:11:02 - INFO -   添加字段: results.spatial_statistics.inflow_count = 0
2025-08-26 12:11:02 - INFO -   添加字段: results.spatial_statistics.total_count = 0
2025-08-26 12:11:02 - INFO -   添加字段: results.spatial_statistics.outflow_area = 0
2025-08-26 12:11:02 - INFO -   添加字段: results.spatial_statistics.inflow_area = 0
2025-08-26 12:11:02 - INFO -   添加字段: results.spatial_statistics.area_threshold = 200.0
2025-08-26 12:11:02 - INFO - 🔄 准备更新TaskInfo.json，更新字段: ['status', 'message', 'progress', 'end_time', 'results.success', 'results.spatial_statistics.outflow_count', 'results.spatial_statistics.inflow_count', 'results.spatial_statistics.total_count', 'results.spatial_statistics.outflow_area', 'results.spatial_statistics.inflow_area', 'results.spatial_statistics.area_threshold']
2025-08-26 12:11:02 - INFO - 📝 更新字段: status = 完成
2025-08-26 12:11:02 - INFO - 📝 更新字段: message = 合并分析任务完成
2025-08-26 12:11:02 - INFO - 📝 更新字段: progress = 100
2025-08-26 12:11:02 - INFO - 📝 更新字段: end_time = 2025-08-26T12:11:02.891956
2025-08-26 12:11:02 - INFO - 📝 更新嵌套字段: results.success = True
2025-08-26 12:11:02 - INFO - 📝 更新嵌套字段: results.spatial_statistics.outflow_count = 0
2025-08-26 12:11:02 - INFO - 📝 更新嵌套字段: results.spatial_statistics.inflow_count = 0
2025-08-26 12:11:02 - INFO - 📝 更新嵌套字段: results.spatial_statistics.total_count = 0
2025-08-26 12:11:02 - INFO - 📝 更新嵌套字段: results.spatial_statistics.outflow_area = 0
2025-08-26 12:11:02 - INFO - 📝 更新嵌套字段: results.spatial_statistics.inflow_area = 0
2025-08-26 12:11:03 - INFO - 📝 更新嵌套字段: results.spatial_statistics.area_threshold = 200.0
2025-08-26 12:11:03 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress', 'end_time', 'results.success', 'results.spatial_statistics.outflow_count', 'results.spatial_statistics.inflow_count', 'results.spatial_statistics.total_count', 'results.spatial_statistics.outflow_area', 'results.spatial_statistics.inflow_area', 'results.spatial_statistics.area_threshold']
2025-08-26 12:11:03 - INFO - 🔍 验证更新结果:
2025-08-26 12:11:03 - INFO -   状态: 完成
2025-08-26 12:11:03 - INFO -   进度: 100%
2025-08-26 12:11:03 - INFO -   AI处理时间: 21
2025-08-26 12:11:03 - INFO -   成功状态: True
2025-08-26 12:11:03 - INFO -   空间统计:
2025-08-26 12:11:03 - INFO -     outflow_count: 0
2025-08-26 12:11:03 - INFO -     inflow_count: 0
2025-08-26 12:11:03 - INFO -     total_count: 0
2025-08-26 12:11:03 - INFO -     outflow_area: 0
2025-08-26 12:11:03 - INFO -     inflow_area: 0
2025-08-26 12:11:03 - INFO -     area_threshold: 200.0
2025-08-26 12:11:03 - INFO - ✅ TaskInfo.json最终更新完成
2025-08-26 12:11:03 - INFO - 🌐 开始自动发布到GeoServer...
2025-08-26 12:11:03 - INFO - 🌐 开始自动发布分析结果到GeoServer
2025-08-26 12:11:03 - INFO - 📊 发布参数:
2025-08-26 12:11:03 - INFO -   工作区: constructionLand
2025-08-26 12:11:03 - INFO -   坐标系: EPSG:32648
2025-08-26 12:11:03 - INFO -   AI结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:11:03 - INFO -   最终结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_2_1756181016.shp
2025-08-26 12:11:03 - INFO - 📤 发布AI分析结果...
2025-08-26 12:11:03 - INFO - ✅ AI结果发布成功: constructionLand:20250705171599_1_1756181016
2025-08-26 12:11:03 - INFO - 📤 发布最终分析结果...
2025-08-26 12:11:03 - INFO - ✅ 最终结果发布成功: constructionLand:20250705171599_2_1756181016
2025-08-26 12:11:03 - INFO - 📊 GeoServer发布总结:
2025-08-26 12:11:03 - INFO -   AI结果: ✅ 成功
2025-08-26 12:11:03 - INFO -   最终结果: ✅ 成功
2025-08-26 12:11:03 - INFO -   总体状态: ✅ 成功
2025-08-26 12:11:03 - INFO - 🎉 所有分析结果已成功发布到GeoServer
2025-08-26 12:11:03 - INFO - 📝 更新字段: geoserver_publish = {'ai_result': {'success': True, 'message': 'Shapefile发布成功 (EPSG:32648)', 'layer_name': '20250705171599_1_1756181016', 'store_name': '20250705171599_1_1756181016_store', 'details': {'file_path': 'D:/Drone_Project/nginxData\\ODM\\AI\\20250705171599\\constructionLand\\20250705171599_1_1756181016.shp', 'workspace': 'constructionLand', 'store': '20250705171599_1_1756181016_store', 'layer': '20250705171599_1_1756181016', 'charset': 'UTF-8', 'crs': 'EPSG:32648', 'replaced_existing': False}}, 'final_result': {'success': True, 'message': 'Shapefile发布成功 (EPSG:32648)', 'layer_name': '20250705171599_2_1756181016', 'store_name': '20250705171599_2_1756181016_store', 'details': {'file_path': 'D:/Drone_Project/nginxData\\ODM\\AI\\20250705171599\\constructionLand\\20250705171599_2_1756181016.shp', 'workspace': 'constructionLand', 'store': '20250705171599_2_1756181016_store', 'layer': '20250705171599_2_1756181016', 'charset': 'UTF-8', 'crs': 'EPSG:32648', 'replaced_existing': False}}, 'overall_success': True, 'workspace': 'constructionLand', 'epsg': '32648', 'publish_time': 1756181463.1215584}
2025-08-26 12:11:03 - INFO - 📝 TaskInfo.json已更新: ['geoserver_publish']
2025-08-26 12:11:03 - INFO - 🔍 验证更新结果:
2025-08-26 12:11:03 - INFO -   状态: 完成
2025-08-26 12:11:03 - INFO -   进度: 100%
2025-08-26 12:11:03 - INFO -   AI处理时间: 21
2025-08-26 12:11:03 - INFO -   成功状态: True
2025-08-26 12:11:03 - INFO -   空间统计:
2025-08-26 12:11:03 - INFO -     outflow_count: 0
2025-08-26 12:11:03 - INFO -     inflow_count: 0
2025-08-26 12:11:03 - INFO -     total_count: 0
2025-08-26 12:11:03 - INFO -     outflow_area: 0
2025-08-26 12:11:03 - INFO -     inflow_area: 0
2025-08-26 12:11:03 - INFO -     area_threshold: 200.0
2025-08-26 12:11:03 - INFO - 🎉 合并分析任务完成，已成功发布到GeoServer
2025-08-26 12:11:03 - INFO - === 合并分析任务完成 ===
2025-08-26 12:11:03 - INFO - AI分析结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756181016.shp
2025-08-26 12:11:03 - INFO - 最终分析结果: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_2_1756181016.shp
2025-08-26 12:11:03 - INFO - GeoServer发布状态: ✅ 成功
2025-08-26 12:11:03 - INFO - TaskInfo.json已更新完成
