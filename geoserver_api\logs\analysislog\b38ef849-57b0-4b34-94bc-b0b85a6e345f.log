2025-08-20 10:51:27 - INFO - === 合并分析任务开始 ===
2025-08-20 10:51:27 - INFO - 任务ID: b38ef849-57b0-4b34-94bc-b0b85a6e345f
2025-08-20 10:51:27 - INFO - 🔧 立即创建TaskInfo.json...
2025-08-20 10:51:27 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-20 10:51:27 - ERROR - ❌ 创建TaskInfo.json模板失败: 'task_dir'
2025-08-20 10:51:27 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 460, in _create_taskinfo_immediately
    task_info_file = os.path.join(task_info['task_dir'], 'TaskInfo.json')
KeyError: 'task_dir'

2025-08-20 10:51:27 - INFO - ✅ TaskInfo.json创建完成
2025-08-20 10:51:27 - INFO - 影像ID: 20250705171601
2025-08-20 10:51:27 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:27 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:51:27 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:51:27 - INFO - 分析类别: arableLand
2025-08-20 10:51:27 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755658287.shp
2025-08-20 10:51:27 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_2_1755658287.shp
2025-08-20 10:51:27 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171601
2025-08-20 10:51:27 - INFO - === 开始执行合并分析任务 ===
2025-08-20 10:51:27 - INFO - 🤖 开始AI语义分割...
2025-08-20 10:51:27 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:27 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:51:27 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755658287.shp
2025-08-20 10:51:27 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-20 10:51:27 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-20 10:51:27 - INFO - 🔍 检测到合并分析任务: image_id=20250705171601, category=arableLand
2025-08-20 10:51:27 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171601\TaskInfo.json
2025-08-20 10:51:27 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-20 10:51:28 - INFO - 📄 读取现有TaskInfo文件...
2025-08-20 10:51:28 - INFO - 📋 读取到数组格式，包含 1 个历史任务
2025-08-20 10:51:28 - INFO - ➕ 添加新任务记录: 44a296ab-9900-406b-be39-9a4359717eac
2025-08-20 10:51:28 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-20 10:51:28 - INFO - 📊 文件验证: 大小=2699字节，任务总数: 2
2025-08-20 10:51:28 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-20 10:51:28 - INFO - 🔧 创建处理参数:
2025-08-20 10:51:28 - INFO -   模型类型: deeplabv3_plus
2025-08-20 10:51:28 - INFO -   类别数量: 2
2025-08-20 10:51:28 - INFO -   目标类别: [1]
2025-08-20 10:51:28 - INFO -   窗口大小: 512
2025-08-20 10:51:28 - INFO -   批处理大小: 16
2025-08-20 10:51:28 - INFO -   重叠比例: 0.5
2025-08-20 10:51:28 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-20 10:51:28 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:28 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:51:28 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755658287.shp
2025-08-20 10:51:28 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-20 10:51:28 - INFO - 🔄 开始AI模型推理...
2025-08-20 10:51:28 - INFO - 
==================================================
2025-08-20 10:51:28 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:28 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755658287.shp
2025-08-20 10:51:28 - INFO - ==================================================
2025-08-20 10:51:28 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:28 - INFO - 📏 图像大小: 17007x20364, 波段数: 3
2025-08-20 10:51:28 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-20 10:51:28 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-20 10:51:29 - INFO - 🔢 类别数量: 2
2025-08-20 10:51:29 - INFO - 🔄 重叠区域: 37 像素
2025-08-20 10:51:29 - INFO - 📦 批处理大小: 16
2025-08-20 10:51:29 - INFO - 💻 计算设备: cuda:0
