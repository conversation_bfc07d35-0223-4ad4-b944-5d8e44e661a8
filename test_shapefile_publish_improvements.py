#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Shapefile发布接口的改进功能
1. Store已存在的提示优化
2. 支持指定EPSG坐标系的新接口
"""

import requests
import json
import time

def test_original_publish_interface():
    """测试原始发布接口的改进"""
    print("🧪 测试原始发布接口的改进...")
    
    base_url = "http://127.0.0.1:8091/api/geo/shapefile/publish/"
    
    # 测试数据
    params = {
        'file_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'workspace': 'testodm',
        'store': 'test_store_original',
        'layer': 'test_layer_original'
    }
    
    try:
        print(f"\n1️⃣ 第一次发布（新建）...")
        response = requests.get(base_url, params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 发布成功")
            print(f"📋 状态: {result['status']}")
            print(f"📝 消息: {result['message']}")
            
            details = result.get('details', {})
            print(f"🌐 坐标系: {details.get('crs', 'N/A')}")
            print(f"🔄 覆盖现有: {details.get('replaced_existing', 'N/A')}")
            
        else:
            print(f"❌ 发布失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
        
        print(f"\n2️⃣ 第二次发布（覆盖）...")
        time.sleep(2)  # 等待一下
        
        response = requests.get(base_url, params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 发布成功")
            print(f"📋 状态: {result['status']}")
            print(f"📝 消息: {result['message']}")
            
            details = result.get('details', {})
            print(f"🌐 坐标系: {details.get('crs', 'N/A')}")
            print(f"🔄 覆盖现有: {details.get('replaced_existing', 'N/A')}")
            
            # 验证消息是否包含覆盖提示
            if "覆盖" in result['message'] or details.get('replaced_existing'):
                print(f"✅ 覆盖提示正常")
            else:
                print(f"⚠️ 未发现覆盖提示")
            
        else:
            print(f"❌ 第二次发布失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试原始接口异常: {e}")
        return False

def test_new_crs_interface():
    """测试新的支持坐标系的接口"""
    print(f"\n🧪 测试新的支持坐标系的接口...")
    
    base_url = "http://127.0.0.1:8091/api/geo/shapefile/publish-with-crs/"
    
    # 测试数据
    test_cases = [
        {
            'name': '默认4326坐标系',
            'params': {
                'file_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'workspace': 'testodm',
                'store': 'test_store_4326',
                'layer': 'test_layer_4326'
            },
            'expected_crs': 'EPSG:4326'
        },
        {
            'name': '指定32648坐标系',
            'params': {
                'file_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'workspace': 'testodm',
                'store': 'test_store_32648',
                'layer': 'test_layer_32648',
                'epsg': '32648'
            },
            'expected_crs': 'EPSG:32648'
        },
        {
            'name': '指定2000坐标系',
            'params': {
                'file_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'workspace': 'testodm',
                'store': 'test_store_2000',
                'layer': 'test_layer_2000',
                'epsg': '2000'
            },
            'expected_crs': 'EPSG:2000'
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            print(f"\n{i}️⃣ 测试: {test_case['name']}")
            
            response = requests.get(base_url, params=test_case['params'], timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 发布成功")
                print(f"📋 状态: {result['status']}")
                print(f"📝 消息: {result['message']}")
                
                details = result.get('details', {})
                actual_crs = details.get('crs', 'N/A')
                expected_crs = test_case['expected_crs']
                
                print(f"🌐 期望坐标系: {expected_crs}")
                print(f"🌐 实际坐标系: {actual_crs}")
                
                if actual_crs == expected_crs:
                    print(f"✅ 坐标系正确")
                    success_count += 1
                else:
                    print(f"❌ 坐标系不匹配")
                
                print(f"🔄 覆盖现有: {details.get('replaced_existing', 'N/A')}")
                
            else:
                print(f"❌ 发布失败: {response.status_code}")
                print(f"错误信息: {response.text}")
            
            time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            print(f"❌ 测试用例异常: {e}")
    
    print(f"\n📊 新接口测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count == len(test_cases)

def test_invalid_parameters():
    """测试无效参数的处理"""
    print(f"\n🧪 测试无效参数的处理...")
    
    base_url = "http://127.0.0.1:8091/api/geo/shapefile/publish-with-crs/"
    
    test_cases = [
        {
            'name': '缺少必要参数',
            'params': {
                'workspace': 'testodm'
                # 缺少file_path
            },
            'expected_status': 400
        },
        {
            'name': '无效EPSG代码',
            'params': {
                'file_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'workspace': 'testodm',
                'epsg': 'invalid'
            },
            'expected_status': 400
        },
        {
            'name': '文件不存在',
            'params': {
                'file_path': 'D:/nonexistent/file.shp',
                'workspace': 'testodm'
            },
            'expected_status': 404
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            print(f"\n{i}️⃣ 测试: {test_case['name']}")
            
            response = requests.get(base_url, params=test_case['params'], timeout=30)
            
            expected_status = test_case['expected_status']
            actual_status = response.status_code
            
            print(f"📊 期望状态码: {expected_status}")
            print(f"📊 实际状态码: {actual_status}")
            
            if actual_status == expected_status:
                print(f"✅ 状态码正确")
                success_count += 1
                
                if response.status_code >= 400:
                    result = response.json()
                    print(f"📝 错误消息: {result.get('message', 'N/A')}")
            else:
                print(f"❌ 状态码不匹配")
                print(f"响应内容: {response.text[:200]}")
            
        except Exception as e:
            print(f"❌ 测试用例异常: {e}")
    
    print(f"\n📊 参数验证测试结果: {success_count}/{len(test_cases)} 成功")
    return success_count == len(test_cases)

def test_post_method():
    """测试POST方法"""
    print(f"\n🧪 测试POST方法...")
    
    base_url = "http://127.0.0.1:8091/api/geo/shapefile/publish-with-crs/"
    
    data = {
        'file_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'workspace': 'testodm',
        'store': 'test_store_post',
        'layer': 'test_layer_post',
        'epsg': '32648'
    }
    
    try:
        response = requests.post(base_url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ POST方法发布成功")
            print(f"📋 状态: {result['status']}")
            print(f"📝 消息: {result['message']}")
            
            details = result.get('details', {})
            print(f"🌐 坐标系: {details.get('crs', 'N/A')}")
            
            return True
        else:
            print(f"❌ POST方法发布失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试POST方法异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试Shapefile发布接口的改进功能")
    
    print(f"\n📝 改进功能说明:")
    print(f"1. 🔄 优化Store已存在的提示信息")
    print(f"2. 🌐 新增支持指定EPSG坐标系的接口")
    print(f"3. 📊 返回更详细的发布信息")
    
    # 测试原始接口改进
    original_ok = test_original_publish_interface()
    
    # 测试新的坐标系接口
    new_interface_ok = test_new_crs_interface()
    
    # 测试参数验证
    validation_ok = test_invalid_parameters()
    
    # 测试POST方法
    post_ok = test_post_method()
    
    # 总结
    print(f"\n📊 测试结果总结:")
    print(f"  原始接口改进: {'✅ 通过' if original_ok else '❌ 失败'}")
    print(f"  新坐标系接口: {'✅ 通过' if new_interface_ok else '❌ 失败'}")
    print(f"  参数验证: {'✅ 通过' if validation_ok else '❌ 失败'}")
    print(f"  POST方法: {'✅ 通过' if post_ok else '❌ 失败'}")
    
    all_passed = all([original_ok, new_interface_ok, validation_ok, post_ok])
    
    if all_passed:
        print(f"\n🎉 所有测试通过！Shapefile发布接口改进成功！")
    else:
        print(f"\n❌ 部分测试失败，请检查相关功能")
    
    print(f"\n📖 使用说明:")
    print(f"1. 原始接口: /api/geo/shapefile/publish/ (强制使用EPSG:4326)")
    print(f"2. 新接口: /api/geo/shapefile/publish-with-crs/ (支持指定EPSG)")
    print(f"3. 支持GET和POST两种方法")
    print(f"4. 返回详细的发布信息，包括是否覆盖现有资源")
    print(f"5. 完善的参数验证和错误处理")

if __name__ == "__main__":
    main()
