2025-08-28 16:05:51,914 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_160551.log
2025-08-28 16:05:51,917 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:05:51,917 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:05:53,527 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:05:53,732 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:05:53,741 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:05:53,773 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:05:53,778 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:05:53,779 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:05:53,806 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:05:53,891 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:05:53,908 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:05:53,962 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:05:54,014 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:05:54,017 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:05:54,039 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:05:54,043 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:05:54,052 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:05:54,072 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:05:54,592 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 16:05:54,870 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 16:06:01,434 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 16:06:02,922 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 16:06:03,057 - analysis_executor - INFO - 加载了 28 个任务状态
