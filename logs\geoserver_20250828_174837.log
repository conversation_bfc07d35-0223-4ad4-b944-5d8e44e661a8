2025-08-28 17:48:37,593 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_174837.log
2025-08-28 17:48:37,598 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:37,598 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:37,654 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:37,657 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:37,658 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:37,670 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:37,672 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:37,673 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:37,688 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:37,690 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:37,691 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:37,718 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:37,726 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:37,726 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:37,739 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:37,741 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:48:37,741 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:48:37,755 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:48:37,779 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:48:37,789 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:48:37,959 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:48:37,976 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:48:37,984 - analysis_executor - INFO - 加载了 28 个任务状态
