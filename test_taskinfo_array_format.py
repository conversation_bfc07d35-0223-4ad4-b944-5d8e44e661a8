#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试数组格式TaskInfo.json实时更新功能
"""

import requests
import time
import json
import os

def test_ai_semantic_segmentation_array_taskinfo():
    """测试AI语义分割的数组格式TaskInfo.json实时更新"""
    print("🧪 测试AI语义分割数组格式TaskInfo.json实时更新功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    model_path = "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
    output_path = "D:/Drone_Project/test_output/ai_result_array.shp"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  输入影像: {image_path}")
        print(f"  模型路径: {model_path}")
        print(f"  输出路径: {output_path}")
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✅ 创建输出目录: {output_dir}")
        
        # 检查现有TaskInfo.json
        task_info_path = os.path.join(output_dir, "TaskInfo.json")
        print(f"\n🔍 检查现有TaskInfo.json: {task_info_path}")
        
        if os.path.exists(task_info_path):
            try:
                with open(task_info_path, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
                
                if isinstance(existing_data, list):
                    print(f"📋 现有TaskInfo.json包含 {len(existing_data)} 个任务记录")
                    for i, task in enumerate(existing_data[-3:]):  # 显示最后3个任务
                        print(f"  任务{i+1}: {task.get('task_id', 'N/A')} - {task.get('status', 'N/A')}")
                elif isinstance(existing_data, dict):
                    print(f"📋 现有TaskInfo.json为单个任务格式")
                    print(f"  任务: {existing_data.get('task_id', 'N/A')} - {existing_data.get('status', 'N/A')}")
                else:
                    print(f"⚠️ 现有TaskInfo.json格式不正确")
                    
            except json.JSONDecodeError as e:
                print(f"❌ 现有TaskInfo.json格式错误: {e}")
        else:
            print(f"📝 TaskInfo.json文件不存在，将创建新文件")
        
        # 启动AI语义分割任务
        print("\n🚀 启动AI语义分割任务...")
        params = {
            'image': image_path,
            'model': model_path,
            'output': output_path,
            'model_type': 'deeplabv3_plus',
            'window_size': 512,
            'batch_size': 16,
            'num_classes': 2
        }
        
        response = requests.get(f"{base_url}/ai-semantic-segmentation/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 立即检查TaskInfo.json是否已更新
            print(f"\n🔍 检查TaskInfo.json是否立即创建...")
            time.sleep(2)  # 等待2秒
            
            if os.path.exists(task_info_path):
                try:
                    with open(task_info_path, 'r', encoding='utf-8') as f:
                        updated_data = json.load(f)
                    
                    if isinstance(updated_data, list):
                        # 查找新任务
                        new_task_found = False
                        for task in updated_data:
                            if task.get('task_id') == task_id:
                                print(f"✅ 新任务已添加到TaskInfo.json")
                                print(f"  状态: {task.get('status', 'N/A')}")
                                print(f"  消息: {task.get('message', 'N/A')}")
                                print(f"  进度: {task.get('progress', 0)}%")
                                new_task_found = True
                                break
                        
                        if not new_task_found:
                            print(f"❌ 新任务未在TaskInfo.json中找到")
                        
                        print(f"📊 TaskInfo.json现在包含 {len(updated_data)} 个任务记录")
                    else:
                        print(f"⚠️ TaskInfo.json格式不是数组")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ 读取更新后的TaskInfo.json失败: {e}")
            else:
                print(f"❌ TaskInfo.json文件仍不存在")
            
            # 监控任务执行和TaskInfo.json更新
            success = monitor_array_taskinfo(base_url, task_id, task_info_path)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_array_taskinfo(base_url, task_id, task_info_path, max_wait_time=300):
    """监控数组格式TaskInfo.json的更新"""
    print(f"\n📊 监控数组格式TaskInfo.json更新...")
    
    start_time = time.time()
    last_progress = -1
    last_status = None
    
    while time.time() - start_time < max_wait_time:
        try:
            # 1. 通过API查询任务状态
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            api_status = None
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                api_status = {
                    'task_status': status_data['task_status'],
                    'message': status_data.get('message', ''),
                    'progress': status_data.get('progress', 0)
                }
                
                # 只在进度变化时输出API状态
                if api_status['progress'] != last_progress:
                    print(f"\n📈 API状态: {api_status['task_status']} ({api_status['progress']}%) - {api_status['message']}")
                    last_progress = api_status['progress']
            
            # 2. 检查TaskInfo.json中的对应任务
            taskinfo_status = None
            if os.path.exists(task_info_path):
                try:
                    with open(task_info_path, 'r', encoding='utf-8') as f:
                        taskinfo_data = json.load(f)
                    
                    if isinstance(taskinfo_data, list):
                        # 查找对应的任务
                        for task in taskinfo_data:
                            if task.get('task_id') == task_id:
                                taskinfo_status = {
                                    'status': task.get('status', 'Unknown'),
                                    'message': task.get('message', ''),
                                    'progress': task.get('progress', 0),
                                    'start_time': task.get('start_time'),
                                    'end_time': task.get('end_time')
                                }
                                break
                        
                        if taskinfo_status and taskinfo_status != last_status:
                            print(f"📄 TaskInfo: {taskinfo_status['status']} ({taskinfo_status['progress']}%) - {taskinfo_status['message']}")
                            last_status = taskinfo_status.copy()
                    else:
                        print(f"⚠️ TaskInfo.json不是数组格式")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ TaskInfo.json格式错误: {e}")
                except Exception as e:
                    print(f"❌ 读取TaskInfo.json失败: {e}")
            
            # 3. 比较API状态和TaskInfo状态
            if api_status and taskinfo_status:
                if api_status['progress'] != taskinfo_status['progress']:
                    print(f"⚠️ 状态不一致: API进度={api_status['progress']}%, TaskInfo进度={taskinfo_status['progress']}%")
            
            # 4. 检查任务是否完成
            if api_status and api_status['task_status'] in ['完成', '失败']:
                print(f"\n✅ 任务结束，最终状态: {api_status['task_status']}")
                
                # 显示最终的TaskInfo内容
                if os.path.exists(task_info_path):
                    print(f"\n📋 最终TaskInfo.json内容:")
                    show_final_array_taskinfo(task_info_path, task_id)
                else:
                    print(f"❌ 最终TaskInfo.json文件不存在")
                
                return api_status['task_status'] == '完成'
            
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(5)
            continue
    
    print(f"\n⏰ 任务监控超时")
    return False

def show_final_array_taskinfo(task_info_path, task_id):
    """显示数组格式TaskInfo.json中指定任务的最终内容"""
    try:
        with open(task_info_path, 'r', encoding='utf-8') as f:
            taskinfo_data = json.load(f)
        
        if isinstance(taskinfo_data, list):
            # 查找指定任务
            target_task = None
            for task in taskinfo_data:
                if task.get('task_id') == task_id:
                    target_task = task
                    break
            
            if target_task:
                print(f"  任务ID: {target_task.get('task_id', 'N/A')}")
                print(f"  任务类型: {target_task.get('task_type', 'N/A')}")
                print(f"  状态: {target_task.get('status', 'N/A')}")
                print(f"  消息: {target_task.get('message', 'N/A')}")
                print(f"  进度: {target_task.get('progress', 0)}%")
                print(f"  开始时间: {target_task.get('start_time', 'N/A')}")
                print(f"  结束时间: {target_task.get('end_time', 'N/A')}")
                
                # 显示统计信息
                if 'statistics' in target_task:
                    stats = target_task['statistics']
                    print(f"\n📊 统计信息:")
                    for key, value in stats.items():
                        if value is not None:
                            print(f"    {key}: {value}")
                
                # 显示错误信息
                if 'error' in target_task and target_task['error']:
                    print(f"\n❌ 错误信息: {target_task['error']}")
                
                print(f"\n📊 TaskInfo.json总任务数: {len(taskinfo_data)}")
            else:
                print(f"❌ 未找到任务ID为 {task_id} 的记录")
        else:
            print(f"⚠️ TaskInfo.json不是数组格式")
            
    except Exception as e:
        print(f"❌ 显示TaskInfo内容失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试数组格式TaskInfo.json实时更新功能")
    
    success = test_ai_semantic_segmentation_array_taskinfo()
    
    if success:
        print("\n🎉 数组格式TaskInfo.json实时更新功能测试通过！")
    else:
        print("\n❌ 数组格式TaskInfo.json实时更新功能测试失败")
    
    print("\n📖 功能说明:")
    print("1. TaskInfo.json采用数组格式，支持多个任务记录")
    print("2. 新任务启动时立即添加到数组中")
    print("3. 任务执行过程中实时更新对应记录")
    print("4. 支持现有文件的读取和更新")
    print("5. 兼容单个对象格式，自动转换为数组格式")
    print("6. 每个任务记录包含完整的状态和统计信息")
