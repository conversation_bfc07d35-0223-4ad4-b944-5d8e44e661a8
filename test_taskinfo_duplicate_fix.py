#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试TaskInfo.json重复记录修复
验证相同task_id不会创建重复记录
"""

import requests
import json
import os
import time

def test_taskinfo_duplicate_prevention():
    """测试TaskInfo.json重复记录防护"""
    print("🧪 测试TaskInfo.json重复记录防护...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    image_id = "20250705171599"  # 使用你提到的image_id
    params = {
        'id': image_id,
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print(f"\n🚀 启动合并分析任务...")
        print(f"   影像ID: {image_id}")
        
        # 启动任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功: {task_id}")
            
            # 监控TaskInfo.json的变化
            success = monitor_taskinfo_changes(image_id, task_id)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_taskinfo_changes(image_id, task_id, max_wait=300):
    """监控TaskInfo.json的变化，检查是否有重复记录"""
    print(f"\n📊 监控TaskInfo.json变化...")
    
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    start_time = time.time()
    last_record_count = 0
    duplicate_detected = False
    
    while time.time() - start_time < max_wait:
        try:
            if os.path.exists(taskinfo_path):
                with open(taskinfo_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    current_count = len(data)
                    
                    # 检查是否有重复的task_id
                    task_ids = [task.get('task_id') for task in data]
                    unique_task_ids = set(task_ids)
                    
                    if len(task_ids) != len(unique_task_ids):
                        print(f"❌ 检测到重复的task_id!")
                        duplicate_detected = True
                        
                        # 分析重复情况
                        from collections import Counter
                        task_id_counts = Counter(task_ids)
                        duplicates = {tid: count for tid, count in task_id_counts.items() if count > 1}
                        
                        print(f"   重复的task_id: {duplicates}")
                        
                        # 显示重复记录的详细信息
                        for tid, count in duplicates.items():
                            print(f"   task_id {tid} 出现 {count} 次:")
                            for i, task in enumerate(data):
                                if task.get('task_id') == tid:
                                    timestamp = task.get('timestamp', 'N/A')
                                    status = task.get('status', 'N/A')
                                    progress = task.get('progress', 'N/A')
                                    has_geoserver = 'geoserver_publish' in task
                                    print(f"     记录{i+1}: timestamp={timestamp}, status={status}, progress={progress}, geoserver_publish={has_geoserver}")
                        
                        return False
                    
                    # 检查记录数量变化
                    if current_count != last_record_count:
                        print(f"📊 TaskInfo记录数量: {last_record_count} -> {current_count}")
                        
                        # 显示当前任务的状态
                        target_task = None
                        for task in data:
                            if task.get('task_id') == task_id:
                                target_task = task
                                break
                        
                        if target_task:
                            status = target_task.get('status', 'N/A')
                            progress = target_task.get('progress', 'N/A')
                            message = target_task.get('message', 'N/A')
                            print(f"   当前任务状态: {status} ({progress}%) - {message}")
                            
                            # 检查任务是否完成
                            if status in ['完成', '失败']:
                                print(f"🏁 任务结束: {status}")
                                return not duplicate_detected
                        
                        last_record_count = current_count
                    
                    # 检查是否只有一个匹配的task_id记录
                    matching_tasks = [task for task in data if task.get('task_id') == task_id]
                    if len(matching_tasks) > 1:
                        print(f"❌ 发现 {len(matching_tasks)} 个相同task_id的记录!")
                        duplicate_detected = True
                        return False
                    elif len(matching_tasks) == 1:
                        print(f"✅ 只有1个匹配的task_id记录")
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(10)
            continue
    
    print(f"⏰ 监控超时")
    return not duplicate_detected

def check_existing_taskinfo_duplicates():
    """检查现有TaskInfo.json文件中是否有重复记录"""
    print(f"\n🔍 检查现有TaskInfo.json文件中的重复记录...")
    
    # 检查几个常见的image_id
    test_image_ids = [
        "20250705171599",
        "20250705171600", 
        "20250705171601"
    ]
    
    duplicates_found = False
    
    for image_id in test_image_ids:
        taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
        
        if os.path.exists(taskinfo_path):
            try:
                with open(taskinfo_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    print(f"📁 {image_id}: {len(data)} 个记录")
                    
                    # 检查重复的task_id
                    task_ids = [task.get('task_id') for task in data]
                    unique_task_ids = set(task_ids)
                    
                    if len(task_ids) != len(unique_task_ids):
                        print(f"  ❌ 发现重复记录!")
                        duplicates_found = True
                        
                        from collections import Counter
                        task_id_counts = Counter(task_ids)
                        duplicates = {tid: count for tid, count in task_id_counts.items() if count > 1}
                        
                        for tid, count in duplicates.items():
                            print(f"    task_id {tid[:8]}... 重复 {count} 次")
                    else:
                        print(f"  ✅ 无重复记录")
                else:
                    print(f"  ⚠️ 格式不是数组")
                    
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"📁 {image_id}: 文件不存在")
    
    return not duplicates_found

def simulate_taskinfo_update_scenario():
    """模拟TaskInfo更新场景，测试是否会创建重复记录"""
    print(f"\n🧪 模拟TaskInfo更新场景...")
    
    # 模拟现有的TaskInfo数据
    existing_task_id = "test-task-123"
    taskinfo_data = [
        {
            "task_id": existing_task_id,
            "image_id": "test_image",
            "status": "进行中",
            "progress": 50
        }
    ]
    
    print(f"📋 模拟现有TaskInfo数据:")
    print(f"  task_id: {existing_task_id}")
    print(f"  记录数量: {len(taskinfo_data)}")
    
    # 模拟更新操作
    updates = {
        'status': '完成',
        'progress': 100,
        'geoserver_publish': {
            'overall_success': True
        }
    }
    
    # 查找并更新记录
    task_found = False
    for i, task_info in enumerate(taskinfo_data):
        if task_info.get('task_id') == existing_task_id:
            # 更新字段
            for key, value in updates.items():
                task_info[key] = value
            
            taskinfo_data[i] = task_info
            task_found = True
            break
    
    if task_found:
        print(f"✅ 找到并更新现有记录")
        print(f"  更新后状态: {taskinfo_data[0]['status']}")
        print(f"  更新后进度: {taskinfo_data[0]['progress']}")
        print(f"  记录数量: {len(taskinfo_data)} (应该仍为1)")
        
        return len(taskinfo_data) == 1
    else:
        print(f"❌ 未找到记录")
        return False

def main():
    """主函数"""
    print("🚀 开始测试TaskInfo.json重复记录修复")
    
    print(f"\n📝 修复内容:")
    print(f"1. _create_taskinfo_immediately函数添加重复检查")
    print(f"2. _update_combined_task_info函数不会创建新记录")
    print(f"3. 所有TaskInfo操作都会检查task_id重复")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 检查现有重复记录
    if check_existing_taskinfo_duplicates():
        success_count += 1
        print(f"✅ 测试1通过: 现有文件无重复记录")
    else:
        print(f"❌ 测试1失败: 发现现有重复记录")
    
    # 测试2: 模拟更新场景
    if simulate_taskinfo_update_scenario():
        success_count += 1
        print(f"✅ 测试2通过: 更新场景无重复")
    else:
        print(f"❌ 测试2失败: 更新场景有问题")
    
    # 测试3: 实际接口测试
    if test_taskinfo_duplicate_prevention():
        success_count += 1
        print(f"✅ 测试3通过: 实际接口无重复")
    else:
        print(f"❌ 测试3失败: 实际接口有重复")
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print(f"🎉 TaskInfo.json重复记录修复验证通过！")
        
        print(f"\n✅ 修复确认:")
        print(f"1. ✅ 相同task_id不会创建重复记录")
        print(f"2. ✅ 更新操作只会修改现有记录")
        print(f"3. ✅ 创建操作会检查并更新现有记录")
        print(f"4. ✅ TaskInfo.json保持数据一致性")
        
    else:
        print(f"❌ 部分测试失败，请检查修复是否完整")
        
        print(f"\n🔧 排查建议:")
        print(f"1. 检查_create_taskinfo_immediately函数的重复检查逻辑")
        print(f"2. 确认_update_combined_task_info函数不会创建新记录")
        print(f"3. 查看日志确认TaskInfo操作的详细过程")

if __name__ == "__main__":
    main()
