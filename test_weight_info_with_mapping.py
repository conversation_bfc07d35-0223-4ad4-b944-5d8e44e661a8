#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试权重信息接口的字段映射功能
"""

import requests
import json

def test_weight_info_api():
    """测试权重信息接口"""
    print("🧪 测试权重信息接口的字段映射功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        # 1. 测试 weight-info 接口
        print("\n1️⃣ 测试 /api/analysis/weight-info/ 接口...")
        
        response = requests.get(f"{base_url}/weight-info/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            print(f"📋 状态: {result['status']}")
            print(f"📝 消息: {result['message']}")
            
            if result['status'] == 'success' and 'data' in result:
                data = result['data']
                print(f"\n📊 权重信息数据:")
                
                for land_type, info in data.items():
                    print(f"\n🏷️ 地物类型: {land_type}")
                    
                    # 检查是否有 display_name 字段
                    if 'display_name' in info:
                        print(f"  📛 显示名称: {info['display_name']}")
                    else:
                        print(f"  ⚠️ 缺少显示名称字段")
                    
                    print(f"  🎯 默认权重: {info.get('default', 'N/A')}")
                    
                    # 显示模型信息
                    models = info.get('models', {})
                    print(f"  🤖 模型类型数量: {len(models)}")
                    for model_type, model_list in models.items():
                        print(f"    - {model_type}: {len(model_list)} 个模型")
                    
                    # 显示SHP文件信息
                    shp_files = info.get('shp_files', [])
                    print(f"  📁 SHP文件数量: {len(shp_files)}")
                    for shp_file in shp_files[:2]:  # 只显示前2个
                        print(f"    - {shp_file}")
                    if len(shp_files) > 2:
                        print(f"    - ... 还有 {len(shp_files) - 2} 个文件")
                
                print(f"\n📈 总结:")
                print(f"  地物类型总数: {len(data)}")
                
                # 检查字段映射是否正确
                expected_mappings = {
                    'arableLand': '耕地',
                    'constructionLand': '建设用地'
                }
                
                mapping_correct = True
                for land_type, expected_name in expected_mappings.items():
                    if land_type in data:
                        actual_name = data[land_type].get('display_name', '')
                        if actual_name == expected_name:
                            print(f"  ✅ {land_type} -> {actual_name} (正确)")
                        else:
                            print(f"  ❌ {land_type} -> {actual_name} (期望: {expected_name})")
                            mapping_correct = False
                    else:
                        print(f"  ⚠️ 缺少地物类型: {land_type}")
                
                if mapping_correct:
                    print(f"  🎉 字段映射功能正常！")
                else:
                    print(f"  ⚠️ 字段映射存在问题")
                
            else:
                print(f"❌ 接口返回数据格式异常")
                print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
                
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
        
        # 2. 测试 weight-config 接口
        print(f"\n2️⃣ 测试 /api/analysis/weight-config/ 接口...")
        
        response = requests.get(f"{base_url}/weight-config/", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            print(f"📋 状态: {result['status']}")
            
            if result['status'] == 'success' and 'data' in result:
                data = result['data']
                print(f"\n📊 配置信息:")
                print(f"  📁 数据路径: {data.get('window_data_path', 'N/A')}")
                
                # 显示默认权重
                default_weights = data.get('default_weights', {})
                print(f"  🎯 默认权重配置:")
                for land_type, weight in default_weights.items():
                    print(f"    {land_type}: {weight}")
                
                # 显示字段映射
                field_mapping = data.get('field_mapping', {})
                print(f"  🏷️ 字段映射配置:")
                for land_type, display_name in field_mapping.items():
                    print(f"    {land_type}: {display_name}")
                
                # 验证字段映射
                if field_mapping:
                    print(f"  ✅ 字段映射配置存在")
                    
                    expected_mappings = {
                        'arableLand': '耕地',
                        'constructionLand': '建设用地'
                    }
                    
                    mapping_correct = True
                    for land_type, expected_name in expected_mappings.items():
                        actual_name = field_mapping.get(land_type, '')
                        if actual_name == expected_name:
                            print(f"    ✅ {land_type} -> {actual_name}")
                        else:
                            print(f"    ❌ {land_type} -> {actual_name} (期望: {expected_name})")
                            mapping_correct = False
                    
                    if mapping_correct:
                        print(f"  🎉 字段映射配置正确！")
                    else:
                        print(f"  ⚠️ 字段映射配置有误")
                else:
                    print(f"  ❌ 缺少字段映射配置")
                
            else:
                print(f"❌ 配置接口返回数据格式异常")
                
        else:
            print(f"❌ 配置接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_task_cfg_format():
    """测试Task.cfg文件格式"""
    print(f"\n3️⃣ 测试Task.cfg文件格式...")
    
    try:
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        response = requests.get(config_url, timeout=10)
        
        if response.status_code == 200:
            config_content = response.text
            print(f"✅ Task.cfg文件读取成功")
            
            # 检查是否包含Field_mapping节
            if '[Field_mapping]' in config_content:
                print(f"✅ 发现 [Field_mapping] 配置节")
                
                # 提取Field_mapping内容
                lines = config_content.split('\n')
                in_field_mapping = False
                field_mappings = {}
                
                for line in lines:
                    line = line.strip()
                    if line == '[Field_mapping]':
                        in_field_mapping = True
                        continue
                    elif line.startswith('[') and line.endswith(']'):
                        in_field_mapping = False
                        continue
                    
                    if in_field_mapping and '=' in line:
                        key, value = line.split('=', 1)
                        field_mappings[key.strip()] = value.strip()
                
                print(f"📋 Field_mapping 内容:")
                for key, value in field_mappings.items():
                    print(f"  {key} = {value}")
                
                # 检查期望的映射
                expected_mappings = {
                    'arableLand': '耕地',
                    'constructionLand': '建设用地'
                }
                
                for key, expected_value in expected_mappings.items():
                    if key in field_mappings:
                        actual_value = field_mappings[key]
                        if actual_value == expected_value:
                            print(f"  ✅ {key} = {actual_value}")
                        else:
                            print(f"  ⚠️ {key} = {actual_value} (期望: {expected_value})")
                    else:
                        print(f"  ❌ 缺少映射: {key}")
                
            else:
                print(f"⚠️ Task.cfg中未发现 [Field_mapping] 配置节")
                print(f"建议在Task.cfg中添加以下内容:")
                print(f"[Field_mapping]")
                print(f"arableLand = 耕地")
                print(f"constructionLand = 建设用地")
                
        else:
            print(f"❌ 无法读取Task.cfg文件: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试Task.cfg格式异常: {e}")

if __name__ == "__main__":
    print("🚀 开始测试权重信息接口的字段映射功能")
    
    success = test_weight_info_api()
    test_task_cfg_format()
    
    if success:
        print(f"\n🎉 权重信息接口字段映射功能测试完成！")
    else:
        print(f"\n❌ 权重信息接口字段映射功能测试失败")
    
    print(f"\n📖 功能说明:")
    print(f"1. /api/analysis/weight-info/ 接口现在包含 display_name 字段")
    print(f"2. /api/analysis/weight-config/ 接口现在包含 field_mapping 配置")
    print(f"3. 从Task.cfg的[Field_mapping]节读取映射关系")
    print(f"4. 如果配置文件中没有[Field_mapping]，使用默认映射")
    print(f"5. 支持的默认映射: arableLand->耕地, constructionLand->建设用地")
