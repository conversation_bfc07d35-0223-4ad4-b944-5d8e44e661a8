#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新项目中所有硬编码的Django API URL
将localhost:8091和127.0.0.1:8091替换为动态获取的URL
"""

import os
import re
import glob
from typing import List, <PERSON><PERSON>

def find_python_files(directory: str) -> List[str]:
    """查找所有Python文件"""
    python_files = []
    
    # 查找.py文件
    for root, dirs, files in os.walk(directory):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if d not in ['venv', 'env', '__pycache__', '.git', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def find_hardcoded_urls(file_path: str) -> List[Tuple[int, str, str]]:
    """查找文件中的硬编码URL"""
    hardcoded_patterns = [
        r'http://localhost:8091',
        r'http://127\.0\.0\.1:8091',
        r'"http://localhost:8091"',
        r'"http://127\.0\.0\.1:8091"',
        r"'http://localhost:8091'",
        r"'http://127\.0\.0\.1:8091'",
        r'f"http://localhost:8091',
        r'f"http://127\.0\.0\.1:8091',
        r"f'http://localhost:8091",
        r"f'http://127\.0\.0\.1:8091",
    ]
    
    matches = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            for pattern in hardcoded_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    matches.append((line_num, line.strip(), pattern))
        
    except Exception as e:
        print(f"读取文件失败 {file_path}: {e}")
    
    return matches

def update_file_urls(file_path: str) -> bool:
    """更新文件中的硬编码URL"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 添加config导入（如果还没有）
        if 'from config import' not in content and 'import config' not in content:
            # 查找合适的位置插入import
            lines = content.split('\n')
            import_line_index = 0
            
            # 找到最后一个import语句的位置
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    import_line_index = i
            
            # 在import语句后插入config导入
            if import_line_index > 0:
                lines.insert(import_line_index + 1, 'from config import DJANGO_BASE_URL')
                content = '\n'.join(lines)
            else:
                # 如果没有找到import语句，在文件开头添加
                content = 'from config import DJANGO_BASE_URL\n' + content
        
        # 替换硬编码的URL
        replacements = [
            (r'http://localhost:8091', 'DJANGO_BASE_URL'),
            (r'http://127\.0\.0\.1:8091', 'DJANGO_BASE_URL'),
            (r'"http://localhost:8091"', 'DJANGO_BASE_URL'),
            (r'"http://127\.0\.0\.1:8091"', 'DJANGO_BASE_URL'),
            (r"'http://localhost:8091'", 'DJANGO_BASE_URL'),
            (r"'http://127\.0\.0\.1:8091'", 'DJANGO_BASE_URL'),
            (r'f"http://localhost:8091', 'f"{DJANGO_BASE_URL}'),
            (r'f"http://127\.0\.0\.1:8091', 'f"{DJANGO_BASE_URL}'),
            (r"f'http://localhost:8091", "f'{DJANGO_BASE_URL}"),
            (r"f'http://127\.0\.0\.1:8091", "f'{DJANGO_BASE_URL}"),
        ]
        
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"更新文件失败 {file_path}: {e}")
        return False

def scan_project():
    """扫描整个项目"""
    print("🔍 扫描项目中的硬编码URL...")
    
    current_dir = os.getcwd()
    python_files = find_python_files(current_dir)
    
    print(f"找到 {len(python_files)} 个Python文件")
    
    files_with_hardcoded_urls = []
    
    for file_path in python_files:
        # 跳过这个脚本本身
        if os.path.basename(file_path) == 'update_hardcoded_urls.py':
            continue
        
        matches = find_hardcoded_urls(file_path)
        if matches:
            files_with_hardcoded_urls.append((file_path, matches))
    
    return files_with_hardcoded_urls

def update_all_files():
    """更新所有文件"""
    print("🔧 开始更新硬编码URL...")
    
    files_with_urls = scan_project()
    
    if not files_with_urls:
        print("✅ 没有找到需要更新的硬编码URL")
        return
    
    print(f"发现 {len(files_with_urls)} 个文件包含硬编码URL:")
    
    updated_count = 0
    
    for file_path, matches in files_with_urls:
        rel_path = os.path.relpath(file_path)
        print(f"\n📁 {rel_path}")
        
        for line_num, line_content, pattern in matches:
            print(f"   行 {line_num}: {line_content}")
        
        # 询问是否更新这个文件
        response = input(f"   更新这个文件? (y/n/a=全部): ").strip().lower()
        
        if response == 'a':
            # 更新所有剩余文件
            for remaining_file, _ in files_with_urls[updated_count:]:
                if update_file_urls(remaining_file):
                    print(f"✅ 已更新: {os.path.relpath(remaining_file)}")
                    updated_count += 1
            break
        elif response == 'y':
            if update_file_urls(file_path):
                print(f"✅ 已更新: {rel_path}")
                updated_count += 1
        else:
            print(f"⏭️ 跳过: {rel_path}")
    
    print(f"\n🎉 更新完成！共更新了 {updated_count} 个文件")

def create_example_usage():
    """创建使用示例"""
    example_code = '''
# 更新前的代码示例:
base_url = "http://127.0.0.1:8091/api/analysis"
response = requests.get("http://localhost:8091/health/")

# 更新后的代码示例:
from config import DJANGO_BASE_URL
base_url = f"{DJANGO_BASE_URL}/api/analysis"
response = requests.get(f"{DJANGO_BASE_URL}/health/")
'''
    
    print("📚 使用示例:")
    print(example_code)

def main():
    """主函数"""
    print("🔧 Django API URL动态化工具")
    print("=" * 50)
    
    print("📝 功能说明:")
    print("1. 扫描项目中所有硬编码的Django API URL")
    print("2. 将硬编码URL替换为动态获取的配置")
    print("3. 自动添加必要的import语句")
    
    print("\n🎯 替换规则:")
    print("- http://localhost:8091 → DJANGO_BASE_URL")
    print("- http://127.0.0.1:8091 → DJANGO_BASE_URL")
    print("- 支持字符串和f-string格式")
    
    choice = input("\n选择操作 (1=扫描, 2=更新, 3=示例): ").strip()
    
    if choice == "1":
        files_with_urls = scan_project()
        if files_with_urls:
            print(f"\n发现 {len(files_with_urls)} 个文件包含硬编码URL:")
            for file_path, matches in files_with_urls:
                rel_path = os.path.relpath(file_path)
                print(f"📁 {rel_path} ({len(matches)} 处)")
        else:
            print("✅ 没有找到硬编码URL")
    
    elif choice == "2":
        update_all_files()
    
    elif choice == "3":
        create_example_usage()
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
