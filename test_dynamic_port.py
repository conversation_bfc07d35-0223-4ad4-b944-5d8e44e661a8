#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试动态端口获取功能
验证config.py中的动态端口获取是否正常工作
"""

import os
import sys
import requests
import subprocess
import time

def test_config_import():
    """测试config模块导入"""
    print("🧪 测试config模块导入...")
    
    try:
        from config import DJANGO_HOST, DJANGO_PORT, DJANGO_BASE_URL
        
        print(f"✅ config模块导入成功")
        print(f"   DJANGO_HOST: {DJANGO_HOST}")
        print(f"   DJANGO_PORT: {DJANGO_PORT}")
        print(f"   DJANGO_BASE_URL: {DJANGO_BASE_URL}")
        
        return True, DJANGO_BASE_URL
        
    except Exception as e:
        print(f"❌ config模块导入失败: {e}")
        return False, None

def test_port_detection_from_args():
    """测试从命令行参数检测端口"""
    print(f"\n🔍 测试命令行参数端口检测...")
    
    # 模拟不同的命令行参数
    test_cases = [
        ['python', 'manage.py', 'runserver', '8091'],
        ['python', 'manage.py', 'runserver', '0.0.0.0:8091'],
        ['python', 'manage.py', 'runserver', '127.0.0.1:8092'],
        ['python', 'manage.py', 'runserver', '0.0.0.0:9000'],
    ]
    
    for test_args in test_cases:
        # 临时修改sys.argv
        original_argv = sys.argv.copy()
        sys.argv = test_args
        
        try:
            # 重新导入config模块以获取新的端口
            if 'config' in sys.modules:
                del sys.modules['config']
            
            from config import get_django_port, get_django_host, get_django_base_url
            
            port = get_django_port()
            host = get_django_host()
            base_url = get_django_base_url()
            
            expected_port = test_args[-1].split(':')[-1] if ':' in test_args[-1] else test_args[-1]
            expected_host = test_args[-1].split(':')[0] if ':' in test_args[-1] and test_args[-1].split(':')[0] != '0.0.0.0' else '127.0.0.1'
            
            print(f"   命令行: {' '.join(test_args)}")
            print(f"   检测到端口: {port} (期望: {expected_port})")
            print(f"   检测到主机: {host} (期望: {expected_host})")
            print(f"   基础URL: {base_url}")
            
            if str(port) == str(expected_port):
                print(f"   ✅ 端口检测正确")
            else:
                print(f"   ❌ 端口检测错误")
            
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
        finally:
            # 恢复原始argv
            sys.argv = original_argv

def test_environment_variable():
    """测试环境变量端口设置"""
    print(f"\n🌍 测试环境变量端口设置...")
    
    # 设置环境变量
    test_ports = ['8080', '9000', '5000']
    
    for test_port in test_ports:
        os.environ['DJANGO_PORT'] = test_port
        
        try:
            # 重新导入config模块
            if 'config' in sys.modules:
                del sys.modules['config']
            
            from config import get_django_port, DJANGO_BASE_URL
            
            port = get_django_port()
            
            print(f"   环境变量 DJANGO_PORT={test_port}")
            print(f"   检测到端口: {port}")
            print(f"   基础URL: {DJANGO_BASE_URL}")
            
            if str(port) == test_port:
                print(f"   ✅ 环境变量端口检测正确")
            else:
                print(f"   ❌ 环境变量端口检测错误")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    # 清理环境变量
    if 'DJANGO_PORT' in os.environ:
        del os.environ['DJANGO_PORT']

def test_api_connection():
    """测试API连接"""
    print(f"\n🌐 测试API连接...")
    
    try:
        from config import DJANGO_BASE_URL
        
        # 测试健康检查接口
        health_url = f"{DJANGO_BASE_URL}/health/"
        print(f"   测试URL: {health_url}")
        
        try:
            response = requests.get(health_url, timeout=5)
            
            if response.status_code == 200:
                print(f"   ✅ API连接成功: {response.status_code}")
                try:
                    data = response.json()
                    print(f"   📋 响应数据: {data}")
                except:
                    print(f"   📋 响应内容: {response.text[:100]}...")
            else:
                print(f"   ⚠️ API响应异常: {response.status_code}")
                
        except requests.ConnectionError:
            print(f"   ⚠️ 无法连接到API服务器")
            print(f"   💡 请确保Django服务器正在运行")
        except requests.Timeout:
            print(f"   ⚠️ API请求超时")
        except Exception as e:
            print(f"   ❌ API连接异常: {e}")
            
    except Exception as e:
        print(f"   ❌ 配置获取异常: {e}")

def test_updated_files():
    """测试已更新的文件"""
    print(f"\n📁 测试已更新的文件...")
    
    updated_files = [
        'test_delete_task.py',
        'check_task_status.py',
        'test_timestamp_fix.py'
    ]
    
    for file_name in updated_files:
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否包含动态配置导入
                if 'from config import DJANGO_BASE_URL' in content:
                    print(f"   ✅ {file_name}: 已更新为动态配置")
                else:
                    print(f"   ❌ {file_name}: 未找到动态配置导入")
                
                # 检查是否还有硬编码URL
                hardcoded_patterns = [
                    'http://localhost:8091',
                    'http://127.0.0.1:8091'
                ]
                
                found_hardcoded = False
                for pattern in hardcoded_patterns:
                    if pattern in content:
                        print(f"   ⚠️ {file_name}: 仍包含硬编码URL: {pattern}")
                        found_hardcoded = True
                
                if not found_hardcoded:
                    print(f"   ✅ {file_name}: 已移除所有硬编码URL")
                    
            except Exception as e:
                print(f"   ❌ {file_name}: 检查失败 - {e}")
        else:
            print(f"   ⚠️ {file_name}: 文件不存在")

def show_usage_examples():
    """显示使用示例"""
    print(f"\n📚 使用示例:")
    
    examples = '''
# 1. 在Python脚本中使用动态URL
from config import DJANGO_BASE_URL

# 替换前
base_url = "http://127.0.0.1:8091/api/analysis"

# 替换后
base_url = f"{DJANGO_BASE_URL}/api/analysis"

# 2. 设置环境变量
export DJANGO_PORT=9000
export DJANGO_HOST=localhost

# 3. 命令行启动Django
python manage.py runserver 0.0.0.0:8091
python manage.py runserver 127.0.0.1:9000

# 4. 在代码中获取配置
from config import DJANGO_HOST, DJANGO_PORT, DJANGO_BASE_URL

print(f"Django运行在: {DJANGO_BASE_URL}")
'''
    
    print(examples)

def main():
    """主函数"""
    print("🧪 Django动态端口获取测试")
    print("=" * 50)
    
    print("📝 测试内容:")
    print("1. config模块导入测试")
    print("2. 命令行参数端口检测")
    print("3. 环境变量端口设置")
    print("4. API连接测试")
    print("5. 已更新文件检查")
    
    # 执行测试
    success, base_url = test_config_import()
    
    if success:
        test_port_detection_from_args()
        test_environment_variable()
        test_api_connection()
        test_updated_files()
        show_usage_examples()
        
        print(f"\n🎉 测试完成！")
        print(f"📊 当前配置:")
        print(f"   Django基础URL: {base_url}")
        
        print(f"\n💡 使用建议:")
        print(f"1. 在所有测试脚本中使用 DJANGO_BASE_URL")
        print(f"2. 通过环境变量或命令行参数设置端口")
        print(f"3. 运行 update_hardcoded_urls.py 更新剩余文件")
        
    else:
        print(f"\n❌ 配置模块测试失败，请检查config.py文件")

if __name__ == "__main__":
    main()
