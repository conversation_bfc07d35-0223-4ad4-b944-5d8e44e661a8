#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试排队合并分析接口
验证任务排队执行、取消任务、状态查询等功能
"""

import requests
import json
import time
import threading

def test_queued_analysis():
    """测试排队合并分析接口"""
    print("🧪 测试排队合并分析接口...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试参数
    test_params = {
        'id': '20250705171601',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus',
        'analysis_category': 'arableLand'
    }
    
    try:
        print(f"\n📤 提交排队分析任务...")
        print(f"   影像ID: {test_params['id']}")
        print(f"   分析类别: {test_params['analysis_category']}")
        
        # 提交任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=test_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                task_id = result['data']['task_id']
                queue_position = result['data']['queue_position']
                
                print(f"✅ 任务提交成功")
                print(f"   任务ID: {task_id}")
                print(f"   队列位置: {queue_position}")
                print(f"   队列状态: {result['data']['queue_status']}")
                
                return task_id
            else:
                print(f"❌ 任务提交失败: {result['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def test_multiple_queued_tasks():
    """测试多个任务排队"""
    print(f"\n🧪 测试多个任务排队...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 创建多个测试任务
    task_ids = []
    
    for i in range(3):
        test_params = {
            'id': f'2025070517160{i}',
            'image': f'D:/Drone_Project/nginxData/ODM/Output/2025070517160{i}/test_out.tif',
            'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
            'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
            'area_threshold': 400.0
        }
        
        try:
            print(f"📤 提交任务 {i+1}/3...")
            
            response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=test_params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                
                if result['status'] == 'success':
                    task_id = result['data']['task_id']
                    queue_position = result['data']['queue_position']
                    
                    task_ids.append(task_id)
                    print(f"✅ 任务 {i+1} 提交成功: {task_id[:8]}..., 队列位置: {queue_position}")
                else:
                    print(f"❌ 任务 {i+1} 提交失败: {result['message']}")
            else:
                print(f"❌ 任务 {i+1} 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 任务 {i+1} 异常: {e}")
    
    print(f"\n📊 成功提交 {len(task_ids)} 个任务到队列")
    return task_ids

def test_queue_status():
    """测试队列状态查询"""
    print(f"\n🧪 测试队列状态查询...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                queue_data = result['data']
                
                print(f"✅ 队列状态查询成功:")
                print(f"   队列长度: {queue_data['queue_size']}")
                print(f"   等待任务: {queue_data['waiting_tasks']}")
                print(f"   执行任务: {queue_data['executing_task'] or '无'}")
                print(f"   已完成: {queue_data['completed_tasks']}")
                print(f"   已取消: {queue_data['cancelled_tasks']}")
                print(f"   运行状态: {'运行中' if queue_data['is_running'] else '已停止'}")
                
                return queue_data
            else:
                print(f"❌ 队列状态查询失败: {result['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def test_task_status_query(task_id):
    """测试任务状态查询"""
    print(f"\n🧪 测试任务状态查询: {task_id[:8]}...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        response = requests.get(f"{base_url}/queued-task-status/", params={'task_id': task_id}, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                task_data = result['data']
                
                print(f"✅ 任务状态查询成功:")
                print(f"   任务ID: {task_data['task_id'][:8]}...")
                print(f"   状态: {task_data['status']}")
                print(f"   队列位置: {task_data.get('queue_position', 'N/A')}")
                print(f"   创建时间: {task_data.get('created_time', 'N/A')}")
                
                return task_data
            else:
                print(f"❌ 任务状态查询失败: {result['message']}")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None

def test_cancel_task(task_id):
    """测试取消任务"""
    print(f"\n🧪 测试取消任务: {task_id[:8]}...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        data = {'task_id': task_id}
        response = requests.post(f"{base_url}/cancel-queued-task/", json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                print(f"✅ 任务取消成功:")
                print(f"   任务ID: {result['data']['task_id'][:8]}...")
                print(f"   状态: {result['data']['task_status']}")
                print(f"   消息: {result['message']}")
                
                return True
            else:
                print(f"❌ 任务取消失败: {result['message']}")
                print(f"   任务状态: {result['data']['task_status']}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_cancel_rules():
    """测试取消规则"""
    print(f"\n🧪 测试取消规则...")
    
    # 提交一个任务
    task_id = test_queued_analysis()
    
    if task_id:
        # 等待一下让任务进入队列
        time.sleep(2)
        
        # 查询任务状态
        task_status = test_task_status_query(task_id)
        
        if task_status:
            current_status = task_status['status']
            print(f"📊 当前任务状态: {current_status}")
            
            # 尝试取消任务
            if current_status == '等待中':
                print(f"🔄 任务在等待中，应该可以取消")
                success = test_cancel_task(task_id)
                
                if success:
                    print(f"✅ 等待中任务取消测试通过")
                else:
                    print(f"❌ 等待中任务取消测试失败")
            else:
                print(f"⚠️ 任务状态为 {current_status}，取消行为可能不同")
                test_cancel_task(task_id)

def monitor_queue_execution():
    """监控队列执行情况"""
    print(f"\n🧪 监控队列执行情况...")
    
    # 提交多个任务
    task_ids = test_multiple_queued_tasks()
    
    if not task_ids:
        print(f"❌ 没有任务可监控")
        return
    
    print(f"\n📊 开始监控 {len(task_ids)} 个任务的执行...")
    
    # 监控5分钟
    start_time = time.time()
    max_monitor_time = 300  # 5分钟
    
    while time.time() - start_time < max_monitor_time:
        try:
            # 查询队列状态
            queue_status = test_queue_status()
            
            if queue_status:
                # 如果队列为空且没有执行任务，说明都完成了
                if queue_status['queue_size'] == 0 and not queue_status['executing_task']:
                    print(f"🎉 所有任务执行完成！")
                    break
            
            # 查询每个任务的状态
            for i, task_id in enumerate(task_ids):
                task_status = test_task_status_query(task_id)
                if task_status:
                    status = task_status['status']
                    position = task_status.get('queue_position', 'N/A')
                    print(f"   任务 {i+1}: {status} (位置: {position})")
            
            print(f"⏰ 等待30秒后继续监控...")
            time.sleep(30)
            
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断监控")
            break
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(10)
    
    print(f"📊 监控结束")

def main():
    """主函数"""
    print("🚀 开始测试排队合并分析功能")
    
    print(f"\n📝 测试内容:")
    print(f"1. 单个任务排队提交")
    print(f"2. 多个任务排队提交")
    print(f"3. 队列状态查询")
    print(f"4. 任务状态查询")
    print(f"5. 任务取消功能")
    print(f"6. 取消规则验证")
    
    # 测试1: 单个任务提交
    task_id = test_queued_analysis()
    
    # 测试2: 队列状态查询
    test_queue_status()
    
    # 测试3: 任务状态查询
    if task_id:
        test_task_status_query(task_id)
    
    # 测试4: 取消规则
    test_cancel_rules()
    
    # 测试5: 多任务监控（可选）
    user_input = input(f"\n❓ 是否进行多任务执行监控测试？(y/n): ")
    if user_input.lower() == 'y':
        monitor_queue_execution()
    
    print(f"\n🎉 排队合并分析功能测试完成！")
    
    print(f"\n📖 功能总结:")
    print(f"✅ 任务排队提交 - 避免并发执行导致系统崩溃")
    print(f"✅ 即时状态反馈 - 创建TaskInfo.json模板，状态为'等待中'")
    print(f"✅ 队列管理 - 按提交顺序执行，同时只执行一个任务")
    print(f"✅ 任务取消 - 等待中的任务可以取消")
    print(f"✅ 状态查询 - 实时查看队列和任务状态")
    print(f"✅ 线程安全 - 使用锁机制确保队列操作安全")

if __name__ == "__main__":
    main()
