#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试队列化合并分析接口
验证队列管理、任务执行、状态跟踪等功能
"""

import requests
import json
import time

def test_queued_analysis():
    """测试队列化合并分析接口"""
    print("🚀 测试队列化合并分析接口")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    test_tasks = [
        {
            'name': '耕地分析任务1',
            'params': {
                'id': '20250705171599',
                'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
                'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
                'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'area_threshold': 400.0,
                'model_type': 'deeplabv3_plus'
            }
        },
        {
            'name': '建设用地分析任务2',
            'params': {
                'id': '20250705171600',
                'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600_out.tif',
                'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
                'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
                'area_threshold': 200.0,
                'model_type': 'deeplabv3_plus'
            }
        }
    ]
    
    task_ids = []
    
    # 测试1: 提交多个任务到队列
    print(f"\n📥 测试1: 提交多个任务到队列")
    for i, task in enumerate(test_tasks):
        try:
            print(f"  提交任务{i+1}: {task['name']}")
            
            response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                                  params=task['params'], timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    task_id = result['data']['task_id']
                    queue_position = result['data']['queue_position']
                    estimated_wait = result['data']['estimated_wait_time']
                    
                    task_ids.append(task_id)
                    print(f"    ✅ 任务已提交: {task_id[:8]}...")
                    print(f"    📊 队列位置: {queue_position}")
                    print(f"    ⏰ 预估等待: {estimated_wait}")
                else:
                    print(f"    ❌ 提交失败: {result['message']}")
            else:
                print(f"    ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 异常: {e}")
    
    if not task_ids:
        print("❌ 没有成功提交的任务，测试终止")
        return
    
    # 测试2: 查看队列状态
    print(f"\n📊 测试2: 查看队列状态")
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                data = result['data']
                print(f"  队列大小: {data['queue_size']}")
                print(f"  当前执行: {data['current_task'][:8] if data['current_task'] else 'None'}...")
                print(f"  总任务数: {data['total_tasks']}")
                print(f"  等待任务: {data['waiting_tasks']}")
                print(f"  完成任务: {data['completed_tasks']}")
            else:
                print(f"  ❌ 获取失败: {result['message']}")
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
    except Exception as e:
        print(f"  ❌ 异常: {e}")
    
    # 测试3: 查看特定任务状态
    print(f"\n🔍 测试3: 查看特定任务状态")
    for i, task_id in enumerate(task_ids):
        try:
            response = requests.get(f"{base_url}/queue-status/", 
                                  params={'task_id': task_id}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    print(f"  任务{i+1} ({task_id[:8]}...):")
                    print(f"    状态: {data['status']}")
                    print(f"    消息: {data['message']}")
                    print(f"    创建时间: {data['created_time']}")
                    if 'queue_position' in data:
                        print(f"    队列位置: {data['queue_position']}")
                        print(f"    预估等待: {data['estimated_wait_time']}")
                else:
                    print(f"  ❌ 获取失败: {result['message']}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    # 测试4: 取消一个等待中的任务
    if len(task_ids) > 1:
        print(f"\n❌ 测试4: 取消等待中的任务")
        cancel_task_id = task_ids[-1]  # 取消最后一个任务
        try:
            response = requests.post(f"{base_url}/cancel-queued-task/", 
                                   json={'task_id': cancel_task_id}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"  ✅ 任务已取消: {cancel_task_id[:8]}...")
                else:
                    print(f"  ❌ 取消失败: {result['message']}")
            else:
                print(f"  ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            print(f"  ❌ 异常: {e}")
    
    # 测试5: 监控任务执行
    print(f"\n👀 测试5: 监控任务执行（60秒）")
    monitor_start = time.time()
    last_status = {}
    
    while time.time() - monitor_start < 60:
        try:
            # 获取队列状态
            response = requests.get(f"{base_url}/queue-status/", timeout=5)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    current_task = data['current_task']
                    queue_size = data['queue_size']
                    
                    # 检查状态变化
                    current_status = f"执行:{current_task[:8] if current_task else 'None'}..., 队列:{queue_size}"
                    if current_status != last_status.get('queue'):
                        print(f"  📊 {time.strftime('%H:%M:%S')} - {current_status}")
                        last_status['queue'] = current_status
            
            # 检查任务状态变化
            for i, task_id in enumerate(task_ids):
                try:
                    response = requests.get(f"{base_url}/queue-status/", 
                                          params={'task_id': task_id}, timeout=5)
                    if response.status_code == 200:
                        result = response.json()
                        if result['success']:
                            data = result['data']
                            status = data['status']
                            
                            # 检查状态变化
                            if task_id not in last_status or last_status[task_id] != status:
                                print(f"  🔄 任务{i+1} ({task_id[:8]}...) 状态变更: {status}")
                                last_status[task_id] = status
                except:
                    pass
            
            time.sleep(5)  # 每5秒检查一次
            
        except Exception as e:
            print(f"  ⚠️ 监控异常: {e}")
            time.sleep(5)
    
    print(f"\n📋 测试完成总结:")
    print(f"  提交任务数: {len(task_ids)}")
    print(f"  任务ID列表: {[tid[:8] + '...' for tid in task_ids]}")
    print(f"  监控时长: 60秒")

def test_queue_management():
    """测试队列管理功能"""
    print(f"\n🔧 测试队列管理功能")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 快速提交3个任务
    task_ids = []
    for i in range(3):
        params = {
            'id': f'test_image_{i}',
            'image': f'D:/test/image_{i}.tif',
            'model': 'D:/test/model.pth',
            'old_data_path': 'D:/test/old_data.shp',
            'area_threshold': 400.0
        }
        
        try:
            response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                                  params=params, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    task_id = result['data']['task_id']
                    task_ids.append(task_id)
                    print(f"  ✅ 任务{i+1}已提交: {task_id[:8]}...")
        except Exception as e:
            print(f"  ❌ 任务{i+1}提交失败: {e}")
    
    # 测试取消功能
    if len(task_ids) >= 2:
        print(f"\n❌ 测试取消功能:")
        for task_id in task_ids[1:]:  # 取消除第一个外的所有任务
            try:
                response = requests.post(f"{base_url}/cancel-queued-task/", 
                                       json={'task_id': task_id}, timeout=10)
                if response.status_code == 200:
                    result = response.json()
                    print(f"  取消 {task_id[:8]}...: {'✅' if result['success'] else '❌'} {result['message']}")
            except Exception as e:
                print(f"  取消 {task_id[:8]}... 异常: {e}")
    
    # 最终队列状态
    print(f"\n📊 最终队列状态:")
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                data = result['data']
                print(f"  队列大小: {data['queue_size']}")
                print(f"  等待任务: {data['waiting_tasks']}")
                print(f"  完成任务: {data['completed_tasks']}")
    except Exception as e:
        print(f"  ❌ 获取队列状态异常: {e}")

def main():
    """主函数"""
    print("🧪 队列化合并分析接口测试")
    
    print(f"\n📝 测试内容:")
    print(f"1. 提交多个任务到队列")
    print(f"2. 查看队列整体状态")
    print(f"3. 查看特定任务状态")
    print(f"4. 取消等待中的任务")
    print(f"5. 监控任务执行过程")
    
    # 选择测试模式
    print(f"\n🔧 测试模式:")
    print(f"1. 完整功能测试（推荐）")
    print(f"2. 队列管理测试")
    
    choice = input("请选择测试模式 (1/2): ").strip()
    
    if choice == "1":
        test_queued_analysis()
    elif choice == "2":
        test_queue_management()
    else:
        print("❌ 无效选择，执行完整功能测试")
        test_queued_analysis()
    
    print(f"\n🎉 测试完成！")
    
    print(f"\n📚 接口说明:")
    print(f"- 队列化分析: GET /api/analysis/queued-combined-ai-spatial-analysis/")
    print(f"- 取消任务: POST /api/analysis/cancel-queued-task/")
    print(f"- 队列状态: GET /api/analysis/queue-status/")
    
    print(f"\n💡 使用建议:")
    print(f"1. 使用队列化接口避免并发执行导致的系统崩溃")
    print(f"2. 通过队列状态接口监控任务进度")
    print(f"3. 及时取消不需要的等待中任务")
    print(f"4. TaskInfo.json会立即创建，状态为'等待中'")

if __name__ == "__main__":
    main()
