2025-08-28 17:49:17,702 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_174917.log
2025-08-28 17:49:17,705 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:49:17,705 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:49:17,725 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:49:17,729 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:49:17,730 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:49:17,743 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:49:17,745 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:49:17,746 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:49:17,758 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:49:17,760 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:49:17,760 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:49:17,772 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:49:17,774 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:49:17,774 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:49:17,786 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:49:17,787 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:49:17,788 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:49:17,799 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:49:17,821 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:49:17,827 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:49:18,007 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:49:18,087 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:49:18,092 - analysis_executor - INFO - 加载了 28 个任务状态
