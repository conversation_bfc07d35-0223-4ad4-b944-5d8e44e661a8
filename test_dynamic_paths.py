#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试动态路径获取功能
验证所有硬编码路径是否已正确修改为动态获取
"""

import os
import sys
import requests
from datetime import datetime

def test_config_access():
    """测试配置文件访问"""
    print("=== 测试配置文件访问 ===")
    
    try:
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        response = requests.get(config_url, timeout=10)
        response.raise_for_status()
        
        print(f"✓ 配置文件访问成功")
        print(f"  URL: {config_url}")
        print(f"  状态码: {response.status_code}")
        print(f"  内容长度: {len(response.text)} 字符")
        
        # 解析配置
        config = {}
        current_section = None
        
        for line in response.text.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            if line.startswith('[') and line.endswith(']'):
                current_section = line[1:-1]
                config[current_section] = {}
            elif '=' in line and current_section:
                key, value = line.split('=', 1)
                config[current_section][key.strip()] = value.strip()
        
        # 显示PATHS部分
        if 'PATHS' in config:
            print(f"  PATHS配置:")
            for key, value in config['PATHS'].items():
                print(f"    {key}: {value}")
            
            window_data_path = config['PATHS'].get('window_data_path', '未设置')
            print(f"  window_data_path: {window_data_path}")
        else:
            print(f"  ⚠️ 未找到PATHS配置段")
        
        return True, config
        
    except Exception as e:
        print(f"✗ 配置文件访问失败: {e}")
        return False, {}

def test_task_queue_manager():
    """测试TaskQueueManager的动态路径"""
    print("\n=== 测试TaskQueueManager动态路径 ===")
    
    try:
        # 设置Django环境
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_django.settings')
        
        import django
        django.setup()
        
        from geoserver_api.core.task_queue_manager import TaskQueueManager
        
        # 创建实例
        manager = TaskQueueManager()
        
        # 测试路径获取
        window_data_path = manager._get_window_data_path()
        print(f"✓ TaskQueueManager路径获取成功")
        print(f"  window_data_path: {window_data_path}")
        
        # 验证路径是否存在
        if os.path.exists(window_data_path):
            print(f"  ✓ 路径存在")
        else:
            print(f"  ⚠️ 路径不存在，但这是正常的")
        
        return True
        
    except Exception as e:
        print(f"✗ TaskQueueManager测试失败: {e}")
        import traceback
        print(f"  错误详情: {traceback.format_exc()}")
        return False

def test_combined_analysis_executor():
    """测试CombinedAnalysisExecutor的动态路径"""
    print("\n=== 测试CombinedAnalysisExecutor动态路径 ===")
    
    try:
        from geoserver_api.core.combined_analysis_executor import CombinedAnalysisExecutor
        
        # 创建实例
        executor = CombinedAnalysisExecutor()
        
        # 测试路径获取
        window_data_path = executor._get_window_data_path()
        print(f"✓ CombinedAnalysisExecutor路径获取成功")
        print(f"  window_data_path: {window_data_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ CombinedAnalysisExecutor测试失败: {e}")
        import traceback
        print(f"  错误详情: {traceback.format_exc()}")
        return False

def test_cleanup_script():
    """测试cleanup脚本的动态路径"""
    print("\n=== 测试cleanup脚本动态路径 ===")
    
    try:
        # 导入cleanup脚本的函数
        sys.path.append('.')
        from cleanup_duplicate_taskinfo import get_window_data_path
        
        # 测试路径获取
        window_data_path = get_window_data_path()
        print(f"✓ cleanup脚本路径获取成功")
        print(f"  window_data_path: {window_data_path}")
        
        return True
        
    except Exception as e:
        print(f"✗ cleanup脚本测试失败: {e}")
        import traceback
        print(f"  错误详情: {traceback.format_exc()}")
        return False

def check_hardcoded_paths():
    """检查是否还有硬编码路径"""
    print("\n=== 检查硬编码路径 ===")
    
    files_to_check = [
        'geoserver_api/core/task_queue_manager.py',
        'geoserver_api/core/combined_analysis_executor.py',
        'cleanup_duplicate_taskinfo.py'
    ]
    
    hardcoded_pattern = 'D:/Drone_Project/nginxData'
    found_hardcoded = False
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            if hardcoded_pattern in content:
                print(f"⚠️ {file_path} 仍包含硬编码路径")
                # 显示包含硬编码的行
                lines = content.split('\n')
                for i, line in enumerate(lines, 1):
                    if hardcoded_pattern in line:
                        print(f"  第{i}行: {line.strip()}")
                found_hardcoded = True
            else:
                print(f"✓ {file_path} 无硬编码路径")
        else:
            print(f"✗ {file_path} 文件不存在")
    
    if not found_hardcoded:
        print("✓ 所有文件都已移除硬编码路径")
    
    return not found_hardcoded

def main():
    """主函数"""
    print("开始测试动态路径获取功能...")
    print(f"测试时间: {datetime.now()}")
    
    results = []
    
    # 测试配置文件访问
    config_success, config = test_config_access()
    results.append(("配置文件访问", config_success))
    
    # 测试各个组件
    if config_success:
        results.append(("TaskQueueManager", test_task_queue_manager()))
        results.append(("CombinedAnalysisExecutor", test_combined_analysis_executor()))
        results.append(("cleanup脚本", test_cleanup_script()))
    
    # 检查硬编码路径
    results.append(("硬编码路径检查", check_hardcoded_paths()))
    
    # 显示测试结果
    print(f"\n{'='*60}")
    print("测试结果汇总:")
    print(f"{'='*60}")
    
    for test_name, success in results:
        status = "✓" if success else "✗"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！动态路径获取功能正常工作。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")

if __name__ == '__main__':
    main()
