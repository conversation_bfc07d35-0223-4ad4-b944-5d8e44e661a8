# GeoServer Django API 使用文档

## 概述

GeoServer Django API 是一个基于Django框架开发的地理信息服务API，提供栅格数据查询、图层管理、批处理执行、TIF文件处理、GeoServer发布和地图相关功能。

## 基础信息

- **基础URL**: 动态获取（默认: `http://127.0.0.1:8091`）
- **协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **CORS支持**: 已启用，支持跨域访问

## API模块概览

### 🏥 [健康检查API](./health_api.md)
- **路径**: `/health/`
- **功能**: 检查API服务运行状态
- **用途**: 服务监控、负载均衡健康检查

### 🔍 [栅格查询API](./query_api.md)
- **路径**: `/api/query/`
- **功能**: 查询指定坐标点的栅格数据值
- **用途**: 地理数据查询、空间分析

### ⚙️ [管理API](./management_api.md)
- **路径**: `/api/management/`
- **功能**: GeoServer工作区和图层管理
- **用途**: 图层信息查询、图层删除、工作区管理

### 🔄 [批处理API](./batch_api.md)
- **路径**: `/api/batch/`
- **功能**: 执行批处理文件和任务状态监控
- **用途**: ODM处理、自动化任务执行

### 🖼️ [TIF处理API](./tif_api.md)
- **路径**: `/api/tif/`
- **功能**: TIF文件信息获取和格式转换
- **用途**: 图像优化、格式转换、文件分析

### 🌐 [GeoServer发布API](./geo_api.md)
- **路径**: `/api/geo/`
- **功能**: 将GeoTIFF文件发布到GeoServer
- **用途**: 地图服务发布、图层管理

### 🗺️ [地图API](./map_api.md)
- **路径**: `/api/map/`
- **功能**: ODM任务管理、日志查询
- **用途**: 任务监控、数据处理

### 📊 [地理空间分析API](./analysis_api.md)
- **路径**: `/api/analysis/`
- **功能**: 耕地流入流出分析、空间变化检测、AI语义分割、权重信息管理
- **用途**: 土地利用变化分析、空间数据对比、AI模型推理

## 快速开始

### 动态端口配置

API支持动态端口配置，优先级如下：
1. **环境变量**: `DJANGO_PORT=9000`
2. **命令行参数**: `python manage.py runserver 0.0.0.0:9000`
3. **默认端口**: `8091`

```bash
# 设置环境变量
export DJANGO_PORT=9000
export DJANGO_HOST=localhost

# 或在代码中使用动态配置
from config import DJANGO_BASE_URL
api_url = f"{DJANGO_BASE_URL}/api/analysis/"
```

### 1. 检查服务状态
```bash
# 使用默认端口
curl -X GET "http://127.0.0.1:8091/health/"

# 使用自定义端口
curl -X GET "http://127.0.0.1:9000/health/"
```

### 2. 查询栅格数据
```bash
curl -X GET "http://127.0.0.1:8091/api/query/?lat=30.5&lon=120.2&workspace=testodm"
```

### 3. 获取工作区列表
```bash
curl -X GET "http://127.0.0.1:8091/api/management/workspaces/"
```

### 4. 发布Shapefile文件
```bash
curl -X GET "http://127.0.0.1:8091/api/geo/shapefile/execute/?path=D:/data/boundary.shp&workspace=testodm"
```

### 5. 发布GeoTIFF文件
```bash
curl -X GET "http://127.0.0.1:8091/api/geo/geotiff/execute/?file_path=D:/data/image.tif&workspace=testodm"
```

### 6. 获取AI模型权重信息
```bash
curl -X GET "http://127.0.0.1:8091/api/analysis/weight-info/"
```

### 7. 启动AI语义分割
```bash
curl -X GET "http://127.0.0.1:8091/api/analysis/ai-semantic-segmentation/?image=D:/data/satellite.tif&model=D:/models/farmland.pth&model_type=deeplabv3_plus"
```

### 8. 空间数据变化分析
```bash
curl -X GET "http://127.0.0.1:8091/api/analysis/spatial-changes/?old_data_path=D:/data/old.shp&new_data_path=D:/data/new.shp"
```

## 认证和权限

当前版本的API不需要认证，但建议在生产环境中实现适当的认证机制。

## 错误处理

所有API端点都遵循统一的错误响应格式：

```json
{
  "status": "error",
  "message": "错误描述信息",
  "error_code": "ERROR_CODE",
  "details": {
    "additional": "详细信息"
  }
}
```

### 常见HTTP状态码

| 状态码 | 说明 | 常见原因 |
|--------|------|----------|
| 200 | 成功 | 请求正常处理 |
| 400 | 请求错误 | 参数缺失或格式错误 |
| 404 | 资源不存在 | 文件、图层或任务不存在 |
| 500 | 服务器错误 | 内部处理错误 |

## 性能和限制

### 文件大小限制
- **Shapefile ZIP**: 最大100MB
- **TIF文件**: 建议小于1GB
- **批处理超时**: 默认1小时

### 并发限制
- **同时查询**: 建议不超过50个并发请求
- **文件处理**: 建议不超过5个并发任务
- **GeoServer发布**: 建议不超过3个并发发布

### 性能优化建议
1. **缓存策略**: 对频繁查询的数据实现客户端缓存
2. **批量操作**: 使用批量API减少请求次数
3. **异步处理**: 大文件处理使用异步方式
4. **分页查询**: 大数据集查询使用分页



## 部署和配置

### 环境要求
- **Python**: 3.8+
- **Django**: 4.0+
- **依赖包**: 见requirements.txt

### 启动服务
```bash
# 使用批处理文件启动
start_django_api.bat

# 或手动启动
python manage.py runserver 0.0.0.0:8091
```

### 配置文件
主要配置文件位于 `geoserver_api/settings.py`：

```python
# 允许的主机
ALLOWED_HOSTS = ['*']

# CORS配置
CORS_ALLOW_ALL_ORIGINS = True

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    # ... 详细配置
}
```

## 监控和日志

### 日志文件位置
- **应用日志**: `logs/geoserver_YYYYMMDD_HHMMSS.log`
- **错误日志**: `logs/error_YYYYMMDD_HHMMSS.log`
- **访问日志**: Django内置访问日志

### 监控指标
- **响应时间**: 平均响应时间应小于1秒
- **错误率**: 错误率应小于1%
- **并发数**: 监控同时处理的请求数
- **资源使用**: CPU、内存、磁盘使用率

## 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口8091是否被占用
   - 确认Python环境和依赖包
   - 查看启动日志

2. **GeoServer连接失败**
   - 检查GeoServer服务状态
   - 确认GeoServer URL配置
   - 检查网络连接

3. **文件处理失败**
   - 确认文件路径正确
   - 检查文件权限
   - 验证文件格式

## 版本更新

### 当前版本: 2.0
- ✅ Django框架重构
- ✅ 完整的API文档
- ✅ CORS跨域支持
- ✅ 统一错误处理
- ✅ 日志系统优化

### 计划功能
- 🔄 用户认证和权限管理
- 🔄 API速率限制
- 🔄 数据缓存机制
- 🔄 WebSocket实时通信
- 🔄 API版本控制

## 技术支持

### 文档链接

#### 📖 API文档
- [健康检查API](./health_api.md)
- [栅格查询API](./query_api.md)
- [管理API](./management_api.md)
- [批处理API](./batch_api.md)
- [TIF处理API](./tif_api.md)
- [GeoServer发布API](./geo_api.md)
- [地图API](./map_api.md)

#### 📚 使用指南
- [**Shapefile处理与发布完整指南**](./shapefile_guide.md) - 🔥 推荐阅读

### 联系方式
- **项目地址**: `D:\Drone_Project\geoserverAPIDJV2`
- **日志位置**: `logs/`
- **配置文件**: `geoserver_api/settings.py`

---

**📝 注意**: 本文档基于Django版本2.0编写，如有更新请及时同步文档内容。
