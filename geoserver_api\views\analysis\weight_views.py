#!/usr/bin/env python3
"""
权重信息视图模块

功能说明:
- 获取AI模型权重信息
- 读取配置文件和权重目录
- 按地物类型分类返回权重信息
"""

import os
import configparser
import requests
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from geoserver_api.core.logger_config import analysis_logger


def find_matching_default_weight(land_type, default_weights):
    """
    查找匹配的默认权重配置

    参数:
        land_type: 地物类型名称（如 arableLand）
        default_weights: 默认权重配置字典（如 {'arableland': '...'} ）

    返回:
        str: 匹配的默认权重路径，如果没找到则返回空字符串
    """
    if not default_weights:
        return ""

    # 尝试多种匹配方式
    possible_keys = [
        land_type,  # 原名：arableLand
        land_type.lower(),  # 小写：arableland
        land_type.lower().replace('_', ''),  # 去掉下划线：arableland
    ]

    # 驼峰转小写：arableLand -> arableland
    camel_to_lower = ""
    for i, char in enumerate(land_type):
        if char.isupper() and i > 0:
            camel_to_lower += char.lower()
        else:
            camel_to_lower += char.lower()
    possible_keys.append(camel_to_lower)

    # 查找匹配的键
    for key in possible_keys:
        if key in default_weights:
            return default_weights[key]

    return ""


@csrf_exempt
@require_http_methods(["GET"])
def get_weight_info(request):
    """
    获取AI模型权重信息和shp文件信息

    返回格式:
    {
        "status": "success",
        "message": "成功获取权重信息",
        "data": {
            "arableLand": {
                "default": "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
                "display_name": "耕地",
                "default_area": 400.0,
                "models": {
                    "deeplabv3_plus": [
                        {
                            "name": "deeplabv3_plus_best_20250807-111949.pth",
                            "path": "D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth"
                        }
                    ]
                },
                "shp_files": [
                    "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp",
                    "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2025_84.shp"
                ]
            },
            "constructionLand": {
                "default": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth",
                "display_name": "建设用地",
                "default_area": 200.0,
                "models": {...},
                "shp_files": [...]
            }
            "constructionLand": {
                "default": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth",
                "models": {
                    "deeplabv3_plus": [
                        {
                            "name": "deeplabv3_plus_best_20250811-162215.pth",
                            "path": "D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth"
                        }
                    ]
                },
                "shp_files": [
                    "D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp"
                ]
            }
        }
    }
    """
    try:
        analysis_logger.info("开始获取AI模型权重信息")
        
        # 读取配置文件
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        analysis_logger.info(f"读取配置文件: {config_url}")
        
        try:
            response = requests.get(config_url, timeout=10)
            response.raise_for_status()
            config_content = response.text
            analysis_logger.info("配置文件读取成功")
        except requests.RequestException as e:
            analysis_logger.error(f"读取配置文件失败: {e}")
            return JsonResponse({
                "status": "error",
                "message": f"无法读取配置文件: {str(e)}",
                "data": None
            }, status=500)
        
        # 解析配置文件
        config = configparser.ConfigParser()
        config.read_string(config_content)
        
        # 获取window_data_path
        if 'PATHS' not in config or 'window_data_path' not in config['PATHS']:
            analysis_logger.error("配置文件中缺少window_data_path")
            return JsonResponse({
                "status": "error",
                "message": "配置文件中缺少window_data_path配置",
                "data": None
            }, status=500)
        
        window_data_path = config['PATHS']['window_data_path']
        analysis_logger.info(f"获取到window_data_path: {window_data_path}")
        
        # 获取默认权重配置
        default_weights = {}
        if 'Default_weight' in config:
            default_weights = dict(config['Default_weight'])
            analysis_logger.info(f"获取到默认权重配置: {default_weights}")

        # 获取字段映射配置
        field_mapping = {}
        if 'Field_mapping' in config:
            field_mapping = dict(config['Field_mapping'])
            analysis_logger.info(f"获取到字段映射配置: {field_mapping}")
        else:
            # 如果配置文件中没有Field_mapping，使用默认映射
            field_mapping = {
                'arableLand': '耕地',
                'constructionLand': '建设用地'
            }
            analysis_logger.info(f"使用默认字段映射配置: {field_mapping}")

        # 获取默认面积阈值配置
        default_areas = {}
        if 'Default_area' in config:
            analysis_logger.info("发现Default_area配置节")
            default_areas_raw = dict(config['Default_area'])
            analysis_logger.info(f"原始默认面积配置: {default_areas_raw}")

            # 创建键名映射，处理configparser的小写转换问题
            key_mapping = {
                'arableland': 'arableLand',
                'constructionland': 'constructionLand'
            }

            # 转换为数值类型并恢复正确的键名
            for raw_key, value in default_areas_raw.items():
                # 恢复正确的键名
                correct_key = key_mapping.get(raw_key.lower(), raw_key)
                analysis_logger.info(f"处理默认面积配置: '{raw_key}' -> '{correct_key}' = '{value}' (类型: {type(value)})")

                try:
                    numeric_value = float(value)
                    default_areas[correct_key] = numeric_value
                    analysis_logger.info(f"默认面积转换成功: {correct_key} = {numeric_value}")
                except (ValueError, TypeError) as e:
                    analysis_logger.warning(f"默认面积配置转换失败: {correct_key}='{value}', 错误: {e}")
                    default_areas[correct_key] = 400.0  # 使用默认值
                    analysis_logger.warning(f"使用默认值: {correct_key} = 400.0")

            analysis_logger.info(f"最终默认面积配置: {default_areas}")
        else:
            # 如果配置文件中没有Default_area，使用默认配置
            default_areas = {
                'arableLand': 400.0,
                'constructionLand': 200.0
            }
            analysis_logger.info(f"未找到Default_area配置节，使用默认面积配置: {default_areas}")
        
        # 构建权重目录路径
        weight_dir = os.path.join(window_data_path, "ODM", "AIWeight")
        analysis_logger.info(f"权重目录路径: {weight_dir}")
        
        # 扫描权重目录
        weight_info = scan_weight_directory(weight_dir, default_weights, field_mapping, default_areas)

        # 扫描AIOLDSHP目录，添加shp文件信息
        shp_dir = os.path.join(window_data_path, "ODM", "AIOLDSHP")
        shp_info = scan_shp_directory(shp_dir, window_data_path)

        # 将shp信息和字段映射添加到权重信息中
        for land_type, shp_files in shp_info.items():
            if land_type in weight_info:
                weight_info[land_type]["shp_files"] = shp_files
            else:
                # 如果权重信息中没有这个地物类型，创建一个新的条目
                weight_info[land_type] = {
                    "default": "",
                    "models": {},
                    "shp_files": shp_files,
                    "display_name": field_mapping.get(land_type, land_type),
                    "default_area": default_areas.get(land_type, 400.0)
                }

        # 为所有地物类型添加显示名称和默认面积
        for land_type in weight_info:
            if "display_name" not in weight_info[land_type]:
                weight_info[land_type]["display_name"] = field_mapping.get(land_type, land_type)
            if "default_area" not in weight_info[land_type]:
                weight_info[land_type]["default_area"] = default_areas.get(land_type, 400.0)

        analysis_logger.info(f"成功获取权重信息，共 {len(weight_info)} 个地物类型")
        
        return JsonResponse({
            "status": "success",
            "message": "成功获取权重信息",
            "data": weight_info
        })
        
    except Exception as e:
        analysis_logger.error(f"获取权重信息失败: {str(e)}")
        import traceback
        analysis_logger.error(f"错误详情: {traceback.format_exc()}")
        
        return JsonResponse({
            "status": "error",
            "message": f"获取权重信息失败: {str(e)}",
            "data": None
        }, status=500)


def scan_weight_directory(weight_dir, default_weights, field_mapping=None, default_areas=None):
    """
    扫描权重目录，按地物类型分类返回权重信息

    参数:
        weight_dir: 权重目录路径
        default_weights: 默认权重配置
        field_mapping: 字段映射配置
        default_areas: 默认面积阈值配置

    返回:
        dict: 按地物类型分类的权重信息
    """
    weight_info = {}

    # 如果没有提供字段映射，使用默认映射
    if field_mapping is None:
        field_mapping = {
            'arableLand': '耕地',
            'constructionLand': '建设用地'
        }

    # 如果没有提供默认面积，使用默认配置
    if default_areas is None:
        default_areas = {
            'arableLand': 400.0,
            'constructionLand': 200.0
        }

    try:
        if not os.path.exists(weight_dir):
            analysis_logger.warning(f"权重目录不存在: {weight_dir}")
            return weight_info
        
        # 遍历地物类型目录
        for land_type in os.listdir(weight_dir):
            land_type_path = os.path.join(weight_dir, land_type)
            
            if not os.path.isdir(land_type_path):
                continue
            
            analysis_logger.info(f"扫描地物类型: {land_type}")
            
            # 查找匹配的默认权重配置
            default_weight = find_matching_default_weight(land_type, default_weights)

            # 初始化地物类型信息
            weight_info[land_type] = {
                "default": default_weight,
                "models": {},
                "display_name": field_mapping.get(land_type, land_type),
                "default_area": default_areas.get(land_type, 400.0)
            }
            
            # 遍历模型类型目录
            for model_type in os.listdir(land_type_path):
                model_type_path = os.path.join(land_type_path, model_type)
                
                if not os.path.isdir(model_type_path):
                    continue
                
                analysis_logger.info(f"  扫描模型类型: {model_type}")
                
                # 初始化模型类型信息
                weight_info[land_type]["models"][model_type] = []
                
                # 遍历权重文件
                for weight_file in os.listdir(model_type_path):
                    weight_file_path = os.path.join(model_type_path, weight_file)
                    
                    if not os.path.isfile(weight_file_path):
                        continue
                    
                    # 只处理.pth文件
                    if not weight_file.endswith('.pth'):
                        continue
                    
                    # 构建完整路径，统一使用正斜杠
                    full_path = weight_file_path.replace('\\', '/')

                    weight_info[land_type]["models"][model_type].append({
                        "name": weight_file,
                        "path": full_path
                    })
                    
                    analysis_logger.info(f"    发现权重文件: {weight_file}")
                
                # 按文件名排序
                weight_info[land_type]["models"][model_type].sort(key=lambda x: x["name"])
        
        analysis_logger.info(f"权重目录扫描完成，共发现 {len(weight_info)} 个地物类型")
        
    except Exception as e:
        analysis_logger.error(f"扫描权重目录失败: {str(e)}")
        import traceback
        analysis_logger.error(f"错误详情: {traceback.format_exc()}")
    
    return weight_info


@csrf_exempt
@require_http_methods(["GET"])
def get_weight_config(request):
    """
    获取权重配置信息（仅返回配置文件内容）

    返回格式:
    {
        "status": "success",
        "message": "成功获取配置信息",
        "data": {
            "window_data_path": "D:/Drone_Project/nginxData",
            "default_weights": {
                "arableLand": "deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth",
                "constructionLand": "deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth"
            },
            "field_mapping": {
                "arableLand": "耕地",
                "constructionLand": "建设用地"
            },
            "default_areas": {
                "arableLand": 400.0,
                "constructionLand": 200.0
            }
        }
    }
    """
    try:
        analysis_logger.info("开始获取权重配置信息")
        
        # 读取配置文件
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        
        try:
            response = requests.get(config_url, timeout=10)
            response.raise_for_status()
            config_content = response.text
        except requests.RequestException as e:
            analysis_logger.error(f"读取配置文件失败: {e}")
            return JsonResponse({
                "status": "error",
                "message": f"无法读取配置文件: {str(e)}",
                "data": None
            }, status=500)
        
        # 解析配置文件
        config = configparser.ConfigParser()
        config.read_string(config_content)
        
        # 提取配置信息
        config_data = {
            "window_data_path": config.get('PATHS', 'window_data_path', fallback=""),
            "default_weights": dict(config['Default_weight']) if 'Default_weight' in config else {},
            "field_mapping": dict(config['Field_mapping']) if 'Field_mapping' in config else {
                'arableLand': '耕地',
                'constructionLand': '建设用地'
            },
            "default_areas": {}
        }

        # 处理默认面积配置
        if 'Default_area' in config:
            default_areas_raw = dict(config['Default_area'])

            # 创建键名映射，处理configparser的小写转换问题
            key_mapping = {
                'arableland': 'arableLand',
                'constructionland': 'constructionLand'
            }

            # 转换为数值类型并恢复正确的键名
            for raw_key, value in default_areas_raw.items():
                # 恢复正确的键名
                correct_key = key_mapping.get(raw_key.lower(), raw_key)
                try:
                    config_data["default_areas"][correct_key] = float(value)
                except (ValueError, TypeError):
                    analysis_logger.warning(f"默认面积配置转换失败: {correct_key}={value}")
                    config_data["default_areas"][correct_key] = 400.0  # 使用默认值
        else:
            # 使用默认配置
            config_data["default_areas"] = {
                'arableLand': 400.0,
                'constructionLand': 200.0
            }
        
        analysis_logger.info("成功获取权重配置信息")
        
        return JsonResponse({
            "status": "success",
            "message": "成功获取配置信息",
            "data": config_data
        })
        
    except Exception as e:
        analysis_logger.error(f"获取权重配置信息失败: {str(e)}")
        
        return JsonResponse({
            "status": "error",
            "message": f"获取权重配置信息失败: {str(e)}",
            "data": None
        }, status=500)


def scan_shp_directory(shp_dir, window_data_path):
    """
    扫描AIOLDSHP目录，按地物类型分类返回shp文件信息

    参数:
        shp_dir: AIOLDSHP目录路径
        window_data_path: window_data_path配置路径

    返回:
        dict: 按地物类型分类的shp文件信息
        格式: {
            "arableLand": [
                "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp",
                "D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2025_84.shp"
            ],
            "constructionLand": [
                "D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp"
            ]
        }
    """
    shp_info = {}

    try:
        if not os.path.exists(shp_dir):
            analysis_logger.warning(f"AIOLDSHP目录不存在: {shp_dir}")
            return shp_info

        analysis_logger.info(f"开始扫描AIOLDSHP目录: {shp_dir}")

        # 遍历地物类型目录
        for land_type in os.listdir(shp_dir):
            land_type_path = os.path.join(shp_dir, land_type)

            if not os.path.isdir(land_type_path):
                continue

            analysis_logger.info(f"扫描地物类型目录: {land_type}")

            # 初始化地物类型的shp文件列表
            shp_files = []

            # 遍历该地物类型目录下的所有文件
            for file_name in os.listdir(land_type_path):
                file_path = os.path.join(land_type_path, file_name)

                # 只处理.shp文件
                if os.path.isfile(file_path) and file_name.lower().endswith('.shp'):
                    # 构建完整的绝对路径，使用正斜杠
                    full_path = file_path.replace('\\', '/')
                    shp_files.append(full_path)

                    analysis_logger.info(f"  发现shp文件: {file_name}")

            # 按文件名排序
            shp_files.sort()

            # 如果找到了shp文件，添加到结果中
            if shp_files:
                shp_info[land_type] = shp_files
                analysis_logger.info(f"  地物类型 {land_type} 共找到 {len(shp_files)} 个shp文件")

        analysis_logger.info(f"AIOLDSHP目录扫描完成，共发现 {len(shp_info)} 个地物类型")

    except Exception as e:
        analysis_logger.error(f"扫描AIOLDSHP目录失败: {str(e)}")
        import traceback
        analysis_logger.error(f"错误详情: {traceback.format_exc()}")

    return shp_info
