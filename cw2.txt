 2025-08-28 17:43:52 - INFO - === 合并分析任务开始 ===
2025-08-28 17:43:52 - INFO - 任务ID: 980d7e36-d23e-447b-b8ac-7752548a018c
2025-08-28 17:43:52 - INFO - 📝 TaskInfo.json已由队列管理器创建，跳过重复创建
2025-08-28 17:43:52 - INFO - 影像ID: 20250403124549
2025-08-28 17:43:52 - INFO - TIF图像: G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
2025-08-28 17:43:52 - INFO - 模型权重: G:/data/uavflight-file/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-28 17:43:52 - INFO - 历史数据: G:/data/uavflight-file/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-28 17:43:52 - INFO - 分析类别: arableLand
2025-08-28 17:43:52 - INFO - AI输出路径: G:\data\uavflight-file\ODM\AI\20250403124549\arableLand\20250403124549_1_1756374232.shp
2025-08-28 17:43:52 - INFO - 最终输出路径: G:\data\uavflight-file\ODM\AI\20250403124549\arableLand\20250403124549_2_1756374232.shp
2025-08-28 17:43:52 - INFO - 任务目录: G:\data\uavflight-file\ODM\AI\20250403124549
2025-08-28 17:43:52 - INFO - === 开始执行合并分析任务 ===
2025-08-28 17:43:52 - WARNING - ⚠️ TaskInfo路径丢失，尝试重新构建...
2025-08-28 17:43:52 - INFO - 📍 重新构建TaskInfo路径: G:\data\uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 17:43:52 - INFO - 📝 更新字段: status = 进行中
2025-08-28 17:43:52 - INFO - 📝 更新字段: message = 开始AI语义分割...
2025-08-28 17:43:52 - INFO - 📝 更新字段: progress = 5
2025-08-28 17:43:52 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress']
2025-08-28 17:43:52 - INFO - 🔍 验证更新结果:
2025-08-28 17:43:53 - INFO -   状态: 进行中
2025-08-28 17:43:53 - INFO -   进度: 5%
2025-08-28 17:43:53 - INFO -   AI处理时间: None
2025-08-28 17:43:53 - INFO -   成功状态: None
2025-08-28 17:43:53 - INFO -   空间统计:
2025-08-28 17:43:53 - INFO -     area_threshold: 400.0
2025-08-28 17:43:53 - INFO - 🤖 开始AI语义分割...
2025-08-28 17:43:53 - INFO - 📂 输入图像: G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
2025-08-28 17:43:53 - INFO - 🤖 模型路径: G:/data/uavflight-file/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-28 17:43:53 - INFO - 📁 输出路径: G:\data\uavflight-file\ODM\AI\20250403124549\arableLand\20250403124549_1_1756374232.shp
2025-08-28 17:43:53 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-28 17:43:53 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-28 17:43:53 - INFO - 🔍 检测到合并分析任务: image_id=20250403124549, category=arableLand
2025-08-28 17:43:53 - INFO - 📁 TaskInfo路径: G:/data/uavflight-file/ODM/AI/20250403124549/TaskInfo.json
2025-08-28 17:43:53 - INFO - ✅ 使用真实任务ID: 980d7e36-d23e-447b-b8ac-7752548a018c
2025-08-28 17:43:53 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-28 17:43:53 - INFO - 📄 读取现有TaskInfo文件...
2025-08-28 17:43:53 - INFO - 📋 读取到数组格式，包含 1 个历史任务
2025-08-28 17:43:53 - INFO - 🔄 更新现有任务记录: 980d7e36-d23e-447b-b8ac-7752548a018c
2025-08-28 17:43:53 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-28 17:43:53 - INFO - 📊 文件验证: 大小=1940字节，任务总数: 1
2025-08-28 17:43:58 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-28 17:43:58 - INFO - 🔧 创建处理参数:
2025-08-28 17:43:58 - INFO -   模型类型: deeplabv3_plus
2025-08-28 17:43:58 - INFO -   类别数量: 2
2025-08-28 17:43:58 - INFO -   目标类别: [1]
2025-08-28 17:43:58 - INFO -   窗口大小: 512
2025-08-28 17:43:58 - INFO -   批处理大小: 16
2025-08-28 17:43:58 - INFO -   重叠比例: 0.5
2025-08-28 17:43:58 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-28 17:43:58 - INFO - 📂 输入图像: G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
2025-08-28 17:43:58 - INFO - 🤖 模型路径: G:/data/uavflight-file/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-28 17:43:58 - INFO - 📁 输出路径: G:\data\uavflight-file\ODM\AI\20250403124549\arableLand\20250403124549_1_1756374232.shp
2025-08-28 17:43:58 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-28 17:43:58 - INFO - 🔄 开始AI模型推理...
2025-08-28 17:43:58 - INFO - 
==================================================
2025-08-28 17:43:58 - INFO - 开始处理图像: G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
2025-08-28 17:43:58 - INFO - 输出路径: G:\data\uavflight-file\ODM\AI\20250403124549\arableLand\20250403124549_1_1756374232.shp
2025-08-28 17:43:58 - INFO - ==================================================
2025-08-28 17:43:58 - INFO - 📖 读取图像: G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
2025-08-28 17:43:59 - INFO - 📏 图像大小: 33833x36887, 波段数: 3
2025-08-28 17:43:59 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-28 17:43:59 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-28 17:43:59 - INFO - 🔢 类别数量: 2
2025-08-28 17:43:59 - INFO - 🔄 重叠区域: 37 像素
2025-08-28 17:43:59 - INFO - 📦 批处理大小: 16
2025-08-28 17:43:59 - INFO - 💻 计算设备: cuda:0
2025-08-28 17:56:13 - INFO - 读取tif并裁剪预处理完毕,目前耗时间: 734s
2025-08-28 17:56:13 - INFO - 🔧 创建模型...
2025-08-28 17:56:25 - ERROR - ❌ 新版AI处理引擎执行失败: <urlopen error [Errno 11001] getaddrinfo failed>
2025-08-28 17:56:25 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 1354, in do_open
    h.request(req.get_method(), req.selector, req.data, headers,
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 1256, in request
    self._send_request(method, url, body, headers, encode_chunked)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 1302, in _send_request
    self.endheaders(body, encode_chunked=encode_chunked)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 1251, in endheaders
    self._send_output(message_body, encode_chunked=encode_chunked)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 1011, in _send_output
    self.send(msg)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 951, in send
    self.connect()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 1418, in connect
    super().connect()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\http\client.py", line 922, in connect
    self.sock = self._create_connection(
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socket.py", line 799, in create_connection
    for res in getaddrinfo(host, port, 0, SOCK_STREAM):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socket.py", line 930, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\analysis_executor.py", line 1015, in _execute_new_ai_processing
    success, testtime, processing_time = pre_pytorch_new.process_single_image(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 1421, in process_single_image
    model = create_model(args, model_path, device)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 337, in create_model
    model = deeplabv3_plus(backbone_type=args.backbone, num_classes=num_classes)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\network\deeplabv3_plus.py", line 379, in deeplabv3_plus
    model = DeepLabV3Plus(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\network\deeplabv3_plus.py", line 262, in __init__
    backbone = models.resnet101(weights=models.ResNet101_Weights.IMAGENET1K_V1)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torchvision\models\_utils.py", line 142, in wrapper
    return fn(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torchvision\models\_utils.py", line 228, in inner_wrapper
    return builder(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torchvision\models\resnet.py", line 795, in resnet101
    return _resnet(Bottleneck, [3, 4, 23, 3], weights, progress, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torchvision\models\resnet.py", line 301, in _resnet
    model.load_state_dict(weights.get_state_dict(progress=progress, check_hash=True))
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torchvision\models\_api.py", line 90, in get_state_dict
    return load_state_dict_from_url(self.url, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torch\hub.py", line 765, in load_state_dict_from_url
    download_url_to_file(url, cached_file, hash_prefix, progress=progress)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\torch\hub.py", line 624, in download_url_to_file
    u = urlopen(req)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 222, in urlopen
    return opener.open(url, data, timeout)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 525, in open
    response = self._open(req, data)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 542, in _open
    result = self._call_chain(self.handle_open, protocol, protocol +
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 502, in _call_chain
    result = func(*args)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 1397, in https_open
    return self.do_open(http.client.HTTPSConnection, req,
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\urllib\request.py", line 1357, in do_open
    raise URLError(err)
urllib.error.URLError: <urlopen error [Errno 11001] getaddrinfo failed>

2025-08-28 17:56:30 - INFO - 📝 更新字段: status = 失败
2025-08-28 17:56:30 - INFO - 📝 更新字段: message = AI语义分割失败: 新版AI处理失败: <urlopen error [Errno 11001] getaddrinfo failed>
2025-08-28 17:56:30 - INFO - 📝 更新字段: progress = 0
2025-08-28 17:56:30 - INFO - 📝 更新字段: end_time = 2025-08-28T17:56:30.097507
2025-08-28 17:56:30 - INFO - 📝 更新嵌套字段: results.success = False
2025-08-28 17:56:30 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress', 'end_time', 'results.success']
2025-08-28 17:56:30 - INFO - 🔍 验证更新结果:
2025-08-28 17:56:30 - INFO -   状态: 失败
2025-08-28 17:56:30 - INFO -   进度: 0%
2025-08-28 17:56:30 - INFO -   AI处理时间: None
2025-08-28 17:56:30 - INFO -   成功状态: False
2025-08-28 17:56:30 - INFO -   空间统计:
2025-08-28 17:56:30 - INFO -     area_threshold: 400.0