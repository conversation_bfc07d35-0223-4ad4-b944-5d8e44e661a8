2025-08-20 10:32:24 - INFO - === 合并分析任务开始 ===
2025-08-20 10:32:24 - INFO - 任务ID: bb090725-071d-4055-a195-3ad638176242
2025-08-20 10:32:24 - INFO - 🔧 准备创建TaskInfo.json模板...
2025-08-20 10:32:24 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-20 10:32:24 - ERROR - ❌ 创建TaskInfo.json模板失败: 'task_dir'
2025-08-20 10:32:24 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 458, in _create_combined_task_info_template
    task_info_file = os.path.join(task_info['task_dir'], 'TaskInfo.json')
KeyError: 'task_dir'

2025-08-20 10:32:24 - INFO - ✅ TaskInfo.json模板创建调用完成
2025-08-20 10:32:24 - INFO - 影像ID: 20250705171601
2025-08-20 10:32:24 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:32:24 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:32:24 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:32:24 - INFO - 分析类别: arableLand
2025-08-20 10:32:24 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755657144.shp
2025-08-20 10:32:24 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_2_1755657144.shp
2025-08-20 10:32:24 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171601
2025-08-20 10:32:24 - INFO - === 开始执行合并分析任务 ===
2025-08-20 10:32:24 - INFO - 🤖 开始AI语义分割...
2025-08-20 10:32:24 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:32:24 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:32:24 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755657144.shp
2025-08-20 10:32:24 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-20 10:32:24 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-20 10:32:27 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-20 10:32:27 - INFO - 🔧 创建处理参数:
2025-08-20 10:32:27 - INFO -   模型类型: deeplabv3_plus
2025-08-20 10:32:27 - INFO -   类别数量: 2
2025-08-20 10:32:27 - INFO -   目标类别: [1]
2025-08-20 10:32:27 - INFO -   窗口大小: 512
2025-08-20 10:32:27 - INFO -   批处理大小: 16
2025-08-20 10:32:27 - INFO -   重叠比例: 0.5
2025-08-20 10:32:27 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-20 10:32:27 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:32:27 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:32:27 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755657144.shp
2025-08-20 10:32:27 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-20 10:32:27 - INFO - 🔄 开始AI模型推理...
2025-08-20 10:32:27 - INFO - 
==================================================
2025-08-20 10:32:27 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:32:27 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755657144.shp
2025-08-20 10:32:27 - INFO - ==================================================
2025-08-20 10:32:27 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:32:27 - INFO - 📏 图像大小: 17007x20364, 波段数: 3
2025-08-20 10:32:27 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-20 10:32:27 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-20 10:32:27 - INFO - 🔢 类别数量: 2
2025-08-20 10:32:27 - INFO - 🔄 重叠区域: 37 像素
2025-08-20 10:32:27 - INFO - 📦 批处理大小: 16
2025-08-20 10:32:27 - INFO - 💻 计算设备: cuda:0
2025-08-20 10:33:06 - INFO - 读取tif并裁剪预处理完毕,目前耗时间: 39s
2025-08-20 10:33:06 - INFO - 🔧 创建模型...
2025-08-20 10:33:10 - INFO - ✅ 模型创建完成
2025-08-20 10:33:10 - INFO - 🧠 开始模型预测...
