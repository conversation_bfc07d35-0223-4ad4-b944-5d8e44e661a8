#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试大图像分块处理功能
"""

import os
import sys
import numpy as np
import rasterio
from rasterio.transform import from_bounds

def create_test_large_image(output_path: str, width: int = 10000, height: int = 10000):
    """
    创建一个测试用的大图像
    
    Args:
        output_path: 输出路径
        width: 图像宽度
        height: 图像高度
    """
    print(f"创建测试大图像: {width}x{height}")
    
    # 创建随机数据
    data = np.random.randint(0, 255, (3, height, width), dtype=np.uint8)
    
    # 设置地理变换
    transform = from_bounds(0, 0, width, height, width, height)
    
    # 保存图像
    with rasterio.open(
        output_path, 'w',
        driver='GTiff',
        height=height,
        width=width,
        count=3,
        dtype=data.dtype,
        crs='EPSG:4326',
        transform=transform,
        nodata=-9999
    ) as dst:
        dst.write(data)
    
    print(f"测试图像已创建: {output_path}")
    return output_path

def test_memory_estimation():
    """测试内存估算功能"""
    print("=== 测试内存估算功能 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        
        processor = LargeImageProcessor()
        
        # 测试不同大小的图像
        test_sizes = [
            (1000, 1000),
            (5000, 5000),
            (10000, 10000),
            (20000, 20000),
            (56283, 67180)  # 实际出错的图像大小
        ]
        
        for width, height in test_sizes:
            print(f"\n图像大小: {width}x{height}")
            
            # 检查是否需要分块
            needs_tiling = processor.should_use_tiling(height, width, 3)
            
            if needs_tiling:
                # 计算网格大小
                grid_rows, grid_cols = processor.calculate_grid_size(height, width, 3)
                print(f"  需要分块: {grid_rows}x{grid_cols}")
                
                # 计算块信息
                blocks = processor.calculate_block_bounds(height, width, grid_rows, grid_cols)
                print(f"  总块数: {len(blocks)}")
                
                # 显示第一个块的信息
                if blocks:
                    first_block = blocks[0]
                    print(f"  第一个块大小: {first_block['ext_height']}x{first_block['ext_width']}")
            else:
                print("  可以直接处理")
        
        return True
        
    except Exception as e:
        print(f"内存估算测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_block_extraction():
    """测试块提取功能"""
    print("\n=== 测试块提取功能 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        
        # 创建测试图像
        test_image_path = "test_large_image.tif"
        create_test_large_image(test_image_path, 2000, 2000)
        
        processor = LargeImageProcessor(overlap_pixels=64)
        
        # 计算分块
        grid_rows, grid_cols = processor.calculate_grid_size(2000, 2000, 3)
        blocks = processor.calculate_block_bounds(2000, 2000, grid_rows, grid_cols)
        
        print(f"分块网格: {grid_rows}x{grid_cols}, 总块数: {len(blocks)}")
        
        # 提取第一个块
        if blocks:
            first_block = blocks[0]
            output_dir = "test_blocks"
            os.makedirs(output_dir, exist_ok=True)
            
            block_path = processor.extract_block_from_image(
                test_image_path, first_block, output_dir
            )
            
            if os.path.exists(block_path):
                # 检查提取的块
                with rasterio.open(block_path) as src:
                    print(f"提取的块大小: {src.width}x{src.height}")
                    print(f"块文件: {block_path}")
                
                # 清理
                os.remove(block_path)
                os.rmdir(output_dir)
                print("✅ 块提取测试成功")
            else:
                print("❌ 块提取失败")
                return False
        
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)
        
        return True
        
    except Exception as e:
        print(f"块提取测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_weight_map_creation():
    """测试权重图创建"""
    print("\n=== 测试权重图创建 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        
        processor = LargeImageProcessor()
        
        # 创建权重图
        weight_map = processor._create_weight_map(100, 100)
        
        print(f"权重图大小: {weight_map.shape}")
        print(f"权重范围: {weight_map.min():.3f} - {weight_map.max():.3f}")
        print(f"中心权重: {weight_map[50, 50]:.3f}")
        print(f"边角权重: {weight_map[0, 0]:.3f}")
        
        # 验证权重图属性
        assert weight_map.shape == (100, 100), "权重图大小不正确"
        assert 0 <= weight_map.min() <= weight_map.max() <= 1, "权重值超出范围"
        assert weight_map[50, 50] > weight_map[0, 0], "中心权重应该大于边角权重"
        
        print("✅ 权重图创建测试成功")
        return True
        
    except Exception as e:
        print(f"权重图测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("开始测试大图像分块处理功能...")
    
    # 检查依赖
    try:
        import psutil
        print(f"✅ psutil已安装，版本: {psutil.__version__}")
    except ImportError:
        print("❌ 需要安装psutil: pip install psutil")
        return
    
    try:
        import rasterio
        print(f"✅ rasterio已安装，版本: {rasterio.__version__}")
    except ImportError:
        print("❌ 需要安装rasterio: pip install rasterio")
        return
    
    # 运行测试
    tests = [
        ("内存估算", test_memory_estimation),
        ("块提取", test_block_extraction),
        ("权重图创建", test_weight_map_creation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"运行测试: {test_name}")
        print(f"{'='*50}")
        
        success = test_func()
        results.append((test_name, success))
    
    # 显示结果
    print(f"\n{'='*50}")
    print("测试结果汇总:")
    print(f"{'='*50}")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！大图像分块处理功能可以使用。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")

if __name__ == '__main__':
    main()
