2025-08-25 18:10:41 - INFO - === 合并分析任务开始 ===
2025-08-25 18:10:41 - INFO - 任务ID: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:41 - INFO - 🔧 立即创建TaskInfo.json...
2025-08-25 18:10:41 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-25 18:10:41 - INFO - 📁 TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:41 - INFO - 📄 发现现有TaskInfo文件，正在读取...
2025-08-25 18:10:41 - INFO - 📋 读取到数组格式，包含 14 个历史任务
2025-08-25 18:10:41 - INFO - 🔄 更新现有任务记录: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:41 - INFO - 💾 准备保存TaskInfo.json到: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:41 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-25 18:10:41 - INFO - 📊 文件验证: 大小=31772字节
2025-08-25 18:10:41 - INFO - ✅ TaskInfo.json模板创建完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:41 - INFO - 📊 当前任务总数: 14
2025-08-25 18:10:41 - INFO - ✅ TaskInfo.json创建完成
2025-08-25 18:10:42 - INFO - 影像ID: 20250705171599
2025-08-25 18:10:42 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:42 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:10:42 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 18:10:42 - INFO - 分析类别: constructionLand
2025-08-25 18:10:42 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756116641.shp
2025-08-25 18:10:42 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_2_1756116641.shp
2025-08-25 18:10:42 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171599
2025-08-25 18:10:42 - INFO - === 开始执行合并分析任务 ===
2025-08-25 18:10:42 - WARNING - ⚠️ TaskInfo路径丢失，尝试重新构建...
2025-08-25 18:10:42 - INFO - 📍 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:42 - INFO - 📝 更新字段: status = 进行中
2025-08-25 18:10:42 - INFO - 📝 更新字段: message = 开始AI语义分割...
2025-08-25 18:10:42 - INFO - 📝 更新字段: progress = 5
2025-08-25 18:10:42 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress']
2025-08-25 18:10:42 - INFO - 🔍 验证更新结果:
2025-08-25 18:10:42 - INFO -   状态: 进行中
2025-08-25 18:10:42 - INFO -   进度: 5%
2025-08-25 18:10:42 - INFO -   AI处理时间: None
2025-08-25 18:10:42 - INFO -   成功状态: None
2025-08-25 18:10:42 - INFO -   空间统计:
2025-08-25 18:10:42 - INFO -     area_threshold: 200.0
2025-08-25 18:10:42 - INFO - 🤖 开始AI语义分割...
2025-08-25 18:10:42 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:42 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:10:42 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756116641.shp
2025-08-25 18:10:42 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-25 18:10:42 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-25 18:10:42 - INFO - 🔍 检测到合并分析任务: image_id=20250705171599, category=constructionLand
2025-08-25 18:10:42 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171599/TaskInfo.json
2025-08-25 18:10:42 - INFO - ✅ 使用真实任务ID: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:42 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-25 18:10:42 - INFO - 📄 读取现有TaskInfo文件...
2025-08-25 18:10:42 - INFO - 📋 读取到数组格式，包含 14 个历史任务
2025-08-25 18:10:42 - INFO - 🔄 更新现有任务记录: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:42 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-25 18:10:42 - INFO - 📊 文件验证: 大小=32299字节，任务总数: 14
2025-08-25 18:10:42 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-25 18:10:42 - INFO - 🔧 创建处理参数:
2025-08-25 18:10:42 - INFO -   模型类型: deeplabv3_plus
2025-08-25 18:10:42 - INFO -   类别数量: 2
2025-08-25 18:10:42 - INFO -   目标类别: [1]
2025-08-25 18:10:42 - INFO -   窗口大小: 512
2025-08-25 18:10:42 - INFO -   批处理大小: 16
2025-08-25 18:10:42 - INFO -   重叠比例: 0.5
2025-08-25 18:10:42 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-25 18:10:42 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:42 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:10:42 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756116641.shp
2025-08-25 18:10:42 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-25 18:10:42 - INFO - 🔄 开始AI模型推理...
2025-08-25 18:10:42 - INFO - 
==================================================
2025-08-25 18:10:42 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:42 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756116641.shp
2025-08-25 18:10:42 - INFO - ==================================================
2025-08-25 18:10:42 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:42 - INFO - 📏 图像大小: 5683x5165, 波段数: 3
2025-08-25 18:10:42 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-25 18:10:42 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-25 18:10:42 - INFO - 🔢 类别数量: 2
2025-08-25 18:10:42 - INFO - 🔄 重叠区域: 37 像素
2025-08-25 18:10:42 - INFO - 📦 批处理大小: 16
2025-08-25 18:10:42 - INFO - 💻 计算设备: cuda:0
2025-08-25 18:10:46 - INFO - 读取tif并裁剪预处理完毕,目前耗时间: 4s
2025-08-25 18:10:46 - INFO - 🔧 创建模型...
2025-08-25 18:10:50 - INFO - ✅ 模型创建完成
2025-08-25 18:10:50 - INFO - 🧠 开始模型预测...
