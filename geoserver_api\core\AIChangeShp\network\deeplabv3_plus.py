import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
import os
import numpy as np


class ASPPModule(nn.Module):
    """Atrous Spatial Pyramid Pooling Module
    
    ASPP模块使用不同扩张率的卷积和全局平均池化来捕获多尺度上下文信息
    
    参数:
        in_channels: 输入通道数
        filters: 输出通道数
    """
    def __init__(self, in_channels, filters):
        super(ASPPModule, self).__init__()
        
        # 全局平均池化分支
        self.global_avg_pool = nn.Sequential(
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Conv2d(in_channels, filters, 1, bias=False),
            nn.BatchNorm2d(filters),
            nn.ReLU(inplace=True)
        )
        
        # 空洞卷积分支
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels, filters, 1, dilation=1, padding=0, bias=False),
            nn.BatchNorm2d(filters),
            nn.ReLU(inplace=True)
        )
        
        self.conv6 = nn.Sequential(
            nn.Conv2d(in_channels, filters, 3, dilation=6, padding=6, bias=False),
            nn.BatchNorm2d(filters),
            nn.ReLU(inplace=True)
        )
        
        self.conv12 = nn.Sequential(
            nn.Conv2d(in_channels, filters, 3, dilation=12, padding=12, bias=False),
            nn.BatchNorm2d(filters),
            nn.ReLU(inplace=True)
        )
        
        self.conv18 = nn.Sequential(
            nn.Conv2d(in_channels, filters, 3, dilation=18, padding=18, bias=False),
            nn.BatchNorm2d(filters),
            nn.ReLU(inplace=True)
        )
        
        # 合并所有分支的输出
        self.project = nn.Sequential(
            nn.Conv2d(filters * 5, filters, 1, bias=False),
            nn.BatchNorm2d(filters),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        size = x.size()[2:]
        
        # 全局平均池化分支
        global_features = self.global_avg_pool(x)
        global_features = F.interpolate(global_features, size=size, mode='bilinear', align_corners=True)
        
        # 空洞卷积分支
        conv1_features = self.conv1(x)
        conv6_features = self.conv6(x)
        conv12_features = self.conv12(x)
        conv18_features = self.conv18(x)
        
        # 合并所有分支
        out = torch.cat([global_features, conv1_features, conv6_features, conv12_features, conv18_features], dim=1)
        out = self.project(out)
        
        return out


class SeparableConv2d(nn.Module):
    """深度可分离卷积
    
    先进行深度卷积(depthwise)，再进行逐点卷积(pointwise)
    用于Xception骨干网络
    
    参数:
        in_channels: 输入通道数
        out_channels: 输出通道数
        kernel_size: 卷积核大小
        stride: 步长
        padding: 填充
        dilation: 扩张率
    """
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1, dilation=1, bias=False):
        super(SeparableConv2d, self).__init__()
        
        # 深度卷积
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size, stride, padding, dilation,
            groups=in_channels, bias=bias
        )
        
        # 逐点卷积
        self.pointwise = nn.Conv2d(
            in_channels, out_channels, kernel_size=1, stride=1, padding=0, bias=bias
        )
        
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        return x


class XceptionBlock(nn.Module):
    """Xception 块
    
    包含两个/三个SeparableConv2d层和可选的skip连接
    
    参数:
        in_channels: 输入通道数
        out_channels: 输出通道数
        stride: 步长
        use_skip: 是否使用skip连接
        reps: 重复SeparableConv2d的次数
    """
    def __init__(self, in_channels, out_channels, stride=1, use_skip=True, reps=3):
        super(XceptionBlock, self).__init__()
        
        # 主分支
        layers = []
        for i in range(reps - 1):
            channels = in_channels if i == 0 else out_channels
            layers.extend([
                SeparableConv2d(channels, out_channels, stride=1),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            ])
        
        # 最后一个SeparableConv2d使用给定的stride
        layers.extend([
            SeparableConv2d(out_channels if reps > 1 else in_channels, out_channels, stride=stride),
            nn.BatchNorm2d(out_channels)
        ])
        
        self.block = nn.Sequential(*layers)
        
        # Skip连接
        self.use_skip = use_skip
        if use_skip and (in_channels != out_channels or stride != 1):
            self.skip = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, bias=False),
                nn.BatchNorm2d(out_channels)
            )
        
    def forward(self, x):
        out = self.block(x)
        
        if self.use_skip:
            if hasattr(self, 'skip'):
                x = self.skip(x)
            out = out + x
            
        return F.relu(out, inplace=True)


class XceptionBackbone(nn.Module):
    """Xception 骨干网络
    
    修改版的Xception，适用于DeepLabV3+
    
    参数:
        in_channels: 输入通道数
    """
    def __init__(self, in_channels=3):
        super(XceptionBackbone, self).__init__()
        
        # 入口流
        self.entry_flow_conv1 = nn.Sequential(
            nn.Conv2d(in_channels, 32, kernel_size=3, stride=2, padding=1, bias=False),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True)
        )
        
        self.entry_flow_conv2 = nn.Sequential(
            nn.Conv2d(32, 64, kernel_size=3, stride=1, padding=1, bias=False),
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True)
        )
        
        self.entry_flow_block1 = XceptionBlock(64, 128, stride=2)
        self.entry_flow_block2 = XceptionBlock(128, 256, stride=2)
        self.entry_flow_block3 = XceptionBlock(256, 728, stride=2)
        
        # 中间流
        self.middle_flow = nn.Sequential(*[
            XceptionBlock(728, 728, stride=1) for _ in range(16)
        ])
        
        # 出口流
        self.exit_flow_block1 = XceptionBlock(728, 1024, stride=2)
        
        self.exit_flow_block2 = nn.Sequential(
            SeparableConv2d(1024, 1536, stride=1),
            nn.BatchNorm2d(1536),
            nn.ReLU(inplace=True),
            SeparableConv2d(1536, 2048, stride=1),
            nn.BatchNorm2d(2048),
            nn.ReLU(inplace=True)
        )
        
    def forward(self, x):
        # 保存低级特征 (用于跳跃连接)
        
        # 入口流
        x = self.entry_flow_conv1(x)
        x = self.entry_flow_conv2(x)
        
        low_level_features = x  # 64通道的低级特征
        
        x = self.entry_flow_block1(x)
        x = self.entry_flow_block2(x)
        x = self.entry_flow_block3(x)
        
        # 中间流
        x = self.middle_flow(x)
        
        # 出口流
        x = self.exit_flow_block1(x)
        x = self.exit_flow_block2(x)
        
        return x, low_level_features


class DeepLabV3Plus(nn.Module):
    """DeepLabV3+语义分割模型
    
    支持ResNet50/101和Xception作为骨干网络
    
    参数:
        backbone_type: 骨干网络类型 ('resnet50', 'resnet101', 'xception')
        num_classes: 分割类别数
        pretrained_backbone: 是否使用预训练骨干网络
    """
    def __init__(self, backbone_type='resnet101', num_classes=2, pretrained_backbone=True):
        super(DeepLabV3Plus, self).__init__()
        
        # 保存类别数
        self.num_classes = num_classes
        # 记录使用的骨干网络类型
        self.backbone_type = backbone_type
        
        # 根据指定的backbone类型选择骨干网络
        if backbone_type == 'resnet50' or backbone_type == 'resnet101':
            if backbone_type == 'resnet50':
                if pretrained_backbone:
                    backbone = models.resnet50(weights=models.ResNet50_Weights.IMAGENET1K_V1)
                else:
                    backbone = models.resnet50(weights=None)
            else:
                if pretrained_backbone:
                    try:
                        # 尝试使用预训练权重
                        backbone = models.resnet101(weights=models.ResNet101_Weights.IMAGENET1K_V1)
                        print("✅ 成功加载ResNet101预训练权重")
                    except Exception as e:
                        print(f"⚠️ 无法下载预训练权重，使用随机初始化: {e}")
                        print("💡 建议: 在有网络的环境中预先下载权重文件")
                        backbone = models.resnet101(weights=None)
                else:
                    backbone = models.resnet101(weights=None)
            
            # 提取特征层
            self.initial = nn.Sequential(*list(backbone.children())[:4])  # 第一层到layer1
            self.layer1 = backbone.layer1  # 低级特征
            self.layer2 = backbone.layer2
            self.layer3 = backbone.layer3
            self.layer4 = backbone.layer4  # 高级特征
            
            # ResNet骨干网络的特征提取
            self.backbone_forward = self._resnet_forward
            
            # ASPP模块的输入通道数
            aspp_in_channels = 2048
            # 低级特征的通道数
            low_level_channels = 256
            
        elif backbone_type == 'xception':
            # Xception骨干网络
            self.backbone = XceptionBackbone(in_channels=3)
            
            # Xception骨干网络的特征提取
            self.backbone_forward = self._xception_forward
            
            # ASPP模块的输入通道数
            aspp_in_channels = 2048
            # 低级特征的通道数
            low_level_channels = 64
            
        else:
            raise ValueError(f"不支持的骨干网络类型: {backbone_type}，当前支持的类型有: 'resnet50', 'resnet101', 'xception'")
            
        # ASPP模块
        self.aspp = ASPPModule(aspp_in_channels, 256)
        
        # 低级特征处理
        self.low_level_conv = nn.Sequential(
            nn.Conv2d(low_level_channels, 48, 1, bias=False),
            nn.BatchNorm2d(48),
            nn.ReLU(inplace=True)
        )
        
        # 解码器
        self.decoder = nn.Sequential(
            nn.Conv2d(304, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1, bias=False),
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, num_classes, 1)
        )
        
    def _resnet_forward(self, x):
        """ResNet骨干网络的前向传播"""
        # 骨干网络特征提取
        x = self.initial(x)
        low_level_features = self.layer1(x)  # 低级特征
        x = self.layer2(low_level_features)
        x = self.layer3(x)
        x = self.layer4(x)  # 高级特征
        
        return x, low_level_features
    
    def _xception_forward(self, x):
        """Xception骨干网络的前向传播"""
        return self.backbone(x)
        
    def forward(self, x):
        input_size = x.size()[2:]
        
        # 骨干网络特征提取
        x, low_level_features = self.backbone_forward(x)
        
        # ASPP模块
        x = self.aspp(x)
        
        # 上采样ASPP输出
        x = F.interpolate(x, size=low_level_features.size()[2:], mode='bilinear', align_corners=True)
        
        # 处理低级特征
        low_level_features = self.low_level_conv(low_level_features)
        
        # 特征融合
        x = torch.cat([x, low_level_features], dim=1)
        
        # 解码器
        x = self.decoder(x)
        
        # 上采样到原始输入大小
        x = F.interpolate(x, size=input_size, mode='bilinear', align_corners=True)
        
        return x


def deeplabv3_plus(pretrained_weights=None, backbone_type='resnet101', num_classes=2, pretrained_backbone=True, specific_weight_file=None):
    """
    创建DeepLabV3+模型
    
    参数:
        pretrained_weights: 预训练权重路径，如果是None，则不加载外部权重
        backbone_type: 骨干网络类型，可选 'resnet50', 'resnet101', 'xception'
        num_classes: 类别数量
        pretrained_backbone: 是否使用预训练骨干网络 (仅在未提供pretrained_weights时生效)
        specific_weight_file: 指定的权重文件名，如果提供，将优先使用此文件
    
    返回:
        model: DeepLabV3+模型
    """
    # 检查骨干网络类型是否有效
    valid_backbones = ['resnet50', 'resnet101', 'xception']
    if backbone_type not in valid_backbones:
        raise ValueError(f"不支持的骨干网络类型: {backbone_type}，当前支持的类型有: {valid_backbones}")
    
    # 创建模型
    model = DeepLabV3Plus(
        backbone_type=backbone_type, 
        num_classes=num_classes,
        pretrained_backbone=pretrained_backbone if pretrained_weights is None else False
    )
    
    print(f"创建DeepLabV3+模型，使用{backbone_type}作为骨干网络")
    
    # 加载预训练权重
    if pretrained_weights:
        try:
            print(f"尝试加载预训练权重: {pretrained_weights}")
            
            # 如果指定了特定权重文件，优先使用
            if specific_weight_file:
                print(f"使用指定的权重文件: {specific_weight_file}")
                # 确定权重文件的完整路径
                if os.path.isdir(pretrained_weights):
                    weight_path = os.path.join(pretrained_weights, specific_weight_file)
                else:
                    weight_path = specific_weight_file
                
                # 检查指定的权重文件是否存在
                if not os.path.isfile(weight_path):
                    print(f"指定的权重文件不存在: {weight_path}")
                    return model
                
                # 使用指定的权重文件
                pretrained_weights = weight_path
            
            # 没有指定特定权重文件，但提供了目录路径
            elif os.path.isdir(pretrained_weights):
                # 如果是目录，根据backbone_type选择对应的权重文件
                weight_filename = f"{backbone_type}_pretrained.pth"
                pretrained_weights = os.path.join(pretrained_weights, weight_filename)
                print(f"预训练权重目录模式，选择权重文件: {pretrained_weights}")
            
            # 检查文件是否存在
            if not os.path.isfile(pretrained_weights):
                print(f"预训练权重文件不存在: {pretrained_weights}")
                return model
                
            # 加载权重
            state_dict = torch.load(pretrained_weights, map_location='cpu')
            model.load_state_dict(state_dict, strict=False)
            print(f"成功加载预训练权重: {pretrained_weights}")
                
        except Exception as e:
            print(f"加载预训练权重失败: {e}")
            import traceback
            traceback.print_exc()
    
    return model


def download_pretrained_weights(backbone_type='resnet101', save_dir='D:/torch_code/pretrained/deeplabv3_plus'):
    """
    下载并保存预训练的骨干网络权重，以便离线使用
    
    参数:
        backbone_type: 骨干网络类型，可选 'resnet50', 'resnet101'
        save_dir: 保存权重的目录
    
    返回:
        保存的权重文件路径
    """
    os.makedirs(save_dir, exist_ok=True)
    
    if backbone_type == 'resnet50':
        # 下载ResNet50预训练权重
        model = models.resnet50(weights=models.ResNet50_Weights.IMAGENET1K_V1)
        save_path = os.path.join(save_dir, 'resnet50_pretrained.pth')
        
    elif backbone_type == 'resnet101':
        # 下载ResNet101预训练权重
        model = models.resnet101(weights=models.ResNet101_Weights.IMAGENET1K_V1)
        save_path = os.path.join(save_dir, 'resnet101_pretrained.pth')
        
    elif backbone_type == 'xception':
        print("Xception预训练权重需要单独下载，请参考Xception官方实现")
        return None
        
    else:
        raise ValueError(f"不支持的骨干网络类型: {backbone_type}")
    
    # 保存权重
    torch.save(model.state_dict(), save_path)
    print(f"已保存{backbone_type}预训练权重到: {save_path}")
    
    return save_path


if __name__ == "__main__":
    # 测试DeepLabV3+模型
    # 选择要测试的骨干网络类型
    for backbone in ['resnet50', 'resnet101', 'xception']:
        print(f"\n测试 {backbone} 骨干网络")
        
        # 创建模型
        model = deeplabv3_plus(backbone_type=backbone, num_classes=2)
        
        # 测试前向传播
        x = torch.randn(2, 3, 256, 256)
        output = model(x)
        
        print(f"输入形状: {x.shape}")
        print(f"输出形状: {output.shape}")
        
        # 计算参数量
        total_params = sum(p.numel() for p in model.parameters())
        print(f"模型参数总数: {total_params:,}")
        
    # 下载预训练权重用于离线使用
    print("\n下载预训练权重到指定目录")
    download_pretrained_weights('resnet50', 'D:/torch_code/pretrained/deeplabv3_plus')
    download_pretrained_weights('resnet101', 'D:/torch_code/pretrained/deeplabv3_plus') 