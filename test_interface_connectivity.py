#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试接口连通性
验证排队接口是否正确配置和可达
"""

import requests
import json
import urllib.parse

def test_interface_connectivity():
    """测试接口连通性"""
    print("🔍 测试接口连通性...")
    
    base_url = "http://192.168.43.148:8091"
    
    # 测试参数
    test_params = {
        'id': '20250705171599',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
        'area_threshold': 200
    }
    
    # 1. 测试排队接口
    print(f"\n📤 测试排队接口...")
    test_queued_interface(base_url, test_params)
    
    # 2. 测试队列状态接口
    print(f"\n📊 测试队列状态接口...")
    test_queue_status_interface(base_url)
    
    # 3. 测试原始接口（对比）
    print(f"\n🔄 测试原始接口（对比）...")
    test_original_interface(base_url, test_params)

def test_queued_interface(base_url, params):
    """测试排队接口"""
    try:
        url = f"{base_url}/api/analysis/combined-ai-spatial-analysis-queued/"
        
        print(f"   请求URL: {url}")
        print(f"   参数: {params}")
        
        # 构建完整URL用于调试
        query_string = urllib.parse.urlencode(params, quote_via=urllib.parse.quote)
        full_url = f"{url}?{query_string}"
        print(f"   完整URL: {full_url}")
        
        response = requests.get(url, params=params, timeout=30)
        
        print(f"   响应状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ 排队接口响应成功")
                print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get('status') == 'success':
                    task_id = result['data']['task_id']
                    print(f"   🎯 任务ID: {task_id}")
                    return task_id
                else:
                    print(f"   ❌ 任务提交失败: {result.get('message', '未知错误')}")
                    return None
                    
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
                return None
                
        elif response.status_code == 404:
            print(f"   ❌ 接口不存在 (404)")
            print(f"   可能的问题:")
            print(f"   1. URL路由配置错误")
            print(f"   2. 视图函数未正确导入")
            print(f"   3. 服务器未重启")
            return None
            
        elif response.status_code == 500:
            print(f"   ❌ 服务器内部错误 (500)")
            print(f"   响应内容: {response.text}")
            return None
            
        else:
            print(f"   ❌ 未知错误: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        print(f"   ❌ 连接错误: 无法连接到服务器")
        return None
    except requests.exceptions.Timeout:
        print(f"   ❌ 请求超时")
        return None
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None

def test_queue_status_interface(base_url):
    """测试队列状态接口"""
    try:
        url = f"{base_url}/api/analysis/queue-status/"
        
        print(f"   请求URL: {url}")
        
        response = requests.get(url, timeout=10)
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ 队列状态接口响应成功")
                print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
                
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
                return None
                
        elif response.status_code == 404:
            print(f"   ❌ 队列状态接口不存在 (404)")
            return None
            
        else:
            print(f"   ❌ 队列状态接口错误: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 队列状态接口异常: {e}")
        return None

def test_original_interface(base_url, params):
    """测试原始接口（对比）"""
    try:
        url = f"{base_url}/api/analysis/combined-ai-spatial-analysis/"
        
        print(f"   请求URL: {url}")
        
        response = requests.get(url, params=params, timeout=30)
        
        print(f"   响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   ✅ 原始接口响应成功")
                print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
                
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
                return None
                
        elif response.status_code == 404:
            print(f"   ❌ 原始接口不存在 (404)")
            return None
            
        else:
            print(f"   ❌ 原始接口错误: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 原始接口异常: {e}")
        return None

def test_double_call():
    """测试连续两次调用排队接口"""
    print(f"\n🔄 测试连续两次调用排队接口...")
    
    base_url = "http://192.168.43.148:8091"
    
    # 第一个任务参数
    params_1 = {
        'id': '20250705171598',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
        'area_threshold': 200
    }
    
    # 第二个任务参数
    params_2 = {
        'id': '20250705171597',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171597/20250705171597_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
        'area_threshold': 200
    }
    
    print(f"📤 提交第一个任务...")
    task_id_1 = test_queued_interface(base_url, params_1)
    
    print(f"\n📤 立即提交第二个任务...")
    task_id_2 = test_queued_interface(base_url, params_2)
    
    if task_id_1 and task_id_2:
        print(f"\n📊 检查队列状态...")
        queue_status = test_queue_status_interface(base_url)
        
        if queue_status and queue_status.get('status') == 'success':
            data = queue_status['data']
            executing_task = data.get('executing_task')
            queue_size = data.get('queue_size', 0)
            waiting_tasks = data.get('waiting_tasks', 0)
            
            print(f"\n🔍 排队验证:")
            print(f"   执行任务: {executing_task or '无'}")
            print(f"   队列大小: {queue_size}")
            print(f"   等待任务: {waiting_tasks}")
            
            if executing_task and (queue_size > 0 or waiting_tasks > 0):
                print(f"   ✅ 排队机制正常工作！")
            elif executing_task and queue_size == 0 and waiting_tasks == 0:
                print(f"   ⚠️ 可能的问题：第二个任务没有正确排队")
            else:
                print(f"   ❌ 排队机制异常")
        else:
            print(f"   ❌ 无法获取队列状态")
    else:
        print(f"   ❌ 任务提交失败")

def main():
    """主函数"""
    print("🚀 开始测试接口连通性")
    
    print(f"\n📝 测试目标:")
    print(f"1. 验证排队接口是否可达")
    print(f"2. 检查接口响应格式")
    print(f"3. 对比原始接口和排队接口")
    print(f"4. 测试连续调用的排队效果")
    
    # 基础连通性测试
    test_interface_connectivity()
    
    # 连续调用测试
    user_input = input(f"\n❓ 是否进行连续调用测试？(y/n): ")
    if user_input.lower() == 'y':
        test_double_call()
    
    print(f"\n📖 调试建议:")
    print(f"1. 如果接口返回404，检查URL路由配置")
    print(f"2. 如果接口返回500，检查视图函数实现")
    print(f"3. 如果没有排队日志，检查代码是否正确加载")
    print(f"4. 确保服务器已重启以加载新代码")

if __name__ == "__main__":
    main()
