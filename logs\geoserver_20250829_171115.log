2025-08-29 17:11:15,341 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_171115.log
2025-08-29 17:11:15,345 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:11:15,346 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:11:15,366 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:11:15,370 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:11:15,373 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:11:15,393 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:11:15,395 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:11:15,396 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:11:15,410 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:11:15,412 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:11:15,413 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:11:15,427 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:11:15,429 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:11:15,429 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:11:15,444 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:11:15,445 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:11:15,445 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:11:15,459 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:11:15,475 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 17:11:15,481 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 17:11:19,455 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 17:11:19,467 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 17:11:19,470 - analysis_executor - INFO - 加载了 28 个任务状态
