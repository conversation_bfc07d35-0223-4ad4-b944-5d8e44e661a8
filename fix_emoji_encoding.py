#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复emoji编码问题的脚本
将代码中的emoji字符替换为普通文本标记
"""

import os
import re

# emoji替换映射
EMOJI_REPLACEMENTS = {
    '📥': '[RECEIVE]',
    '🔄': '[PROCESS]',
    '📋': '[QUEUE]',
    '🚀': '[START]',
    '📝': '[UPDATE]',
    '➕': '[ADD]',
    '✅': '[SUCCESS]',
    '❌': '[ERROR]',
    '🏁': '[FINISH]',
    '🎯': '[TARGET]',
    '🔧': '[CONFIG]',
    '📁': '[FOLDER]',
    '📂': '[DIR]',
    '📄': '[FILE]',
    '💾': '[SAVE]',
    '📊': '[STATS]',
    '🔍': '[SEARCH]',
    '🌐': '[PUBLISH]',
    '📤': '[UPLOAD]',
    '🎉': '[COMPLETE]',
    '⚠️': '[WARNING]',
    '🗺️': '[MAP]',
    '🤖': '[AI]',
    '📏': '[MEASURE]',
    '📍': '[LOCATION]',
}

def fix_emoji_in_file(file_path):
    """修复单个文件中的emoji字符"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换emoji字符
        for emoji, replacement in EMOJI_REPLACEMENTS.items():
            content = content.replace(emoji, replacement)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已修复: {file_path}")
            return True
        else:
            print(f"- 无需修复: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 修复失败: {file_path} - {str(e)}")
        return False

def main():
    """主函数"""
    print("开始修复emoji编码问题...")
    
    # 需要修复的文件列表
    files_to_fix = [
        'geoserver_api/core/task_queue_manager.py',
        'geoserver_api/core/combined_analysis_executor.py',
        'geoserver_api/views/analysis/analysis_views.py',
    ]
    
    fixed_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            if fix_emoji_in_file(file_path):
                fixed_count += 1
        else:
            print(f"✗ 文件不存在: {file_path}")
    
    print(f"\n修复完成！共修复了 {fixed_count} 个文件。")

if __name__ == '__main__':
    main()
