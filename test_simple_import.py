#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的导入测试 - 只测试导入是否成功
"""

import os
import sys
import django

def setup_django():
    """设置Django环境"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_django.settings')
    django.setup()

def test_basic_import():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    
    try:
        from geoserver_api.core.AIChangeShp.large_image_processor import LargeImageProcessor
        print("✅ LargeImageProcessor导入成功")
        
        # 测试创建实例
        processor = LargeImageProcessor()
        print("✅ LargeImageProcessor实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def test_django_dynamic_import():
    """测试Django中的动态导入"""
    print("\n=== 测试Django动态导入 ===")
    
    try:
        import importlib.util
        
        # 模拟analysis_executor.py中的动态导入方式
        module_path = "geoserver_api/core/AIChangeShp/pre_pytorch_new.py"
        
        if not os.path.exists(module_path):
            print(f"❌ 模块文件不存在: {module_path}")
            return False
        
        spec = importlib.util.spec_from_file_location("pre_pytorch_new", module_path)
        
        if spec is None:
            print(f"❌ 无法创建模块规范")
            return False
        
        pre_pytorch_new = importlib.util.module_from_spec(spec)
        
        print("正在执行模块导入（这可能需要一些时间）...")
        
        # 这里会触发所有导入，包括LargeImageProcessor
        spec.loader.exec_module(pre_pytorch_new)
        
        print("✅ 动态导入成功")
        
        # 检查关键函数是否存在
        if hasattr(pre_pytorch_new, 'process_large_image_with_tiling'):
            print("✅ process_large_image_with_tiling函数存在")
        else:
            print("❌ process_large_image_with_tiling函数不存在")
        
        if hasattr(pre_pytorch_new, 'LargeImageProcessor'):
            print("✅ LargeImageProcessor在模块中可用")
        else:
            print("⚠️ LargeImageProcessor在模块中不可用（这是正常的）")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态导入失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

def main():
    """主函数"""
    print("简单导入测试")
    print("="*40)
    
    # 设置Django环境
    try:
        setup_django()
        print("✅ Django环境设置成功")
    except Exception as e:
        print(f"❌ Django环境设置失败: {e}")
        return
    
    # 运行测试
    tests = [
        ("基本导入", test_basic_import),
        ("Django动态导入", test_django_dynamic_import),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*30}")
        print(f"运行测试: {test_name}")
        print(f"{'='*30}")
        
        success = test_func()
        results.append((test_name, success))
    
    # 显示结果
    print(f"\n{'='*40}")
    print("测试结果汇总:")
    print(f"{'='*40}")
    
    for test_name, success in results:
        status = "✅" if success else "❌"
        print(f"{status} {test_name}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 导入测试通过！")
        print("现在可以在API中使用大图像分块处理功能了。")
        print("\n注意事项:")
        print("- 如果缺少numpy/rasterio等依赖，某些功能会受限")
        print("- 建议安装完整依赖以获得最佳性能")
    else:
        print("⚠️ 部分测试失败。")
        print("但如果基本导入成功，大图像处理功能应该可以工作。")

if __name__ == '__main__':
    main()
