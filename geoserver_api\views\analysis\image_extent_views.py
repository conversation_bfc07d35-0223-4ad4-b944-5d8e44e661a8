#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
影像范围提取API视图

功能说明:
- 提取TIF影像的有效数据范围
- 输出WGS84坐标系的Shapefile
- 排除NoData区域，只保留有效数据区域

接口列表:
- GET /api/analysis/image-extent-extraction/ - 影像有效范围提取
"""

import os
import logging
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

# 导入核心模块
from ...core.analysis_executor import analysis_executor, analysis_logger

@api_view(['GET'])
def image_extent_extraction(request):
    """
    影像有效范围提取接口
    
    功能:
        提取TIF影像中的有效数据范围，排除NoData区域，输出Shapefile

    查询参数:
        image: 输入TIF影像路径 (必选)
        output: 输出Shapefile路径 (可选，默认自动生成)
        simplify_tolerance: 简化容差，单位为米 (可选，默认1.0)
        min_area: 最小面积阈值，单位为平方米 (可选，默认1000.0)
        keep_original_crs: 是否保持原始坐标系 (可选，默认true)
        
    返回:
        JSON格式的任务信息
    """
    try:
        # 获取请求参数
        image_path = request.GET.get('image')
        output_path = request.GET.get('output')
        simplify_tolerance = float(request.GET.get('simplify_tolerance', 1.0))
        min_area = float(request.GET.get('min_area', 1000.0))
        keep_original_crs = request.GET.get('keep_original_crs', 'true').lower() == 'true'
        
        # 验证必需参数
        if not image_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: image (TIF影像路径)'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证文件存在
        if not os.path.exists(image_path):
            return Response({
                'status': 'error',
                'message': f'输入影像文件不存在: {image_path}'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 验证文件格式
        if not image_path.lower().endswith(('.tif', '.tiff')):
            return Response({
                'status': 'error',
                'message': '输入文件必须是TIF格式'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 验证参数范围
        if simplify_tolerance < 0:
            return Response({
                'status': 'error',
                'message': '简化容差必须大于等于0'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if min_area < 0:
            return Response({
                'status': 'error',
                'message': '最小面积阈值必须大于等于0'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        analysis_logger.info(f"收到影像范围提取请求")
        analysis_logger.info(f"输入影像: {image_path}")
        analysis_logger.info(f"输出路径: {output_path or '自动生成'}")
        analysis_logger.info(f"简化容差: {simplify_tolerance}米")
        analysis_logger.info(f"最小面积: {min_area}平方米")
        analysis_logger.info(f"保持原始坐标系: {'是' if keep_original_crs else '否，转换为WGS84'}")
        
        # 执行影像范围提取任务
        result = analysis_executor.execute_image_extent_extraction(
            image_path=image_path,
            output_path=output_path,
            simplify_tolerance=simplify_tolerance,
            min_area=min_area,
            keep_original_crs=keep_original_crs
        )
        
        if result['success']:
            return Response({
                'status': 'success',
                'message': '影像范围提取任务已启动',
                'data': result
            })
        else:
            return Response({
                'status': 'error',
                'message': result['message']
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    except ValueError as e:
        analysis_logger.error(f"参数错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'参数错误: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        analysis_logger.error(f"影像范围提取任务启动失败: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'任务启动失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
