#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本 - 逐步测试各个组件
"""

import os
import sys
import traceback

def test_step(step_name, test_func):
    """测试步骤包装器"""
    print(f"\n{'='*50}")
    print(f"测试步骤: {step_name}")
    print(f"{'='*50}")
    
    try:
        result = test_func()
        print(f"✓ {step_name} - 成功")
        return True, result
    except Exception as e:
        print(f"✗ {step_name} - 失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
        return False, str(e)

def test_basic_imports():
    """测试基本导入"""
    import django
    print(f"Django版本: {django.get_version()}")
    
    import rest_framework
    print(f"DRF版本: {rest_framework.VERSION}")
    
    return "基本导入成功"

def test_django_setup():
    """测试Django设置"""
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'geoserver_django.settings')
    
    import django
    django.setup()
    
    from django.conf import settings
    print(f"DEBUG模式: {settings.DEBUG}")
    print(f"数据库: {settings.DATABASES['default']['ENGINE']}")
    
    return "Django设置成功"

def test_app_imports():
    """测试应用导入"""
    from geoserver_api import apps
    print(f"geoserver_api应用: {apps.GeoserverApiConfig.name}")
    
    return "应用导入成功"

def test_core_imports():
    """测试核心模块导入"""
    from geoserver_api.core.analysis_executor import analysis_executor
    print(f"analysis_executor: {type(analysis_executor)}")
    
    # 不导入task_queue_manager，因为它可能有问题
    print("跳过task_queue_manager导入测试")
    
    return "核心模块导入成功"

def test_views_imports():
    """测试视图导入"""
    from geoserver_api.views.analysis import analysis_views
    print(f"analysis_views模块: {analysis_views}")
    
    return "视图导入成功"

def test_database():
    """测试数据库连接"""
    from django.db import connection
    
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        print(f"数据库查询结果: {result}")
    
    return "数据库连接成功"

def main():
    """主函数"""
    print("开始逐步测试...")
    
    tests = [
        ("基本导入", test_basic_imports),
        ("Django设置", test_django_setup),
        ("应用导入", test_app_imports),
        ("核心模块导入", test_core_imports),
        ("视图导入", test_views_imports),
        ("数据库连接", test_database),
    ]
    
    results = []
    
    for step_name, test_func in tests:
        success, result = test_step(step_name, test_func)
        results.append((step_name, success, result))
        
        if not success:
            print(f"\n❌ 测试在 '{step_name}' 步骤失败，停止后续测试")
            break
    
    print(f"\n{'='*60}")
    print("测试结果汇总:")
    print(f"{'='*60}")
    
    for step_name, success, result in results:
        status = "✓" if success else "✗"
        print(f"{status} {step_name}: {result}")
    
    print(f"\n总计: {sum(1 for _, success, _ in results if success)}/{len(results)} 个测试通过")

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(f"测试脚本执行失败: {e}")
        print(f"错误详情: {traceback.format_exc()}")
