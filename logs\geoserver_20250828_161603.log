2025-08-28 16:16:03,981 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_161603.log
2025-08-28 16:16:04,009 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:16:04,010 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:16:04,029 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:16:04,050 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:16:04,051 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:16:04,066 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:16:04,069 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:16:04,069 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:16:04,082 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:16:04,084 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:16:04,084 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:16:04,097 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:16:04,099 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:16:04,099 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:16:04,113 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:16:04,115 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:16:04,116 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:16:04,129 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:16:04,269 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 16:16:04,363 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 16:16:04,757 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 16:16:04,920 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 16:16:05,005 - analysis_executor - INFO - 加载了 28 个任务状态
