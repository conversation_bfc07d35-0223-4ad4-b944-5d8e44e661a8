#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试服务器启动脚本 - 捕获所有错误并输出到文件
"""

import os
import sys
import traceback
import subprocess
from datetime import datetime
from contextlib import redirect_stdout, redirect_stderr

class TeeOutput:
    """同时输出到控制台和文件的类"""
    def __init__(self, filename):
        self.terminal = sys.stdout
        self.log = open(filename, 'a', encoding='utf-8')
        
    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()
        
    def flush(self):
        self.terminal.flush()
        self.log.flush()
        
    def close(self):
        self.log.close()

def main():
    """主函数"""
    # 创建输出文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = f"server_debug_{timestamp}.txt"
    
    print(f"开始调试Django服务器启动过程...")
    print(f"所有输出将同时保存到: {output_file}")
    
    # 重定向输出
    tee = TeeOutput(output_file)
    original_stdout = sys.stdout
    original_stderr = sys.stderr
    
    try:
        sys.stdout = tee
        sys.stderr = tee
        
        print(f"\n{'='*80}")
        print(f"Django服务器调试启动 - {datetime.now()}")
        print(f"{'='*80}")
        
        # 设置环境变量
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        os.environ['DJANGO_SETTINGS_MODULE'] = 'geoserver_django.settings'
        
        print(f"当前工作目录: {os.getcwd()}")
        print(f"Python版本: {sys.version}")
        print(f"Python路径: {sys.executable}")
        
        # 步骤1: 运行简化测试
        print(f"\n{'='*60}")
        print("步骤1: 运行组件测试")
        print(f"{'='*60}")
        
        try:
            exec(open('simple_test.py').read())
        except Exception as e:
            print(f"组件测试失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
        
        # 步骤2: 尝试启动Django
        print(f"\n{'='*60}")
        print("步骤2: 启动Django开发服务器")
        print(f"{'='*60}")
        
        try:
            # 导入Django
            import django
            from django.core.management import execute_from_command_line
            
            print("正在启动Django开发服务器...")
            print("如果看到 'Starting development server' 消息，说明启动成功")
            print("按 Ctrl+C 停止服务器")
            
            # 启动服务器
            execute_from_command_line(['manage.py', 'runserver', '0.0.0.0:8091'])
            
        except KeyboardInterrupt:
            print("\n服务器已停止")
        except Exception as e:
            print(f"Django服务器启动失败: {e}")
            print(f"错误详情: {traceback.format_exc()}")
            
            # 尝试收集更多信息
            print(f"\n{'='*60}")
            print("错误分析:")
            print(f"{'='*60}")
            
            # 检查manage.py
            if os.path.exists('manage.py'):
                print("✓ manage.py 存在")
            else:
                print("✗ manage.py 不存在")
            
            # 检查设置文件
            settings_file = 'geoserver_django/settings.py'
            if os.path.exists(settings_file):
                print(f"✓ {settings_file} 存在")
            else:
                print(f"✗ {settings_file} 不存在")
            
            # 检查应用目录
            app_dir = 'geoserver_api'
            if os.path.exists(app_dir):
                print(f"✓ {app_dir} 目录存在")
                files = os.listdir(app_dir)
                print(f"  包含文件: {files}")
            else:
                print(f"✗ {app_dir} 目录不存在")
    
    finally:
        # 恢复原始输出
        sys.stdout = original_stdout
        sys.stderr = original_stderr
        tee.close()
        
        print(f"\n调试完成，详细日志已保存到: {output_file}")

if __name__ == '__main__':
    main()
