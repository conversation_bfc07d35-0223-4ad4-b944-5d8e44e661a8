2025-08-25 17:07:49 - INFO - === 合并分析任务开始 ===
2025-08-25 17:07:49 - INFO - 任务ID: 817b1e4d-8cc9-41d2-9667-607cae3de451
2025-08-25 17:07:49 - INFO - 🔧 立即创建TaskInfo.json...
2025-08-25 17:07:49 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-25 17:07:49 - INFO - 📁 TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\TaskInfo.json
2025-08-25 17:07:49 - ERROR - ❌ 创建TaskInfo.json模板失败: 'analysis_category'
2025-08-25 17:07:49 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 513, in _create_taskinfo_immediately
    'analysis_category': task_info['analysis_category'],
KeyError: 'analysis_category'

2025-08-25 17:07:49 - INFO - ✅ TaskInfo.json创建完成
2025-08-25 17:07:49 - INFO - 影像ID: 20250705171598
2025-08-25 17:07:49 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:49 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:07:49 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:07:49 - INFO - 分析类别: constructionLand
2025-08-25 17:07:49 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\constructionLand\20250705171598_1_1756112868.shp
2025-08-25 17:07:49 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\constructionLand\20250705171598_2_1756112868.shp
2025-08-25 17:07:49 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171598
2025-08-25 17:07:49 - INFO - === 开始执行合并分析任务 ===
2025-08-25 17:07:49 - WARNING - ⚠️ TaskInfo路径丢失，尝试重新构建...
2025-08-25 17:07:49 - INFO - 📍 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\TaskInfo.json
2025-08-25 17:07:49 - ERROR - ❌ TaskInfo.json文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171598\TaskInfo.json
2025-08-25 17:07:49 - INFO - 🤖 开始AI语义分割...
2025-08-25 17:07:49 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:49 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:07:49 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\constructionLand\20250705171598_1_1756112868.shp
2025-08-25 17:07:49 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-25 17:07:49 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-25 17:07:49 - INFO - 🔍 检测到合并分析任务: image_id=20250705171598, category=constructionLand
2025-08-25 17:07:49 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171598/TaskInfo.json
2025-08-25 17:07:49 - INFO - ✅ 使用真实任务ID: 817b1e4d-8cc9-41d2-9667-607cae3de451
2025-08-25 17:07:49 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-25 17:07:49 - INFO - 📝 TaskInfo文件不存在，将创建新文件
2025-08-25 17:07:49 - INFO - ➕ 添加新任务记录: 817b1e4d-8cc9-41d2-9667-607cae3de451
2025-08-25 17:07:49 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-25 17:07:49 - INFO - 📊 文件验证: 大小=1933字节，任务总数: 1
2025-08-25 17:07:51 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-25 17:07:51 - INFO - 🔧 创建处理参数:
2025-08-25 17:07:51 - INFO -   模型类型: deeplabv3_plus
2025-08-25 17:07:51 - INFO -   类别数量: 2
2025-08-25 17:07:51 - INFO -   目标类别: [1]
2025-08-25 17:07:51 - INFO -   窗口大小: 512
2025-08-25 17:07:51 - INFO -   批处理大小: 16
2025-08-25 17:07:51 - INFO -   重叠比例: 0.5
2025-08-25 17:07:51 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-25 17:07:51 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:51 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:07:51 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\constructionLand\20250705171598_1_1756112868.shp
2025-08-25 17:07:51 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-25 17:07:51 - INFO - 🔄 开始AI模型推理...
2025-08-25 17:07:51 - INFO - 
==================================================
2025-08-25 17:07:51 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:51 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171598\constructionLand\20250705171598_1_1756112868.shp
2025-08-25 17:07:51 - INFO - ==================================================
2025-08-25 17:07:51 - ERROR - 输入图像文件不存在: D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:51 - ERROR - ❌ AI处理失败
2025-08-25 17:07:51 - INFO - 🔄 更新TaskInfo.json: AI处理完成，成功=False
2025-08-25 17:07:51 - INFO - ✅ 找到并更新任务记录: 817b1e4d-8cc9-41d2-9667-607cae3de451
2025-08-25 17:07:51 - INFO - 📝 TaskInfo.json已更新: AI处理时间=0秒
2025-08-25 17:07:51 - INFO - 📝 更新字段: status = 失败
2025-08-25 17:07:51 - INFO - 📝 更新字段: message = AI语义分割失败: AI处理失败
2025-08-25 17:07:51 - INFO - 📝 更新字段: progress = 0
2025-08-25 17:07:51 - INFO - 📝 更新字段: end_time = 2025-08-25T17:07:51.163986
2025-08-25 17:07:51 - INFO - 📝 更新嵌套字段: results.success = False
2025-08-25 17:07:51 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress', 'end_time', 'results.success']
2025-08-25 17:07:51 - INFO - 🔍 验证更新结果:
2025-08-25 17:07:51 - INFO -   状态: 失败
2025-08-25 17:07:51 - INFO -   进度: 0%
2025-08-25 17:07:51 - INFO -   AI处理时间: 0
2025-08-25 17:07:51 - INFO -   成功状态: False
2025-08-25 17:07:51 - INFO -   空间统计:
2025-08-25 17:07:51 - INFO -     area_threshold: 200.0
