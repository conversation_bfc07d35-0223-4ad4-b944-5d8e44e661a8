#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务队列管理器
用于管理合并分析任务的排队执行，确保同时只有一个任务在执行
"""

import threading
import queue
import time
import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

# 配置日志
logging.basicConfig(level=logging.INFO)
queue_logger = logging.getLogger('task_queue')

class TaskQueueManager:
    """任务队列管理器 - 单例模式"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TaskQueueManager, cls).__new__(cls)
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if self._initialized:
            return

        self.task_queue = queue.Queue()  # 任务队列
        self.waiting_tasks = {}  # 等待中的任务 {task_id: task_info}
        self.executing_task = None  # 当前执行的任务
        self.completed_tasks = {}  # 已完成的任务
        self.cancelled_tasks = set()  # 已取消的任务ID集合

        self.queue_lock = threading.Lock()  # 队列锁
        self.worker_thread = None  # 工作线程
        self.is_running = False  # 队列是否运行中

        self._initialized = True

        # 启动工作线程
        self.start_worker()

        queue_logger.info("TaskQueueManager单例实例已创建并初始化")
    
    def start_worker(self):
        """启动工作线程"""
        if self.worker_thread is None or not self.worker_thread.is_alive():
            self.is_running = True
            self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
            self.worker_thread.start()
            queue_logger.info("任务队列工作线程已启动")
    
    def stop_worker(self):
        """停止工作线程"""
        self.is_running = False
        if self.worker_thread and self.worker_thread.is_alive():
            self.worker_thread.join(timeout=5)
            queue_logger.info("任务队列工作线程已停止")
    
    def add_task(self, task_id: str, task_params: Dict[str, Any], task_info_path: str) -> bool:
        """
        添加任务到队列

        参数:
            task_id: 任务ID
            task_params: 任务参数
            task_info_path: TaskInfo.json文件路径

        返回:
            bool: 是否成功添加
        """
        try:
            with self.queue_lock:
                # 调试信息
                queue_logger.info(f"🔍 添加任务前状态检查:")
                queue_logger.info(f"   队列大小: {self.task_queue.qsize()}")
                queue_logger.info(f"   等待任务数: {len(self.waiting_tasks)}")
                queue_logger.info(f"   执行任务: {self.executing_task['task_id'] if self.executing_task else '无'}")
                queue_logger.info(f"   工作线程运行: {self.is_running}")

                # 检查任务是否已存在
                if task_id in self.waiting_tasks or task_id in self.completed_tasks:
                    queue_logger.warning(f"任务已存在: {task_id}")
                    return False

                # 创建任务信息
                task_info = {
                    'task_id': task_id,
                    'params': task_params,
                    'task_info_path': task_info_path,
                    'status': '等待中',
                    'created_time': datetime.now().isoformat(),
                    'queue_position': self.task_queue.qsize() + 1
                }

                # 添加到等待队列
                self.waiting_tasks[task_id] = task_info
                self.task_queue.put(task_info)

                queue_logger.info(f"✅ 任务已添加到队列: {task_id}")
                queue_logger.info(f"   队列位置: {task_info['queue_position']}")
                queue_logger.info(f"   添加后队列大小: {self.task_queue.qsize()}")
                queue_logger.info(f"   添加后等待任务数: {len(self.waiting_tasks)}")

                return True

        except Exception as e:
            queue_logger.error(f"添加任务到队列失败: {task_id}, 错误: {str(e)}")
            return False
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消等待中的任务
        
        参数:
            task_id: 任务ID
            
        返回:
            dict: 取消结果
        """
        try:
            with self.queue_lock:
                # 检查任务状态
                if task_id not in self.waiting_tasks:
                    if task_id in self.completed_tasks:
                        return {
                            'success': False,
                            'message': '任务已完成，无法取消',
                            'status': self.completed_tasks[task_id]['status']
                        }
                    elif self.executing_task and self.executing_task['task_id'] == task_id:
                        return {
                            'success': False,
                            'message': '任务正在执行中，无法取消',
                            'status': '执行中'
                        }
                    else:
                        return {
                            'success': False,
                            'message': '任务不存在',
                            'status': '不存在'
                        }
                
                # 标记任务为已取消
                task_info = self.waiting_tasks[task_id]
                task_info['status'] = '已取消'
                task_info['cancelled_time'] = datetime.now().isoformat()
                
                # 移动到已取消集合
                self.cancelled_tasks.add(task_id)
                del self.waiting_tasks[task_id]
                
                # 更新TaskInfo.json
                self._update_task_info_status(task_info['task_info_path'], task_id, '已取消', '任务已被用户取消')
                
                queue_logger.info(f"任务已取消: {task_id}")
                
                return {
                    'success': True,
                    'message': '任务已成功取消',
                    'status': '已取消',
                    'task_id': task_id
                }
                
        except Exception as e:
            queue_logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
            return {
                'success': False,
                'message': f'取消任务失败: {str(e)}',
                'status': '取消失败'
            }
    
    def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        with self.queue_lock:
            return {
                'queue_size': self.task_queue.qsize(),
                'waiting_tasks': len(self.waiting_tasks),
                'executing_task': self.executing_task['task_id'] if self.executing_task else None,
                'completed_tasks': len(self.completed_tasks),
                'cancelled_tasks': len(self.cancelled_tasks),
                'is_running': self.is_running
            }
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取特定任务状态"""
        with self.queue_lock:
            if task_id in self.waiting_tasks:
                return {
                    'status': '等待中',
                    'queue_position': self._get_queue_position(task_id),
                    **self.waiting_tasks[task_id]
                }
            elif self.executing_task and self.executing_task['task_id'] == task_id:
                return {
                    'status': '执行中',
                    'queue_position': 0,
                    **self.executing_task
                }
            elif task_id in self.completed_tasks:
                return self.completed_tasks[task_id]
            elif task_id in self.cancelled_tasks:
                return {
                    'status': '已取消',
                    'task_id': task_id
                }
            else:
                return None
    
    def _worker_loop(self):
        """工作线程主循环"""
        queue_logger.info("🚀 任务队列工作线程开始运行")

        while self.is_running:
            try:
                # 获取下一个任务（阻塞等待，超时1秒）
                try:
                    queue_logger.debug(f"🔍 工作线程等待任务... (队列大小: {self.task_queue.qsize()})")
                    task_info = self.task_queue.get(timeout=1.0)
                    queue_logger.info(f"📥 工作线程获取到任务: {task_info['task_id']}")
                except queue.Empty:
                    # 每10秒输出一次状态
                    if int(time.time()) % 10 == 0:
                        queue_logger.debug(f"⏰ 工作线程等待中... (队列: {self.task_queue.qsize()}, 等待: {len(self.waiting_tasks)})")
                    continue

                # 检查任务是否已被取消
                if task_info['task_id'] in self.cancelled_tasks:
                    queue_logger.info(f"⏭️ 跳过已取消的任务: {task_info['task_id']}")
                    self.task_queue.task_done()
                    continue

                # 检查是否有其他任务正在执行
                with self.queue_lock:
                    if self.executing_task is not None:
                        queue_logger.warning(f"⚠️ 发现并发执行！当前执行: {self.executing_task['task_id']}, 新任务: {task_info['task_id']}")

                # 执行任务
                queue_logger.info(f"🎯 开始执行任务: {task_info['task_id']}")
                self._execute_task(task_info)
                self.task_queue.task_done()
                queue_logger.info(f"✅ 任务执行完成: {task_info['task_id']}")

            except Exception as e:
                queue_logger.error(f"❌ 工作线程异常: {str(e)}")
                time.sleep(1)

        queue_logger.info("🛑 任务队列工作线程已退出")
    
    def _execute_task(self, task_info: Dict[str, Any]):
        """执行单个任务"""
        task_id = task_info['task_id']
        
        try:
            with self.queue_lock:
                # 从等待队列移除
                if task_id in self.waiting_tasks:
                    del self.waiting_tasks[task_id]
                
                # 设置为当前执行任务
                self.executing_task = task_info
                task_info['status'] = '执行中'
                task_info['start_time'] = datetime.now().isoformat()
            
            queue_logger.info(f"开始执行任务: {task_id}")
            
            # 更新TaskInfo.json状态
            self._update_task_info_status(
                task_info['task_info_path'], 
                task_id, 
                '进行中', 
                '任务开始执行'
            )
            
            # 调用原有的合并分析执行器
            from .combined_analysis_executor import combined_analysis_executor

            params = task_info['params']
            result = combined_analysis_executor.execute_combined_analysis(
                image_id=params['id'],
                image_path=params['image'],
                model_path=params['model'],
                old_data_path=params['old_data_path'],
                area_threshold=params.get('area_threshold', 400.0),
                model_type=params.get('model_type', 'deeplabv3_plus'),
                num_classes=params.get('num_classes', 2)
            )
            
            # 处理执行结果
            with self.queue_lock:
                task_info['end_time'] = datetime.now().isoformat()
                task_info['result'] = result
                
                if result.get('success', False):
                    task_info['status'] = '完成'
                    queue_logger.info(f"任务执行成功: {task_id}")
                else:
                    task_info['status'] = '失败'
                    queue_logger.error(f"任务执行失败: {task_id}, 错误: {result.get('message', '未知错误')}")
                
                # 移动到已完成队列
                self.completed_tasks[task_id] = task_info
                self.executing_task = None
            
        except Exception as e:
            queue_logger.error(f"执行任务异常: {task_id}, 错误: {str(e)}")
            
            with self.queue_lock:
                task_info['status'] = '失败'
                task_info['end_time'] = datetime.now().isoformat()
                task_info['error'] = str(e)
                
                # 更新TaskInfo.json
                self._update_task_info_status(
                    task_info['task_info_path'], 
                    task_id, 
                    '失败', 
                    f'任务执行异常: {str(e)}'
                )
                
                self.completed_tasks[task_id] = task_info
                self.executing_task = None
    
    def _get_queue_position(self, task_id: str) -> int:
        """获取任务在队列中的位置"""
        position = 1
        temp_queue = queue.Queue()
        
        # 临时取出所有任务来计算位置
        while not self.task_queue.empty():
            try:
                task = self.task_queue.get_nowait()
                temp_queue.put(task)
                if task['task_id'] == task_id:
                    break
                position += 1
            except queue.Empty:
                break
        
        # 将任务放回队列
        while not temp_queue.empty():
            self.task_queue.put(temp_queue.get_nowait())
        
        return position
    
    def _update_task_info_status(self, task_info_path: str, task_id: str, status: str, message: str):
        """更新TaskInfo.json中的任务状态"""
        try:
            if os.path.exists(task_info_path):
                with open(task_info_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 确保data是列表
                if not isinstance(data, list):
                    data = [data] if data else []
                
                # 查找并更新目标任务
                for task in data:
                    if task.get('task_id') == task_id:
                        task['status'] = status
                        task['message'] = message
                        task['update_time'] = datetime.now().isoformat()
                        break
                
                # 保存更新后的数据
                with open(task_info_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                    
        except Exception as e:
            queue_logger.error(f"更新TaskInfo.json失败: {task_info_path}, 错误: {str(e)}")


# 创建全局任务队列管理器实例
task_queue_manager = TaskQueueManager()
