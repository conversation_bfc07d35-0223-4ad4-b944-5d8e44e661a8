#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务队列管理器
实现生产者-消费者模型，确保合并分析任务按顺序执行
"""

import threading
import queue
import time
import uuid
import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from .combined_analysis_executor import CombinedAnalysisExecutor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskQueueManager:
    """任务队列管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(TaskQueueManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        
        # 任务队列
        self.task_queue = queue.Queue()
        
        # 任务状态存储
        self.tasks = {}
        
        # 当前执行的任务
        self.current_task = None
        
        # 队列处理线程
        self.queue_thread = None
        self.is_running = False
        
        # 合并分析执行器
        self.executor = CombinedAnalysisExecutor()
        
        # 启动队列处理线程
        self.start_queue_processor()
        
        logger.info("[STARTUP] 任务队列管理器已启动")
    
    def start_queue_processor(self):
        """启动队列处理线程"""
        if self.queue_thread is None or not self.queue_thread.is_alive():
            self.is_running = True
            self.queue_thread = threading.Thread(
                target=self._process_queue,
                daemon=True,
                name="TaskQueueProcessor"
            )
            self.queue_thread.start()
            logger.info("[THREAD] 队列处理线程已启动")
    
    def stop_queue_processor(self):
        """停止队列处理线程"""
        self.is_running = False
        if self.queue_thread and self.queue_thread.is_alive():
            self.queue_thread.join(timeout=5)
        logger.info("⏹️ 队列处理线程已停止")
    
    def add_task(self, task_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        添加任务到队列
        
        Args:
            task_params: 任务参数
            
        Returns:
            Dict: 包含task_id和状态信息
        """
        try:
            # 生成任务ID
            task_id = str(uuid.uuid4())
            
            # 创建任务信息
            task_info = {
                'task_id': task_id,
                'params': task_params,
                'status': '等待中',
                'message': '任务已创建，等待执行',
                'created_time': datetime.now().isoformat(),
                'start_time': None,
                'end_time': None,
                'queue_position': self.task_queue.qsize() + 1
            }
            
            # 存储任务信息
            self.tasks[task_id] = task_info
            
            # 立即创建TaskInfo.json模板
            self._create_taskinfo_template(task_id, task_params)
            
            # 添加到队列
            self.task_queue.put(task_info)
            
            logger.info(f"[QUEUE] 任务已添加到队列: {task_id}, 队列位置: {task_info['queue_position']}")

            return {
                'success': True,
                'message': '任务已添加到队列',
                'data': {
                    'task_id': task_id,
                    'status': '等待中',
                    'queue_position': task_info['queue_position'],
                    'estimated_wait_time': self._estimate_wait_time()
                }
            }

        except Exception as e:
            logger.error(f"[ERROR] 添加任务到队列失败: {str(e)}")
            return {
                'success': False,
                'message': f'添加任务失败: {str(e)}'
            }
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        取消等待中的任务

        Args:
            task_id: 任务ID

        Returns:
            Dict: 取消结果
        """
        try:
            if task_id not in self.tasks:
                return {
                    'success': False,
                    'message': '任务不存在'
                }

            task_info = self.tasks[task_id]
            current_status = task_info['status']

            # 检查当前执行的任务
            current_task_id = self.current_task['task_id'] if self.current_task else None

            # 不能取消正在执行的任务
            if task_id == current_task_id:
                return {
                    'success': False,
                    'message': '任务正在执行中，无法取消'
                }

            # 只能取消等待中的任务
            if current_status != '等待中':
                return {
                    'success': False,
                    'message': f'任务状态为"{current_status}"，无法取消'
                }

            logger.info(f"[SEARCH] 准备取消任务: {task_id}, 当前状态: {current_status}")
            logger.info(f"[SEARCH] 当前执行任务: {current_task_id}")

            # 标记任务为已取消
            task_info['status'] = '已取消'
            task_info['message'] = '任务已被用户取消'
            task_info['end_time'] = datetime.now().isoformat()

            # 更新TaskInfo.json（使用安全的更新方法）
            self._safe_update_taskinfo_status(task_id, '已取消', '任务已被用户取消')

            logger.info(f"[SUCCESS] 任务已取消: {task_id}")

            return {
                'success': True,
                'message': '任务已取消'
            }
            
        except Exception as e:
            logger.error(f"[ERROR] 取消任务失败: {str(e)}")
            return {
                'success': False,
                'message': f'取消任务失败: {str(e)}'
            }
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict: 任务状态信息
        """
        if task_id not in self.tasks:
            return {
                'success': False,
                'message': '任务不存在'
            }
        
        task_info = self.tasks[task_id]
        
        # 如果是等待中的任务，计算当前队列位置
        if task_info['status'] == '等待中':
            queue_position = self._calculate_queue_position(task_id)
            task_info['queue_position'] = queue_position
            task_info['estimated_wait_time'] = self._estimate_wait_time(queue_position)
        
        return {
            'success': True,
            'data': task_info
        }
    
    def get_queue_status(self) -> Dict[str, Any]:
        """
        获取队列状态
        
        Returns:
            Dict: 队列状态信息
        """
        waiting_tasks = [task for task in self.tasks.values() if task['status'] == '等待中']
        
        return {
            'success': True,
            'data': {
                'queue_size': len(waiting_tasks),
                'current_task': self.current_task['task_id'] if self.current_task else None,
                'total_tasks': len(self.tasks),
                'waiting_tasks': len(waiting_tasks),
                'completed_tasks': len([t for t in self.tasks.values() if t['status'] in ['完成', '失败', '已取消']])
            }
        }
    
    def _process_queue(self):
        """队列处理主循环"""
        logger.info("[PROCESSOR] 队列处理器开始运行")
        
        while self.is_running:
            try:
                # 获取下一个任务（阻塞等待，超时1秒）
                try:
                    task_info = self.task_queue.get(timeout=1.0)
                except queue.Empty:
                    continue
                
                # 检查任务是否已被取消
                if task_info['status'] == '已取消':
                    logger.info(f"⏭️ 跳过已取消的任务: {task_info['task_id']}")
                    self.task_queue.task_done()
                    continue
                
                # 执行任务
                self._execute_task(task_info)
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except Exception as e:
                logger.error(f"[ERROR] 队列处理异常: {str(e)}")
                time.sleep(1)
        
        logger.info("⏹️ 队列处理器已停止")
    
    def _execute_task(self, task_info: Dict[str, Any]):
        """
        执行单个任务
        
        Args:
            task_info: 任务信息
        """
        task_id = task_info['task_id']
        params = task_info['params']
        
        try:
            # 设置当前任务
            self.current_task = task_info
            
            # 更新任务状态
            task_info['status'] = '进行中'
            task_info['message'] = '任务正在执行中...'
            task_info['start_time'] = datetime.now().isoformat()
            
            # 更新TaskInfo.json状态
            self._update_taskinfo_status(task_id, '进行中', '任务正在执行中...')
            
            logger.info(f"[START] 开始执行任务: {task_id}")

            # 设置标志位，表示由队列管理器调用
            self.executor._called_by_queue_manager = True

            try:
                # 调用原有的合并分析执行器，传入task_id避免重复生成
                result = self.executor.execute_combined_analysis(
                    image_id=params['id'],
                    image_path=params['image'],
                    model_path=params['model'],
                    old_data_path=params['old_data_path'],
                    area_threshold=params.get('area_threshold', 400.0),
                    model_type=params.get('model_type', 'deeplabv3_plus'),
                    task_id=task_id  # 传入队列管理器生成的task_id
                )
            finally:
                # 清除标志位
                if hasattr(self.executor, '_called_by_queue_manager'):
                    delattr(self.executor, '_called_by_queue_manager')
            
            if result['success']:
                task_info['status'] = '完成'
                task_info['message'] = '任务执行完成'
                logger.info(f"[SUCCESS] 任务执行完成: {task_id}")
            else:
                task_info['status'] = '失败'
                task_info['message'] = f'任务执行失败: {result.get("message", "未知错误")}'
                logger.error(f"[ERROR] 任务执行失败: {task_id}")
            
        except Exception as e:
            task_info['status'] = '失败'
            task_info['message'] = f'任务执行异常: {str(e)}'
            logger.error(f"[ERROR] 任务执行异常: {task_id}, 错误: {str(e)}")
        
        finally:
            # 更新结束时间
            task_info['end_time'] = datetime.now().isoformat()
            
            # 清除当前任务
            self.current_task = None
            
            logger.info(f"[FINISH] 任务处理结束: {task_id}, 状态: {task_info['status']}")
    
    def _create_taskinfo_template(self, task_id: str, params: Dict[str, Any]):
        """
        创建TaskInfo.json模板
        
        Args:
            task_id: 任务ID
            params: 任务参数
        """
        try:
            # 提取分析类别
            analysis_category = self._extract_analysis_category(params['model'])
            
            # 生成时间戳
            timestamp = int(time.time())
            
            # 构建输出路径
            ai_output_path = f"D:/Drone_Project/nginxData/ODM/AI/{params['id']}/{analysis_category}/{params['id']}_1_{timestamp}.shp"
            final_output_path = f"D:/Drone_Project/nginxData/ODM/AI/{params['id']}/{analysis_category}/{params['id']}_2_{timestamp}.shp"
            
            # 提取模型名称
            model_name = os.path.basename(params['model'])
            
            # 创建TaskInfo模板
            taskinfo_template = {
                "task_id": task_id,
                "image_id": params['id'],
                "analysis_category": analysis_category,
                "timestamp": timestamp,
                "datetime": datetime.now().isoformat(),
                "input_files": {
                    "image_path": params['image'],
                    "model_path": params['model'],
                    "old_data_path": params['old_data_path']
                },
                "output_files": {
                    "ai_output_path": ai_output_path,
                    "final_output_path": final_output_path
                },
                "parameters": {
                    "model_type": params.get('model_type', 'deeplabv3_plus'),
                    "model_name": model_name,
                    "num_classes": 2,
                    "area_threshold": params.get('area_threshold', 400.0)
                },
                "results": {
                    "ai_processing_time": None,
                    "spatial_statistics": {
                        "outflow_count": None,
                        "inflow_count": None,
                        "total_count": None,
                        "outflow_area": None,
                        "inflow_area": None,
                        "area_threshold": params.get('area_threshold', 400.0)
                    },
                    "success": None
                },
                "geoserver_publish": {
                    "ai_result": {
                        "success": False,
                        "message": "待发布",
                        "layer_name": ""
                    },
                    "final_result": {
                        "success": False,
                        "message": "待发布",
                        "layer_name": ""
                    },
                    "overall_success": False,
                    "workspace": analysis_category,
                    "epsg": "32648",
                    "publish_time": None
                },
                "status": "等待中",
                "log_file": f"D:/Drone_Project/geoserverAPIDJV2/geoserver_api/logs/analysislog/{task_id}.log"
            }
            
            # 保存TaskInfo.json
            taskinfo_dir = f"D:/Drone_Project/nginxData/ODM/AI/{params['id']}"
            os.makedirs(taskinfo_dir, exist_ok=True)
            
            taskinfo_path = os.path.join(taskinfo_dir, "TaskInfo.json")
            
            # 读取现有数据
            existing_tasks = []
            if os.path.exists(taskinfo_path):
                try:
                    with open(taskinfo_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            existing_tasks = data
                        elif isinstance(data, dict):
                            existing_tasks = [data]
                except Exception as e:
                    logger.warning(f"[WARNING] 读取现有TaskInfo失败: {str(e)}")
            
            # 检查是否已存在相同task_id的记录
            task_found = False
            for i, task in enumerate(existing_tasks):
                if task.get('task_id') == task_id:
                    existing_tasks[i] = taskinfo_template
                    task_found = True
                    break
            
            if not task_found:
                existing_tasks.append(taskinfo_template)
            
            # 保存文件
            with open(taskinfo_path, 'w', encoding='utf-8') as f:
                json.dump(existing_tasks, f, ensure_ascii=False, indent=2)
            
            logger.info(f"[UPDATE] TaskInfo.json模板已创建: {task_id}")
            
        except Exception as e:
            logger.error(f"[ERROR] 创建TaskInfo.json模板失败: {str(e)}")
    
    def _update_taskinfo_status(self, task_id: str, status: str, message: str):
        """
        更新TaskInfo.json中的状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            message: 状态消息
        """
        try:
            # 从任务信息中获取image_id
            if task_id not in self.tasks:
                return
            
            params = self.tasks[task_id]['params']
            taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{params['id']}/TaskInfo.json"
            
            if not os.path.exists(taskinfo_path):
                return
            
            # 读取现有数据
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                return
            
            # 更新对应的任务记录
            for task in data:
                if task.get('task_id') == task_id:
                    task['status'] = status
                    task['message'] = message
                    break
            
            # 保存文件
            with open(taskinfo_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"[UPDATE] TaskInfo.json状态已更新: {task_id} -> {status}")
            
        except Exception as e:
            logger.error(f"[ERROR] 更新TaskInfo.json状态失败: {str(e)}")

    def _safe_update_taskinfo_status(self, task_id: str, status: str, message: str):
        """
        安全地更新TaskInfo.json中的状态，避免影响其他任务

        Args:
            task_id: 任务ID
            status: 新状态
            message: 状态消息
        """
        try:
            # 从任务信息中获取image_id
            if task_id not in self.tasks:
                logger.warning(f"[WARNING] 任务不存在于队列管理器中: {task_id}")
                return

            params = self.tasks[task_id]['params']
            taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{params['id']}/TaskInfo.json"

            if not os.path.exists(taskinfo_path):
                logger.warning(f"[WARNING] TaskInfo.json文件不存在: {taskinfo_path}")
                return

            # 读取现有数据
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            if not isinstance(data, list):
                logger.warning(f"[WARNING] TaskInfo.json格式不是数组: {taskinfo_path}")
                return

            # 记录更新前的状态
            logger.info(f"[UPDATE] 准备更新TaskInfo.json: {task_id} -> {status}")
            logger.info(f"[UPDATE] 文件路径: {taskinfo_path}")
            logger.info(f"[UPDATE] 当前记录数量: {len(data)}")

            # 查找并更新对应的任务记录
            updated_count = 0
            for i, task in enumerate(data):
                if task.get('task_id') == task_id:
                    old_status = task.get('status', 'unknown')
                    task['status'] = status
                    task['message'] = message
                    updated_count += 1
                    logger.info(f"[UPDATE] 更新记录{i+1}: {task_id} 状态 {old_status} -> {status}")

            if updated_count == 0:
                logger.warning(f"[WARNING] 未找到匹配的任务记录: {task_id}")
                return
            elif updated_count > 1:
                logger.warning(f"[WARNING] 发现{updated_count}个重复的task_id记录: {task_id}")

            # 保存文件
            with open(taskinfo_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info(f"[SUCCESS] TaskInfo.json状态已安全更新: {task_id} -> {status} (更新了{updated_count}个记录)")

        except Exception as e:
            logger.error(f"[ERROR] 安全更新TaskInfo.json状态失败: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")

    def _extract_analysis_category(self, model_path: str) -> str:
        """从模型路径中提取分析类别"""
        try:
            path_parts = model_path.replace('\\', '/').split('/')
            for i, part in enumerate(path_parts):
                if part == 'AIWeight' and i + 1 < len(path_parts):
                    return path_parts[i + 1]
            return 'unknown'
        except:
            return 'unknown'
    
    def _calculate_queue_position(self, task_id: str) -> int:
        """计算任务在队列中的位置"""
        position = 1
        for task in self.tasks.values():
            if task['status'] == '等待中' and task['created_time'] < self.tasks[task_id]['created_time']:
                position += 1
        return position
    
    def _estimate_wait_time(self, position: int = None) -> str:
        """估算等待时间"""
        if position is None:
            position = self.task_queue.qsize()
        
        # 假设每个任务平均需要10分钟
        estimated_minutes = position * 10
        
        if estimated_minutes < 60:
            return f"约 {estimated_minutes} 分钟"
        else:
            hours = estimated_minutes // 60
            minutes = estimated_minutes % 60
            return f"约 {hours} 小时 {minutes} 分钟"


# 全局队列管理器实例
task_queue_manager = TaskQueueManager()
