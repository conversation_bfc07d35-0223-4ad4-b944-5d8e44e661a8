#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地理空间分析处理器

功能说明:
- 耕地流入流出分析：分析两个时期耕地数据的变化
- 空间交集计算：计算两个图层的空间交集
- 面积阈值过滤：根据面积阈值过滤分析结果
- 结果输出：生成流入流出的Shapefile数据

开发者注意:
- 使用GDAL/OGR进行空间数据处理
- 使用Shapely进行几何运算
- 支持大数据量的空间分析
- 提供详细的分析日志和进度跟踪
"""

import os
import logging
import json
import numpy as np
from datetime import datetime
from osgeo import ogr, osr, gdal
from shapely.geometry import shape, mapping, Polygon
from shapely.ops import unary_union
# from skimage import measure  # 暂时不需要
import cv2

# 设置日志
analysis_logger = logging.getLogger('analysis_processor')

class AnalysisProcessor:
    """地理空间分析处理器"""
    
    def __init__(self):
        """初始化分析处理器"""
        self.area_threshold = 200.0  # 面积阈值，单位：平方米
        
    def analyze_farmland_changes(self, old_data_path, new_data_path, output_dir=None,
                               shp_filename="changes_shapefile"):
        """
        分析空间数据变化

        参数:
            old_data_path: 老数据路径
            new_data_path: 新数据路径
            output_dir: 输出目录（可选，默认为新数据根目录）
            shp_filename: 输出文件名（可选，默认changes_shapefile）

        返回:
            dict: 分析结果
        """
        try:
            analysis_logger.info("开始空间数据变化分析")
            analysis_logger.info(f"老数据: {old_data_path}")
            analysis_logger.info(f"新数据: {new_data_path}")

            # 验证输入文件
            if not os.path.exists(old_data_path):
                raise FileNotFoundError(f"老数据文件不存在: {old_data_path}")
            if not os.path.exists(new_data_path):
                raise FileNotFoundError(f"新数据文件不存在: {new_data_path}")

            # 确定输出目录：如果未指定，使用新数据的根目录
            if output_dir is None:
                output_dir = os.path.dirname(new_data_path)

            analysis_logger.info(f"输出目录: {output_dir}")

            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 1. 读取数据
            analysis_logger.info("步骤1: 读取Shapefile数据")
            old_data = self._read_shapefile(old_data_path)
            new_data = self._read_shapefile(new_data_path)

            analysis_logger.info(f"老数据图斑数量: {len(old_data)}")
            analysis_logger.info(f"新数据图斑数量: {len(new_data)}")

            # 2. 计算新数据的边界，用于裁剪老数据
            analysis_logger.info("步骤2: 计算分析区域边界")
            analysis_boundary = self._get_analysis_boundary(new_data)

            # 3. 裁剪老数据到分析区域
            analysis_logger.info("步骤3: 裁剪老数据到分析区域")
            old_data_clipped = self._clip_to_boundary(old_data, analysis_boundary)

            analysis_logger.info(f"裁剪后老数据图斑数量: {len(old_data_clipped)}")

            # 4. 计算流出数据 (老数据 - 新数据)
            analysis_logger.info("步骤4: 计算流出数据")
            outflow_data = self._calculate_outflow(old_data_clipped, new_data)

            # 5. 计算流入数据 (新数据 - 老数据)
            analysis_logger.info("步骤5: 计算流入数据")
            inflow_data = self._calculate_inflow(new_data, old_data_clipped)

            # 6. 过滤小于阈值的图斑
            analysis_logger.info(f"步骤6: 过滤小于{self.area_threshold}平方米的图斑")
            outflow_filtered = self._filter_by_area(outflow_data, self.area_threshold)
            inflow_filtered = self._filter_by_area(inflow_data, self.area_threshold)

            analysis_logger.info(f"流出图斑数量: {len(outflow_filtered)}")
            analysis_logger.info(f"流入图斑数量: {len(inflow_filtered)}")

            # 7. 合并流出和流入数据，添加flowtype属性
            analysis_logger.info("步骤7: 合并流出和流入数据")
            combined_data = []

            # 添加流出数据 (flowtype = "out")
            for feature in outflow_filtered:
                feature_copy = feature.copy()
                feature_copy['flowtype'] = "out"
                combined_data.append(feature_copy)

            # 添加流入数据 (flowtype = "in")
            for feature in inflow_filtered:
                feature_copy = feature.copy()
                feature_copy['flowtype'] = "in"
                combined_data.append(feature_copy)

            analysis_logger.info(f"合并后总图斑数量: {len(combined_data)}")

            # 8. 输出合并后的结果
            analysis_logger.info("步骤8: 输出分析结果")
            output_path = os.path.join(output_dir, f"{shp_filename}.shp")
            self._write_combined_shapefile(combined_data, output_path)

            # 9. 计算统计信息
            outflow_area = sum([feature['area'] for feature in outflow_filtered])
            inflow_area = sum([feature['area'] for feature in inflow_filtered])

            result = {
                'success': True,
                'message': '空间数据变化分析完成',
                'analysis_time': datetime.now().isoformat(),
                'statistics': {
                    'outflow_count': len(outflow_filtered),
                    'inflow_count': len(inflow_filtered),
                    'total_count': len(combined_data),
                    'outflow_area': round(outflow_area, 2),
                    'inflow_area': round(inflow_area, 2),
                    'area_threshold': self.area_threshold
                },
                'output_files': {
                    'combined_shapefile': output_path
                },
                'input_files': {
                    'old_data': old_data_path,
                    'new_data': new_data_path
                }
            }

            analysis_logger.info("空间数据变化分析完成")
            analysis_logger.info(f"流出面积: {outflow_area:.2f} 平方米")
            analysis_logger.info(f"流入面积: {inflow_area:.2f} 平方米")
            
            return result
            
        except Exception as e:
            error_msg = f"耕地流入流出分析失败: {str(e)}"
            analysis_logger.error(error_msg)
            import traceback
            analysis_logger.error(traceback.format_exc())
            return {
                'success': False,
                'message': error_msg,
                'error': str(e)
            }
    
    def _read_shapefile(self, shapefile_path):
        """读取Shapefile数据并转换坐标系"""
        features = []

        # 打开数据源
        driver = ogr.GetDriverByName("ESRI Shapefile")
        datasource = driver.Open(shapefile_path, 0)

        if datasource is None:
            raise Exception(f"无法打开Shapefile: {shapefile_path}")

        layer = datasource.GetLayer()
        layer_defn = layer.GetLayerDefn()

        # 获取原始坐标系
        source_srs = layer.GetSpatialRef()
        analysis_logger.info(f"正在检查文件坐标系: {shapefile_path}")
        if source_srs:
            analysis_logger.info(f"原始坐标系: {source_srs.GetName()}")
            epsg_code = source_srs.GetAuthorityCode(None)
            analysis_logger.info(f"EPSG代码: {epsg_code if epsg_code else '未知'}")
            analysis_logger.info(f"是否为投影坐标系: {'是' if source_srs.IsProjected() else '否'}")
            if source_srs.IsProjected():
                linear_units = source_srs.GetLinearUnitsName()
                analysis_logger.info(f"线性单位: {linear_units}")
            else:
                angular_units = source_srs.GetAngularUnitsName()
                analysis_logger.info(f"角度单位: {angular_units}")
        else:
            analysis_logger.warning("未找到坐标系信息，假设为WGS84")

        # 创建目标坐标系（使用适合中国的投影坐标系）
        target_srs = self._get_target_projection(source_srs, layer)

        # 创建坐标转换
        transform = None
        if source_srs and target_srs and not source_srs.IsSame(target_srs):
            transform = osr.CoordinateTransformation(source_srs, target_srs)
            analysis_logger.info(f"将转换到目标坐标系: {target_srs.GetName()}")
            # 保存目标坐标系供输出使用
            self._target_srs = target_srs
        else:
            analysis_logger.info("无需坐标系转换")
            # 保存原始坐标系
            self._target_srs = source_srs if source_srs else None

        # 读取所有要素
        for feature in layer:
            geom = feature.GetGeometryRef()
            if geom is not None:
                # 坐标系转换
                if transform:
                    geom_copy = geom.Clone()
                    geom_copy.Transform(transform)
                    geom = geom_copy

                # 转换为Shapely几何对象
                geom_json = json.loads(geom.ExportToJson())
                shapely_geom = shape(geom_json)

                # 修复几何体方向（确保外环为逆时针，内环为顺时针）
                if hasattr(shapely_geom, 'exterior'):
                    # 对于多边形，确保正确的方向
                    from shapely.geometry import Polygon
                    if isinstance(shapely_geom, Polygon):
                        # 确保外环为逆时针方向
                        if not shapely_geom.exterior.is_ccw:
                            shapely_geom = Polygon(list(reversed(shapely_geom.exterior.coords)))

                # 计算面积（现在单位应该是平方米）
                area = abs(shapely_geom.area)  # 使用绝对值确保面积为正

                # 获取属性字段
                attributes = {}
                for i in range(layer_defn.GetFieldCount()):
                    field_defn = layer_defn.GetFieldDefn(i)
                    field_name = field_defn.GetName()
                    field_value = feature.GetField(i)
                    attributes[field_name] = field_value

                features.append({
                    'geometry': shapely_geom,
                    'area': area,
                    'attributes': attributes
                })

        datasource = None
        return features

    def _get_target_projection(self, source_srs, layer):
        """获取目标投影坐标系"""
        try:
            # 如果已经是合适的投影坐标系，直接使用
            if source_srs and source_srs.IsProjected():
                linear_units = source_srs.GetLinearUnitsName()
                if linear_units and 'metre' in linear_units.lower():
                    analysis_logger.info(f"原坐标系已是米制投影坐标系: {linear_units}")
                    return source_srs

            # 获取数据的地理范围来选择合适的投影
            extent = layer.GetExtent()  # (min_x, max_x, min_y, max_y)
            center_lon = (extent[0] + extent[1]) / 2
            center_lat = (extent[2] + extent[3]) / 2

            analysis_logger.info(f"数据中心点: 经度 {center_lon:.6f}, 纬度 {center_lat:.6f}")

            # 根据经纬度范围选择合适的投影坐标系
            target_srs = osr.SpatialReference()

            # 判断是否在中国范围内
            if 70 <= center_lon <= 140 and 15 <= center_lat <= 55:
                # 中国范围，使用CGCS2000 / 3-degree Gauss-Kruger
                zone = int((center_lon + 1.5) / 3)  # 计算3度带号
                central_meridian = zone * 3  # 中央经线

                # 使用CGCS2000 3度带投影
                epsg_code = 4490 + zone  # CGCS2000 3度带的EPSG代码范围

                # 如果EPSG代码超出范围，使用通用的UTM投影
                if epsg_code > 4560:  # CGCS2000 3度带的最大EPSG
                    utm_zone = int((center_lon + 180) / 6) + 1
                    epsg_code = 32600 + utm_zone  # WGS84 UTM北半球
                    analysis_logger.info(f"使用UTM投影，带号: {utm_zone}")
                else:
                    analysis_logger.info(f"使用CGCS2000 3度带投影，带号: {zone}, 中央经线: {central_meridian}")

                target_srs.ImportFromEPSG(epsg_code)

            else:
                # 非中国范围，使用UTM投影
                utm_zone = int((center_lon + 180) / 6) + 1
                if center_lat >= 0:
                    epsg_code = 32600 + utm_zone  # 北半球
                else:
                    epsg_code = 32700 + utm_zone  # 南半球

                analysis_logger.info(f"使用UTM投影，带号: {utm_zone}")
                target_srs.ImportFromEPSG(epsg_code)

            analysis_logger.info(f"选择的目标坐标系: {target_srs.GetName()}")
            return target_srs

        except Exception as e:
            analysis_logger.error(f"选择目标投影坐标系失败: {str(e)}")
            # 回退到WGS84 UTM
            target_srs = osr.SpatialReference()
            target_srs.ImportFromEPSG(32649)  # WGS84 UTM 49N (适合中国东部)
            analysis_logger.info("回退到WGS84 UTM 49N投影")
            return target_srs
    
    def _get_analysis_boundary(self, features):
        """获取分析区域边界"""
        geometries = [feature['geometry'] for feature in features]
        boundary = unary_union(geometries).convex_hull
        return boundary
    
    def _clip_to_boundary(self, features, boundary):
        """将要素裁剪到指定边界"""
        clipped_features = []
        
        for feature in features:
            geom = feature['geometry']
            
            # 检查是否与边界相交
            if geom.intersects(boundary):
                # 计算交集
                intersection = geom.intersection(boundary)
                
                if not intersection.is_empty and intersection.area > 0:
                    clipped_features.append({
                        'geometry': intersection,
                        'area': intersection.area,
                        'attributes': feature['attributes']
                    })
        
        return clipped_features
    
    def _calculate_outflow(self, farmland_2024, farmland_2025):
        """计算流出数据 (2024年 - 2025年)"""
        outflow_features = []
        
        # 合并2025年的所有几何体
        geometries_2025 = [feature['geometry'] for feature in farmland_2025]
        union_2025 = unary_union(geometries_2025)
        
        # 对每个2024年的图斑，计算与2025年的差集
        for feature_2024 in farmland_2024:
            geom_2024 = feature_2024['geometry']
            
            # 计算差集
            difference = geom_2024.difference(union_2025)
            
            if not difference.is_empty and difference.area > 0:
                outflow_features.append({
                    'geometry': difference,
                    'area': abs(difference.area),
                    'attributes': feature_2024['attributes'],
                    'change_type': 'outflow'
                })
        
        return outflow_features
    
    def _calculate_inflow(self, farmland_2025, farmland_2024):
        """计算流入数据 (2025年 - 2024年)"""
        inflow_features = []
        
        # 合并2024年的所有几何体
        geometries_2024 = [feature['geometry'] for feature in farmland_2024]
        union_2024 = unary_union(geometries_2024)
        
        # 对每个2025年的图斑，计算与2024年的差集
        for feature_2025 in farmland_2025:
            geom_2025 = feature_2025['geometry']
            
            # 计算差集
            difference = geom_2025.difference(union_2024)
            
            if not difference.is_empty and difference.area > 0:
                inflow_features.append({
                    'geometry': difference,
                    'area': abs(difference.area),
                    'attributes': feature_2025['attributes'],
                    'change_type': 'inflow'
                })
        
        return inflow_features
    
    def _explode_multipolygons(self, features):
        """将MultiPolygon分解为单独的Polygon"""
        exploded_features = []

        # 统计几何体类型
        geom_type_stats = {}
        multipolygon_count = 0
        total_parts = 0

        for feature in features:
            geom = feature['geometry']
            geom_type = geom.geom_type
            geom_type_stats[geom_type] = geom_type_stats.get(geom_type, 0) + 1

            # 检查是否为MultiPolygon或GeometryCollection
            if geom.geom_type == 'MultiPolygon':
                multipolygon_count += 1
                parts_count = len(geom.geoms)
                total_parts += parts_count

                # 分解MultiPolygon为单独的Polygon
                for i, polygon in enumerate(geom.geoms):
                    if polygon.area > 0:  # 只保留有效的多边形
                        exploded_features.append({
                            'geometry': polygon,
                            'area': abs(polygon.area),
                            'attributes': feature['attributes'],
                            'change_type': feature.get('change_type', 'unknown')
                        })

            elif geom.geom_type == 'Polygon':
                # 单个多边形，直接添加
                exploded_features.append({
                    'geometry': geom,
                    'area': abs(geom.area),
                    'attributes': feature['attributes'],
                    'change_type': feature.get('change_type', 'unknown')
                })

            elif geom.geom_type == 'GeometryCollection':
                # 处理GeometryCollection
                for sub_geom in geom.geoms:
                    if sub_geom.geom_type == 'Polygon' and sub_geom.area > 0:
                        exploded_features.append({
                            'geometry': sub_geom,
                            'area': abs(sub_geom.area),
                            'attributes': feature['attributes'],
                            'change_type': feature.get('change_type', 'unknown')
                        })
                    elif sub_geom.geom_type == 'MultiPolygon':
                        for polygon in sub_geom.geoms:
                            if polygon.area > 0:
                                exploded_features.append({
                                    'geometry': polygon,
                                    'area': abs(polygon.area),
                                    'attributes': feature['attributes'],
                                    'change_type': feature.get('change_type', 'unknown')
                                })

        # 记录详细的分解信息
        analysis_logger.info(f"几何体类型统计: {geom_type_stats}")
        analysis_logger.info(f"MultiPolygon数量: {multipolygon_count}")
        if multipolygon_count > 0:
            analysis_logger.info(f"平均每个MultiPolygon包含: {total_parts/multipolygon_count:.1f} 个部分")
        analysis_logger.info(f"几何体分解: {len(features)} -> {len(exploded_features)} 个独立图斑")

        return exploded_features

    def _filter_by_area(self, features, min_area):
        """根据面积阈值过滤要素（先分解MultiPolygon）"""
        if not features:
            return []

        # 首先分解MultiPolygon为单独的Polygon
        exploded_features = self._explode_multipolygons(features)

        # 添加调试信息
        areas = [feature['area'] for feature in exploded_features]
        analysis_logger.info(f"分解后图斑数量: {len(exploded_features)}")
        analysis_logger.info(f"面积范围: {min(areas):.6f} - {max(areas):.6f} 平方单位")
        analysis_logger.info(f"面积阈值: {min_area} 平方单位")

        # 统计不同面积范围的图斑数量
        small_count = len([a for a in areas if a < min_area])
        large_count = len([a for a in areas if a >= min_area])

        analysis_logger.info(f"小于阈值的图斑: {small_count} 个")
        analysis_logger.info(f"大于等于阈值的图斑: {large_count} 个")

        # 显示面积分布
        if areas:
            sorted_areas = sorted(areas, reverse=True)
            analysis_logger.info(f"最大的5个面积值: {[f'{a:.6f}' for a in sorted_areas[:5]]}")
            analysis_logger.info(f"最小的5个面积值: {[f'{a:.6f}' for a in sorted_areas[-5:]]}")

        # 按单个图斑面积过滤
        filtered_features = [feature for feature in exploded_features if feature['area'] >= min_area]
        analysis_logger.info(f"过滤后图斑数量: {len(filtered_features)}")

        return filtered_features
    
    def _write_shapefile(self, features, output_path, change_type):
        """写入Shapefile"""
        if not features:
            analysis_logger.warning(f"没有{change_type}数据需要输出")
            return

        # 创建输出数据源
        driver = ogr.GetDriverByName("ESRI Shapefile")

        # 如果文件已存在，删除它
        if os.path.exists(output_path):
            driver.DeleteDataSource(output_path)

        datasource = driver.CreateDataSource(output_path)

        # 使用与输入数据相同的坐标系（保持转换后的投影坐标系）
        srs = osr.SpatialReference()
        if hasattr(self, '_target_srs') and self._target_srs:
            srs = self._target_srs.Clone()
            analysis_logger.info(f"输出文件使用坐标系: {srs.GetName()}")
        else:
            # 回退到适合的投影坐标系而不是WGS84
            srs.ImportFromEPSG(32649)  # WGS84 UTM 49N，适合中国东部
            analysis_logger.warning("使用默认UTM投影坐标系输出")

        # 创建图层
        layer = datasource.CreateLayer(change_type, srs, ogr.wkbPolygon)
        
        # 添加字段
        layer.CreateField(ogr.FieldDefn("area", ogr.OFTReal))
        layer.CreateField(ogr.FieldDefn("change_type", ogr.OFTString))
        
        # 添加原始属性字段（如果有）
        if features and features[0]['attributes']:
            for attr_name in features[0]['attributes'].keys():
                field_def = ogr.FieldDefn(str(attr_name)[:10], ogr.OFTString)  # 限制字段名长度
                layer.CreateField(field_def)
        
        # 写入要素
        for feature_data in features:
            # 创建要素
            feature = ogr.Feature(layer.GetLayerDefn())

            # 设置几何体
            shapely_geom = feature_data['geometry']

            # 确保几何体有效且方向正确
            if not shapely_geom.is_valid:
                analysis_logger.warning("修复无效几何体")
                shapely_geom = shapely_geom.buffer(0)

            # 确保面积为正（修复几何体方向）
            if hasattr(shapely_geom, 'exterior') and shapely_geom.area < 0:
                from shapely.geometry import Polygon
                if isinstance(shapely_geom, Polygon):
                    # 反转外环方向
                    exterior_coords = list(reversed(shapely_geom.exterior.coords))
                    holes = [list(reversed(hole.coords)) for hole in shapely_geom.interiors]
                    shapely_geom = Polygon(exterior_coords, holes)

            geom_json = mapping(shapely_geom)
            geom = ogr.CreateGeometryFromJson(json.dumps(geom_json))
            feature.SetGeometry(geom)
            
            # 重新计算面积确保正确
            corrected_area = abs(shapely_geom.area)

            # 设置属性
            feature.SetField("area", corrected_area)
            feature.SetField("change_type", change_type)
            
            # 设置原始属性
            if feature_data['attributes']:
                for attr_name, attr_value in feature_data['attributes'].items():
                    try:
                        feature.SetField(str(attr_name)[:10], str(attr_value))
                    except:
                        pass  # 忽略无法设置的字段
            
            # 添加要素到图层
            layer.CreateFeature(feature)
            feature = None
        
        # 关闭数据源
        datasource = None
        
        analysis_logger.info(f"成功输出{change_type}数据到: {output_path}")

    def _write_combined_shapefile(self, features, output_path):
        """写入合并的Shapefile，包含flowtype属性"""
        if not features:
            analysis_logger.warning("没有数据需要输出")
            return

        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                analysis_logger.info(f"创建输出目录: {output_dir}")

            # 创建输出数据源
            driver = ogr.GetDriverByName("ESRI Shapefile")
            if driver is None:
                raise Exception("无法获取ESRI Shapefile驱动")

            # 如果文件已存在，删除它
            if os.path.exists(output_path):
                try:
                    driver.DeleteDataSource(output_path)
                    analysis_logger.info(f"删除已存在的文件: {output_path}")
                except Exception as e:
                    analysis_logger.warning(f"删除已存在文件失败: {str(e)}")

            # 创建数据源
            datasource = driver.CreateDataSource(output_path)
            if datasource is None:
                raise Exception(f"无法创建数据源: {output_path}")

            analysis_logger.info(f"成功创建数据源: {output_path}")

            # 使用与输入数据相同的坐标系（保持转换后的投影坐标系）
            srs = osr.SpatialReference()
            if hasattr(self, '_target_srs') and self._target_srs:
                srs = self._target_srs.Clone()
                analysis_logger.info(f"输出文件使用坐标系: {srs.GetName()}")
            else:
                # 回退到适合的投影坐标系而不是WGS84
                srs.ImportFromEPSG(32649)  # WGS84 UTM 49N，适合中国东部
                analysis_logger.warning("使用默认UTM投影坐标系输出")

            # 创建图层
            layer = datasource.CreateLayer("changes", srs, ogr.wkbPolygon)

            # 添加字段
            layer.CreateField(ogr.FieldDefn("area", ogr.OFTReal))
            layer.CreateField(ogr.FieldDefn("flowtype", ogr.OFTString))  # "out"=流出, "in"=流入
            layer.CreateField(ogr.FieldDefn("change_type", ogr.OFTString))  # 保留原有字段

            # 添加原始属性字段（如果有）
            if features and features[0]['attributes']:
                for attr_name in features[0]['attributes'].keys():
                    field_def = ogr.FieldDefn(str(attr_name)[:10], ogr.OFTString)  # 限制字段名长度
                    layer.CreateField(field_def)

            # 写入要素
            for feature_data in features:
                # 创建要素
                feature = ogr.Feature(layer.GetLayerDefn())

                # 设置几何体
                shapely_geom = feature_data['geometry']

                # 确保几何体有效且方向正确
                if not shapely_geom.is_valid:
                    analysis_logger.warning("修复无效几何体")
                    shapely_geom = shapely_geom.buffer(0)

                # 确保面积为正（修复几何体方向）
                if hasattr(shapely_geom, 'exterior') and shapely_geom.area < 0:
                    from shapely.geometry import Polygon
                    if isinstance(shapely_geom, Polygon):
                        # 反转外环方向
                        exterior_coords = list(reversed(shapely_geom.exterior.coords))
                        holes = [list(reversed(hole.coords)) for hole in shapely_geom.interiors]
                        shapely_geom = Polygon(exterior_coords, holes)

                geom_json = mapping(shapely_geom)
                geom = ogr.CreateGeometryFromJson(json.dumps(geom_json))
                feature.SetGeometry(geom)

                # 重新计算面积确保正确
                corrected_area = abs(shapely_geom.area)

                # 设置属性
                feature.SetField("area", corrected_area)
                feature.SetField("flowtype", feature_data['flowtype'])

                # 设置change_type字段（根据flowtype）
                change_type = "inflow" if feature_data['flowtype'] == "in" else "outflow"
                feature.SetField("change_type", change_type)

                # 设置原始属性
                if feature_data['attributes']:
                    for attr_name, attr_value in feature_data['attributes'].items():
                        try:
                            feature.SetField(str(attr_name)[:10], str(attr_value))
                        except:
                            pass  # 忽略无法设置的字段

                # 添加要素到图层
                layer.CreateFeature(feature)
                feature = None

            # 关闭数据源
            datasource = None

            analysis_logger.info(f"成功输出合并数据到: {output_path}")

        except Exception as e:
            error_msg = f"写入合并Shapefile失败: {str(e)}"
            analysis_logger.error(error_msg)
            analysis_logger.error(f"输出路径: {output_path}")
            analysis_logger.error(f"要素数量: {len(features) if features else 0}")
            raise Exception(error_msg)

    def extract_image_valid_extent(self, image_path: str, output_path: str,
                                 simplify_tolerance: float = 1.0,
                                 min_area: float = 1000.0,
                                 keep_original_crs: bool = True) -> dict:
        """
        提取TIF影像的有效数据范围，输出Shapefile

        参数:
            image_path: 输入TIF影像路径
            output_path: 输出Shapefile路径
            simplify_tolerance: 简化容差，单位为米
            min_area: 最小面积阈值，单位为平方米
            keep_original_crs: 是否保持原始坐标系，默认True

        返回:
            dict: 处理结果信息
        """
        try:
            analysis_logger.info(f"开始提取影像有效数据范围: {image_path}")

            # 打开影像文件
            analysis_logger.info("步骤1: 打开影像文件...")
            dataset = gdal.Open(image_path)
            if dataset is None:
                raise Exception(f"无法打开影像文件: {image_path}")

            # 获取影像信息
            analysis_logger.info("步骤2: 获取影像信息...")
            width = dataset.RasterXSize
            height = dataset.RasterYSize
            bands = dataset.RasterCount
            geotransform = dataset.GetGeoTransform()
            projection = dataset.GetProjection()

            analysis_logger.info(f"影像尺寸: {width}x{height}, 波段数: {bands}")
            analysis_logger.info(f"地理变换参数: {geotransform}")
            analysis_logger.info(f"投影信息: {projection[:100]}...")  # 只显示前100个字符

            # 创建有效数据掩膜
            analysis_logger.info("步骤3: 创建有效数据掩膜...")
            valid_mask = self._create_valid_data_mask(dataset)

            # 计算像素面积阈值
            analysis_logger.info("步骤4: 计算像素面积阈值...")
            pixel_size_x = abs(geotransform[1])  # 像素X方向分辨率
            pixel_size_y = abs(geotransform[5])  # 像素Y方向分辨率
            pixel_area = pixel_size_x * pixel_size_y  # 单个像素面积（平方米）
            min_area_pixels = max(1, int(min_area / pixel_area))  # 转换为像素数量

            analysis_logger.info(f"像素分辨率: {pixel_size_x:.2f}m x {pixel_size_y:.2f}m")
            analysis_logger.info(f"单个像素面积: {pixel_area:.6f}平方米")
            analysis_logger.info(f"最小面积阈值: {min_area}平方米 = {min_area_pixels}像素")

            # 提取有效数据轮廓
            analysis_logger.info("步骤5: 提取有效数据轮廓...")
            contours = self._extract_contours(valid_mask, min_area_pixels)

            if not contours:
                analysis_logger.warning("未找到有效数据区域")
                return {
                    'success': False,
                    'message': '未找到有效数据区域',
                    'valid_area': 0,
                    'contour_count': 0
                }

            analysis_logger.info(f"找到 {len(contours)} 个有效轮廓")

            # 转换为地理坐标
            analysis_logger.info("步骤6: 转换为地理坐标...")
            geo_polygons = self._pixel_to_geo_polygons(contours, geotransform)
            analysis_logger.info(f"转换得到 {len(geo_polygons)} 个地理坐标多边形")

            # 简化几何体
            if simplify_tolerance > 0:
                analysis_logger.info("步骤7: 简化几何体...")
                original_count = sum([len(poly.exterior.coords) for poly in geo_polygons])
                geo_polygons = [poly.simplify(simplify_tolerance) for poly in geo_polygons]
                simplified_count = sum([len(poly.exterior.coords) for poly in geo_polygons])
                analysis_logger.info(f"简化前顶点数: {original_count}, 简化后顶点数: {simplified_count}")

            # 确定输出坐标系和多边形
            if keep_original_crs:
                analysis_logger.info("步骤8: 保持原始坐标系...")
                output_polygons = geo_polygons
                output_projection = projection

                # 获取坐标系信息
                srs = osr.SpatialReference()
                srs.ImportFromWkt(projection)
                crs_name = srs.GetAttrValue('PROJCS') or srs.GetAttrValue('GEOGCS') or "Unknown"
                analysis_logger.info(f"保持原始坐标系: {crs_name}")
            else:
                analysis_logger.info("步骤8: 转换坐标系到WGS84...")
                output_polygons = self._transform_to_wgs84(geo_polygons, projection)
                output_projection = 'GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223563]],PRIMEM["Greenwich",0],UNIT["degree",0.0174532925199433]]'
                analysis_logger.info(f"坐标转换完成，得到 {len(output_polygons)} 个WGS84多边形")

            # 保存为Shapefile
            analysis_logger.info("步骤9: 保存为Shapefile...")
            self._save_extent_shapefile_with_projection(output_polygons, output_path, output_projection)

            # 计算统计信息
            total_area = sum([poly.area for poly in output_polygons])

            # 获取坐标系描述
            if keep_original_crs:
                srs = osr.SpatialReference()
                srs.ImportFromWkt(projection)
                crs_description = srs.GetAttrValue('PROJCS') or srs.GetAttrValue('GEOGCS') or "原始坐标系"
                epsg_code = srs.GetAttrValue('AUTHORITY', 1) if srs.GetAttrValue('AUTHORITY') else "Unknown"
                coordinate_system = f"{crs_description} (EPSG:{epsg_code})" if epsg_code != "Unknown" else crs_description
            else:
                coordinate_system = 'WGS84 (EPSG:4326)'

            analysis_logger.info(f"影像范围提取完成，输出: {output_path}")
            analysis_logger.info(f"有效区域数量: {len(output_polygons)}, 总面积: {total_area:.6f}")
            analysis_logger.info(f"输出坐标系: {coordinate_system}")

            return {
                'success': True,
                'message': '影像有效范围提取完成',
                'output_path': output_path,
                'valid_area': total_area,
                'contour_count': len(output_polygons),
                'coordinate_system': coordinate_system
            }

        except Exception as e:
            error_msg = f"影像范围提取失败: {str(e)}"
            analysis_logger.error(error_msg)
            analysis_logger.error(f"错误类型: {type(e).__name__}")
            analysis_logger.error(f"错误详情: {str(e)}")

            # 添加更多调试信息
            import traceback
            analysis_logger.error(f"完整错误堆栈:\n{traceback.format_exc()}")

            return {
                'success': False,
                'message': error_msg,
                'error': str(e),
                'error_type': type(e).__name__
            }
        finally:
            if 'dataset' in locals():
                dataset = None

    def _create_valid_data_mask(self, dataset):
        """创建有效数据掩膜"""
        try:
            analysis_logger.info("开始创建有效数据掩膜...")

            # 读取所有波段数据
            bands_data = []
            nodata_values = []

            analysis_logger.info(f"读取 {dataset.RasterCount} 个波段的数据...")
            for i in range(1, dataset.RasterCount + 1):
                analysis_logger.info(f"读取波段 {i}...")
                band = dataset.GetRasterBand(i)
                data = band.ReadAsArray()
                nodata = band.GetNoDataValue()

                analysis_logger.info(f"波段 {i}: 数据类型={data.dtype}, 形状={data.shape}, NoData值={nodata}")
                analysis_logger.info(f"波段 {i}: 最小值={np.min(data)}, 最大值={np.max(data)}, 平均值={np.mean(data):.2f}")

                bands_data.append(data)
                nodata_values.append(nodata)

            # 创建有效数据掩膜
            height, width = bands_data[0].shape
            analysis_logger.info(f"创建掩膜，尺寸: {height}x{width}")
            valid_mask = np.ones((height, width), dtype=bool)

            for i, (data, nodata) in enumerate(zip(bands_data, nodata_values)):
                analysis_logger.info(f"处理波段 {i+1} 的掩膜...")

                if nodata is not None:
                    # 排除NoData值
                    nodata_count = np.sum(data == nodata)
                    valid_mask &= (data != nodata)
                    analysis_logger.info(f"波段 {i+1}: 排除 {nodata_count} 个NoData像素")

                # 排除常见的无效值
                zero_count = np.sum(data == 0)
                valid_mask &= (data != 0)  # 排除0值
                analysis_logger.info(f"波段 {i+1}: 排除 {zero_count} 个零值像素")

                max255_count = np.sum(data == 255)
                valid_mask &= (data != 255)  # 排除255值（8位图像的最大值）
                analysis_logger.info(f"波段 {i+1}: 排除 {max255_count} 个255值像素")

                nan_count = np.sum(np.isnan(data))
                valid_mask &= ~np.isnan(data)  # 排除NaN值
                analysis_logger.info(f"波段 {i+1}: 排除 {nan_count} 个NaN像素")

            valid_pixel_count = np.sum(valid_mask)
            total_pixels = valid_mask.size
            valid_percentage = valid_pixel_count / total_pixels * 100

            analysis_logger.info(f"有效像素数量: {valid_pixel_count}/{total_pixels} ({valid_percentage:.2f}%)")

            if valid_pixel_count == 0:
                raise Exception("没有找到任何有效像素，请检查影像数据")

            return valid_mask.astype(np.uint8)

        except Exception as e:
            analysis_logger.error(f"创建有效数据掩膜失败: {str(e)}")
            analysis_logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            analysis_logger.error(f"错误堆栈:\n{traceback.format_exc()}")
            raise

    def _extract_contours(self, mask, min_area_pixels=1000):
        """提取轮廓"""
        try:
            analysis_logger.info(f"开始提取轮廓，最小面积阈值: {min_area_pixels} 像素")
            analysis_logger.info(f"输入掩膜形状: {mask.shape}, 数据类型: {mask.dtype}")
            analysis_logger.info(f"掩膜中有效像素数量: {np.sum(mask > 0)}")

            # 使用形态学操作清理掩膜
            analysis_logger.info("执行形态学操作清理掩膜...")
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            mask_cleaned = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
            mask_cleaned = cv2.morphologyEx(mask_cleaned, cv2.MORPH_OPEN, kernel)

            analysis_logger.info(f"清理后有效像素数量: {np.sum(mask_cleaned > 0)}")

            # 查找轮廓
            analysis_logger.info("查找轮廓...")
            contours, _ = cv2.findContours(mask_cleaned, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            analysis_logger.info(f"找到 {len(contours)} 个原始轮廓")

            # 过滤小面积轮廓
            analysis_logger.info("过滤小面积轮廓...")
            filtered_contours = []
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                analysis_logger.info(f"轮廓 {i+1}: 面积 = {area:.2f} 像素")
                if area >= min_area_pixels:
                    filtered_contours.append(contour)
                    analysis_logger.info(f"轮廓 {i+1}: 保留（面积 >= {min_area_pixels}）")
                else:
                    analysis_logger.info(f"轮廓 {i+1}: 丢弃（面积 < {min_area_pixels}）")

            analysis_logger.info(f"过滤后保留 {len(filtered_contours)} 个有效轮廓")

            if len(filtered_contours) == 0:
                analysis_logger.warning("没有找到满足面积阈值的轮廓")

            return filtered_contours

        except Exception as e:
            analysis_logger.error(f"提取轮廓失败: {str(e)}")
            analysis_logger.error(f"错误类型: {type(e).__name__}")
            import traceback
            analysis_logger.error(f"错误堆栈:\n{traceback.format_exc()}")
            raise

    def _pixel_to_geo_polygons(self, contours, geotransform):
        """将像素坐标轮廓转换为地理坐标多边形"""
        try:
            polygons = []

            for contour in contours:
                # 简化轮廓
                epsilon = 0.002 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)

                # 转换为地理坐标
                geo_coords = []
                for point in approx:
                    x_pixel, y_pixel = point[0]

                    # 像素坐标转地理坐标
                    x_geo = geotransform[0] + x_pixel * geotransform[1] + y_pixel * geotransform[2]
                    y_geo = geotransform[3] + x_pixel * geotransform[4] + y_pixel * geotransform[5]

                    geo_coords.append((x_geo, y_geo))

                # 确保多边形闭合
                if len(geo_coords) >= 3:
                    if geo_coords[0] != geo_coords[-1]:
                        geo_coords.append(geo_coords[0])

                    # 创建Shapely多边形
                    polygon = Polygon(geo_coords)
                    if polygon.is_valid and polygon.area > 0:
                        polygons.append(polygon)

            analysis_logger.info(f"转换了 {len(polygons)} 个有效多边形")
            return polygons

        except Exception as e:
            analysis_logger.error(f"坐标转换失败: {str(e)}")
            raise

    def _transform_to_wgs84(self, polygons, source_projection):
        """将多边形转换到WGS84坐标系"""
        try:
            # 创建源坐标系
            source_srs = osr.SpatialReference()
            source_srs.ImportFromWkt(source_projection)

            # 创建目标坐标系 (WGS84)
            target_srs = osr.SpatialReference()
            target_srs.ImportFromEPSG(4326)

            # 创建坐标转换
            transform = osr.CoordinateTransformation(source_srs, target_srs)

            wgs84_polygons = []

            for polygon in polygons:
                # 获取外环坐标
                coords = list(polygon.exterior.coords)

                # 转换坐标
                transformed_coords = []
                for x, y in coords:
                    # 执行坐标转换
                    point = ogr.Geometry(ogr.wkbPoint)
                    point.AddPoint(x, y)
                    point.Transform(transform)

                    transformed_coords.append((point.GetX(), point.GetY()))

                # 创建转换后的多边形
                if len(transformed_coords) >= 3:
                    wgs84_polygon = Polygon(transformed_coords)
                    if wgs84_polygon.is_valid:
                        wgs84_polygons.append(wgs84_polygon)

            analysis_logger.info(f"坐标转换完成，得到 {len(wgs84_polygons)} 个WGS84多边形")
            return wgs84_polygons

        except Exception as e:
            analysis_logger.error(f"坐标系转换失败: {str(e)}")
            raise

    def _save_extent_shapefile(self, polygons, output_path):
        """保存范围Shapefile"""
        try:
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 创建Shapefile驱动
            driver = ogr.GetDriverByName("ESRI Shapefile")

            # 如果文件已存在，删除它
            if os.path.exists(output_path):
                driver.DeleteDataSource(output_path)

            # 创建数据源
            datasource = driver.CreateDataSource(output_path)

            # 创建WGS84坐标系
            srs = osr.SpatialReference()
            srs.ImportFromEPSG(4326)

            # 创建图层
            layer = datasource.CreateLayer("extent", srs, ogr.wkbPolygon)

            # 添加字段
            layer.CreateField(ogr.FieldDefn("id", ogr.OFTInteger))
            layer.CreateField(ogr.FieldDefn("area", ogr.OFTReal))
            layer.CreateField(ogr.FieldDefn("perimeter", ogr.OFTReal))

            # 写入要素
            for i, polygon in enumerate(polygons):
                # 创建要素
                feature = ogr.Feature(layer.GetLayerDefn())

                # 设置属性
                feature.SetField("id", i + 1)
                feature.SetField("area", polygon.area)
                feature.SetField("perimeter", polygon.length)

                # 设置几何体
                wkt = polygon.wkt
                geom = ogr.CreateGeometryFromWkt(wkt)
                feature.SetGeometry(geom)

                # 添加要素到图层
                layer.CreateFeature(feature)

                # 清理
                feature = None
                geom = None

            # 清理
            datasource = None

            analysis_logger.info(f"成功保存Shapefile: {output_path}")
            analysis_logger.info(f"包含 {len(polygons)} 个多边形要素")

            return {
                'success': True,
                'output_path': output_path,
                'feature_count': len(polygons)
            }

        except Exception as e:
            analysis_logger.error(f"保存Shapefile失败: {str(e)}")
            raise

    def _save_extent_shapefile_with_projection(self, polygons, output_path, projection_wkt):
        """保存范围Shapefile，支持指定投影"""
        try:
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            if output_dir and not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 创建Shapefile驱动
            driver = ogr.GetDriverByName("ESRI Shapefile")

            # 如果文件已存在，删除它
            if os.path.exists(output_path):
                driver.DeleteDataSource(output_path)

            # 创建数据源
            datasource = driver.CreateDataSource(output_path)

            # 创建坐标系
            srs = osr.SpatialReference()
            srs.ImportFromWkt(projection_wkt)

            # 创建图层
            layer = datasource.CreateLayer("extent", srs, ogr.wkbPolygon)

            # 添加字段
            layer.CreateField(ogr.FieldDefn("id", ogr.OFTInteger))
            layer.CreateField(ogr.FieldDefn("area", ogr.OFTReal))
            layer.CreateField(ogr.FieldDefn("perimeter", ogr.OFTReal))

            # 写入要素
            for i, polygon in enumerate(polygons):
                # 创建要素
                feature = ogr.Feature(layer.GetLayerDefn())

                # 设置属性
                feature.SetField("id", i + 1)
                feature.SetField("area", polygon.area)
                feature.SetField("perimeter", polygon.length)

                # 设置几何体
                wkt = polygon.wkt
                geom = ogr.CreateGeometryFromWkt(wkt)
                feature.SetGeometry(geom)

                # 添加要素到图层
                layer.CreateFeature(feature)

                # 清理
                feature = None
                geom = None

            # 清理
            datasource = None

            analysis_logger.info(f"成功保存Shapefile: {output_path}")
            analysis_logger.info(f"包含 {len(polygons)} 个多边形要素")
            analysis_logger.info(f"坐标系: {srs.GetAttrValue('PROJCS') or srs.GetAttrValue('GEOGCS') or 'Unknown'}")

            return {
                'success': True,
                'output_path': output_path,
                'feature_count': len(polygons)
            }

        except Exception as e:
            analysis_logger.error(f"保存Shapefile失败: {str(e)}")
            raise


# 创建全局实例
analysis_processor = AnalysisProcessor()
