#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的状态保存功能
"""

import requests
import time
import json
import os

def test_status_saving():
    """测试状态保存功能"""
    print("🧪 测试影像范围提取的状态保存功能...")
    
    # 测试参数
    base_url = "http://127.0.0.1:8091/api/analysis"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    try:
        print(f"\n📁 测试文件: {test_image_path}")
        
        # 1. 启动任务
        print("\n1️⃣ 启动影像范围提取任务...")
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 2. 立即检查状态文件
            print(f"\n2️⃣ 检查analysis_status.json文件...")
            status_file_path = "geoserver_api/logs/analysislog/analysis_status.json"
            
            if os.path.exists(status_file_path):
                with open(status_file_path, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
                
                if task_id in status_data:
                    task_info = status_data[task_id]
                    print(f"✅ 任务已保存到状态文件")
                    print(f"📊 状态: {task_info.get('status', 'unknown')}")
                    print(f"📝 消息: {task_info.get('message', 'no message')}")
                    print(f"📈 进度: {task_info.get('progress', 0)}%")
                    print(f"📁 参数: {task_info.get('parameters', {})}")
                else:
                    print(f"❌ 任务ID {task_id} 未在状态文件中找到")
                    print(f"📋 状态文件中的任务ID: {list(status_data.keys())[-5:]}")  # 显示最后5个任务ID
            else:
                print(f"❌ 状态文件不存在: {status_file_path}")
            
            # 3. 通过API查询状态
            print(f"\n3️⃣ 通过API查询任务状态...")
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            if status_response.status_code == 200:
                api_status = status_response.json()['data']
                print(f"✅ API状态查询成功")
                print(f"📊 API状态: {api_status.get('task_status', 'unknown')}")
                print(f"📝 API消息: {api_status.get('message', 'no message')}")
                print(f"📈 API进度: {api_status.get('progress', 0)}%")
            else:
                print(f"❌ API状态查询失败: {status_response.status_code}")
                print(f"错误信息: {status_response.text}")
            
            # 4. 监控任务执行
            print(f"\n4️⃣ 监控任务执行...")
            max_wait_time = 60  # 最大等待1分钟
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                try:
                    status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()['data']
                        task_status = status_data['task_status']
                        message = status_data.get('message', '')
                        progress = status_data.get('progress', 0)
                        
                        print(f"📊 状态更新: {task_status} ({progress}%) - {message}")
                        
                        if task_status in ['完成', '失败']:
                            print(f"\n✅ 任务结束，最终状态: {task_status}")
                            
                            # 再次检查状态文件
                            print(f"\n5️⃣ 检查最终状态文件...")
                            if os.path.exists(status_file_path):
                                with open(status_file_path, 'r', encoding='utf-8') as f:
                                    final_status_data = json.load(f)
                                
                                if task_id in final_status_data:
                                    final_task_info = final_status_data[task_id]
                                    print(f"✅ 最终状态已保存")
                                    print(f"📊 最终状态: {final_task_info.get('status', 'unknown')}")
                                    print(f"📝 最终消息: {final_task_info.get('message', 'no message')}")
                                    print(f"📈 最终进度: {final_task_info.get('progress', 0)}%")
                                    
                                    if 'result' in final_task_info:
                                        print(f"📋 结果信息: {final_task_info['result']}")
                                    
                                    if 'error' in final_task_info:
                                        print(f"❌ 错误信息: {final_task_info['error']}")
                                else:
                                    print(f"❌ 最终状态未保存到文件")
                            
                            return task_status == '完成'
                        
                        time.sleep(3)
                    else:
                        print(f"❌ 状态查询失败: {status_response.status_code}")
                        return False
                        
                except Exception as e:
                    print(f"❌ 状态查询异常: {e}")
                    time.sleep(5)
                    continue
            
            print("\n⏰ 任务监控超时")
            return False
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def check_status_file_structure():
    """检查状态文件结构"""
    print("\n🔍 检查状态文件结构...")
    
    status_file_path = "geoserver_api/logs/analysislog/analysis_status.json"
    
    if os.path.exists(status_file_path):
        try:
            with open(status_file_path, 'r', encoding='utf-8') as f:
                status_data = json.load(f)
            
            print(f"✅ 状态文件存在，包含 {len(status_data)} 个任务")
            
            # 显示最近的几个任务
            recent_tasks = list(status_data.keys())[-3:]
            print(f"📋 最近的任务ID: {recent_tasks}")
            
            # 检查任务结构
            if recent_tasks:
                sample_task = status_data[recent_tasks[-1]]
                print(f"\n📊 任务结构示例:")
                for key, value in sample_task.items():
                    if isinstance(value, dict):
                        print(f"  {key}: {type(value).__name__} (包含 {len(value)} 个字段)")
                    else:
                        print(f"  {key}: {type(value).__name__}")
            
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ 状态文件JSON格式错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")
            return False
    else:
        print(f"❌ 状态文件不存在: {status_file_path}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试状态保存功能")
    
    # 检查状态文件结构
    file_ok = check_status_file_structure()
    
    if file_ok:
        # 测试状态保存
        success = test_status_saving()
        
        if success:
            print("\n🎉 状态保存功能测试通过！")
        else:
            print("\n❌ 状态保存功能测试失败")
    else:
        print("\n❌ 状态文件检查失败")
    
    print("\n📖 修复说明:")
    print("1. 添加了任务参数保存到analysis_status.json")
    print("2. 修复了任务状态更新机制")
    print("3. 确保任务状态与其他接口保持一致")
    print("4. 添加了完整的错误信息记录")
    print("5. 现在可以通过/api/analysis/status/查询任务状态")
