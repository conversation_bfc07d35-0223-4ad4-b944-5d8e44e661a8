#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基础API视图模块

功能说明:
- 健康检查端点：检查服务状态
- 坐标查询端点：查询指定坐标点的栅格图层数据
- 图层相交查询端点：查询图层之间的边界框相交情况

开发者注意:
- 这些是系统的核心查询功能
- 健康检查用于监控服务状态
- 坐标查询是地理数据查询的基础功能
- 图层相交查询用于空间分析
"""

import logging
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

# 导入核心模块
from ..core.manager import GeoServerManager, logger
from ..core.raster_query import GeoServerRasterQuery

# 初始化查询和管理器实例
manager = GeoServerManager()
query = GeoServerRasterQuery(manager)


@api_view(['GET'])
def health_check(request):
    """
    健康检查端点
    
    功能: 检查GeoServer Django API服务状态
    返回: 服务状态信息
    """
    return Response({
        'status': 'ok',
        'message': 'GeoServer Django API服务正在运行'
    })


@api_view(['GET'])
def query_coordinate(request):
    """
    查询坐标点处的栅格图层
    
    功能: 查询指定经纬度坐标点在指定工作区中有有效数据的栅格图层
    
    查询参数:
        lat: 纬度 (浮点数)
        lon: 经度 (浮点数)
        workspace: 工作区名称 (字符串)

    返回:
        包含该坐标点有效数据的图层信息列表
    """
    try:
        # 获取查询参数
        lat_str = request.GET.get('lat')
        lon_str = request.GET.get('lon')
        workspace = request.GET.get('workspace')

        # 验证必要参数
        if not lat_str or not lon_str or not workspace:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 转换坐标参数
        try:
            lat = float(lat_str)
            lon = float(lon_str)
        except ValueError:
            return Response({
                'status': 'error',
                'message': '坐标参数必须是有效的数字'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"查询坐标点: lat={lat}, lon={lon}, workspace={workspace}")

        # 执行查询
        result_layers = query.query_point_with_values(lat, lon, workspace)

        # 检查是否找到有效图层
        if not result_layers:
            return Response({
                'status': 'success',
                'message': f'在坐标点 ({lat}, {lon}) 的工作区 {workspace} 中未找到有有效数据的栅格图层',
                'count': 0,
                'layers': []
            })

        return Response({
            'status': 'success',
            'count': len(result_layers),
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layers': result_layers
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_layer_intersections(request):
    """
    查询指定栅格图层与同一工作空间其他栅格图层的边界框相交情况
    
    功能: 分析图层之间的空间相交关系，计算重叠面积和百分比
    
    查询参数:
        workspace: 工作区名称 (必选)
        layer: 图层名称 (必选)

    返回:
        与查询图层相交的其他图层信息，包括相交区域和重叠百分比
    """
    try:
        # 获取查询参数
        workspace = request.GET.get('workspace')
        layer_name = request.GET.get('layer')

        # 验证必要参数
        if not workspace:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: workspace'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not layer_name:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: layer'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"查询图层相交情况: workspace={workspace}, layer={layer_name}")

        # 只获取工作区中的栅格图层
        raster_layers = query.get_raster_layers(workspace)

        logger.info(f"在工作区 {workspace} 中找到 {len(raster_layers)} 个栅格图层")

        if not raster_layers:
            return Response({
                'status': 'error',
                'message': f'工作区 {workspace} 中没有找到栅格图层'
            }, status=status.HTTP_404_NOT_FOUND)

        # 查找目标图层
        target_layer = None
        for layer in raster_layers:
            layer_name_str = str(layer.get("name", ""))
            logger.info(f"检查图层: {layer_name_str} (类型: {type(layer.get('name'))})")
            # 比较时都转换为字符串
            if layer_name_str == str(layer_name):
                target_layer = layer
                break

        if not target_layer:
            # 记录所有可用的图层名称以便调试
            available_layers = [str(layer["name"]) for layer in raster_layers]
            logger.warning(f"未找到图层 {layer_name}，可用图层: {available_layers}")
            return Response({
                'status': 'error',
                'message': f'在工作区 {workspace} 中未找到栅格图层 {layer_name}',
                'available_layers': available_layers
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取目标图层的边界框
        target_bbox = target_layer.get('bbox', {})
        if not target_bbox:
            return Response({
                'status': 'error',
                'message': f'图层 {layer_name} 没有边界框信息'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            target_minx = float(target_bbox.get('minx', 0))
            target_miny = float(target_bbox.get('miny', 0))
            target_maxx = float(target_bbox.get('maxx', 0))
            target_maxy = float(target_bbox.get('maxy', 0))
        except (ValueError, TypeError):
            return Response({
                'status': 'error',
                'message': f'图层 {layer_name} 的边界框数据无效'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 计算目标图层的面积
        target_area = (target_maxx - target_minx) * (target_maxy - target_miny)

        # 查找与目标图层相交的其他图层
        intersecting_layers = []

        for layer in raster_layers:
            # 跳过目标图层本身
            if str(layer.get("name", "")) == str(layer_name):
                continue

            # 获取当前图层的边界框
            bbox = layer.get('bbox', {})
            if not bbox:
                continue

            try:
                layer_minx = float(bbox.get('minx', 0))
                layer_miny = float(bbox.get('miny', 0))
                layer_maxx = float(bbox.get('maxx', 0))
                layer_maxy = float(bbox.get('maxy', 0))

                # 检查边界框是否相交
                intersection_minx = max(target_minx, layer_minx)
                intersection_miny = max(target_miny, layer_miny)
                intersection_maxx = min(target_maxx, layer_maxx)
                intersection_maxy = min(target_maxy, layer_maxy)

                # 如果有相交区域
                if intersection_minx < intersection_maxx and intersection_miny < intersection_maxy:
                    # 计算相交面积
                    intersection_area = (intersection_maxx - intersection_minx) * (intersection_maxy - intersection_miny)
                    
                    # 计算当前图层的面积
                    layer_area = (layer_maxx - layer_minx) * (layer_maxy - layer_miny)
                    
                    # 计算重叠百分比
                    overlap_percentage_target = (intersection_area / target_area) * 100 if target_area > 0 else 0
                    overlap_percentage_layer = (intersection_area / layer_area) * 100 if layer_area > 0 else 0

                    layer_info = {
                        'name': layer['name'],
                        'store': layer['store'],
                        'workspace': layer['workspace'],
                        'type': 'raster',
                        'bbox': {
                            'minx': layer_minx,
                            'miny': layer_miny,
                            'maxx': layer_maxx,
                            'maxy': layer_maxy
                        },
                        'intersection': {
                            'bbox': {
                                'minx': intersection_minx,
                                'miny': intersection_miny,
                                'maxx': intersection_maxx,
                                'maxy': intersection_maxy
                            },
                            'area': intersection_area,
                            'overlap_percentage_with_target': round(overlap_percentage_target, 2),
                            'overlap_percentage_with_self': round(overlap_percentage_layer, 2)
                        }
                    }

                    # 添加图层ID（如果存在）
                    if 'id' in layer:
                        layer_info['id'] = layer['id']

                    intersecting_layers.append(layer_info)

            except (ValueError, TypeError) as e:
                logger.warning(f"处理图层 {layer['name']} 的边界框时出错: {str(e)}")
                continue

        # 按重叠面积降序排序
        intersecting_layers.sort(key=lambda x: x['intersection']['area'], reverse=True)

        return Response({
            'status': 'success',
            'target_layer': {
                'name': target_layer['name'],
                'store': target_layer['store'],
                'workspace': target_layer['workspace'],
                'type': 'raster',
                'bbox': {
                    'minx': target_minx,
                    'miny': target_miny,
                    'maxx': target_maxx,
                    'maxy': target_maxy
                }
            },
            'intersecting_layers_count': len(intersecting_layers),
            'intersecting_layers': intersecting_layers
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def query_intersecting_layers(request):
    """
    查询指定经纬度与工作空间下所有图层的相交情况

    功能: 查询指定坐标点与工作区中所有图层的相交关系

    查询参数:
        lat: 纬度 (必选)
        lon: 经度 (必选)
        workspace: 工作区名称 (必选)

    返回:
        与坐标点相交的所有图层信息，包括边界框信息
    """
    try:
        # 获取查询参数
        lat = request.GET.get('lat')
        lon = request.GET.get('lon')
        workspace = request.GET.get('workspace')

        # 验证必要参数
        if not lat or not lon or not workspace:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: lat, lon, workspace'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            lat = float(lat)
            lon = float(lon)
        except ValueError:
            return Response({
                'status': 'error',
                'message': '纬度和经度必须是有效的数字'
            }, status=status.HTTP_400_BAD_REQUEST)

        logger.info(f"查询坐标点与图层相交情况: lat={lat}, lon={lon}, workspace={workspace}")

        # 获取工作区中的所有栅格图层
        raster_layers = query.get_raster_layers(workspace)

        logger.info(f"在工作区 {workspace} 中找到 {len(raster_layers)} 个栅格图层")

        if not raster_layers:
            return Response({
                'status': 'success',
                'message': f'工作区 {workspace} 中没有找到栅格图层',
                'count': 0,
                'coordinates': {'lat': lat, 'lon': lon},
                'workspace': workspace,
                'layers': []
            })

        # 筛选出与坐标点相交的图层
        intersecting_layers = []

        for layer in raster_layers:
            bbox = layer.get('bbox', {})

            # 检查图层是否有边界框信息
            if not bbox:
                logger.warning(f"图层 '{layer['name']}' 没有边界框信息，跳过")
                continue

            # 检查坐标点是否在边界框内
            minx = bbox.get('minx')
            miny = bbox.get('miny')
            maxx = bbox.get('maxx')
            maxy = bbox.get('maxy')

            if all(coord is not None for coord in [minx, miny, maxx, maxy]):
                point_inside_bbox = minx <= lon <= maxx and miny <= lat <= maxy

                if point_inside_bbox:
                    intersecting_layers.append({
                        'name': layer['name'],
                        'store': layer.get('store', ''),
                        'workspace': layer.get('workspace', workspace),
                        'type': 'raster',
                        'bbox': bbox,
                        'point_inside_bbox': True
                    })
                    logger.info(f"坐标点 ({lat}, {lon}) 与图层 '{layer['name']}' 相交")
            else:
                logger.warning(f"图层 '{layer['name']}' 的边界框信息不完整")

        return Response({
            'status': 'success',
            'count': len(intersecting_layers),
            'coordinates': {'lat': lat, 'lon': lon},
            'workspace': workspace,
            'layers': intersecting_layers
        })

    except Exception as e:
        logger.error(f"API错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
