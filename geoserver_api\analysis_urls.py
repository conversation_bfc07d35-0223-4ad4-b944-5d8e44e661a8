#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
地理空间分析模块URL配置

路由说明:
- /api/analysis/spatial-changes/ - 空间数据变化分析
- /api/analysis/status/ - 查询分析任务状态
- /api/analysis/logs/ - 获取分析日志
- /api/analysis/ai-semantic-segmentation/ - AI语义分割
- /api/analysis/ai-batch-processing/ - AI批量处理
- /api/analysis/ai-models-info/ - AI模型信息
- /api/analysis/weight-info/ - 获取AI模型权重信息
- /api/analysis/weight-config/ - 获取权重配置信息
- /api/analysis/combined-ai-spatial-analysis/ - 合并AI分析和空间变化分析
- /api/analysis/image-extent-extraction/ - 影像有效范围提取
- /api/analysis/taskinfo/ - 根据ID获取TaskInfo.json内容
"""

from django.urls import path
from .views.analysis import analysis_views

# 尝试导入AI分析视图
try:
    from .views.analysis import ai_analysis_views
    AI_VIEWS_AVAILABLE = True
except ImportError:
    AI_VIEWS_AVAILABLE = False

# 尝试导入权重信息视图
try:
    from .views.analysis import weight_views
    WEIGHT_VIEWS_AVAILABLE = True
except ImportError:
    WEIGHT_VIEWS_AVAILABLE = False

# 尝试导入合并分析视图
try:
    from .views.analysis import combined_analysis_views
    COMBINED_VIEWS_AVAILABLE = True
except ImportError:
    COMBINED_VIEWS_AVAILABLE = False

# 尝试导入影像范围提取视图
try:
    from .views.analysis import image_extent_views
    IMAGE_EXTENT_VIEWS_AVAILABLE = True
except ImportError:
    IMAGE_EXTENT_VIEWS_AVAILABLE = False

urlpatterns = [
    # 空间数据变化分析
    path('spatial-changes/', analysis_views.spatial_changes_analysis, name='spatial_changes_analysis'),

    # 任务状态查询
    path('status/', analysis_views.get_analysis_status, name='get_analysis_status'),

    # 分析日志查询
    path('logs/', analysis_views.get_analysis_logs, name='get_analysis_logs'),

    # 根据ID获取TaskInfo.json内容
    path('taskinfo/', analysis_views.get_taskinfo_by_id, name='get_taskinfo_by_id'),

    # 数据下载
    path('download-data/', analysis_views.download_analysis_data, name='download_analysis_data'),

    # 排队执行的合并分析
    path('combined-ai-spatial-analysis-queued/', analysis_views.combined_ai_spatial_analysis_queued, name='combined_ai_spatial_analysis_queued'),

    # 队列管理
    path('queue-status/', analysis_views.get_queue_status, name='get_queue_status'),
    path('cancel-task/', analysis_views.cancel_waiting_task, name='cancel_waiting_task'),
]

# 添加AI分析路由（如果可用）
if AI_VIEWS_AVAILABLE:
    urlpatterns.extend([
        # AI语义分割
        path('ai-semantic-segmentation/', ai_analysis_views.ai_semantic_segmentation, name='ai_semantic_segmentation'),

        # AI批量处理
        path('ai-batch-processing/', ai_analysis_views.ai_batch_processing, name='ai_batch_processing'),

        # AI模型信息
        path('ai-models-info/', ai_analysis_views.ai_models_info, name='ai_models_info'),
    ])

# 添加权重信息路由（如果可用）
if WEIGHT_VIEWS_AVAILABLE:
    urlpatterns.extend([
        # 获取权重信息
        path('weight-info/', weight_views.get_weight_info, name='get_weight_info'),

        # 获取权重配置
        path('weight-config/', weight_views.get_weight_config, name='get_weight_config'),
    ])

# 添加合并分析路由（如果可用）
if COMBINED_VIEWS_AVAILABLE:
    urlpatterns.extend([
        # 合并的AI语义分割和空间变化分析
        path('combined-ai-spatial-analysis/', combined_analysis_views.combined_ai_spatial_analysis, name='combined_ai_spatial_analysis'),
    ])

# 添加影像范围提取路由（如果可用）
if IMAGE_EXTENT_VIEWS_AVAILABLE:
    urlpatterns.extend([
        # 影像有效范围提取
        path('image-extent-extraction/', image_extent_views.image_extent_extraction, name='image_extent_extraction'),
    ])
