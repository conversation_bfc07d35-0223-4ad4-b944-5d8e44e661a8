2025-08-21 18:04:18,992 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250821_180418.log
2025-08-21 18:04:18,995 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:04:18,995 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:04:19,012 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:04:19,017 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:04:19,017 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:04:19,031 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:04:19,033 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:04:19,033 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:04:19,046 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:04:19,048 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:04:19,049 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:04:19,062 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:04:19,065 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:04:19,070 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:04:19,080 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:04:19,085 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:04:19,085 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:04:19,100 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:04:19,114 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-21 18:04:19,120 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-21 18:04:22,913 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-21 18:04:22,963 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-21 18:04:22,969 - analysis_executor - INFO - 加载了 28 个任务状态
