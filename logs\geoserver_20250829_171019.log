2025-08-29 17:10:19,545 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_171019.log
2025-08-29 17:10:19,547 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:19,547 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:19,566 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:19,569 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:19,570 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:19,612 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:19,614 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:19,615 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:19,630 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:19,632 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:19,633 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:19,647 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:19,649 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:19,650 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:19,664 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:19,665 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 17:10:19,665 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 17:10:19,679 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 17:10:19,803 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 17:10:19,833 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 17:10:23,966 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 17:10:23,978 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 17:10:23,982 - analysis_executor - INFO - 加载了 28 个任务状态
