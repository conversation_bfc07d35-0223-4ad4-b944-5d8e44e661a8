#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试合并分析接口的改进功能
1. 坐标系转换 (EPSG:32648 -> EPSG:4326)
2. TaskInfo.json中的相对路径
"""

import requests
import time
import json
import os

def test_combined_analysis_improvements():
    """测试合并分析接口的改进功能"""
    print("🧪 测试合并分析接口的改进功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    image_id = "20250705171601"
    params = {
        'id': image_id,
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print(f"\n🚀 启动合并分析任务...")
        print(f"   影像ID: {image_id}")
        
        # 启动任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功: {task_id}")
            
            # 监控任务执行和验证改进功能
            success = monitor_improvements(base_url, task_id, image_id)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_improvements(base_url, task_id, image_id, max_wait=1200):
    """监控任务执行并验证改进功能"""
    print(f"\n📊 监控任务执行并验证改进功能...")
    
    start_time = time.time()
    
    # TaskInfo.json路径
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    while time.time() - start_time < max_wait:
        try:
            # 检查API状态
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                current_status = status_data['task_status']
                
                print(f"🔄 当前状态: {current_status}")
                
                # 检查是否完成
                if current_status in ['完成', '失败']:
                    print(f"\n🏁 任务结束: {current_status}")
                    
                    if current_status == '完成':
                        # 验证改进功能
                        verify_improvements(taskinfo_path, task_id, image_id)
                        return True
                    else:
                        print(f"❌ 任务失败")
                        return False
            
            time.sleep(20)  # 每20秒检查一次
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(20)
            continue
    
    print(f"\n⏰ 任务监控超时")
    return False

def verify_improvements(taskinfo_path, task_id, image_id):
    """验证改进功能"""
    print(f"\n🔍 验证改进功能...")
    
    try:
        # 1. 验证TaskInfo.json内容
        if os.path.exists(taskinfo_path):
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            target_task = None
            if isinstance(data, list):
                for task in data:
                    if task.get('task_id') == task_id:
                        target_task = task
                        break
            
            if target_task:
                print(f"📋 验证TaskInfo.json内容:")
                
                # 验证相对路径字段
                verify_relative_paths(target_task)
                
                # 验证坐标转换信息
                verify_coordinate_conversion(target_task)
                
                # 验证输出文件
                verify_output_files(target_task, image_id)
                
            else:
                print(f"❌ 未找到目标任务")
        else:
            print(f"❌ TaskInfo.json文件不存在")
            
    except Exception as e:
        print(f"❌ 验证改进功能失败: {e}")

def verify_relative_paths(task_info):
    """验证相对路径功能"""
    print(f"\n1️⃣ 验证相对路径功能:")
    
    # 检查是否有output_files_relative字段
    output_files_relative = task_info.get('output_files_relative', {})
    
    if output_files_relative:
        print(f"  ✅ 发现output_files_relative字段")
        
        # 检查相对路径内容
        ai_relative = output_files_relative.get('ai_output_path', 'N/A')
        final_relative = output_files_relative.get('final_output_path', 'N/A')
        
        print(f"  📁 AI输出相对路径: {ai_relative}")
        print(f"  📁 最终输出相对路径: {final_relative}")
        
        # 验证相对路径格式
        if ai_relative != 'N/A' and not ai_relative.startswith('D:'):
            print(f"    ✅ AI输出相对路径格式正确")
        else:
            print(f"    ❌ AI输出相对路径格式错误")
        
        if final_relative != 'N/A' and not final_relative.startswith('D:'):
            print(f"    ✅ 最终输出相对路径格式正确")
        else:
            print(f"    ❌ 最终输出相对路径格式错误")
        
        # 显示绝对路径对比
        output_files = task_info.get('output_files', {})
        ai_absolute = output_files.get('ai_output_path', 'N/A')
        final_absolute = output_files.get('final_output_path', 'N/A')
        
        print(f"\n  📊 路径对比:")
        print(f"    绝对路径: {ai_absolute}")
        print(f"    相对路径: {ai_relative}")
        print(f"    绝对路径: {final_absolute}")
        print(f"    相对路径: {final_relative}")
        
    else:
        print(f"  ❌ 缺少output_files_relative字段")

def verify_coordinate_conversion(task_info):
    """验证坐标转换功能"""
    print(f"\n2️⃣ 验证坐标转换功能:")
    
    # 检查坐标转换信息
    coord_conversion = task_info.get('coordinate_conversion', {})
    
    if coord_conversion:
        print(f"  ✅ 发现coordinate_conversion字段")
        
        success = coord_conversion.get('success', False)
        original_crs = coord_conversion.get('original_crs', 'Unknown')
        target_crs = coord_conversion.get('target_crs', 'Unknown')
        
        print(f"  🌐 转换成功: {success}")
        print(f"  📍 原始坐标系: {original_crs}")
        print(f"  📍 目标坐标系: {target_crs}")
        
        if success:
            converted_file = coord_conversion.get('converted_file', 'N/A')
            print(f"  📁 转换后文件: {converted_file}")
            
            # 检查是否转换为EPSG:4326
            if target_crs == 'EPSG:4326':
                print(f"    ✅ 成功转换为EPSG:4326")
            else:
                print(f"    ⚠️ 目标坐标系不是EPSG:4326")
        else:
            error_msg = coord_conversion.get('error_message', 'Unknown error')
            print(f"  ❌ 转换失败: {error_msg}")
    else:
        print(f"  ❌ 缺少coordinate_conversion字段")

def verify_output_files(task_info, image_id):
    """验证输出文件"""
    print(f"\n3️⃣ 验证输出文件:")
    
    output_files = task_info.get('output_files', {})
    final_output_path = output_files.get('final_output_path', '')
    
    if final_output_path:
        print(f"  📁 最终输出文件: {final_output_path}")
        
        # 检查文件是否存在
        if os.path.exists(final_output_path):
            file_size = os.path.getsize(final_output_path)
            print(f"    ✅ 文件存在，大小: {file_size} 字节")
            
            # 检查是否有4326版本的文件
            if '_4326.shp' in final_output_path:
                print(f"    ✅ 发现坐标转换后的文件")
                
                # 检查原始文件是否也存在
                original_path = final_output_path.replace('_4326.shp', '.shp')
                if os.path.exists(original_path):
                    original_size = os.path.getsize(original_path)
                    print(f"    📊 原始文件: {original_path} ({original_size} 字节)")
                    print(f"    📊 转换文件: {final_output_path} ({file_size} 字节)")
                
                # 尝试验证坐标系
                try:
                    import geopandas as gpd
                    gdf = gpd.read_file(final_output_path)
                    crs = gdf.crs
                    print(f"    🌐 文件坐标系: {crs}")
                    
                    if crs and crs.to_epsg() == 4326:
                        print(f"    ✅ 坐标系验证通过: EPSG:4326")
                    else:
                        print(f"    ⚠️ 坐标系验证失败: {crs}")
                        
                except ImportError:
                    print(f"    ⚠️ 无法验证坐标系（缺少geopandas）")
                except Exception as e:
                    print(f"    ❌ 坐标系验证失败: {e}")
            else:
                print(f"    ⚠️ 未发现坐标转换后的文件")
        else:
            print(f"    ❌ 文件不存在")
    else:
        print(f"  ❌ 缺少最终输出文件路径")

def test_taskinfo_api_with_improvements():
    """测试TaskInfo API是否包含改进功能"""
    print(f"\n4️⃣ 测试TaskInfo API:")
    
    try:
        base_url = "http://127.0.0.1:8091/api/analysis"
        image_id = "20250705171601"
        
        response = requests.get(f"{base_url}/taskinfo/?id={image_id}", timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success' and result['data']:
                data = result['data']
                
                print(f"  ✅ TaskInfo API调用成功")
                print(f"  📊 任务数量: {len(data)}")
                
                # 检查最新任务的改进功能
                if data:
                    latest_task = data[-1]  # 最新任务
                    
                    # 检查相对路径
                    if 'output_files_relative' in latest_task:
                        print(f"  ✅ API返回包含相对路径")
                    else:
                        print(f"  ❌ API返回缺少相对路径")
                    
                    # 检查坐标转换信息
                    if 'coordinate_conversion' in latest_task:
                        print(f"  ✅ API返回包含坐标转换信息")
                    else:
                        print(f"  ❌ API返回缺少坐标转换信息")
                
            else:
                print(f"  ❌ API返回数据为空")
        else:
            print(f"  ❌ API调用失败: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 测试TaskInfo API失败: {e}")

def main():
    """主函数"""
    print("🚀 开始测试合并分析接口的改进功能")
    
    print(f"\n📝 改进功能说明:")
    print(f"1. 🌐 坐标系转换: EPSG:32648 -> EPSG:4326")
    print(f"2. 📁 相对路径: 添加output_files_relative字段")
    print(f"3. 📊 转换信息: 添加coordinate_conversion字段")
    
    # 测试合并分析
    success = test_combined_analysis_improvements()
    
    # 测试TaskInfo API
    test_taskinfo_api_with_improvements()
    
    if success:
        print(f"\n🎉 合并分析接口改进功能测试通过！")
    else:
        print(f"\n❌ 合并分析接口改进功能测试失败")
    
    print(f"\n📖 使用说明:")
    print(f"1. 输出的SHP文件现在是EPSG:4326坐标系")
    print(f"2. TaskInfo.json包含相对路径，便于局域网访问")
    print(f"3. 相对路径格式: ODM/AI/{'{image_id}'}/arableLand/{'{filename}'}")
    print(f"4. 局域网访问: http://服务器IP:81/{'{相对路径}'}")

if __name__ == "__main__":
    main()
