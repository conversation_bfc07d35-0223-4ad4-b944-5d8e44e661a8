目前执行接口/api/analysis/queued-combined-ai-spatial-analysis/如果图像过大，会发生一下错误。
2025-08-29 10:43:48 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 10:43:48 - INFO - 📏 图像大小: 56283x67180, 波段数: 3
2025-08-29 10:43:48 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-29 10:43:48 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-29 10:43:48 - INFO - 🔢 类别数量: 2
2025-08-29 10:43:48 - INFO - 🔄 重叠区域: 37 像素
2025-08-29 10:43:48 - INFO - 📦 批处理大小: 16
2025-08-29 10:43:48 - INFO - 💻 计算设备: cuda:0
2025-08-29 11:08:29 - ERROR - ❌ 新版AI处理引擎执行失败: Unable to allocate 28.2 GiB for an array with shape (67180, 56283) and data type int64
2025-08-29 11:08:29 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\analysis_executor.py", line 1015, in _execute_new_ai_processing
    success, testtime, processing_time = pre_pytorch_new.process_single_image(
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 1385, in process_single_image
    nodata_mask = create_nodata_mask(im_data, nodata_values)
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 721, in create_nodata_mask
    labeled_mask, num_features = ndimage.label(mask)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\scipy\ndimage\_measurements.py", line 200, in label
    output = np.empty(input.shape, np.intp if need_64bits else np.int32)
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 28.2 GiB for an array with shape (67180, 56283) and data type int64

目前的执行ai分析的代码，例如deeplabv3_plus.py的代码为例
第一步 切割图像
第二步 预测数据
第三部 合并分析结果

但是由于图像过大在第一步切割图像时会发生内存溢出的情况。

解决办法，在执行ai分析的代码前，分析图像的大小有多大，获取目前代码可以支配的内存大小。将图像等均分为小图，每份图像占用不超过代码可以最大支持的80%大小。
注意，均分后的图像遵循2x2,3x3,4x4这样布局的均分。
由于分析完毕后还要拼接，为了避免拼接效果明显，分割后的图像与相邻的图像要有128像素的重合。
将分出来的小图依次放入原来的ai分析任务中分析
每次分析完毕小图后将小图本地保存，保存在输出地址下面，并以例如文件名_1_1.tif保存，其中_1_1表示的是小图在原来大图中的位置。
将各个小图分析完毕后，在融合拼接为大图，中间有重合的128像素，参考deeplabv3_plus.py中合并分析结果的功能进行取舍，最后合并为一张大图。
这样是否能够解决内存不足的问题