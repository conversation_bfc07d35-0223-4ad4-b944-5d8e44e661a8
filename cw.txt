
INFO "GET /api/analysis/weight-info/ HTTP/1.1" 200 978
INFO "OPTIONS /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 0
2025-08-28 16:47:40,892 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250403124549
INFO 📥 收到队列化合并分析请求: 20250403124549
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4e5' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1249, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"📥 收到队列化合并分析请求: {task_params['id']}")
Message: '📥 收到队列化合并分析请求: 20250403124549'
Arguments: ()
INFO 🔄 队列处理器开始运行
--- Logging error ---
INFO 📋 队列处理线程已启动
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f504' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 250, in _process_queue
    logger.info("🔄 队列处理器开始运行")
Message: '🔄 队列处理器开始运行'
Arguments: ()
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4cb' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1252, in queued_combined_ai_spatial_analysis
    from ...core.task_queue_manager import task_queue_manager
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 600, in <module>
    task_queue_manager = TaskQueueManager()
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 59, in __init__
    self.start_queue_processor()
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 73, in start_queue_processor
    logger.info("📋 队列处理线程已启动")
Message: '📋 队列处理线程已启动'
Arguments: ()
INFO 🚀 任务队列管理器已启动
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1252, in queued_combined_ai_spatial_analysis
    from ...core.task_queue_manager import task_queue_manager
  File "<frozen importlib._bootstrap>", line 991, in _find_and_load
  File "<frozen importlib._bootstrap>", line 975, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 671, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 843, in exec_module
  File "<frozen importlib._bootstrap>", line 219, in _call_with_frames_removed
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 600, in <module>
    task_queue_manager = TaskQueueManager()
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 61, in __init__
    logger.info("🚀 任务队列管理器已启动")
Message: '🚀 任务队列管理器已启动'
Arguments: ()
INFO 📝 TaskInfo.json模板已创建: a83546fe-e195-4f44-8842-913b6de39022
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 112, in add_task
    self._create_taskinfo_template(task_id, task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 454, in _create_taskinfo_template
    logger.info(f"📝 TaskInfo.json模板已创建: {task_id}")
Message: '📝 TaskInfo.json模板已创建: a83546fe-e195-4f44-8842-913b6de39022'
Arguments: ()
INFO ➕ 任务已添加到队列: a83546fe-e195-4f44-8842-913b6de39022, 队列位置: 1
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2795' in position 60: illegal multibyte sequence
Call stack:
INFO 📝 TaskInfo.json状态已更新: a83546fe-e195-4f44-8842-913b6de39022 -> 进行中
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 117, in add_task
    logger.info(f"➕ 任务已添加到队列: {task_id}, 队列位置: {task_info['queue_position']}")
Message: '➕ 任务已添加到队列: a83546fe-e195-4f44-8842-913b6de39022, 队列位置: 1'
Arguments: ()
--- Logging error ---
2025-08-28 16:47:40,951 - analysis_executor - INFO - ✅ 任务已添加到队列: a83546fe-e195-4f44-8842-913b6de39022
INFO ✅ 任务已添加到队列: a83546fe-e195-4f44-8842-913b6de39022
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 298, in _execute_task
    self._update_taskinfo_status(task_id, '进行中', '任务正在执行中...')
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 497, in _update_taskinfo_status
    logger.info(f"📝 TaskInfo.json状态已更新: {task_id} -> {status}")
Message: '📝 TaskInfo.json状态已更新: a83546fe-e195-4f44-8842-913b6de39022 -> 进行中'
Arguments: ()
INFO 🚀 开始执行任务: a83546fe-e195-4f44-8842-913b6de39022
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1256, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"✅ 任务已添加到队列: {result['data']['task_id']}")
Message: '✅ 任务已添加到队列: a83546fe-e195-4f44-8842-913b6de39022'
Arguments: ()
--- Logging error ---
INFO "GET /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 188
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 300, in _execute_task
    logger.info(f"🚀 开始执行任务: {task_id}")
Message: '🚀 开始执行任务: a83546fe-e195-4f44-8842-913b6de39022'
Arguments: ()
2025-08-28 16:47:40,963 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
INFO 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 68: illegal multibyte sequence
Call stack:
2025-08-28 16:47:40,965 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 73, in execute_combined_analysis
    analysis_logger.info("🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀")
Message: '🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀'
Arguments: ()
2025-08-28 16:47:40,967 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
INFO 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
2025-08-28 16:47:40,972 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
--- Logging error ---
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3af' in position 68: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 74, in execute_combined_analysis
    analysis_logger.info(f"🎯 开始执行合并分析，参数: image_id={image_id}, image_path={image_path}")
Message: '🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif'
Arguments: ()
ERROR ❌ 任务执行失败: a83546fe-e195-4f44-8842-913b6de39022
--- Logging error ---
2025-08-28 16:47:40,976 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 61: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 328, in _execute_task
    logger.error(f"❌ 任务执行失败: {task_id}")
Message: '❌ 任务执行失败: a83546fe-e195-4f44-8842-913b6de39022'
Arguments: ()
INFO 🏁 任务处理结束: a83546fe-e195-4f44-8842-913b6de39022, 状态: 失败
--- Logging error ---
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3c1' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 342, in _execute_task
    logger.info(f"🏁 任务处理结束: {task_id}, 状态: {task_info['status']}")
Message: '🏁 任务处理结束: a83546fe-e195-4f44-8842-913b6de39022, 状态: 失败'
Arguments: ()
2025-08-28 16:47:44,355 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:47:44,382 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:47:44,383 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:47:45,443 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:47:45,466 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:47:45,467 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:47:45,642 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:47:45,658 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:47:45,659 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:47:45,842 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:47:45,866 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:47:45,866 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:47:50,348 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:47:52,553 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:47:52,554 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:47:52,554 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:48:04,473 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:48:07,008 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:48:07,009 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:48:07,009 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:48:50,359 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:48:52,761 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:48:52,762 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:48:52,762 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:49:04,474 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:49:06,977 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:49:06,978 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:49:06,978 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:49:50,359 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:49:52,653 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:49:52,654 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:49:52,655 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:50:04,469 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:50:07,041 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:50:07,041 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:50:07,042 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:50:50,361 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:50:52,818 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:50:52,819 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:50:52,819 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:51:04,462 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:51:06,974 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:51:06,975 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:51:06,975 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:51:50,351 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:51:52,501 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:51:52,502 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:51:52,503 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:52:04,472 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:52:07,014 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:52:07,015 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:52:07,016 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:52:50,353 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:52:52,385 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:52:52,386 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:52:52,386 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:53:04,470 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:53:06,622 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:53:06,623 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:53:06,623 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:53:20,403 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:53:20,425 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:53:20,426 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:53:21,770 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:53:21,795 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:53:21,796 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:53:27,002 - analysis_executor - INFO - 开始获取AI模型权重信息
INFO 开始获取AI模型权重信息
2025-08-28 16:53:27,003 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
INFO 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-28 16:53:27,024 - analysis_executor - INFO - 配置文件读取成功
INFO 配置文件读取成功
2025-08-28 16:53:27,025 - analysis_executor - INFO - 获取到window_data_path: G:/data/uavflight-file
INFO 获取到window_data_path: G:/data/uavflight-file
2025-08-28 16:53:27,025 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
INFO 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-28 16:53:27,025 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': ' 建设用地'}
INFO 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-28 16:53:27,026 - analysis_executor - INFO - 发现Default_area配置节
INFO 发现Default_area配置节
2025-08-28 16:53:27,026 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
INFO 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-28 16:53:27,026 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
INFO 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-28 16:53:27,027 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
INFO 默认面积转换成功: arableLand = 400.0
2025-08-28 16:53:27,027 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
INFO 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-28 16:53:27,027 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
INFO 默认面积转换成功: constructionLand = 200.0
2025-08-28 16:53:27,027 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
INFO 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-28 16:53:27,028 - analysis_executor - INFO - 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
INFO 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
2025-08-28 16:53:27,028 - analysis_executor - INFO - 扫描地物类型: arableLand
INFO 扫描地物类型: arableLand
2025-08-28 16:53:27,029 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:53:27,030 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
INFO     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-28 16:53:27,030 - analysis_executor - INFO - 扫描地物类型: constructionLand
INFO 扫描地物类型: constructionLand
2025-08-28 16:53:27,030 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:53:27,031 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
INFO     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-28 16:53:27,032 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
INFO 权重目录扫描完成，共发现 2 个地物类型
2025-08-28 16:53:27,032 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
INFO 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
2025-08-28 16:53:27,033 - analysis_executor - INFO - 扫描地物类型目录: arableLand
INFO 扫描地物类型目录: arableLand
2025-08-28 16:53:27,034 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
INFO   发现shp文件: 耕地2024_84.shp
2025-08-28 16:53:27,034 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
INFO   地物类型 arableLand 共找到 1 个shp文件
2025-08-28 16:53:27,035 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
INFO 扫描地物类型目录: constructionLand
2025-08-28 16:53:27,036 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
INFO   发现shp文件: 建设2024_84.shp
2025-08-28 16:53:27,036 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
INFO   地物类型 constructionLand 共找到 1 个shp文件
2025-08-28 16:53:27,037 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
INFO AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-28 16:53:27,037 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
INFO 成功获取权重信息，共 2 个地物类型
INFO "GET /api/analysis/weight-info/ HTTP/1.1" 200 978
2025-08-28 16:53:30,045 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250403124549
INFO 📥 收到队列化合并分析请求: 20250403124549
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4e5' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1249, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"📥 收到队列化合并分析请求: {task_params['id']}")
Message: '📥 收到队列化合并分析请求: 20250403124549'
Arguments: ()
INFO 📝 TaskInfo.json模板已创建: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 112, in add_task
    self._create_taskinfo_template(task_id, task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 454, in _create_taskinfo_template
    logger.info(f"📝 TaskInfo.json模板已创建: {task_id}")
Message: '📝 TaskInfo.json模板已创建: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9'
Arguments: ()
INFO ➕ 任务已添加到队列: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9, 队列位置: 1
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2795' in position 60: illegal multibyte sequence
Call stack:
INFO 📝 TaskInfo.json状态已更新: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9 -> 进行中
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 117, in add_task
    logger.info(f"➕ 任务已添加到队列: {task_id}, 队列位置: {task_info['queue_position']}")
Message: '➕ 任务已添加到队列: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9, 队列位置: 1'
Arguments: ()
--- Logging error ---
2025-08-28 16:53:30,073 - analysis_executor - INFO - ✅ 任务已添加到队列: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9
INFO ✅ 任务已添加到队列: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 298, in _execute_task
    self._update_taskinfo_status(task_id, '进行中', '任务正在执行中...')
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 497, in _update_taskinfo_status
    logger.info(f"📝 TaskInfo.json状态已更新: {task_id} -> {status}")
Message: '📝 TaskInfo.json状态已更新: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9 -> 进行中'
Arguments: ()
INFO 🚀 开始执行任务: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1256, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"✅ 任务已添加到队列: {result['data']['task_id']}")
Message: '✅ 任务已添加到队列: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9'
Arguments: ()
--- Logging error ---
INFO "GET /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 188
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 300, in _execute_task
    logger.info(f"🚀 开始执行任务: {task_id}")
Message: '🚀 开始执行任务: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9'
Arguments: ()
2025-08-28 16:53:30,086 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
INFO 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
--- Logging error ---
2025-08-28 16:53:30,087 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 68: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 73, in execute_combined_analysis
    analysis_logger.info("🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀")
Message: '🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀'
Arguments: ()
2025-08-28 16:53:30,089 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
INFO 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3af' in position 68: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 74, in execute_combined_analysis
    analysis_logger.info(f"🎯 开始执行合并分析，参数: image_id={image_id}, image_path={image_path}")
Message: '🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif'
Arguments: ()
ERROR ❌ 任务执行失败: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9
2025-08-28 16:53:30,094 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
--- Logging error ---
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 61: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 328, in _execute_task
    logger.error(f"❌ 任务执行失败: {task_id}")
Message: '❌ 任务执行失败: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9'
Arguments: ()
INFO 🏁 任务处理结束: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9, 状态: 失败
--- Logging error ---
2025-08-28 16:53:30,096 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
Traceback (most recent call last):
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3c1' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 342, in _execute_task
    logger.info(f"🏁 任务处理结束: {task_id}, 状态: {task_info['status']}")
Message: '🏁 任务处理结束: e29da0b6-fed0-40e6-9c93-34c0f6fd34e9, 状态: 失败'
Arguments: ()
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:53:50,362 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:53:52,616 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:53:52,618 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:53:52,619 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:54:04,466 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:54:06,881 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:54:06,885 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:54:06,886 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:54:25,372 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:54:25,395 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:54:25,396 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:54:26,363 - analysis_executor - INFO - 开始获取AI模型权重信息
INFO 开始获取AI模型权重信息
2025-08-28 16:54:26,363 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
INFO 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
INFO 查询图层边界框: drone/20250403124549
INFO 获取图层 drone:20250403124549 的信息
2025-08-28 16:54:26,393 - analysis_executor - INFO - 配置文件读取成功
INFO 配置文件读取成功
2025-08-28 16:54:26,394 - analysis_executor - INFO - 获取到window_data_path: G:/data/uavflight-file
INFO 获取到window_data_path: G:/data/uavflight-file
2025-08-28 16:54:26,395 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
INFO 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-28 16:54:26,395 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': ' 建设用地'}
INFO 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-28 16:54:26,396 - analysis_executor - INFO - 发现Default_area配置节
INFO 发现Default_area配置节
2025-08-28 16:54:26,396 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
INFO 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-28 16:54:26,397 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
INFO 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-28 16:54:26,397 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
INFO 默认面积转换成功: arableLand = 400.0
2025-08-28 16:54:26,397 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
INFO 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-28 16:54:26,398 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
INFO 默认面积转换成功: constructionLand = 200.0
2025-08-28 16:54:26,398 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
INFO 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-28 16:54:26,398 - analysis_executor - INFO - 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
INFO 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
2025-08-28 16:54:26,399 - analysis_executor - INFO - 扫描地物类型: arableLand
INFO 扫描地物类型: arableLand
2025-08-28 16:54:26,400 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:54:26,401 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
INFO     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-28 16:54:26,401 - analysis_executor - INFO - 扫描地物类型: constructionLand
INFO 扫描地物类型: constructionLand
2025-08-28 16:54:26,402 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:54:26,403 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
INFO     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-28 16:54:26,403 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
INFO 权重目录扫描完成，共发现 2 个地物类型
2025-08-28 16:54:26,404 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
INFO 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
2025-08-28 16:54:26,404 - analysis_executor - INFO - 扫描地物类型目录: arableLand
INFO 扫描地物类型目录: arableLand
2025-08-28 16:54:26,406 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
INFO   发现shp文件: 耕地2024_84.shp
2025-08-28 16:54:26,408 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
INFO   地物类型 arableLand 共找到 1 个shp文件
2025-08-28 16:54:26,409 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
INFO 扫描地物类型目录: constructionLand
2025-08-28 16:54:26,410 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
INFO   发现shp文件: 建设2024_84.shp
2025-08-28 16:54:26,411 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
INFO   地物类型 constructionLand 共找到 1 个shp文件
2025-08-28 16:54:26,411 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
INFO AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-28 16:54:26,412 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
INFO 成功获取权重信息，共 2 个地物类型
INFO "GET /api/analysis/weight-info/ HTTP/1.1" 200 978
INFO 成功获取图层 drone:20250403124549 的边界框信息
INFO "GET /api/management/layers/bbox/?workspace=drone&layer=20250403124549 HTTP/1.1" 200 363
2025-08-28 16:54:28,973 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250403124549
INFO 📥 收到队列化合并分析请求: 20250403124549
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4e5' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1249, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"📥 收到队列化合并分析请求: {task_params['id']}")
Message: '📥 收到队列化合并分析请求: 20250403124549'
Arguments: ()
INFO 📝 TaskInfo.json模板已创建: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 112, in add_task
    self._create_taskinfo_template(task_id, task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 454, in _create_taskinfo_template
    logger.info(f"📝 TaskInfo.json模板已创建: {task_id}")
Message: '📝 TaskInfo.json模板已创建: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3'
Arguments: ()
INFO ➕ 任务已添加到队列: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3, 队列位置: 1
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2795' in position 60: illegal multibyte sequence
Call stack:
INFO 📝 TaskInfo.json状态已更新: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3 -> 进行中
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 117, in add_task
    logger.info(f"➕ 任务已添加到队列: {task_id}, 队列位置: {task_info['queue_position']}")
Message: '➕ 任务已添加到队列: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3, 队列位置: 1'
Arguments: ()
--- Logging error ---
2025-08-28 16:54:29,001 - analysis_executor - INFO - ✅ 任务已添加到队列: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3
INFO ✅ 任务已添加到队列: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 298, in _execute_task
    self._update_taskinfo_status(task_id, '进行中', '任务正在执行中...')
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 497, in _update_taskinfo_status
    logger.info(f"📝 TaskInfo.json状态已更新: {task_id} -> {status}")
Message: '📝 TaskInfo.json状态已更新: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3 -> 进行中'
Arguments: ()
INFO 🚀 开始执行任务: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1256, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"✅ 任务已添加到队列: {result['data']['task_id']}")
Message: '✅ 任务已添加到队列: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3'
Arguments: ()
--- Logging error ---
INFO "GET /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 188
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 300, in _execute_task
    logger.info(f"🚀 开始执行任务: {task_id}")
Message: '🚀 开始执行任务: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3'
Arguments: ()
2025-08-28 16:54:29,014 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
INFO 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 68: illegal multibyte sequence
Call stack:
2025-08-28 16:54:29,016 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 73, in execute_combined_analysis
    analysis_logger.info("🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀")
Message: '🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀'
Arguments: ()
2025-08-28 16:54:29,017 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
INFO 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
--- Logging error ---
Traceback (most recent call last):
2025-08-28 16:54:29,022 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3af' in position 68: illegal multibyte sequence
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 74, in execute_combined_analysis
    analysis_logger.info(f"🎯 开始执行合并分析，参数: image_id={image_id}, image_path={image_path}")
Message: '🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif'
Arguments: ()
ERROR ❌ 任务执行失败: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3
--- Logging error ---
2025-08-28 16:54:29,026 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 61: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 328, in _execute_task
    logger.error(f"❌ 任务执行失败: {task_id}")
Message: '❌ 任务执行失败: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3'
Arguments: ()
INFO 🏁 任务处理结束: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3, 状态: 失败
--- Logging error ---
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3c1' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 342, in _execute_task
    logger.info(f"🏁 任务处理结束: {task_id}, 状态: {task_info['status']}")
Message: '🏁 任务处理结束: 7ab61c4a-d55c-41e1-9a80-3e7a9a4466f3, 状态: 失败'
Arguments: ()
2025-08-28 16:54:50,360 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:54:52,624 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:54:52,625 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:54:52,626 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:55:50,357 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:55:52,395 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:55:52,396 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:55:52,397 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:56:09,739 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:56:09,745 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:56:09,746 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:56:13,811 - analysis_executor - INFO - 开始获取AI模型权重信息
INFO 开始获取AI模型权重信息
2025-08-28 16:56:13,812 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
INFO 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-28 16:56:13,817 - analysis_executor - INFO - 配置文件读取成功
INFO 配置文件读取成功
2025-08-28 16:56:13,818 - analysis_executor - INFO - 获取到window_data_path: G:/data/uavflight-file
INFO 获取到window_data_path: G:/data/uavflight-file
2025-08-28 16:56:13,818 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
INFO 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-28 16:56:13,819 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': ' 建设用地'}
INFO 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-28 16:56:13,819 - analysis_executor - INFO - 发现Default_area配置节
INFO 发现Default_area配置节
2025-08-28 16:56:13,819 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
INFO 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-28 16:56:13,820 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
INFO 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-28 16:56:13,820 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
INFO 默认面积转换成功: arableLand = 400.0
2025-08-28 16:56:13,821 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
INFO 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-28 16:56:13,821 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
INFO 默认面积转换成功: constructionLand = 200.0
2025-08-28 16:56:13,821 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
INFO 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-28 16:56:13,822 - analysis_executor - INFO - 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
INFO 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
2025-08-28 16:56:13,822 - analysis_executor - INFO - 扫描地物类型: arableLand
INFO 扫描地物类型: arableLand
2025-08-28 16:56:13,823 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:56:13,824 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
INFO     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-28 16:56:13,824 - analysis_executor - INFO - 扫描地物类型: constructionLand
INFO 扫描地物类型: constructionLand
2025-08-28 16:56:13,825 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:56:13,825 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
INFO     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-28 16:56:13,826 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
INFO 权重目录扫描完成，共发现 2 个地物类型
2025-08-28 16:56:13,826 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
INFO 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
2025-08-28 16:56:13,827 - analysis_executor - INFO - 扫描地物类型目录: arableLand
INFO 扫描地物类型目录: arableLand
2025-08-28 16:56:13,828 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
INFO   发现shp文件: 耕地2024_84.shp
2025-08-28 16:56:13,829 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
INFO   地物类型 arableLand 共找到 1 个shp文件
2025-08-28 16:56:13,829 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
INFO 扫描地物类型目录: constructionLand
2025-08-28 16:56:13,830 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
INFO   发现shp文件: 建设2024_84.shp
2025-08-28 16:56:13,831 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
INFO   地物类型 constructionLand 共找到 1 个shp文件
2025-08-28 16:56:13,831 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
INFO AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-28 16:56:13,831 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
INFO 成功获取权重信息，共 2 个地物类型
INFO "GET /api/analysis/weight-info/ HTTP/1.1" 200 978
2025-08-28 16:56:16,285 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250403124549
INFO 📥 收到队列化合并分析请求: 20250403124549
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4e5' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1249, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"📥 收到队列化合并分析请求: {task_params['id']}")
Message: '📥 收到队列化合并分析请求: 20250403124549'
Arguments: ()
INFO 📝 TaskInfo.json模板已创建: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 112, in add_task
    self._create_taskinfo_template(task_id, task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 454, in _create_taskinfo_template
    logger.info(f"📝 TaskInfo.json模板已创建: {task_id}")
Message: '📝 TaskInfo.json模板已创建: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61'
Arguments: ()
INFO ➕ 任务已添加到队列: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61, 队列位置: 1
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2795' in position 60: illegal multibyte sequence
Call stack:
INFO 📝 TaskInfo.json状态已更新: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61 -> 进行中
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 117, in add_task
    logger.info(f"➕ 任务已添加到队列: {task_id}, 队列位置: {task_info['queue_position']}")
Message: '➕ 任务已添加到队列: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61, 队列位置: 1'
Arguments: ()
--- Logging error ---
2025-08-28 16:56:16,314 - analysis_executor - INFO - ✅ 任务已添加到队列: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61
INFO ✅ 任务已添加到队列: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 298, in _execute_task
    self._update_taskinfo_status(task_id, '进行中', '任务正在执行中...')
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 497, in _update_taskinfo_status
    logger.info(f"📝 TaskInfo.json状态已更新: {task_id} -> {status}")
Message: '📝 TaskInfo.json状态已更新: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61 -> 进行中'
Arguments: ()
INFO 🚀 开始执行任务: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 229, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1256, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"✅ 任务已添加到队列: {result['data']['task_id']}")
Message: '✅ 任务已添加到队列: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61'
Arguments: ()
--- Logging error ---
INFO "GET /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 188
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 300, in _execute_task
    logger.info(f"🚀 开始执行任务: {task_id}")
Message: '🚀 开始执行任务: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61'
Arguments: ()
2025-08-28 16:56:16,327 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
INFO 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
--- Logging error ---
2025-08-28 16:56:16,328 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 68: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 73, in execute_combined_analysis
    analysis_logger.info("🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀")
Message: '🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀'
Arguments: ()
2025-08-28 16:56:16,330 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
INFO 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3af' in position 68: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 74, in execute_combined_analysis
    analysis_logger.info(f"🎯 开始执行合并分析，参数: image_id={image_id}, image_path={image_path}")
Message: '🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif'
Arguments: ()
ERROR ❌ 任务执行失败: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 61: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 328, in _execute_task
    logger.error(f"❌ 任务执行失败: {task_id}")
Message: '❌ 任务执行失败: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61'
Arguments: ()
INFO 🏁 任务处理结束: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61, 状态: 失败
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3c1' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 342, in _execute_task
    logger.info(f"🏁 任务处理结束: {task_id}, 状态: {task_info['status']}")
Message: '🏁 任务处理结束: aff26d5c-5d29-4b3b-8e6a-bc9c8f9eec61, 状态: 失败'
Arguments: ()
2025-08-28 16:56:16,358 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:56:16,359 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:56:50,361 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:56:52,708 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:56:52,713 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:56:52,714 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:57:50,348 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:57:52,535 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:57:52,536 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:57:52,537 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:58:09,520 - map_api - INFO - 开始获取ODM任务列表
INFO 开始获取ODM任务列表
2025-08-28 16:58:11,419 - map_api - INFO - 获取到 167 个任务信息
INFO 获取到 167 个任务信息
2025-08-28 16:58:11,420 - map_api - INFO - 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
INFO 任务排序结果: 运行中(6) > 未开始(0) > 已完成(161)
2025-08-28 16:58:11,420 - map_api - INFO - 成功获取 167 个ODM任务
INFO 成功获取 167 个ODM任务
INFO "GET /api/map/odm/tasks HTTP/1.1" 200 554656
2025-08-28 16:58:15,644 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250528110906
INFO 获取TaskInfo请求: image_id=20250528110906
2025-08-28 16:58:15,659 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250528110906\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250528110906\TaskInfo.json
2025-08-28 16:58:15,660 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250528110906\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250528110906\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250528110906 HTTP/1.1" 200 82
INFO "GET /api/management/layers/bbox?workspace=drone&layer=20250528110906 HTTP/1.1" 301 0
INFO 查询图层边界框: drone/20250528110906
INFO 获取图层 drone:20250528110906 的信息
INFO 成功获取图层 drone:20250528110906 的边界框信息
INFO "GET /api/management/layers/bbox/?workspace=drone&layer=20250528110906 HTTP/1.1" 200 360
2025-08-28 16:58:18,524 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:58:18,530 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:58:18,531 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:58:19,445 - analysis_executor - INFO - 开始获取AI模型权重信息
INFO 开始获取AI模型权重信息
2025-08-28 16:58:19,446 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
INFO 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-28 16:58:19,452 - analysis_executor - INFO - 配置文件读取成功
INFO 配置文件读取成功
2025-08-28 16:58:19,452 - analysis_executor - INFO - 获取到window_data_path: G:/data/uavflight-file
INFO 获取到window_data_path: G:/data/uavflight-file
2025-08-28 16:58:19,453 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
INFO 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-28 16:58:19,453 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': ' 建设用地'}
INFO 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-28 16:58:19,454 - analysis_executor - INFO - 发现Default_area配置节
INFO 发现Default_area配置节
2025-08-28 16:58:19,454 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
INFO 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-28 16:58:19,454 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
INFO 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-28 16:58:19,455 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
INFO 默认面积转换成功: arableLand = 400.0
2025-08-28 16:58:19,455 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
INFO 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-28 16:58:19,455 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
INFO 默认面积转换成功: constructionLand = 200.0
2025-08-28 16:58:19,456 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
INFO 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-28 16:58:19,456 - analysis_executor - INFO - 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
INFO 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
2025-08-28 16:58:19,457 - analysis_executor - INFO - 扫描地物类型: arableLand
INFO 扫描地物类型: arableLand
2025-08-28 16:58:19,458 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:58:19,458 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
INFO     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-28 16:58:19,459 - analysis_executor - INFO - 扫描地物类型: constructionLand
INFO 扫描地物类型: constructionLand
2025-08-28 16:58:19,460 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:58:19,460 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
INFO     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-28 16:58:19,461 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
INFO 权重目录扫描完成，共发现 2 个地物类型
2025-08-28 16:58:19,461 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
INFO 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
2025-08-28 16:58:19,462 - analysis_executor - INFO - 扫描地物类型目录: arableLand
INFO 扫描地物类型目录: arableLand
2025-08-28 16:58:19,463 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
INFO   发现shp文件: 耕地2024_84.shp
2025-08-28 16:58:19,464 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
INFO   地物类型 arableLand 共找到 1 个shp文件
2025-08-28 16:58:19,465 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
INFO 扫描地物类型目录: constructionLand
2025-08-28 16:58:19,468 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
INFO   发现shp文件: 建设2024_84.shp
2025-08-28 16:58:19,468 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
INFO   地物类型 constructionLand 共找到 1 个shp文件
2025-08-28 16:58:19,469 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
INFO AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-28 16:58:19,469 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
INFO 成功获取权重信息，共 2 个地物类型
INFO "GET /api/analysis/weight-info/ HTTP/1.1" 200 978
INFO 查询图层边界框: drone/20250403124549
INFO 获取图层 drone:20250403124549 的信息
INFO 成功获取图层 drone:20250403124549 的边界框信息
INFO "GET /api/management/layers/bbox/?workspace=drone&layer=20250403124549 HTTP/1.1" 200 363
2025-08-28 16:58:27,748 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
2025-08-28 16:58:27,767 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:58:27,768 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82
2025-08-28 16:58:28,659 - analysis_executor - INFO - 开始获取AI模型权重信息
INFO 开始获取AI模型权重信息
2025-08-28 16:58:28,660 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
INFO 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-28 16:58:28,664 - analysis_executor - INFO - 配置文件读取成功
INFO 配置文件读取成功
2025-08-28 16:58:28,665 - analysis_executor - INFO - 获取到window_data_path: G:/data/uavflight-file
INFO 获取到window_data_path: G:/data/uavflight-file
2025-08-28 16:58:28,665 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
INFO 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-28 16:58:28,666 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': ' 建设用地'}
INFO 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-28 16:58:28,666 - analysis_executor - INFO - 发现Default_area配置节
INFO 发现Default_area配置节
2025-08-28 16:58:28,666 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
INFO 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-28 16:58:28,666 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
INFO 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-28 16:58:28,667 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
INFO 默认面积转换成功: arableLand = 400.0
2025-08-28 16:58:28,667 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
INFO 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-28 16:58:28,667 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
INFO 默认面积转换成功: constructionLand = 200.0
2025-08-28 16:58:28,667 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
INFO 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-28 16:58:28,668 - analysis_executor - INFO - 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
INFO 权重目录路径: G:/data/uavflight-file\ODM\AIWeight
2025-08-28 16:58:28,668 - analysis_executor - INFO - 扫描地物类型: arableLand
INFO 扫描地物类型: arableLand
2025-08-28 16:58:28,669 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:58:28,669 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
INFO     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-28 16:58:28,670 - analysis_executor - INFO - 扫描地物类型: constructionLand
INFO 扫描地物类型: constructionLand
2025-08-28 16:58:28,670 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
INFO   扫描模型类型: deeplabv3_plus
2025-08-28 16:58:28,673 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
INFO     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-28 16:58:28,673 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
INFO 权重目录扫描完成，共发现 2 个地物类型
2025-08-28 16:58:28,674 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
INFO 开始扫描AIOLDSHP目录: G:/data/uavflight-file\ODM\AIOLDSHP
2025-08-28 16:58:28,674 - analysis_executor - INFO - 扫描地物类型目录: arableLand
INFO 扫描地物类型目录: arableLand
2025-08-28 16:58:28,676 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
INFO   发现shp文件: 耕地2024_84.shp
2025-08-28 16:58:28,676 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
INFO   地物类型 arableLand 共找到 1 个shp文件
2025-08-28 16:58:28,676 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
INFO 扫描地物类型目录: constructionLand
2025-08-28 16:58:28,678 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
INFO   发现shp文件: 建设2024_84.shp
2025-08-28 16:58:28,678 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
INFO   地物类型 constructionLand 共找到 1 个shp文件
2025-08-28 16:58:28,679 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
INFO AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-28 16:58:28,679 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
INFO 成功获取权重信息，共 2 个地物类型
INFO "GET /api/analysis/weight-info/ HTTP/1.1" 200 978
INFO 查询图层边界框: drone/20250403124549
INFO 获取图层 drone:20250403124549 的信息
INFO 成功获取图层 drone:20250403124549 的边界框信息
INFO "GET /api/management/layers/bbox/?workspace=drone&layer=20250403124549 HTTP/1.1" 200 363
INFO "OPTIONS /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 0
2025-08-28 16:58:31,108 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250403124549
INFO 📥 收到队列化合并分析请求: 20250403124549
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4e5' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1249, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"📥 收到队列化合并分析请求: {task_params['id']}")
Message: '📥 收到队列化合并分析请求: 20250403124549'
Arguments: ()
INFO 📝 TaskInfo.json模板已创建: 36c06c01-a38b-46e6-9db1-88b5b9df573f
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 112, in add_task
    self._create_taskinfo_template(task_id, task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 454, in _create_taskinfo_template
    logger.info(f"📝 TaskInfo.json模板已创建: {task_id}")
Message: '📝 TaskInfo.json模板已创建: 36c06c01-a38b-46e6-9db1-88b5b9df573f'
Arguments: ()
INFO ➕ 任务已添加到队列: 36c06c01-a38b-46e6-9db1-88b5b9df573f, 队列位置: 1
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2795' in position 60: illegal multibyte sequence
Call stack:
INFO 📝 TaskInfo.json状态已更新: 36c06c01-a38b-46e6-9db1-88b5b9df573f -> 进行中
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1253, in queued_combined_ai_spatial_analysis
    result = task_queue_manager.add_task(task_params)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 117, in add_task
    logger.info(f"➕ 任务已添加到队列: {task_id}, 队列位置: {task_info['queue_position']}")
Message: '➕ 任务已添加到队列: 36c06c01-a38b-46e6-9db1-88b5b9df573f, 队列位置: 1'
Arguments: ()
--- Logging error ---
2025-08-28 16:58:31,133 - analysis_executor - INFO - ✅ 任务已添加到队列: 36c06c01-a38b-46e6-9db1-88b5b9df573f
INFO ✅ 任务已添加到队列: 36c06c01-a38b-46e6-9db1-88b5b9df573f
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4dd' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 298, in _execute_task
    self._update_taskinfo_status(task_id, '进行中', '任务正在执行中...')
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 497, in _update_taskinfo_status
    logger.info(f"📝 TaskInfo.json状态已更新: {task_id} -> {status}")
Message: '📝 TaskInfo.json状态已更新: 36c06c01-a38b-46e6-9db1-88b5b9df573f -> 进行中'
Arguments: ()
INFO 🚀 开始执行任务: 36c06c01-a38b-46e6-9db1-88b5b9df573f
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u2705' in position 56: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 683, in process_request_thread
    self.finish_request(request, client_address)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 360, in finish_request
    self.RequestHandlerClass(request, client_address, self)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\socketserver.py", line 747, in __init__
    self.handle()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 227, in handle
    self.handle_one_request()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\servers\basehttp.py", line 252, in handle_one_request
    handler.run(self.server.get_app())
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\wsgiref\handlers.py", line 137, in run
    self.result = application(self.environ, self.start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\contrib\staticfiles\handlers.py", line 80, in __call__
    return self.application(environ, start_response)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\wsgi.py", line 124, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 140, in get_response
    response = self._middleware_chain(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\gty\geoserverAPIDJV2\geoserver_django\middleware.py", line 47, in __call__
    response = self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\utils\deprecation.py", line 134, in __call__
    response = response or self.get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\site-packages\rest_framework\decorators.py", line 50, in handler
    return func(*args, **kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\views\analysis\analysis_views.py", line 1256, in queued_combined_ai_spatial_analysis
    analysis_logger.info(f"✅ 任务已添加到队列: {result['data']['task_id']}")
Message: '✅ 任务已添加到队列: 36c06c01-a38b-46e6-9db1-88b5b9df573f'
Arguments: ()
--- Logging error ---
INFO "GET /api/analysis/queued-combined-ai-spatial-analysis/?id=20250403124549&image=G%3A%2Fdata%2Fuavflight-file%2FODM%2FOutput%2F20250403124549%2F20250403124549_out.tif&model=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIWeight%2FarableLand%2Fdeeplabv3_plus%2Fdeeplabv3_plus_best_20250807-111949.pth&old_data_path=G%3A%2Fdata%2Fuavflight-file%2FODM%2FAIOLDSHP%2FarableLand%2F%E8%80%95%E5%9C%B02024_84.shp&area_threshold=400 HTTP/1.1" 200 188
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 300, in _execute_task
    logger.info(f"🚀 开始执行任务: {task_id}")
Message: '🚀 开始执行任务: 36c06c01-a38b-46e6-9db1-88b5b9df573f'
Arguments: ()
2025-08-28 16:58:31,146 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
INFO 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 68: illegal multibyte sequence
Call stack:
2025-08-28 16:58:31,148 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250403124549
INFO 获取TaskInfo请求: image_id=20250403124549
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 73, in execute_combined_analysis
    analysis_logger.info("🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀")
Message: '🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀'
Arguments: ()
2025-08-28 16:58:31,149 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
INFO 🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3af' in position 68: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 307, in _execute_task
    result = self.executor.execute_combined_analysis(
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 74, in execute_combined_analysis
    analysis_logger.info(f"🎯 开始执行合并分析，参数: image_id={image_id}, image_path={image_path}")
Message: '🎯 开始执行合并分析，参数: image_id=20250403124549, image_path=G:/data/uavflight-file/ODM/Output/20250403124549/20250403124549_out.tif'
Arguments: ()
ERROR ❌ 任务执行失败: 36c06c01-a38b-46e6-9db1-88b5b9df573f
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 61: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 328, in _execute_task
    logger.error(f"❌ 任务执行失败: {task_id}")
Message: '❌ 任务执行失败: 36c06c01-a38b-46e6-9db1-88b5b9df573f'
Arguments: ()
INFO 🏁 任务处理结束: 36c06c01-a38b-46e6-9db1-88b5b9df573f, 状态: 失败
--- Logging error ---
Traceback (most recent call last):
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\logging\__init__.py", line 1088, in emit
    stream.write(msg + self.terminator)
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f3c1' in position 60: illegal multibyte sequence
Call stack:
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 890, in _bootstrap
    self._bootstrap_inner()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 932, in _bootstrap_inner
    self.run()
  File "D:\Program Files\anaconda3\envs\geoserverapi\lib\threading.py", line 870, in run
    self._target(*self._args, **self._kwargs)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 267, in _process_queue
    self._execute_task(task_info)
  File "D:\gty\geoserverAPIDJV2\geoserver_api\core\task_queue_manager.py", line 342, in _execute_task
    logger.info(f"🏁 任务处理结束: {task_id}, 状态: {task_info['status']}")
Message: '🏁 任务处理结束: 36c06c01-a38b-46e6-9db1-88b5b9df573f, 状态: 失败'
Arguments: ()
2025-08-28 16:58:31,169 - analysis_executor - INFO - TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件路径: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
2025-08-28 16:58:31,170 - analysis_executor - INFO - TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO TaskInfo文件不存在: G:/data/uavflight-file\ODM\AI\20250403124549\TaskInfo.json
INFO "GET /api/analysis/taskinfo/?id=20250403124549 HTTP/1.1" 200 82