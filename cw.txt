目前执行接口/api/analysis/queued-combined-ai-spatial-analysis/如果图像过大，会发生一下错误。
2025-08-29 10:43:48 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250523123608/20250523123608_out.tif
2025-08-29 10:43:48 - INFO - 📏 图像大小: 56283x67180, 波段数: 3
2025-08-29 10:43:48 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-29 10:43:48 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-29 10:43:48 - INFO - 🔢 类别数量: 2
2025-08-29 10:43:48 - INFO - 🔄 重叠区域: 37 像素
2025-08-29 10:43:48 - INFO - 📦 批处理大小: 16
2025-08-29 10:43:48 - INFO - 💻 计算设备: cuda:0
2025-08-29 11:08:29 - ERROR - ❌ 新版AI处理引擎执行失败: Unable to allocate 28.2 GiB for an array with shape (67180, 56283) and data type int64
2025-08-29 11:08:29 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\analysis_executor.py", line 1015, in _execute_new_ai_processing
    success, testtime, processing_time = pre_pytorch_new.process_single_image(
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 1385, in process_single_image
    nodata_mask = create_nodata_mask(im_data, nodata_values)
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 721, in create_nodata_mask
    labeled_mask, num_features = ndimage.label(mask)
  File "C:\Users\<USER>\anaconda3\envs\geoserverapi\lib\site-packages\scipy\ndimage\_measurements.py", line 200, in label
    output = np.empty(input.shape, np.intp if need_64bits else np.int32)
numpy.core._exceptions._ArrayMemoryError: Unable to allocate 28.2 GiB for an array with shape (67180, 56283) and data type int64

目前的
