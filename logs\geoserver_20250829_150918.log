2025-08-29 15:09:18,901 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_150918.log
2025-08-29 15:09:18,903 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:09:18,903 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:09:18,925 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:09:18,929 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:09:18,930 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:09:18,946 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:09:18,947 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:09:18,948 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:09:18,964 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:09:18,965 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:09:18,966 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:09:18,981 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:09:18,983 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:09:18,984 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:09:18,999 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:09:19,001 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:09:19,002 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:09:19,018 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:09:19,037 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 15:09:19,042 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 15:09:24,312 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 15:09:24,366 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 15:09:24,374 - analysis_executor - INFO - 加载了 28 个任务状态
