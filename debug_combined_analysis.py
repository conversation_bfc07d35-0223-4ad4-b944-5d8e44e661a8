#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试合并分析执行问题
"""

import os
import json
import requests

def debug_combined_analysis():
    """调试合并分析执行问题"""
    print("🔍 调试合并分析执行问题...")
    
    # 从TaskInfo.json中获取最新任务的信息
    image_id = "20250705171601"
    task_info_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    if not os.path.exists(task_info_path):
        print(f"❌ TaskInfo.json不存在: {task_info_path}")
        return
    
    try:
        with open(task_info_path, 'r', encoding='utf-8') as f:
            task_data = json.load(f)
        
        if not isinstance(task_data, list) or len(task_data) == 0:
            print(f"❌ TaskInfo.json格式错误或为空")
            return
        
        # 获取最新任务
        latest_task = task_data[-1]
        task_id = latest_task.get('task_id')
        log_file = latest_task.get('log_file')
        
        print(f"📋 最新任务信息:")
        print(f"  任务ID: {task_id}")
        print(f"  状态: {latest_task.get('status')}")
        print(f"  进度: {latest_task.get('progress')}%")
        print(f"  日志文件: {log_file}")
        
        # 检查日志文件
        if log_file and os.path.exists(log_file):
            print(f"\n📄 分析日志文件内容:")
            analyze_log_file(log_file)
        else:
            print(f"❌ 日志文件不存在: {log_file}")
        
        # 检查API状态
        print(f"\n📡 检查API状态:")
        check_api_status(task_id)
        
        # 检查输出文件
        print(f"\n📁 检查输出文件:")
        check_output_files(latest_task)
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def analyze_log_file(log_file_path):
    """分析日志文件内容"""
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        print(f"  文件大小: {len(log_content)} 字符")
        
        # 检查关键步骤
        key_steps = [
            ("AI语义分割开始", "🚀 使用新版AI分析引擎进行处理"),
            ("AI语义分割完成", "✅ AI处理成功完成"),
            ("影像范围提取开始", "🗺️ 开始提取影像有效范围"),
            ("影像范围提取完成", "✅ 影像范围提取成功"),
            ("空间变化分析开始", "📊 开始空间变化分析"),
            ("空间变化分析完成", "空间数据变化分析完成"),
            ("任务完成", "=== 合并分析任务完成 ===")
        ]
        
        print(f"  关键步骤检查:")
        for step_name, search_text in key_steps:
            if search_text in log_content:
                print(f"    ✅ {step_name}")
            else:
                print(f"    ❌ {step_name} (未找到: {search_text})")
        
        # 检查错误信息
        error_keywords = ["❌", "ERROR", "失败", "异常", "Exception"]
        errors_found = []
        
        lines = log_content.split('\n')
        for i, line in enumerate(lines):
            for keyword in error_keywords:
                if keyword in line:
                    errors_found.append(f"第{i+1}行: {line.strip()}")
        
        if errors_found:
            print(f"  ⚠️ 发现错误信息:")
            for error in errors_found[-5:]:  # 只显示最后5个错误
                print(f"    {error}")
        else:
            print(f"  ✅ 未发现明显错误")
        
        # 检查TaskInfo更新相关日志
        taskinfo_updates = []
        for i, line in enumerate(lines):
            if "TaskInfo.json" in line or "📝 更新" in line:
                taskinfo_updates.append(f"第{i+1}行: {line.strip()}")
        
        if taskinfo_updates:
            print(f"  📝 TaskInfo更新日志:")
            for update in taskinfo_updates[-10:]:  # 显示最后10条更新日志
                print(f"    {update}")
        else:
            print(f"  ⚠️ 未找到TaskInfo更新日志")
        
        # 显示最后20行日志
        print(f"  📋 最后20行日志:")
        for line in lines[-20:]:
            if line.strip():
                print(f"    {line}")
                
    except Exception as e:
        print(f"  ❌ 分析日志文件失败: {e}")

def check_api_status(task_id):
    """检查API状态"""
    try:
        base_url = "http://127.0.0.1:8091/api/analysis"
        response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
        
        if response.status_code == 200:
            status_data = response.json()['data']
            print(f"  API状态: {status_data['task_status']}")
            print(f"  API消息: {status_data.get('message', 'N/A')}")
            print(f"  API进度: {status_data.get('progress', 0)}%")
        else:
            print(f"  ❌ API状态查询失败: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ API状态检查失败: {e}")

def check_output_files(task_info):
    """检查输出文件"""
    output_files = task_info.get('output_files', {})
    
    for file_type, file_path in output_files.items():
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"  ✅ {file_type}: {file_path} ({file_size} 字节)")
        else:
            print(f"  ❌ {file_type}: {file_path} (不存在)")
    
    # 检查可能的中间文件
    image_id = task_info.get('image_id')
    analysis_category = task_info.get('analysis_category')
    
    if image_id and analysis_category:
        base_dir = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/{analysis_category}"
        if os.path.exists(base_dir):
            print(f"  📁 分析目录内容:")
            try:
                files = os.listdir(base_dir)
                for file in files:
                    file_path = os.path.join(base_dir, file)
                    if os.path.isfile(file_path):
                        file_size = os.path.getsize(file_path)
                        print(f"    {file} ({file_size} 字节)")
            except Exception as e:
                print(f"    ❌ 读取目录失败: {e}")
        else:
            print(f"  ❌ 分析目录不存在: {base_dir}")

def test_new_combined_analysis():
    """测试修复后的合并分析"""
    print(f"\n🧪 测试修复后的合并分析...")

    base_url = "http://127.0.0.1:8091/api/analysis"

    # 测试数据
    params = {
        'id': '20250705171601',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }

    try:
        print(f"🚀 启动新的合并分析任务...")
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)

        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功: {task_id}")

            # 监控任务执行
            monitor_task_execution(base_url, task_id, params['id'])

        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")

    except Exception as e:
        print(f"❌ 测试失败: {e}")

def monitor_task_execution(base_url, task_id, image_id, max_wait=1200):
    """监控任务执行过程"""
    print(f"\n📊 监控任务执行过程...")

    import time
    start_time = time.time()
    last_status = None

    task_info_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"

    while time.time() - start_time < max_wait:
        try:
            # 检查API状态
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)

            if status_response.status_code == 200:
                api_status = status_response.json()['data']
                current_status = api_status['task_status']
                current_message = api_status.get('message', '')
                current_progress = api_status.get('progress', 0)

                if current_status != last_status:
                    print(f"🔄 状态变化: {current_status} - {current_message} ({current_progress}%)")
                    last_status = current_status

                # 检查TaskInfo.json
                if os.path.exists(task_info_path):
                    check_taskinfo_content(task_info_path, task_id)

                # 检查是否完成
                if current_status in ['完成', '失败']:
                    print(f"🏁 任务结束: {current_status}")

                    # 最终验证
                    final_check(task_info_path, task_id)
                    break

            time.sleep(15)  # 每15秒检查一次

        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(15)
            continue

    print(f"⏰ 监控结束")

def check_taskinfo_content(task_info_path, task_id):
    """检查TaskInfo.json内容"""
    try:
        with open(task_info_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        if isinstance(data, list):
            for task in data:
                if task.get('task_id') == task_id:
                    status = task.get('status', 'Unknown')
                    progress = task.get('progress', 0)

                    # 检查parameters中的model_name
                    params = task.get('parameters', {})
                    model_name = params.get('model_name', 'N/A')

                    # 检查results
                    results = task.get('results', {})
                    ai_time = results.get('ai_processing_time', 'N/A')
                    success = results.get('success', 'N/A')

                    # 检查空间统计
                    spatial_stats = results.get('spatial_statistics', {})
                    stats_complete = all(v is not None for v in spatial_stats.values() if v != spatial_stats.get('area_threshold'))

                    print(f"  📋 TaskInfo状态: {status} ({progress}%)")
                    print(f"  🏷️ 模型名称: {model_name}")
                    print(f"  ⏱️ AI处理时间: {ai_time}")
                    print(f"  ✅ 成功状态: {success}")
                    print(f"  📊 空间统计完整: {'是' if stats_complete else '否'}")

                    if spatial_stats:
                        non_null_stats = {k: v for k, v in spatial_stats.items() if v is not None}
                        print(f"  📈 非空统计字段: {len(non_null_stats)}/{len(spatial_stats)}")

                    break

    except Exception as e:
        print(f"  ❌ 检查TaskInfo失败: {e}")

def final_check(task_info_path, task_id):
    """最终检查"""
    print(f"\n🔍 最终检查结果...")

    try:
        with open(task_info_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        target_task = None
        if isinstance(data, list):
            for task in data:
                if task.get('task_id') == task_id:
                    target_task = task
                    break

        if not target_task:
            print(f"❌ 未找到目标任务")
            return

        # 验证关键字段
        checks = [
            ('model_name', target_task.get('parameters', {}).get('model_name')),
            ('ai_processing_time', target_task.get('results', {}).get('ai_processing_time')),
            ('success', target_task.get('results', {}).get('success')),
            ('status', target_task.get('status')),
            ('progress', target_task.get('progress'))
        ]

        print(f"📋 关键字段检查:")
        for field_name, value in checks:
            status = "✅" if value is not None and value != 'N/A' else "❌"
            print(f"  {status} {field_name}: {value}")

        # 检查空间统计
        spatial_stats = target_task.get('results', {}).get('spatial_statistics', {})
        if spatial_stats:
            print(f"📊 空间统计字段:")
            for key, value in spatial_stats.items():
                status = "✅" if value is not None else "❌"
                print(f"  {status} {key}: {value}")
        else:
            print(f"❌ 缺少空间统计信息")

        # 总体评估
        has_model_name = target_task.get('parameters', {}).get('model_name') is not None
        has_ai_time = target_task.get('results', {}).get('ai_processing_time') is not None
        has_spatial_stats = bool(spatial_stats and any(v is not None for v in spatial_stats.values()))
        is_complete = target_task.get('status') == '完成'

        if has_model_name and has_ai_time and has_spatial_stats and is_complete:
            print(f"🎉 所有修复都成功！")
        else:
            print(f"⚠️ 仍有问题需要解决:")
            if not has_model_name:
                print(f"  - 缺少模型名称")
            if not has_ai_time:
                print(f"  - 缺少AI处理时间")
            if not has_spatial_stats:
                print(f"  - 缺少空间统计信息")
            if not is_complete:
                print(f"  - 任务未完成")

    except Exception as e:
        print(f"❌ 最终检查失败: {e}")

def suggest_fixes():
    """建议修复方案"""
    print(f"\n🔧 可能的问题和修复建议:")
    print(f"1. 如果AI处理完成但没有进入后续步骤:")
    print(f"   - 检查_execute_ai_segmentation的返回值")
    print(f"   - 确认ai_result['success']为True")
    print(f"2. 如果影像范围提取失败:")
    print(f"   - 检查影像文件是否存在且可读")
    print(f"   - 确认输出目录权限")
    print(f"3. 如果空间变化分析失败:")
    print(f"   - 检查老数据文件是否存在")
    print(f"   - 确认坐标系是否一致")
    print(f"4. 如果TaskInfo更新失败:")
    print(f"   - 检查TaskInfo路径重建逻辑")
    print(f"   - 确认任务参数保存完整")
    print(f"   - 验证文件写入权限")
    print(f"5. 新增的修复:")
    print(f"   - TaskInfo路径丢失时自动重建")
    print(f"   - 增强的嵌套字段更新支持")
    print(f"   - 详细的调试日志输出")

if __name__ == "__main__":
    print("🚀 开始调试合并分析执行问题")

    # 先分析现有问题
    debug_combined_analysis()

    # 测试修复后的功能
    test_new_combined_analysis()

    suggest_fixes()

    print(f"\n📖 调试完成")
    print(f"请根据上述分析结果确定问题所在")
