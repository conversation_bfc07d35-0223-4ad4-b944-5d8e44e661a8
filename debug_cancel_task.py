#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试取消任务功能
验证取消任务不会影响正在执行的任务
"""

import requests
import json
import time
import threading
from datetime import datetime

def submit_multiple_tasks():
    """提交多个任务用于测试"""
    print("📤 提交多个测试任务...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试任务
    test_tasks = [
        {
            'name': '任务1-应该执行',
            'params': {
                'id': '20250705171599',
                'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
                'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
                'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'area_threshold': 400.0,
                'model_type': 'deeplabv3_plus'
            }
        },
        {
            'name': '任务2-将被取消',
            'params': {
                'id': '20250705171600',
                'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600_out.tif',
                'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
                'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
                'area_threshold': 200.0,
                'model_type': 'deeplabv3_plus'
            }
        },
        {
            'name': '任务3-应该等待',
            'params': {
                'id': '20250705171601',
                'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
                'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
                'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
                'area_threshold': 400.0,
                'model_type': 'deeplabv3_plus'
            }
        }
    ]
    
    task_ids = []
    
    for task in test_tasks:
        try:
            print(f"  提交: {task['name']}")
            
            response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                                  params=task['params'], timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    task_id = result['data']['task_id']
                    queue_position = result['data']['queue_position']
                    
                    task_ids.append({
                        'name': task['name'],
                        'task_id': task_id,
                        'queue_position': queue_position
                    })
                    
                    print(f"    ✅ 成功: {task_id[:8]}..., 队列位置: {queue_position}")
                else:
                    print(f"    ❌ 失败: {result['message']}")
            else:
                print(f"    ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"    ❌ 异常: {e}")
    
    return task_ids

def monitor_tasks(task_ids, duration=120):
    """监控任务状态变化"""
    print(f"\n👀 监控任务状态变化（{duration}秒）...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    start_time = time.time()
    last_status = {}
    
    while time.time() - start_time < duration:
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            
            # 获取队列整体状态
            response = requests.get(f"{base_url}/queue-status/", timeout=5)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    current_task = data.get('current_task')
                    queue_size = data.get('queue_size', 0)
                    waiting_tasks = data.get('waiting_tasks', 0)
                    
                    queue_status = f"执行中:{current_task[:8] if current_task else 'None'}..., 队列:{queue_size}, 等待:{waiting_tasks}"
                    
                    if queue_status != last_status.get('queue'):
                        print(f"📊 {timestamp} - {queue_status}")
                        last_status['queue'] = queue_status
            
            # 检查每个任务的状态
            for task_info in task_ids:
                task_id = task_info['task_id']
                task_name = task_info['name']
                
                try:
                    response = requests.get(f"{base_url}/queue-status/", 
                                          params={'task_id': task_id}, timeout=5)
                    if response.status_code == 200:
                        result = response.json()
                        if result['success']:
                            data = result['data']
                            status = data.get('status', 'unknown')
                            message = data.get('message', '')
                            
                            status_key = f"task_{task_id}"
                            current_status = f"{status} - {message}"
                            
                            if current_status != last_status.get(status_key):
                                print(f"🔄 {timestamp} - {task_name} ({task_id[:8]}...): {current_status}")
                                last_status[status_key] = current_status
                except:
                    pass
            
            time.sleep(3)  # 每3秒检查一次
            
        except Exception as e:
            print(f"⚠️ 监控异常: {e}")
            time.sleep(3)

def test_cancel_waiting_task(task_ids):
    """测试取消等待中的任务"""
    print(f"\n❌ 测试取消等待中的任务...")
    
    # 等待第一个任务开始执行
    print("⏰ 等待10秒，让第一个任务开始执行...")
    time.sleep(10)
    
    # 查找等待中的任务
    waiting_tasks = []
    for task_info in task_ids:
        task_id = task_info['task_id']
        task_name = task_info['name']
        
        try:
            response = requests.get("http://127.0.0.1:8091/api/analysis/queue-status/", 
                                  params={'task_id': task_id}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    status = data.get('status', 'unknown')
                    
                    if status == '等待中':
                        waiting_tasks.append(task_info)
                        print(f"  发现等待中的任务: {task_name} ({task_id[:8]}...)")
        except Exception as e:
            print(f"  检查任务状态异常: {e}")
    
    if not waiting_tasks:
        print("  ⚠️ 没有找到等待中的任务")
        return
    
    # 取消第一个等待中的任务
    cancel_task = waiting_tasks[0]
    print(f"\n🎯 尝试取消任务: {cancel_task['name']} ({cancel_task['task_id'][:8]}...)")
    
    try:
        response = requests.get("http://127.0.0.1:8091/api/analysis/cancel-queued-task/", 
                              params={'task_id': cancel_task['task_id']}, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                print(f"  ✅ 取消成功: {result['message']}")
            else:
                print(f"  ❌ 取消失败: {result['message']}")
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 取消异常: {e}")

def check_taskinfo_files(task_ids):
    """检查TaskInfo.json文件状态"""
    print(f"\n📁 检查TaskInfo.json文件状态...")
    
    for task_info in task_ids:
        task_id = task_info['task_id']
        task_name = task_info['name']
        
        # 从任务参数中获取image_id
        image_id = None
        if 'params' in task_info:
            image_id = task_info['params']['id']
        else:
            # 尝试从任务ID推断（这里需要根据实际情况调整）
            continue
        
        taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
        
        try:
            if os.path.exists(taskinfo_path):
                with open(taskinfo_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    for record in data:
                        if record.get('task_id') == task_id:
                            status = record.get('status', 'unknown')
                            message = record.get('message', '')
                            print(f"  📄 {task_name}: {status} - {message}")
                            break
                    else:
                        print(f"  📄 {task_name}: 未找到记录")
                else:
                    print(f"  📄 {task_name}: 文件格式错误")
            else:
                print(f"  📄 {task_name}: 文件不存在")
                
        except Exception as e:
            print(f"  📄 {task_name}: 读取异常 - {e}")

def main():
    """主函数"""
    print("🧪 调试取消任务功能")
    
    print(f"\n📝 测试场景:")
    print(f"1. 提交3个任务到队列")
    print(f"2. 等待第1个任务开始执行")
    print(f"3. 取消第2个任务（等待中）")
    print(f"4. 观察第1个任务是否继续正常执行")
    print(f"5. 观察第3个任务是否正常等待")
    
    # 提交任务
    task_ids = submit_multiple_tasks()
    
    if len(task_ids) < 3:
        print("❌ 提交的任务数量不足，测试终止")
        return
    
    # 启动监控线程
    monitor_thread = threading.Thread(target=monitor_tasks, args=(task_ids, 180))
    monitor_thread.daemon = True
    monitor_thread.start()
    
    # 测试取消功能
    test_cancel_waiting_task(task_ids)
    
    # 继续观察
    print(f"\n⏰ 继续观察60秒，检查取消操作的影响...")
    time.sleep(60)
    
    # 检查文件状态
    check_taskinfo_files(task_ids)
    
    print(f"\n📊 测试完成")
    print(f"\n🔍 预期结果:")
    print(f"1. ✅ 第1个任务应该继续执行，不受取消操作影响")
    print(f"2. ✅ 第2个任务应该被成功取消，状态变为'已取消'")
    print(f"3. ✅ 第3个任务应该继续等待，不受影响")
    print(f"4. ❌ 如果第1个任务异常结束，说明取消操作有问题")

if __name__ == "__main__":
    import os
    main()
