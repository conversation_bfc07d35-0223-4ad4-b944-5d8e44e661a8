#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试队列修复效果
验证排队机制是否正常工作，确保任务按顺序执行
"""

import requests
import json
import time
import threading

def test_queue_fix():
    """测试队列修复效果"""
    print("🔧 测试队列修复效果...")
    
    base_url = "http://192.168.43.148:8091/api/analysis"
    
    # 首先检查队列状态
    print(f"\n📊 检查初始队列状态...")
    initial_status = check_queue_status(base_url)
    
    if initial_status and initial_status.get('executing_task'):
        print(f"⚠️ 发现有任务正在执行: {initial_status['executing_task']}")
        print(f"   请等待当前任务完成后再测试")
        return False
    
    # 测试参数 - 使用你的实际参数
    test_params_1 = {
        'id': '20250705171598',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
        'area_threshold': 200
    }
    
    test_params_2 = {
        'id': '20250705171597',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171597/20250705171597_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
        'area_threshold': 200
    }
    
    print(f"\n🚀 开始排队测试...")
    
    # 快速连续提交两个任务
    print(f"📤 快速提交第一个任务...")
    task_id_1 = submit_queued_task(base_url, test_params_1, "任务1")
    
    print(f"📤 立即提交第二个任务...")
    task_id_2 = submit_queued_task(base_url, test_params_2, "任务2")
    
    if not task_id_1 or not task_id_2:
        print(f"❌ 任务提交失败")
        return False
    
    # 等待2秒让任务开始执行
    time.sleep(2)
    
    # 检查队列状态
    print(f"\n📊 提交两个任务后的队列状态...")
    queue_status = check_queue_status(base_url)
    
    if not queue_status:
        print(f"❌ 无法获取队列状态")
        return False
    
    # 验证排队机制
    executing_task = queue_status.get('executing_task')
    queue_size = queue_status.get('queue_size', 0)
    waiting_tasks = queue_status.get('waiting_tasks', 0)
    
    print(f"\n🔍 排队机制验证:")
    print(f"   执行任务: {executing_task or '无'}")
    print(f"   队列大小: {queue_size}")
    print(f"   等待任务: {waiting_tasks}")
    
    # 检查任务状态
    print(f"\n📋 检查任务状态...")
    task_1_status = check_task_status(base_url, task_id_1, "任务1")
    task_2_status = check_task_status(base_url, task_id_2, "任务2")
    
    # 判断排队是否正常
    if executing_task and (queue_size > 0 or waiting_tasks > 0):
        print(f"✅ 排队机制正常工作！")
        print(f"   有任务在执行，其他任务在等待")
        
        # 继续监控
        monitor_queue_execution(base_url, [task_id_1, task_id_2], max_time=120)
        return True
        
    elif executing_task and queue_size == 0 and waiting_tasks == 0:
        print(f"⚠️ 可能的情况：")
        print(f"   1. 第二个任务已经完成（不太可能）")
        print(f"   2. 第二个任务没有正确加入队列")
        
        # 检查任务状态来确认
        if task_1_status and task_2_status:
            status_1 = task_1_status.get('status', 'unknown')
            status_2 = task_2_status.get('status', 'unknown')
            
            if status_1 == '执行中' and status_2 == '等待中':
                print(f"✅ 排队机制正常：任务1执行中，任务2等待中")
                return True
            elif status_1 == '执行中' and status_2 == '执行中':
                print(f"❌ 排队机制失败：两个任务都在执行中！")
                return False
            else:
                print(f"🤔 状态不明确：任务1={status_1}, 任务2={status_2}")
                return False
        
    else:
        print(f"❌ 排队机制异常：")
        print(f"   没有任务在执行，但应该有任务开始执行")
        return False

def submit_queued_task(base_url, params, task_name):
    """提交排队任务"""
    try:
        print(f"   {task_name}: {params['id']}")
        
        # 使用排队接口
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=params, timeout=30)
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                task_id = result['data']['task_id']
                queue_position = result['data']['queue_position']
                
                print(f"   ✅ {task_name}提交成功")
                print(f"      任务ID: {task_id[:8]}...")
                print(f"      队列位置: {queue_position}")
                
                return task_id
            else:
                print(f"   ❌ {task_name}提交失败: {result['message']}")
                return None
        else:
            print(f"   ❌ {task_name}请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ {task_name}异常: {e}")
        return None

def check_queue_status(base_url):
    """检查队列状态"""
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                queue_data = result['data']
                
                print(f"   📊 队列状态:")
                print(f"      队列长度: {queue_data['queue_size']}")
                print(f"      等待任务: {queue_data['waiting_tasks']}")
                print(f"      执行任务: {queue_data['executing_task'] or '无'}")
                print(f"      已完成: {queue_data['completed_tasks']}")
                print(f"      已取消: {queue_data['cancelled_tasks']}")
                print(f"      运行状态: {'运行中' if queue_data['is_running'] else '已停止'}")
                
                return queue_data
            else:
                print(f"   ❌ 队列状态查询失败: {result['message']}")
                return None
        else:
            print(f"   ❌ 队列状态请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 队列状态查询异常: {e}")
        return None

def check_task_status(base_url, task_id, task_name):
    """检查任务状态"""
    try:
        response = requests.get(f"{base_url}/queued-task-status/", params={'task_id': task_id}, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                task_data = result['data']
                
                print(f"   📋 {task_name}状态:")
                print(f"      状态: {task_data['status']}")
                print(f"      队列位置: {task_data.get('queue_position', 'N/A')}")
                
                return task_data
            else:
                print(f"   ❌ {task_name}状态查询失败: {result['message']}")
                return None
        else:
            print(f"   ❌ {task_name}状态请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ {task_name}状态查询异常: {e}")
        return None

def monitor_queue_execution(base_url, task_ids, max_time=120):
    """监控队列执行"""
    print(f"\n🔍 监控队列执行情况（最多{max_time}秒）...")
    
    start_time = time.time()
    last_status = {}
    
    while time.time() - start_time < max_time:
        try:
            print(f"\n⏰ 监控时间: {int(time.time() - start_time)}秒")
            
            # 检查队列状态
            queue_status = check_queue_status(base_url)
            
            # 检查每个任务状态
            for i, task_id in enumerate(task_ids):
                task_name = f"任务{i+1}"
                task_status = check_task_status(base_url, task_id, task_name)
                
                if task_status:
                    current_status = task_status['status']
                    
                    # 检查状态变化
                    if task_id not in last_status or last_status[task_id] != current_status:
                        print(f"   🔄 {task_name}状态变化: {current_status}")
                        last_status[task_id] = current_status
            
            # 验证排队机制
            if queue_status:
                executing_task = queue_status['executing_task']
                queue_size = queue_status['queue_size']
                
                if executing_task and queue_size > 0:
                    print(f"   ✅ 排队正常: 执行中，队列还有{queue_size}个任务")
                elif executing_task and queue_size == 0:
                    print(f"   ✅ 排队正常: 执行最后一个任务")
                elif not executing_task and queue_size == 0:
                    print(f"   🎉 所有任务执行完成！")
                    break
                else:
                    print(f"   ⚠️ 异常状态: 无执行任务但队列有{queue_size}个任务")
            
            time.sleep(10)  # 每10秒检查一次
            
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断监控")
            break
        except Exception as e:
            print(f"   ❌ 监控异常: {e}")
            time.sleep(10)
    
    print(f"\n📊 监控结束")

def main():
    """主函数"""
    print("🚀 开始测试队列修复效果")
    
    print(f"\n📝 修复内容:")
    print(f"1. 强化了队列管理器的单例模式")
    print(f"2. 添加了全局锁防止多个实例")
    print(f"3. 增强了任务执行前的并发检查")
    print(f"4. 改进了日志输出，便于调试")
    
    print(f"\n🎯 测试目标:")
    print(f"1. 验证两个任务不会同时执行")
    print(f"2. 确认第一个任务执行时，第二个任务等待")
    print(f"3. 检查队列状态的准确性")
    
    success = test_queue_fix()
    
    if success:
        print(f"\n🎉 队列修复测试通过！")
        print(f"✅ 排队机制正常工作")
        print(f"✅ 任务按顺序执行")
        print(f"✅ 避免了并发执行问题")
    else:
        print(f"\n❌ 队列修复测试失败")
        print(f"请检查服务器日志，查看详细的调试信息")
        print(f"特别关注以下日志标记:")
        print(f"- 🏗️ 创建全局任务队列管理器实例")
        print(f"- 🔍 使用队列管理器实例ID")
        print(f"- 🚨 严重错误：尝试并发执行任务！")
        print(f"- 🎯 设置执行任务")

if __name__ == "__main__":
    main()
