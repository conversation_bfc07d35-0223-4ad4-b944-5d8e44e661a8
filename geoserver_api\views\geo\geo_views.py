#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer发布视图模块

功能说明:
- 异步Shapefile发布：异步发布Shapefile到GeoServer
- 异步GeoTIFF发布：异步发布GeoTIFF到GeoServer
- 发布任务状态查询：查询发布任务的执行状态和进度
- 发布任务管理：获取所有发布任务列表和详细信息

开发者注意:
- 发布功能支持异步执行，避免长时间阻塞API响应
- 自动处理图层冲突，支持覆盖已存在的图层
- 提供详细的发布进度和错误信息
- 支持Shapefile和GeoTIFF两种主要地理数据格式
"""

import os
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

# 导入核心模块
from ...core.geo_publisher import geo_executor, geo_logger


@api_view(['GET'])
def execute_publish_shapefile(request):
    """
    异步发布Shapefile到GeoServer
    
    功能: 启动异步任务发布Shapefile数据到GeoServer
    
    查询参数:
        path: Shapefile文件路径 (必选)
        workspace: 工作区名称 (必选)
        store_name: 存储名称 (可选)
        layer_name: 图层名称 (可选)
        charset: 字符集，默认UTF-8 (可选)
        
    返回: 任务ID及状态信息
        
    注意: 如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层
    """
    try:
        # 获取参数
        shapefile_path = request.GET.get("path")
        workspace = request.GET.get("workspace")
        
        # 验证必要参数
        if not shapefile_path or not workspace:
            missing_params = []
            if not shapefile_path:
                missing_params.append("path")
            if not workspace:
                missing_params.append("workspace")
                
            return Response({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查文件是否存在
        if not os.path.exists(shapefile_path):
            return Response({
                'status': 'error',
                'message': f'Shapefile文件不存在: {shapefile_path}'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 验证文件格式
        if not shapefile_path.lower().endswith('.shp'):
            return Response({
                'status': 'error',
                'message': '文件必须是Shapefile格式 (.shp)'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 可选参数
        store_name = request.GET.get("store_name")
        layer_name = request.GET.get("layer_name")
        charset = request.GET.get("charset", "UTF-8")
        
        geo_logger.info(f"开始异步发布Shapefile: {shapefile_path} -> {workspace}")
        
        # 执行异步发布任务
        task_id = geo_executor.execute_publish_shapefile(
            shapefile_path, workspace, store_name, layer_name, charset
        )
        
        if task_id:
            geo_logger.info(f"Shapefile发布任务已启动，任务ID: {task_id}")
            return Response({
                'status': 'success',
                'message': 'Shapefile发布任务已启动',
                'task_id': task_id,
                'details': {
                    'shapefile_path': shapefile_path,
                    'workspace': workspace,
                    'store_name': store_name,
                    'layer_name': layer_name,
                    'charset': charset
                }
            })
        else:
            geo_logger.error("启动Shapefile发布任务失败")
            return Response({
                'status': 'error',
                'message': '启动Shapefile发布任务失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        geo_logger.error(f"发布Shapefile时出错: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def execute_publish_geotiff(request):
    """
    异步发布GeoTIFF到GeoServer
    
    功能: 启动异步任务发布GeoTIFF数据到GeoServer
    
    查询参数:
        path: GeoTIFF文件路径 (必选)
        workspace: 工作区名称 (必选)
        store_name: 存储名称 (可选)
        layer_name: 图层名称 (可选)
        
    返回: 任务ID及状态信息
        
    注意: 如果GeoServer中已存在相同工作区和图层名的图层，系统会自动删除已有图层，然后发布新图层
    """
    try:
        # 获取参数
        geotiff_path = request.GET.get("path")
        workspace = request.GET.get("workspace")
        
        # 验证必要参数
        if not geotiff_path or not workspace:
            missing_params = []
            if not geotiff_path:
                missing_params.append("path")
            if not workspace:
                missing_params.append("workspace")
                
            return Response({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查文件是否存在
        if not os.path.exists(geotiff_path):
            return Response({
                'status': 'error',
                'message': f'GeoTIFF文件不存在: {geotiff_path}'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # 验证文件格式
        valid_extensions = ['.tif', '.tiff', '.geotiff']
        if not any(geotiff_path.lower().endswith(ext) for ext in valid_extensions):
            return Response({
                'status': 'error',
                'message': f'文件必须是GeoTIFF格式 {valid_extensions}'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 可选参数
        store_name = request.GET.get("store_name")
        layer_name = request.GET.get("layer_name")
        
        geo_logger.info(f"开始异步发布GeoTIFF: {geotiff_path} -> {workspace}")
        
        # 执行异步发布任务
        task_id = geo_executor.execute_publish_geotiff(
            geotiff_path, workspace, store_name, layer_name
        )
        
        if task_id:
            geo_logger.info(f"GeoTIFF发布任务已启动，任务ID: {task_id}")
            return Response({
                'status': 'success',
                'message': 'GeoTIFF发布任务已启动',
                'task_id': task_id,
                'details': {
                    'geotiff_path': geotiff_path,
                    'workspace': workspace,
                    'store_name': store_name,
                    'layer_name': layer_name
                }
            })
        else:
            geo_logger.error("启动GeoTIFF发布任务失败")
            return Response({
                'status': 'error',
                'message': '启动GeoTIFF发布任务失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    except Exception as e:
        geo_logger.error(f"发布GeoTIFF时出错: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_publish_status(request):
    """
    获取发布任务状态
    
    功能: 查询指定发布任务的执行状态和详细信息
    
    查询参数:
        task_id: 任务ID (必选)
        
    返回: 任务状态信息
    """
    try:
        # 获取任务ID
        task_id = request.GET.get('task_id')
        
        # 检查任务ID是否提供
        if not task_id:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        geo_logger.info(f"查询发布任务状态: {task_id}")
        
        # 获取任务状态
        task_status = geo_executor.get_task_status(task_id)
        
        if not task_status:
            return Response({
                'status': 'error',
                'message': f'未找到任务: {task_id}'
            }, status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            'status': 'success',
            'task': task_status
        })
        
    except Exception as e:
        geo_logger.error(f"获取发布任务状态时出错: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_all_publish_status(request):
    """
    获取所有发布任务状态
    
    功能: 查询系统中所有发布任务的状态列表
    
    返回: 所有任务状态信息
    """
    try:
        geo_logger.info("查询所有发布任务状态")
        
        # 获取所有任务状态
        tasks = geo_executor.get_all_tasks()
        
        # 统计任务状态
        status_counts = {}
        for task in tasks:
            task_status = task.get('status', 'unknown')
            status_counts[task_status] = status_counts.get(task_status, 0) + 1
        
        return Response({
            'status': 'success',
            'count': len(tasks),
            'status_counts': status_counts,
            'tasks': tasks
        })
        
    except Exception as e:
        geo_logger.error(f"获取所有发布任务状态时出错: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_geo_task_log(request):
    """
    获取GeoServer发布任务日志

    功能: 获取指定发布任务的执行日志内容

    查询参数:
        task_id: 任务ID (必选)
        lines: 返回的日志行数 (可选，默认: 100)

    返回: 任务日志信息
    """
    try:
        task_id = request.GET.get('task_id')
        lines = int(request.GET.get('lines', 100))

        if not task_id:
            return Response({
                'status': 'error',
                'message': '缺少必要参数: task_id'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证lines参数
        if lines <= 0:
            return Response({
                'status': 'error',
                'message': 'lines参数必须是正整数'
            }, status=status.HTTP_400_BAD_REQUEST)

        geo_logger.info(f"获取发布任务日志: {task_id}, 行数: {lines}")

        log_content = geo_executor.get_task_log(task_id, lines)

        if log_content is None:
            return Response({
                'status': 'error',
                'message': f'未找到任务日志: {task_id}'
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            'status': 'success',
            'task_id': task_id,
            'log_lines': lines,
            'log_content': log_content
        })

    except ValueError:
        return Response({
            'status': 'error',
            'message': 'lines参数必须是有效的整数'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        geo_logger.error(f"获取发布任务日志时出错: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def execute_publish_structured_geotiff(request):
    """
    异步发布特定结构的GeoTIFF文件到GeoServer

    功能: 扫描根目录下的子目录，发布每个子目录中与子目录同名的GeoTIFF文件

    查询参数:
        root_directory: 根目录路径 (必选)
        workspace: 工作区名称 (必选)
        store_name: 存储名称前缀 (可选)

    返回: 任务ID及状态信息

    示例:
        输入根目录: D:/Drone_Project/nginxData/ODM/Output
        如果存在子目录: 20250705171600/20250705171600.tif
        则会发布该GeoTIFF文件到GeoServer
    """
    try:
        # 获取参数
        root_directory = request.GET.get("root_directory")
        workspace = request.GET.get("workspace")

        # 验证必要参数
        if not root_directory or not workspace:
            missing_params = []
            if not root_directory:
                missing_params.append("root_directory")
            if not workspace:
                missing_params.append("workspace")

            return Response({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查根目录是否存在
        if not os.path.exists(root_directory):
            return Response({
                'status': 'error',
                'message': f'根目录不存在: {root_directory}'
            }, status=status.HTTP_404_NOT_FOUND)

        # 检查是否是目录
        if not os.path.isdir(root_directory):
            return Response({
                'status': 'error',
                'message': f'指定路径不是目录: {root_directory}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 可选参数
        store_name = request.GET.get("store_name")

        geo_logger.info(f"开始异步发布结构化GeoTIFF: {root_directory} -> {workspace}")

        # 执行异步发布任务
        task_id = geo_executor.execute_publish_structured_geotiff(
            root_directory, workspace, store_name
        )

        if task_id:
            geo_logger.info(f"结构化GeoTIFF发布任务已启动，任务ID: {task_id}")
            return Response({
                'status': 'success',
                'message': '结构化GeoTIFF发布任务已启动',
                'task_id': task_id,
                'details': {
                    'root_directory': root_directory,
                    'workspace': workspace,
                    'store_name': store_name
                }
            })
        else:
            geo_logger.error("启动结构化GeoTIFF发布任务失败")
            return Response({
                'status': 'error',
                'message': '启动结构化GeoTIFF发布任务失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        geo_logger.error(f"发布结构化GeoTIFF时出错: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
def publish_shapefile_sync(request):
    """
    同步发布Shapefile到GeoServer

    功能: 同步发布Shapefile数据到GeoServer（立即返回结果）

    查询参数 (GET) 或请求体参数 (POST):
        file_path: Shapefile文件路径 (必选)
        workspace: 工作区名称 (必选)
        store: 存储名称 (可选，默认使用文件名)
        layer: 图层名称 (可选，默认使用文件名)
        charset: 字符集 (可选，默认: UTF-8)

    返回: 发布结果状态
    """
    try:
        # 导入必要的模块
        from pathlib import Path
        from ...core.geoserver_manager import GeoServerManager

        # 获取参数（支持GET和POST）
        if request.method == 'GET':
            file_path = request.GET.get('file_path')
            workspace = request.GET.get('workspace')
            store = request.GET.get('store')
            layer = request.GET.get('layer')
            charset = request.GET.get('charset', 'UTF-8')
        else:  # POST
            data = request.data
            file_path = data.get('file_path')
            workspace = data.get('workspace')
            store = data.get('store')
            layer = data.get('layer')
            charset = data.get('charset', 'UTF-8')

        # 验证必要参数
        if not all([file_path, workspace]):
            missing_params = []
            if not file_path:
                missing_params.append('file_path')
            if not workspace:
                missing_params.append('workspace')

            return Response({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证文件路径
        if not os.path.exists(file_path):
            return Response({
                'status': 'error',
                'message': f'文件不存在: {file_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        # 验证文件格式
        if not file_path.lower().endswith('.shp'):
            return Response({
                'status': 'error',
                'message': '文件必须是Shapefile格式 (.shp)'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查Shapefile完整性
        shp_base = Path(file_path).with_suffix('')
        required_files = ['.shp', '.shx', '.dbf']
        missing_files = []

        for ext in required_files:
            if not (shp_base.with_suffix(ext)).exists():
                missing_files.append(ext)

        if missing_files:
            return Response({
                'status': 'error',
                'message': f'Shapefile缺少必要文件: {missing_files}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 如果未指定存储名称和图层名称，使用文件名
        original_name = Path(file_path).stem  # 保存原始文件名
        if not store:
            store = original_name
        if not layer:
            layer = original_name

        geo_logger.info(f"开始同步发布Shapefile: {file_path} -> {workspace}:{layer}")

        # 创建GeoServer管理器实例
        manager = GeoServerManager()

        # 创建工作区（如果不存在会自动创建）
        workspace_result = manager.create_workspace(workspace)
        if not workspace_result.get('success', False):
            geo_logger.warning(f"工作区创建结果: {workspace_result.get('message', '未知错误')}")
            # 继续执行，可能工作区已存在

        # 检查工作区-存储仓库-图层组合是否已存在
        layer_exists = manager.check_layer_exists(workspace, layer)
        store_exists = manager.check_datastore_exists(workspace, store)

        if layer_exists or store_exists:
            geo_logger.info(f"检测到冲突 - 图层存在: {layer_exists}, 存储存在: {store_exists}")
            geo_logger.info(f"正在删除组合: {workspace}:{store}:{layer}")

            # 删除指定的工作区-存储仓库-图层组合
            delete_result = manager.delete_workspace_store_layer(workspace, store, layer)
            geo_logger.info(f"删除结果: {delete_result.get('message', '未知')}")

            if delete_result.get('details', {}).get('deleted'):
                geo_logger.info(f"已删除: {delete_result['details']['deleted']}")
            if delete_result.get('details', {}).get('errors'):
                geo_logger.warning(f"删除错误: {delete_result['details']['errors']}")

            geo_logger.info(f"清理完成，开始重新发布")
        else:
            geo_logger.info(f"无冲突，直接发布: {workspace}:{store}:{layer}")

        # 创建Shapefile数据存储
        datastore_result = manager.create_shapefile_datastore(workspace, store, file_path)
        if not datastore_result.get('success', False):
            geo_logger.error(f"创建数据存储失败: {datastore_result.get('message', '未知错误')}")
            return Response({
                'status': 'error',
                'message': f'创建数据存储失败: {datastore_result.get("message", "未知错误")}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 发布图层
        layer_result = manager.publish_layer(workspace, store, layer)

        if layer_result.get('success', False):
            geo_logger.info(f"Shapefile同步发布成功: {workspace}:{layer}")

            # 构建成功消息
            success_message = 'Shapefile发布成功'
            if layer_exists or store_exists:
                success_message += ' (已覆盖现有资源)'

            return Response({
                'status': 'success',
                'message': success_message,
                'details': {
                    'file_path': file_path,
                    'workspace': workspace,
                    'store': store,
                    'layer': layer,
                    'charset': charset,
                    'crs': 'EPSG:4326',  # 当前强制使用4326
                    'replaced_existing': layer_exists or store_exists
                }
            })
        else:
            geo_logger.error(f"Shapefile同步发布失败: {layer_result.get('message', '未知错误')}")
            return Response({
                'status': 'error',
                'message': f'Shapefile发布失败: {layer_result.get("message", "未知错误")}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        geo_logger.error(f"同步发布Shapefile时出错: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET', 'POST'])
def publish_shapefile_with_crs(request):
    """
    发布Shapefile到GeoServer（支持指定坐标系）

    功能: 发布Shapefile数据到GeoServer，支持指定EPSG坐标系

    查询参数 (GET) 或请求体参数 (POST):
        file_path: Shapefile文件路径 (必选)
        workspace: 工作区名称 (必选)
        store: 存储名称 (可选，默认使用文件名)
        layer: 图层名称 (可选，默认使用文件名)
        charset: 字符集 (可选，默认: UTF-8)
        epsg: EPSG坐标系代码 (可选，默认: 4326)

    返回: 发布结果状态
    """
    try:
        # 导入必要的模块
        from pathlib import Path
        from ...core.geoserver_manager import GeoServerManager

        # 获取参数（支持GET和POST）
        if request.method == 'GET':
            file_path = request.GET.get('file_path')
            workspace = request.GET.get('workspace')
            store = request.GET.get('store')
            layer = request.GET.get('layer')
            charset = request.GET.get('charset', 'UTF-8')
            epsg = request.GET.get('epsg', '4326')
        else:  # POST
            data = request.data
            file_path = data.get('file_path')
            workspace = data.get('workspace')
            store = data.get('store')
            layer = data.get('layer')
            charset = data.get('charset', 'UTF-8')
            epsg = data.get('epsg', '4326')

        # 验证必要参数
        if not all([file_path, workspace]):
            missing_params = []
            if not file_path:
                missing_params.append('file_path')
            if not workspace:
                missing_params.append('workspace')

            return Response({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证EPSG代码格式
        try:
            epsg_code = int(epsg)
            if epsg_code <= 0:
                raise ValueError("EPSG代码必须是正整数")
        except (ValueError, TypeError):
            return Response({
                'status': 'error',
                'message': f'无效的EPSG代码: {epsg}，必须是正整数'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证文件路径
        if not os.path.exists(file_path):
            return Response({
                'status': 'error',
                'message': f'文件不存在: {file_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        # 验证文件格式
        if not file_path.lower().endswith('.shp'):
            return Response({
                'status': 'error',
                'message': '文件必须是Shapefile格式 (.shp)'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查Shapefile完整性
        shp_base = Path(file_path).with_suffix('')
        required_files = ['.shp', '.shx', '.dbf']
        missing_files = []

        for ext in required_files:
            if not (shp_base.with_suffix(ext)).exists():
                missing_files.append(ext)

        if missing_files:
            return Response({
                'status': 'error',
                'message': f'Shapefile缺少必要文件: {missing_files}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 如果未指定存储名称和图层名称，使用文件名
        original_name = Path(file_path).stem
        if not store:
            store = original_name
        if not layer:
            layer = original_name

        geo_logger.info(f"开始发布Shapefile (EPSG:{epsg}): {file_path} -> {workspace}:{layer}")

        # 创建GeoServer管理器实例
        manager = GeoServerManager()

        # 创建工作区（如果不存在会自动创建）
        workspace_result = manager.create_workspace(workspace)
        if not workspace_result.get('success', False):
            geo_logger.warning(f"工作区创建结果: {workspace_result.get('message', '未知错误')}")

        # 检查工作区-存储仓库-图层组合是否已存在
        layer_exists = manager.check_layer_exists(workspace, layer)
        store_exists = manager.check_datastore_exists(workspace, store)

        if layer_exists or store_exists:
            geo_logger.info(f"检测到冲突 - 图层存在: {layer_exists}, 存储存在: {store_exists}")
            geo_logger.info(f"正在删除组合: {workspace}:{store}:{layer}")

            # 删除指定的工作区-存储仓库-图层组合
            delete_result = manager.delete_workspace_store_layer(workspace, store, layer)
            geo_logger.info(f"删除结果: {delete_result.get('message', '未知')}")

            if delete_result.get('details', {}).get('deleted'):
                geo_logger.info(f"已删除: {delete_result['details']['deleted']}")
            if delete_result.get('details', {}).get('errors'):
                geo_logger.warning(f"删除错误: {delete_result['details']['errors']}")

            geo_logger.info(f"清理完成，开始重新发布")
        else:
            geo_logger.info(f"无冲突，直接发布: {workspace}:{store}:{layer}")

        # 创建Shapefile数据存储（支持指定坐标系）
        datastore_result = manager.create_shapefile_datastore_with_crs(workspace, store, file_path, f"EPSG:{epsg}")
        if not datastore_result.get('success', False):
            geo_logger.error(f"创建数据存储失败: {datastore_result.get('message', '未知错误')}")
            return Response({
                'status': 'error',
                'message': f'创建数据存储失败: {datastore_result.get("message", "未知错误")}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # 发布图层（使用指定的坐标系）
        layer_result = manager.publish_layer_with_crs(workspace, store, layer, f"EPSG:{epsg}", file_path)

        if layer_result.get('success', False):
            geo_logger.info(f"Shapefile发布成功 (EPSG:{epsg}): {workspace}:{layer}")

            # 构建成功消息
            success_message = f'Shapefile发布成功 (EPSG:{epsg})'
            if layer_exists or store_exists:
                success_message += ' (已覆盖现有资源)'

            return Response({
                'status': 'success',
                'message': success_message,
                'details': {
                    'file_path': file_path,
                    'workspace': workspace,
                    'store': store,
                    'layer': layer,
                    'charset': charset,
                    'crs': f'EPSG:{epsg}',
                    'replaced_existing': layer_exists or store_exists
                }
            })
        else:
            geo_logger.error(f"Shapefile发布失败: {layer_result.get('message', '未知错误')}")
            return Response({
                'status': 'error',
                'message': f'Shapefile发布失败: {layer_result.get("message", "未知错误")}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        geo_logger.error(f"发布Shapefile时出错: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def publish_shapefile_directory(request):
    """
    批量发布目录中所有Shapefile到GeoServer

    功能: 批量发布指定目录中的所有Shapefile文件

    查询参数:
        directory: Shapefile目录路径 (必选)
        workspace: 工作区名称 (必选)
        store: 存储名称前缀 (可选，默认使用文件名)
        charset: 字符集 (可选，默认UTF-8)

    返回: 批量发布结果状态
    """
    try:
        # 导入必要的模块
        from ...core.geoserver_manager import GeoServerManager

        directory = request.GET.get('directory')
        workspace = request.GET.get('workspace')
        store = request.GET.get('store')
        charset = request.GET.get('charset', 'UTF-8')

        # 验证必要参数
        if not all([directory, workspace]):
            missing_params = []
            if not directory:
                missing_params.append('directory')
            if not workspace:
                missing_params.append('workspace')

            return Response({
                'status': 'error',
                'message': f'缺少必要参数: {", ".join(missing_params)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证目录路径
        if not os.path.exists(directory):
            return Response({
                'status': 'error',
                'message': f'目录不存在: {directory}'
            }, status=status.HTTP_404_NOT_FOUND)

        if not os.path.isdir(directory):
            return Response({
                'status': 'error',
                'message': f'路径不是目录: {directory}'
            }, status=status.HTTP_400_BAD_REQUEST)

        geo_logger.info(f"开始批量发布目录中的Shapefile: {directory}")

        # 创建GeoServer管理器实例
        manager = GeoServerManager()

        # 手动实现批量发布逻辑
        import glob
        from pathlib import Path

        # 查找目录中的所有Shapefile
        shp_pattern = os.path.join(directory, "*.shp")
        shp_files = glob.glob(shp_pattern)

        if not shp_files:
            return Response({
                'status': 'error',
                'message': f'目录 {directory} 中没有找到Shapefile文件'
            }, status=status.HTTP_404_NOT_FOUND)

        # 创建工作区（如果不存在）
        workspace_result = manager.create_workspace(workspace)
        if not workspace_result.get('success', False):
            geo_logger.warning(f"工作区创建结果: {workspace_result.get('message', '未知错误')}")

        success_files = []
        failed_files = []

        # 逐个发布Shapefile
        for shp_file in shp_files:
            try:
                file_name = Path(shp_file).stem

                # 使用原始文件名作为store和layer名称
                store_name = f"{store}_{file_name}" if store else file_name
                layer_name = file_name  # 使用原始文件名作为图层名称

                geo_logger.info(f"发布Shapefile: {shp_file} -> {workspace}:{layer_name}")

                # 检查工作区-存储仓库-图层组合是否已存在
                layer_exists = manager.check_layer_exists(workspace, layer_name)
                store_exists = manager.check_datastore_exists(workspace, store_name)

                if layer_exists or store_exists:
                    geo_logger.info(f"检测到冲突 - 图层存在: {layer_exists}, 存储存在: {store_exists}")
                    geo_logger.info(f"正在删除组合: {workspace}:{store_name}:{layer_name}")

                    # 删除指定的工作区-存储仓库-图层组合
                    delete_result = manager.delete_workspace_store_layer(workspace, store_name, layer_name)
                    geo_logger.info(f"删除结果: {delete_result.get('message', '未知')}")

                    if delete_result.get('details', {}).get('errors'):
                        geo_logger.warning(f"删除时有错误: {delete_result['details']['errors']}")

                    geo_logger.info(f"清理完成: {file_name}")

                # 创建数据存储
                datastore_result = manager.create_shapefile_datastore(workspace, store_name, shp_file)
                if not datastore_result.get('success', False):
                    failed_files.append({
                        'file': file_name,
                        'error': f"创建数据存储失败: {datastore_result.get('message', '未知错误')}"
                    })
                    continue

                # 发布图层
                layer_result = manager.publish_layer(workspace, store_name, layer_name)
                if layer_result.get('success', False):
                    success_files.append(file_name)
                    geo_logger.info(f"成功发布: {file_name}")
                else:
                    failed_files.append({
                        'file': file_name,
                        'error': f"发布图层失败: {layer_result.get('message', '未知错误')}"
                    })

            except Exception as e:
                failed_files.append({
                    'file': Path(shp_file).stem,
                    'error': f"处理文件时出错: {str(e)}"
                })
                geo_logger.error(f"处理文件 {shp_file} 时出错: {str(e)}")

        success_count = len(success_files)
        failed_count = len(failed_files)

        geo_logger.info(f"批量发布完成: 成功 {success_count} 个，失败 {failed_count} 个")

        return Response({
            'status': 'success' if success_count > 0 else 'error',
            'message': f'发布了 {success_count} 个Shapefile，失败 {failed_count} 个',
            'details': {
                'success': success_files,
                'failed': failed_files
            }
        })

    except Exception as e:
        geo_logger.error(f"批量发布Shapefile时出错: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
def auto_tile_layer(request):
    """
    自动切片图层接口

    功能: 对指定的图层进行自动切片，使用EPSG:4326网格集，生成0-20级的PNG瓦片

    请求参数 (JSON):
        workspace: 工作区名称 (必选)
        layer_name: 图层名称 (必选)
        gridset: 网格集名称 (可选，默认EPSG:4326)
        zoom_start: 起始缩放级别 (可选，默认0)
        zoom_stop: 结束缩放级别 (可选，默认20)
        image_format: 图像格式 (可选，默认image/png)

    返回: 切片任务状态信息
    """
    try:
        # 导入GeoServer管理器
        from ...core.geoserver_manager import geoserver_manager

        # 获取请求参数
        workspace = request.data.get('workspace')
        layer_name = request.data.get('layer_name')
        gridset = request.data.get('gridset', 'EPSG:4326')
        zoom_start = int(request.data.get('zoom_start', 0))
        zoom_stop = int(request.data.get('zoom_stop', 20))
        image_format = request.data.get('image_format', 'image/png')

        # 验证必需参数
        if not workspace:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: workspace'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not layer_name:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: layer_name'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证缩放级别
        if zoom_start < 0 or zoom_stop < 0 or zoom_start > zoom_stop:
            return Response({
                'status': 'error',
                'message': '缩放级别参数无效，zoom_start和zoom_stop必须为非负数，且zoom_start <= zoom_stop'
            }, status=status.HTTP_400_BAD_REQUEST)

        geo_logger.info(f"开始自动切片图层: {workspace}:{layer_name}")
        geo_logger.info(f"切片参数: 网格集={gridset}, 级别={zoom_start}-{zoom_stop}, 格式={image_format}")

        # 检查图层是否存在
        layer_exists = geoserver_manager.check_layer_exists(workspace, layer_name)
        if not layer_exists:
            return Response({
                'status': 'error',
                'message': f'图层不存在: {workspace}:{layer_name}'
            }, status=status.HTTP_404_NOT_FOUND)

        # 执行自动切片
        result = geoserver_manager.auto_tile_layer(
            workspace=workspace,
            layer_name=layer_name,
            gridset=gridset,
            zoom_start=zoom_start,
            zoom_stop=zoom_stop,
            image_format=image_format
        )

        if result['success']:
            geo_logger.info(f"自动切片任务启动成功: {workspace}:{layer_name}")
            return Response({
                'status': 'success',
                'message': result['message'],
                'data': {
                    'workspace': workspace,
                    'layer_name': layer_name,
                    'gridset': gridset,
                    'zoom_levels': f'{zoom_start}-{zoom_stop}',
                    'image_format': image_format,
                    'details': result.get('details', {})
                }
            })
        else:
            geo_logger.error(f"自动切片任务启动失败: {result['message']}")
            return Response({
                'status': 'error',
                'message': result['message'],
                'data': {
                    'workspace': workspace,
                    'layer_name': layer_name,
                    'details': result.get('details', {})
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except ValueError as e:
        geo_logger.error(f"参数错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'参数错误: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        geo_logger.error(f"自动切片API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def get_tile_status(request):
    """
    获取切片任务状态接口

    功能: 查询指定图层或所有图层的切片任务状态

    查询参数:
        workspace: 工作区名称 (可选)
        layer_name: 图层名称 (可选)

    返回: 切片任务状态列表
    """
    try:
        # 导入GeoServer管理器
        from ...core.geoserver_manager import geoserver_manager

        # 获取查询参数
        workspace = request.GET.get('workspace')
        layer_name = request.GET.get('layer_name')

        geo_logger.info(f"查询切片任务状态: workspace={workspace}, layer_name={layer_name}")

        # 获取切片任务状态
        result = geoserver_manager.get_seed_status(workspace, layer_name)

        if result['success']:
            geo_logger.info(f"成功获取切片任务状态，共 {len(result['tasks'])} 个任务")
            return Response({
                'status': 'success',
                'message': result['message'],
                'data': {
                    'tasks': result['tasks'],
                    'total_tasks': len(result['tasks']),
                    'query_params': {
                        'workspace': workspace,
                        'layer_name': layer_name
                    }
                }
            })
        else:
            geo_logger.error(f"获取切片任务状态失败: {result['message']}")
            return Response({
                'status': 'error',
                'message': result['message'],
                'data': {
                    'tasks': [],
                    'query_params': {
                        'workspace': workspace,
                        'layer_name': layer_name
                    }
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    except Exception as e:
        geo_logger.error(f"获取切片状态API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
def batch_upload_styles(request):
    """
    批量上传文件夹中的GeoServer样式文件

    功能: 将指定文件夹中的所有样式文件(.txt/.sld/.xml)批量添加到GeoServer中

    查询参数:
        folder_path: 样式文件夹路径 (必选)
        workspace: 工作区名称 (可选，默认为空，表示全局样式)
        overwrite: 是否覆盖已存在的样式 (可选，默认为true)

    返回: 批量上传结果及详细信息
    """
    try:
        # 获取参数
        folder_path = request.GET.get("folder_path")
        workspace = request.GET.get("workspace", "")  # 空字符串表示全局样式
        overwrite_str = request.GET.get("overwrite", "true")
        overwrite = overwrite_str.lower() in ['true', '1', 'yes']

        # 参数验证
        if not folder_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: folder_path'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查文件夹是否存在
        if not os.path.exists(folder_path):
            return Response({
                'status': 'error',
                'message': f'指定的文件夹不存在: {folder_path}'
            }, status=status.HTTP_404_NOT_FOUND)

        if not os.path.isdir(folder_path):
            return Response({
                'status': 'error',
                'message': f'指定的路径不是文件夹: {folder_path}'
            }, status=status.HTTP_400_BAD_REQUEST)

        geo_logger.info(f"开始批量上传样式文件，文件夹: {folder_path}")
        geo_logger.info(f"工作区: {workspace if workspace else '全局样式'}")
        geo_logger.info(f"覆盖模式: {overwrite}")

        # 导入GeoServer管理器
        from ...core.geoserver_manager import geoserver_manager

        # 扫描文件夹中的样式文件
        style_files = []
        supported_extensions = ['.txt', '.sld', '.xml']

        for filename in os.listdir(folder_path):
            file_path = os.path.join(folder_path, filename)
            if os.path.isfile(file_path):
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in supported_extensions:
                    style_files.append({
                        'filename': filename,
                        'filepath': file_path,
                        'extension': file_ext
                    })

        if not style_files:
            return Response({
                'status': 'warning',
                'message': f'在指定文件夹中未找到支持的样式文件 ({", ".join(supported_extensions)})',
                'data': {
                    'folder_path': folder_path,
                    'supported_extensions': supported_extensions,
                    'found_files': 0
                }
            }, status=status.HTTP_200_OK)

        geo_logger.info(f"找到 {len(style_files)} 个样式文件")

        # 批量上传样式
        upload_results = []
        success_count = 0
        error_count = 0

        for style_file in style_files:
            filename = style_file['filename']
            filepath = style_file['filepath']

            # 从文件名提取样式名称（去掉扩展名）
            style_name = os.path.splitext(filename)[0]

            geo_logger.info(f"正在上传样式: {style_name} ({filename})")

            try:
                # 读取样式文件内容
                with open(filepath, 'r', encoding='utf-8') as f:
                    style_content = f.read()

                # 验证样式文件内容
                if not style_content.strip():
                    upload_results.append({
                        'filename': filename,
                        'style_name': style_name,
                        'status': 'error',
                        'message': '样式文件内容为空'
                    })
                    error_count += 1
                    continue

                # 检查是否为有效的SLD格式
                if not ('StyledLayerDescriptor' in style_content or 'FeatureTypeStyle' in style_content):
                    geo_logger.warning(f"样式文件 {filename} 可能不是有效的SLD格式")

                # 上传样式到GeoServer
                result = geoserver_manager.create_style(
                    style_name=style_name,
                    style_content=style_content,
                    workspace=workspace if workspace else None,
                    overwrite=overwrite
                )

                if result.get('success', False):
                    upload_results.append({
                        'filename': filename,
                        'style_name': style_name,
                        'status': 'success',
                        'message': '样式上传成功',
                        'workspace': workspace if workspace else 'global'
                    })
                    success_count += 1
                    geo_logger.info(f"样式 {style_name} 上传成功")
                else:
                    upload_results.append({
                        'filename': filename,
                        'style_name': style_name,
                        'status': 'error',
                        'message': result.get('message', '上传失败')
                    })
                    error_count += 1
                    geo_logger.error(f"样式 {style_name} 上传失败: {result.get('message', '未知错误')}")

            except UnicodeDecodeError as e:
                upload_results.append({
                    'filename': filename,
                    'style_name': style_name,
                    'status': 'error',
                    'message': f'文件编码错误: {str(e)}'
                })
                error_count += 1
                geo_logger.error(f"读取样式文件 {filename} 时编码错误: {str(e)}")

            except Exception as e:
                upload_results.append({
                    'filename': filename,
                    'style_name': style_name,
                    'status': 'error',
                    'message': f'处理文件时出错: {str(e)}'
                })
                error_count += 1
                geo_logger.error(f"处理样式文件 {filename} 时出错: {str(e)}")

        # 生成总结信息
        total_files = len(style_files)

        if success_count == total_files:
            response_status = 'success'
            message = f'成功上传所有 {total_files} 个样式文件'
        elif success_count > 0:
            response_status = 'partial_success'
            message = f'部分成功：成功上传 {success_count} 个，失败 {error_count} 个样式文件'
        else:
            response_status = 'error'
            message = f'所有样式文件上传失败，共 {total_files} 个文件'

        geo_logger.info(f"批量样式上传完成: {message}")

        return Response({
            'status': response_status,
            'message': message,
            'data': {
                'folder_path': folder_path,
                'workspace': workspace if workspace else 'global',
                'overwrite': overwrite,
                'summary': {
                    'total_files': total_files,
                    'success_count': success_count,
                    'error_count': error_count,
                    'supported_extensions': supported_extensions
                },
                'results': upload_results
            }
        })

    except Exception as e:
        geo_logger.error(f"批量样式上传API错误: {str(e)}")
        import traceback
        geo_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
