2025-08-21 17:44:34 - INFO - === 合并分析任务开始 ===
2025-08-21 17:44:34 - INFO - 任务ID: 77e1e6f9-406d-4c51-a003-9d7991a16b5e
2025-08-21 17:44:34 - INFO - 🔧 立即创建TaskInfo.json...
2025-08-21 17:44:34 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-21 17:44:34 - ERROR - ❌ 创建TaskInfo.json模板失败: 'task_dir'
2025-08-21 17:44:34 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 518, in _create_taskinfo_immediately
    task_info_file = os.path.join(task_info['task_dir'], 'TaskInfo.json')
                                  ~~~~~~~~~^^^^^^^^^^^^
KeyError: 'task_dir'

2025-08-21 17:44:34 - INFO - ✅ TaskInfo.json创建完成
2025-08-21 17:44:34 - INFO - 影像ID: 20250705171601
2025-08-21 17:44:34 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:44:34 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 17:44:34 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 17:44:34 - INFO - 分析类别: arableLand
2025-08-21 17:44:34 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755769474.shp
2025-08-21 17:44:34 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_2_1755769474.shp
2025-08-21 17:44:34 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171601
2025-08-21 17:44:34 - INFO - === 开始执行合并分析任务 ===
2025-08-21 17:44:34 - WARNING - ⚠️ TaskInfo路径丢失，尝试重新构建...
2025-08-21 17:44:34 - INFO - 📍 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 17:44:34 - WARNING - ⚠️ 未找到task_id为77e1e6f9-406d-4c51-a003-9d7991a16b5e的记录
2025-08-21 17:44:34 - INFO - 🤖 开始AI语义分割...
2025-08-21 17:44:34 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:44:34 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 17:44:34 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755769474.shp
2025-08-21 17:44:34 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-21 17:44:34 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-21 17:44:34 - INFO - 🔍 检测到合并分析任务: image_id=20250705171601, category=arableLand
2025-08-21 17:44:34 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171601/TaskInfo.json
2025-08-21 17:44:34 - INFO - ✅ 使用真实任务ID: 77e1e6f9-406d-4c51-a003-9d7991a16b5e
2025-08-21 17:44:34 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-21 17:44:34 - INFO - 📄 读取现有TaskInfo文件...
2025-08-21 17:44:34 - INFO - 📋 读取到数组格式，包含 1 个历史任务
2025-08-21 17:44:34 - INFO - ➕ 添加新任务记录: 77e1e6f9-406d-4c51-a003-9d7991a16b5e
2025-08-21 17:44:34 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-21 17:44:34 - INFO - 📊 文件验证: 大小=3611字节，任务总数: 2
2025-08-21 17:44:34 - ERROR - ❌ 新版AI处理引擎执行失败: No module named 'torch'
2025-08-21 17:44:34 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\analysis_executor.py", line 990, in _execute_new_ai_processing
    spec.loader.exec_module(pre_pytorch_new)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\AIChangeShp\pre_pytorch_new.py", line 192, in <module>
    import torch
ModuleNotFoundError: No module named 'torch'

2025-08-21 17:44:34 - INFO - 📝 更新字段: status = 失败
2025-08-21 17:44:34 - INFO - 📝 更新字段: message = AI语义分割失败: 新版AI处理失败: No module named 'torch'
2025-08-21 17:44:34 - INFO - 📝 更新字段: progress = 0
2025-08-21 17:44:34 - INFO - 📝 更新字段: end_time = 2025-08-21T17:44:34.319650
2025-08-21 17:44:34 - INFO - 📝 更新嵌套字段: results.success = False
2025-08-21 17:44:34 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress', 'end_time', 'results.success']
2025-08-21 17:44:34 - INFO - 🔍 验证更新结果:
2025-08-21 17:44:34 - INFO -   状态: 失败
2025-08-21 17:44:34 - INFO -   进度: 0%
2025-08-21 17:44:34 - INFO -   AI处理时间: None
2025-08-21 17:44:34 - INFO -   成功状态: False
2025-08-21 17:44:34 - INFO -   空间统计:
2025-08-21 17:44:34 - INFO -     area_threshold: 400.0
