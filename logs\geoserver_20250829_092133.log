2025-08-29 09:21:33,597 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_092133.log
2025-08-29 09:21:33,599 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 09:21:33,600 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 09:21:34,191 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 09:21:34,198 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 09:21:34,198 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 09:21:34,216 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 09:21:34,218 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 09:21:34,219 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 09:21:34,238 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 09:21:34,240 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 09:21:34,240 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 09:21:34,266 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 09:21:34,270 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 09:21:34,271 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 09:21:34,290 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 09:21:34,293 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 09:21:34,293 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 09:21:34,312 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 09:21:34,463 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 09:21:34,531 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 09:21:41,217 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 09:21:41,236 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 09:21:41,719 - analysis_executor - INFO - 加载了 28 个任务状态
