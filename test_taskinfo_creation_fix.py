#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试TaskInfo.json创建修复
验证analysis_category键错误是否已修复
"""

import requests
import json
import os
import time

def test_combined_analysis_taskinfo_creation():
    """测试合并分析接口的TaskInfo.json创建"""
    print("🧪 测试合并分析接口TaskInfo.json创建修复...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    image_id = "20250705171601"
    params = {
        'id': image_id,
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print(f"\n🚀 启动合并分析任务...")
        print(f"   影像ID: {image_id}")
        print(f"   模型类型: {params['model_type']}")
        print(f"   面积阈值: {params['area_threshold']}")
        
        # 启动任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功: {task_id}")
            
            # 等待一段时间让TaskInfo.json创建
            print(f"⏳ 等待TaskInfo.json创建...")
            time.sleep(5)
            
            # 验证TaskInfo.json是否创建成功
            success = verify_taskinfo_creation(image_id, task_id)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def verify_taskinfo_creation(image_id, task_id):
    """验证TaskInfo.json是否正确创建"""
    print(f"\n🔍 验证TaskInfo.json创建...")
    
    # TaskInfo.json路径
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    try:
        if os.path.exists(taskinfo_path):
            print(f"✅ TaskInfo.json文件存在: {taskinfo_path}")
            
            # 读取文件内容
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📊 TaskInfo.json内容验证:")
            
            if isinstance(data, list):
                print(f"  📋 任务记录数量: {len(data)}")
                
                # 查找当前任务
                current_task = None
                for task in data:
                    if task.get('task_id') == task_id:
                        current_task = task
                        break
                
                if current_task:
                    print(f"  ✅ 找到当前任务记录")
                    
                    # 验证关键字段
                    required_fields = [
                        'task_id', 'image_id', 'analysis_category', 
                        'timestamp', 'datetime', 'input_files', 
                        'output_files', 'parameters'
                    ]
                    
                    missing_fields = []
                    for field in required_fields:
                        if field not in current_task:
                            missing_fields.append(field)
                        else:
                            print(f"    ✅ {field}: {current_task[field] if field != 'parameters' else '...'}")
                    
                    if missing_fields:
                        print(f"  ❌ 缺少字段: {missing_fields}")
                        return False
                    else:
                        print(f"  🎉 所有必需字段都存在")
                        
                        # 特别验证analysis_category字段
                        analysis_category = current_task.get('analysis_category')
                        if analysis_category:
                            print(f"  🎯 analysis_category: {analysis_category}")
                            print(f"  ✅ analysis_category字段修复成功！")
                        else:
                            print(f"  ❌ analysis_category字段仍然缺失")
                            return False
                        
                        return True
                else:
                    print(f"  ❌ 未找到当前任务记录")
                    return False
            else:
                print(f"  ❌ TaskInfo.json格式错误，应该是数组")
                return False
                
        else:
            print(f"❌ TaskInfo.json文件不存在: {taskinfo_path}")
            return False
            
    except Exception as e:
        print(f"❌ 验证TaskInfo.json失败: {e}")
        return False

def test_taskinfo_structure():
    """测试TaskInfo.json的结构完整性"""
    print(f"\n🧪 测试TaskInfo.json结构完整性...")
    
    # 模拟task_info结构
    mock_task_info = {
        'task_id': 'test-task-id',
        'image_id': '20250705171601',
        'status': '等待中',
        'message': '任务已创建，等待执行',
        'progress': 0,
        'start_time': '2025-01-01T00:00:00',
        'log_file': '/path/to/log',
        'parameters': {
            'image_id': '20250705171601',
            'image_path': '/path/to/image.tif',
            'model_path': '/path/to/model.pth',
            'old_data_path': '/path/to/old_data.shp',
            'area_threshold': 400.0,
            'model_type': 'deeplabv3_plus',
            'num_classes': 2,
            'analysis_category': 'arableLand',  # 这是关键字段
            'ai_output_path': '/path/to/ai_output.shp',
            'final_output_path': '/path/to/final_output.shp',
            'task_dir': '/path/to/task_dir',
            'window_data_path': '/path/to/window_data'
        }
    }
    
    print(f"📋 模拟task_info结构:")
    print(f"  task_id: {mock_task_info['task_id']}")
    print(f"  image_id: {mock_task_info['image_id']}")
    print(f"  parameters.analysis_category: {mock_task_info['parameters']['analysis_category']}")
    
    # 验证从parameters中获取analysis_category的逻辑
    try:
        params = mock_task_info['parameters']
        analysis_category = params['analysis_category']
        print(f"✅ 成功从parameters中获取analysis_category: {analysis_category}")
        return True
    except KeyError as e:
        print(f"❌ 从parameters中获取analysis_category失败: {e}")
        return False

def check_log_for_errors():
    """检查最新的日志文件是否还有错误"""
    print(f"\n🔍 检查最新日志文件...")
    
    log_dir = "D:/Drone_Project/geoserverAPIDJV2/logs/analysis"
    
    try:
        if os.path.exists(log_dir):
            # 获取最新的日志文件
            log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
            if log_files:
                latest_log = max(log_files, key=lambda x: os.path.getctime(os.path.join(log_dir, x)))
                latest_log_path = os.path.join(log_dir, latest_log)
                
                print(f"📄 最新日志文件: {latest_log}")
                
                # 读取日志内容，查找错误
                with open(latest_log_path, 'r', encoding='utf-8') as f:
                    log_content = f.read()
                
                # 查找特定错误
                error_patterns = [
                    "KeyError: 'analysis_category'",
                    "❌ 创建TaskInfo.json模板失败",
                    "ERROR - ❌ 创建TaskInfo.json模板失败: 'analysis_category'"
                ]
                
                found_errors = []
                for pattern in error_patterns:
                    if pattern in log_content:
                        found_errors.append(pattern)
                
                if found_errors:
                    print(f"❌ 发现错误:")
                    for error in found_errors:
                        print(f"  - {error}")
                    return False
                else:
                    print(f"✅ 未发现analysis_category相关错误")
                    return True
            else:
                print(f"⚠️ 日志目录中没有日志文件")
                return True
        else:
            print(f"⚠️ 日志目录不存在: {log_dir}")
            return True
            
    except Exception as e:
        print(f"❌ 检查日志失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试TaskInfo.json创建修复")
    
    print(f"\n📝 修复说明:")
    print(f"1. 问题: _create_taskinfo_immediately函数中直接访问task_info['analysis_category']")
    print(f"2. 原因: analysis_category实际存储在task_info['parameters']['analysis_category']")
    print(f"3. 修复: 从task_info['parameters']中获取analysis_category")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: TaskInfo结构验证
    if test_taskinfo_structure():
        success_count += 1
        print(f"✅ 测试1通过: TaskInfo结构验证")
    else:
        print(f"❌ 测试1失败: TaskInfo结构验证")
    
    # 测试2: 检查日志错误
    if check_log_for_errors():
        success_count += 1
        print(f"✅ 测试2通过: 日志错误检查")
    else:
        print(f"❌ 测试2失败: 日志错误检查")
    
    # 测试3: 实际接口测试
    if test_combined_analysis_taskinfo_creation():
        success_count += 1
        print(f"✅ 测试3通过: 实际接口测试")
    else:
        print(f"❌ 测试3失败: 实际接口测试")
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print(f"🎉 TaskInfo.json创建修复验证通过！")
        
        print(f"\n✅ 修复确认:")
        print(f"1. ✅ analysis_category字段访问路径已修正")
        print(f"2. ✅ TaskInfo.json模板创建不再报错")
        print(f"3. ✅ 所有必需字段都能正确获取")
        print(f"4. ✅ 合并分析任务可以正常启动")
        
    else:
        print(f"❌ 部分测试失败，请检查修复是否完整")
        
        print(f"\n🔧 排查建议:")
        print(f"1. 检查_create_taskinfo_immediately函数中的字段访问路径")
        print(f"2. 确认task_info字典的结构是否正确")
        print(f"3. 查看最新的日志文件确认错误是否消失")

if __name__ == "__main__":
    main()
