2025-08-20 09:12:57,919 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_091257.log
2025-08-20 09:12:57,933 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:12:57,934 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:12:57,989 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:12:57,994 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:12:57,996 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:12:58,015 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:12:58,017 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:12:58,017 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:12:58,029 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:12:58,039 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:12:58,041 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:12:58,055 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:12:58,072 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:12:58,072 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:12:58,084 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:12:58,087 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:12:58,088 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:12:58,100 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:12:58,206 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 09:12:58,269 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:13:05,927 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 09:13:06,851 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 09:13:07,044 - analysis_executor - INFO - 加载了 27 个任务状态
