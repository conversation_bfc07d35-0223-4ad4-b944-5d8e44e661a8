#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
清理TaskInfo.json中的重复记录
合并相同task_id的记录，保留最新和最完整的信息
"""

import os
import json
import shutil
from datetime import datetime
from collections import defaultdict

def cleanup_duplicate_taskinfo(image_id):
    """清理指定image_id的TaskInfo.json重复记录"""
    print(f"🧹 清理 {image_id} 的TaskInfo.json重复记录...")
    
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    if not os.path.exists(taskinfo_path):
        print(f"❌ TaskInfo.json不存在: {taskinfo_path}")
        return False
    
    try:
        # 创建备份
        backup_path = f"{taskinfo_path}.backup_{int(datetime.now().timestamp())}"
        shutil.copy2(taskinfo_path, backup_path)
        print(f"💾 已创建备份: {backup_path}")
        
        # 读取现有数据
        with open(taskinfo_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print(f"❌ TaskInfo.json格式不是数组")
            return False
        
        print(f"📊 原始记录数量: {len(data)}")
        
        # 按task_id分组
        task_groups = defaultdict(list)
        for task in data:
            task_id = task.get('task_id')
            if task_id:
                task_groups[task_id].append(task)
        
        print(f"📊 唯一task_id数量: {len(task_groups)}")
        
        # 查找重复记录
        duplicates = {tid: tasks for tid, tasks in task_groups.items() if len(tasks) > 1}
        
        if not duplicates:
            print(f"✅ 未发现重复记录")
            return True
        
        print(f"❌ 发现 {len(duplicates)} 个重复的task_id:")
        
        # 处理重复记录
        cleaned_data = []
        
        for task_id, tasks in task_groups.items():
            if len(tasks) == 1:
                # 无重复，直接添加
                cleaned_data.append(tasks[0])
            else:
                # 有重复，合并记录
                print(f"  🔄 处理重复task_id: {task_id} ({len(tasks)} 个记录)")
                
                merged_task = merge_duplicate_tasks(tasks)
                cleaned_data.append(merged_task)
                
                print(f"    ✅ 已合并为1个记录")
        
        # 按时间戳排序
        cleaned_data.sort(key=lambda x: x.get('timestamp', 0))
        
        print(f"📊 清理后记录数量: {len(cleaned_data)}")
        
        # 保存清理后的数据
        with open(taskinfo_path, 'w', encoding='utf-8') as f:
            json.dump(cleaned_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ TaskInfo.json已清理完成")
        print(f"💾 备份文件: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        return False

def merge_duplicate_tasks(tasks):
    """合并重复的任务记录，保留最新和最完整的信息"""
    if len(tasks) == 1:
        return tasks[0]
    
    print(f"    📋 合并 {len(tasks)} 个重复记录:")
    
    # 按时间戳排序，最新的在前
    tasks_sorted = sorted(tasks, key=lambda x: x.get('timestamp', 0), reverse=True)
    
    # 以最新的记录为基础
    merged_task = tasks_sorted[0].copy()
    
    # 显示每个记录的关键信息
    for i, task in enumerate(tasks_sorted):
        timestamp = task.get('timestamp', 'N/A')
        status = task.get('status', 'N/A')
        progress = task.get('progress', 'N/A')
        has_geoserver = 'geoserver_publish' in task
        has_results = task.get('results', {}).get('success') is not None
        
        print(f"      记录{i+1}: timestamp={timestamp}, status={status}, progress={progress}%, geoserver={has_geoserver}, results={has_results}")
    
    # 合并逻辑：保留最完整的信息
    for task in tasks_sorted[1:]:
        # 合并results字段
        if 'results' in task:
            task_results = task['results']
            merged_results = merged_task.get('results', {})
            
            # 如果当前记录的results更完整，则使用它
            if task_results.get('success') is not None and merged_results.get('success') is None:
                merged_task['results'] = task_results
                print(f"      ↳ 使用记录{tasks_sorted.index(task)+1}的results")
            
            # 合并spatial_statistics
            if 'spatial_statistics' in task_results:
                task_stats = task_results['spatial_statistics']
                merged_stats = merged_results.get('spatial_statistics', {})
                
                for key, value in task_stats.items():
                    if value is not None and merged_stats.get(key) is None:
                        if 'spatial_statistics' not in merged_task['results']:
                            merged_task['results']['spatial_statistics'] = {}
                        merged_task['results']['spatial_statistics'][key] = value
        
        # 合并geoserver_publish字段
        if 'geoserver_publish' in task and 'geoserver_publish' not in merged_task:
            merged_task['geoserver_publish'] = task['geoserver_publish']
            print(f"      ↳ 使用记录{tasks_sorted.index(task)+1}的geoserver_publish")
        
        # 合并parameters字段中的额外信息
        if 'parameters' in task:
            task_params = task['parameters']
            merged_params = merged_task.get('parameters', {})
            
            for key, value in task_params.items():
                if key not in merged_params and value is not None:
                    merged_task['parameters'][key] = value
    
    print(f"      ✅ 合并完成，保留最新时间戳: {merged_task.get('timestamp')}")
    
    return merged_task

def scan_and_cleanup_all():
    """扫描并清理所有TaskInfo.json文件中的重复记录"""
    print(f"🔍 扫描所有TaskInfo.json文件...")
    
    base_dir = "D:/Drone_Project/nginxData/ODM/AI"
    
    if not os.path.exists(base_dir):
        print(f"❌ AI目录不存在: {base_dir}")
        return
    
    cleaned_count = 0
    total_count = 0
    
    try:
        for item in os.listdir(base_dir):
            item_path = os.path.join(base_dir, item)
            
            if os.path.isdir(item_path):
                taskinfo_path = os.path.join(item_path, "TaskInfo.json")
                
                if os.path.exists(taskinfo_path):
                    total_count += 1
                    print(f"\n📁 处理: {item}")
                    
                    if cleanup_duplicate_taskinfo(item):
                        cleaned_count += 1
    
    except Exception as e:
        print(f"❌ 扫描失败: {e}")
    
    print(f"\n📊 清理完成:")
    print(f"  总文件数: {total_count}")
    print(f"  成功清理: {cleaned_count}")
    print(f"  失败数量: {total_count - cleaned_count}")

def verify_cleanup_result(image_id):
    """验证清理结果"""
    print(f"\n🔍 验证清理结果: {image_id}")
    
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    if not os.path.exists(taskinfo_path):
        print(f"❌ TaskInfo.json不存在")
        return False
    
    try:
        with open(taskinfo_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print(f"❌ 格式不是数组")
            return False
        
        # 检查重复
        task_ids = [task.get('task_id') for task in data]
        unique_task_ids = set(task_ids)
        
        print(f"📊 记录数量: {len(data)}")
        print(f"📊 唯一task_id: {len(unique_task_ids)}")
        
        if len(task_ids) == len(unique_task_ids):
            print(f"✅ 无重复记录")
            
            # 显示每个记录的关键信息
            for i, task in enumerate(data):
                task_id = task.get('task_id', 'N/A')[:8]
                status = task.get('status', 'N/A')
                progress = task.get('progress', 'N/A')
                has_geoserver = 'geoserver_publish' in task
                
                print(f"  记录{i+1}: {task_id}... {status} ({progress}%) geoserver={has_geoserver}")
            
            return True
        else:
            print(f"❌ 仍有重复记录")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🧹 TaskInfo.json重复记录清理工具")
    
    print(f"\n📝 清理策略:")
    print(f"1. 为每个TaskInfo.json创建备份")
    print(f"2. 按task_id分组，识别重复记录")
    print(f"3. 合并重复记录，保留最新和最完整的信息")
    print(f"4. 按时间戳排序，保存清理后的数据")
    
    # 选择清理模式
    print(f"\n🔧 清理模式:")
    print(f"1. 清理指定image_id")
    print(f"2. 清理所有TaskInfo.json文件")
    
    choice = input("请选择模式 (1/2): ").strip()
    
    if choice == "1":
        image_id = input("请输入image_id: ").strip()
        if image_id:
            success = cleanup_duplicate_taskinfo(image_id)
            if success:
                verify_cleanup_result(image_id)
        else:
            print("❌ image_id不能为空")
    
    elif choice == "2":
        confirm = input("确认清理所有TaskInfo.json文件? (y/N): ").strip().lower()
        if confirm == 'y':
            scan_and_cleanup_all()
        else:
            print("❌ 已取消操作")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
