@echo off
chcp 65001
set PYTHONIOENCODING=utf-8
set PYTHONUTF8=1
set DJANGO_SETTINGS_MODULE=geoserver_django.settings

echo ========================================
echo Django服务器启动脚本
echo ========================================
echo 当前时间: %date% %time%
echo 当前目录: %cd%
echo Python版本:
python --version
echo ========================================

echo 1. 运行调试脚本...
python debug_startup.py > debug_output.txt 2>&1
if errorlevel 1 (
    echo 调试脚本执行失败，查看 debug_output.txt
    type debug_output.txt
    pause
    exit /b 1
)

echo 2. 检查数据库迁移...
python manage.py makemigrations > migration_output.txt 2>&1
python manage.py migrate > migration_output.txt 2>&1

echo 3. 启动Django服务器...
echo 如果出现错误，所有输出将保存到 server_output.txt
python manage.py runserver 0.0.0.0:8091 > server_output.txt 2>&1
if errorlevel 1 (
    echo 服务器启动失败，错误信息:
    type server_output.txt
    echo.
    echo 错误信息已保存到 server_output.txt
    pause
    exit /b 1
)

pause
