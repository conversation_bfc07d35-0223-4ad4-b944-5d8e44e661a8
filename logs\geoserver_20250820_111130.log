2025-08-20 11:11:30,327 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_111130.log
2025-08-20 11:11:30,329 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 11:11:30,329 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 11:11:30,352 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 11:11:30,356 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 11:11:30,357 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 11:11:30,374 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 11:11:30,376 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 11:11:30,376 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 11:11:30,392 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 11:11:30,393 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 11:11:30,394 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 11:11:30,412 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 11:11:30,414 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 11:11:30,415 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 11:11:30,430 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 11:11:30,431 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 11:11:30,433 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 11:11:30,450 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 11:11:30,463 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 11:11:30,469 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 11:11:34,583 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 11:11:34,596 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 11:11:34,610 - analysis_executor - INFO - 加载了 28 个任务状态
