#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修复后的排队合并分析接口
验证参数匹配问题是否已解决，TaskInfo.json是否正确创建
"""

import requests
import json
import os
import time

def test_queued_analysis_fix():
    """测试修复后的排队合并分析接口"""
    print("🧪 测试修复后的排队合并分析接口...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试参数
    test_params = {
        'id': '20250705171599',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus',
        'analysis_category': 'arableLand'
    }
    
    try:
        print(f"\n📤 提交排队分析任务...")
        print(f"   影像ID: {test_params['id']}")
        print(f"   分析类别: {test_params['analysis_category']}")
        
        # 提交任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=test_params, timeout=30)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📋 响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result['status'] == 'success':
                task_id = result['data']['task_id']
                queue_position = result['data']['queue_position']
                
                print(f"✅ 任务提交成功")
                print(f"   任务ID: {task_id}")
                print(f"   队列位置: {queue_position}")
                
                # 验证TaskInfo.json是否创建
                verify_taskinfo_creation(test_params['id'], task_id)
                
                # 监控任务执行
                monitor_task_execution(base_url, task_id, test_params['id'])
                
                return True
            else:
                print(f"❌ 任务提交失败: {result['message']}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def verify_taskinfo_creation(image_id, task_id):
    """验证TaskInfo.json是否正确创建"""
    print(f"\n🔍 验证TaskInfo.json创建...")
    
    # TaskInfo.json路径
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    try:
        if os.path.exists(taskinfo_path):
            print(f"✅ TaskInfo.json文件存在: {taskinfo_path}")
            
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"📋 TaskInfo.json内容:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            # 查找目标任务
            target_task = None
            if isinstance(data, list):
                for task in data:
                    if task.get('task_id') == task_id:
                        target_task = task
                        break
            elif isinstance(data, dict) and data.get('task_id') == task_id:
                target_task = data
            
            if target_task:
                print(f"✅ 找到目标任务信息:")
                print(f"   任务ID: {target_task['task_id']}")
                print(f"   状态: {target_task['status']}")
                print(f"   消息: {target_task['message']}")
                print(f"   创建时间: {target_task['created_time']}")
                
                # 验证各个阶段的状态
                ai_status = target_task.get('ai_analysis', {}).get('status', 'N/A')
                spatial_status = target_task.get('spatial_analysis', {}).get('status', 'N/A')
                geoserver_status = target_task.get('geoserver_publish', {}).get('status', 'N/A')
                
                print(f"   AI分析状态: {ai_status}")
                print(f"   空间分析状态: {spatial_status}")
                print(f"   GeoServer发布状态: {geoserver_status}")
                
                if target_task['status'] == '等待中':
                    print(f"🎉 TaskInfo.json状态正确：等待中")
                    return True
                else:
                    print(f"⚠️ TaskInfo.json状态异常: {target_task['status']}")
                    return False
            else:
                print(f"❌ 未找到目标任务: {task_id}")
                return False
        else:
            print(f"❌ TaskInfo.json文件不存在: {taskinfo_path}")
            return False
            
    except Exception as e:
        print(f"❌ 验证TaskInfo.json失败: {e}")
        return False

def monitor_task_execution(base_url, task_id, image_id, max_wait=300):
    """监控任务执行过程"""
    print(f"\n📊 监控任务执行过程...")
    
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            # 查询任务状态
            response = requests.get(f"{base_url}/queued-task-status/", params={'task_id': task_id}, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                
                if result['status'] == 'success':
                    task_data = result['data']
                    current_status = task_data['status']
                    queue_position = task_data.get('queue_position', 'N/A')
                    
                    if current_status != last_status:
                        print(f"🔄 状态变化: {current_status} (队列位置: {queue_position})")
                        last_status = current_status
                    
                    # 检查是否完成
                    if current_status in ['完成', '失败', '已取消']:
                        print(f"🏁 任务结束: {current_status}")
                        
                        # 最终验证TaskInfo.json
                        final_verify_taskinfo(image_id, task_id, current_status)
                        return current_status == '完成'
                        
                elif result['status'] == 'error' and '任务不存在' in result['message']:
                    print(f"⚠️ 任务可能已完成并从队列中移除")
                    # 检查TaskInfo.json的最终状态
                    final_verify_taskinfo(image_id, task_id, '检查中')
                    return True
                else:
                    print(f"❌ 查询任务状态失败: {result['message']}")
            else:
                print(f"❌ 查询请求失败: {response.status_code}")
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"❌ 监控异常: {e}")
            time.sleep(10)
            continue
    
    print(f"⏰ 监控超时")
    return False

def final_verify_taskinfo(image_id, task_id, expected_status):
    """最终验证TaskInfo.json状态"""
    print(f"\n🔍 最终验证TaskInfo.json状态...")
    
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    try:
        if os.path.exists(taskinfo_path):
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 查找目标任务
            target_task = None
            if isinstance(data, list):
                for task in data:
                    if task.get('task_id') == task_id:
                        target_task = task
                        break
            elif isinstance(data, dict) and data.get('task_id') == task_id:
                target_task = data
            
            if target_task:
                final_status = target_task['status']
                print(f"📊 最终状态: {final_status}")
                
                if 'geoserver_publish' in target_task:
                    geoserver_info = target_task['geoserver_publish']
                    print(f"🌐 GeoServer发布信息:")
                    print(f"   状态: {geoserver_info.get('status', 'N/A')}")
                    if 'overall_success' in geoserver_info:
                        print(f"   发布成功: {geoserver_info['overall_success']}")
                
                return final_status
            else:
                print(f"❌ 未找到目标任务")
                return None
        else:
            print(f"❌ TaskInfo.json文件不存在")
            return None
            
    except Exception as e:
        print(f"❌ 最终验证失败: {e}")
        return None

def test_queue_status():
    """测试队列状态查询"""
    print(f"\n🧪 测试队列状态查询...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                queue_data = result['data']
                
                print(f"✅ 队列状态查询成功:")
                print(f"   队列长度: {queue_data['queue_size']}")
                print(f"   等待任务: {queue_data['waiting_tasks']}")
                print(f"   执行任务: {queue_data['executing_task'] or '无'}")
                print(f"   已完成: {queue_data['completed_tasks']}")
                print(f"   已取消: {queue_data['cancelled_tasks']}")
                print(f"   运行状态: {'运行中' if queue_data['is_running'] else '已停止'}")
                
                return True
            else:
                print(f"❌ 队列状态查询失败: {result['message']}")
                return False
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始测试修复后的排队合并分析功能")
    
    print(f"\n📝 修复内容:")
    print(f"1. 修复了 analysis_category 参数不匹配问题")
    print(f"2. 确保调用原始方法时使用正确的参数")
    print(f"3. 验证TaskInfo.json的正确创建和状态更新")
    
    # 测试队列状态
    test_queue_status()
    
    # 测试修复后的排队分析
    success = test_queued_analysis_fix()
    
    if success:
        print(f"\n🎉 修复验证成功！")
        
        print(f"\n✅ 确认修复:")
        print(f"1. ✅ 参数匹配问题已解决")
        print(f"2. ✅ TaskInfo.json正确创建，状态为'等待中'")
        print(f"3. ✅ 任务能正常加入队列并执行")
        print(f"4. ✅ 队列管理功能正常工作")
        
        print(f"\n📖 使用说明:")
        print(f"1. 排队接口现在与原接口参数完全兼容")
        print(f"2. 任务提交后立即创建TaskInfo.json，状态为'等待中'")
        print(f"3. 可以通过队列状态接口监控执行情况")
        print(f"4. 支持任务取消功能（仅限等待中的任务）")
        
    else:
        print(f"\n❌ 修复验证失败")
        print(f"请检查:")
        print(f"1. 服务器是否正常运行")
        print(f"2. 参数是否正确传递")
        print(f"3. TaskInfo.json是否正确创建")
        print(f"4. 队列管理器是否正常工作")

if __name__ == "__main__":
    main()
