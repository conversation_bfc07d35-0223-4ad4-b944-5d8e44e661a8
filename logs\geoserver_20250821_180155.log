2025-08-21 18:01:55,932 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250821_180155.log
2025-08-21 18:01:55,934 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:01:55,934 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:01:55,961 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:01:55,964 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:01:55,964 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:01:55,981 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:01:55,983 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:01:55,983 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:01:56,003 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:01:56,006 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:01:56,006 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:01:56,021 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:01:56,023 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:01:56,023 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:01:56,041 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:01:56,043 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-21 18:01:56,043 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-21 18:01:56,065 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-21 18:01:56,081 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-21 18:01:56,096 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-21 18:02:00,072 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-21 18:02:00,166 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-21 18:02:00,172 - analysis_executor - INFO - 加载了 28 个任务状态
