# 合并分析API文档

## 接口概述

新的合并分析接口将AI语义分割和空间变化分析两个功能合并为一个统一的接口，提供完整的影像分析解决方案。

## 接口地址

```
GET http://localhost:8091/api/analysis/combined-ai-spatial-analysis/
```

## 功能说明

### 处理流程

1. **读取配置文件** - 从 `127.0.0.1:81/ODM/Task.cfg` 读取配置信息
2. **AI语义分割** - 对输入TIF图像进行AI预测，生成SHP结果
3. **空间变化分析** - 将AI结果与历史SHP数据进行变化分析
4. **保存结果** - 保存所有输出文件和任务信息JSON

### 输出文件结构

```
{window_data_path}/ODM/AI/{影像ID}/
├── {分析类别}/
│   ├── {影像ID}_1_{时间戳}.shp    # AI分析结果
│   ├── {影像ID}_1_{时间戳}.shx
│   ├── {影像ID}_1_{时间戳}.dbf
│   ├── {影像ID}_1_{时间戳}.prj
│   ├── {影像ID}_2_{时间戳}.shp    # 最终分析结果
│   ├── {影像ID}_2_{时间戳}.shx
│   ├── {影像ID}_2_{时间戳}.dbf
│   └── {影像ID}_2_{时间戳}.prj
└── TaskInfo.json                   # 任务信息文件
```

## 请求参数

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| id | string | 是 | 影像ID | 20250705171601 |
| image | string | 是 | TIF图像路径 | D:\Drone_Project\nginxData\ODM\Output\20250705171601\20250705171601_out.tif |
| model | string | 是 | 权重路径 | D:\Drone_Project\nginxData\ODM\AIWeight\arableLand\deeplabv3_plus\deeplabv3_plus_best_20250807-111949.pth |
| old_data_path | string | 是 | 历史SHP路径 | D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp |
| area_threshold | float | 否 | 面积阈值（平方米） | 400 |
| model_type | string | 否 | 模型类型 | deeplabv3_plus |
| num_classes | int | 否 | 类别数量 | 2 |

## 返回格式

### 成功响应

```json
{
  "status": "success",
  "message": "合并分析任务已启动",
  "data": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "task_status": "等待中",
    "message": "任务已创建，等待执行",
    "image_id": "20250705171601",
    "estimated_time": "预计15-20分钟完成"
  }
}
```

### 错误响应

```json
{
  "status": "error",
  "message": "缺少必需参数: id (影像ID)",
  "error": "参数验证失败"
}
```

## 任务状态查询

使用现有的状态查询接口：

```
GET http://localhost:8091/api/analysis/status/?task_id={task_id}
```

### 状态说明

- **等待中** - 任务已创建，等待执行
- **正在运行** - 任务正在执行中
  - 开始AI语义分割... (5%)
  - 开始空间变化分析... (60%)
  - 保存任务信息... (90%)
- **完成** - 任务执行完成 (100%)
- **失败** - 任务执行失败

## 配置文件

### Task.cfg 格式

```ini
[PATHS]
airflow_data_path = /opt/airflow/data
window_data_path = D:/Drone_Project/nginxData

[API]
geoserver_host = host.docker.internal
geoserver_hd_port = 5083

[GEOSERVER]
odm_workspace = testodm

[TASK]
id = NONE
time = NONE
```

### 关键配置项

- `window_data_path`: 127.0.0.1:81的根目录，用于拼接所有输出路径

## TaskInfo.json 格式

```json
[
  {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "image_id": "20250705171601",
    "analysis_category": "arableLand",
    "timestamp": 1640995200,
    "datetime": "2025-01-01T12:00:00",
    "input_files": {
      "image_path": "D:/path/to/image.tif",
      "model_path": "D:/path/to/model.pth",
      "old_data_path": "D:/path/to/old_data.shp"
    },
    "output_files": {
      "ai_output_path": "D:/path/to/ai_result.shp",
      "final_output_path": "D:/path/to/final_result.shp"
    },
    "parameters": {
      "model_type": "deeplabv3_plus",
      "num_classes": 2,
      "area_threshold": 400
    },
    "results": {
      "ai_processing_time": 634.5,
      "spatial_statistics": {
        "outflow_count": 15,
        "inflow_count": 8,
        "outflow_area": 12500.75,
        "inflow_area": 8200.25,
        "total_count": 23,
        "area_threshold": 400
      },
      "success": true
    },
    "status": "完成",
    "log_file": "D:/path/to/logs/task_id.log"
  }
]
```

## 使用示例

### JavaScript 调用

```javascript
// 启动合并分析任务
const response = await fetch('http://localhost:8091/api/analysis/combined-ai-spatial-analysis/', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  },
  params: new URLSearchParams({
    id: '20250705171601',
    image: 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
    model: 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
    old_data_path: 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
    area_threshold: 400
  })
});

const data = await response.json();

if (data.status === 'success') {
  const taskId = data.data.task_id;
  
  // 监控任务状态
  const checkStatus = async () => {
    const statusResponse = await fetch(`http://localhost:8091/api/analysis/status/?task_id=${taskId}`);
    const statusData = await statusResponse.json();
    
    if (statusData.data.status === '完成') {
      console.log('任务完成！');
      console.log('AI结果:', statusData.data.parameters.ai_output_path);
      console.log('最终结果:', statusData.data.parameters.final_output_path);
    } else if (statusData.data.status === '失败') {
      console.log('任务失败:', statusData.data.message);
    } else {
      console.log(`任务进行中: ${statusData.data.progress}%`);
      setTimeout(checkStatus, 30000); // 30秒后再次检查
    }
  };
  
  checkStatus();
}
```

### Python 调用

```python
import requests
import time

# 启动合并分析任务
response = requests.get('http://localhost:8091/api/analysis/combined-ai-spatial-analysis/', params={
    'id': '20250705171601',
    'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
    'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
    'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
    'area_threshold': 400
})

data = response.json()

if data['status'] == 'success':
    task_id = data['data']['task_id']
    print(f"任务已启动: {task_id}")
    
    # 监控任务状态
    while True:
        status_response = requests.get(f'http://localhost:8091/api/analysis/status/', params={'task_id': task_id})
        status_data = status_response.json()
        
        if status_data['data']['status'] == '完成':
            print("任务完成！")
            print(f"AI结果: {status_data['data']['parameters']['ai_output_path']}")
            print(f"最终结果: {status_data['data']['parameters']['final_output_path']}")
            break
        elif status_data['data']['status'] == '失败':
            print(f"任务失败: {status_data['data']['message']}")
            break
        else:
            print(f"任务进行中: {status_data['data']['progress']}%")
            time.sleep(30)
```

## 注意事项

1. **处理时间**: 合并分析通常需要15-20分钟，具体时间取决于图像大小和硬件性能
2. **文件路径**: 所有路径都使用绝对路径，基于 `window_data_path` 配置
3. **任务管理**: 每个任务都有独立的日志文件和状态跟踪
4. **错误处理**: 任何步骤失败都会导致整个任务失败，详细错误信息记录在日志中
5. **并发限制**: 建议同时运行的任务数量不超过系统CPU核心数

## 错误排查

1. **配置文件错误**: 检查 `D:/Drone_Project/nginxData/ODM/Task.cfg` 是否存在且格式正确
2. **文件不存在**: 确保所有输入文件（TIF、模型、历史SHP）都存在
3. **权限问题**: 确保输出目录有写入权限
4. **内存不足**: 大图像处理可能需要大量内存，建议监控系统资源使用情况
