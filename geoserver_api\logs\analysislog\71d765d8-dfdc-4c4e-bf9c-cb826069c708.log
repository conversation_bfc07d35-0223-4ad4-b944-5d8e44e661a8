2025-08-25 17:33:29 - INFO - === 合并分析任务开始 ===
2025-08-25 17:33:29 - INFO - 任务ID: 71d765d8-dfdc-4c4e-bf9c-cb826069c708
2025-08-25 17:33:29 - INFO - 🔧 立即创建TaskInfo.json...
2025-08-25 17:33:29 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-25 17:33:30 - INFO - 📁 TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:30 - INFO - 📄 发现现有TaskInfo文件，正在读取...
2025-08-25 17:33:30 - INFO - 📋 读取到数组格式，包含 9 个历史任务
2025-08-25 17:33:30 - INFO - ➕ 添加新任务记录: 71d765d8-dfdc-4c4e-bf9c-cb826069c708
2025-08-25 17:33:30 - INFO - 💾 准备保存TaskInfo.json到: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:30 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-25 17:33:30 - INFO - 📊 文件验证: 大小=21717字节
2025-08-25 17:33:30 - INFO - ✅ TaskInfo.json模板创建完成: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:30 - INFO - 📊 当前任务总数: 10
2025-08-25 17:33:30 - INFO - ✅ TaskInfo.json创建完成
2025-08-25 17:33:30 - INFO - 影像ID: 20250705171599
2025-08-25 17:33:30 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:33:30 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:33:30 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:33:30 - INFO - 分析类别: constructionLand
2025-08-25 17:33:30 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756114409.shp
2025-08-25 17:33:30 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_2_1756114409.shp
2025-08-25 17:33:30 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171599
2025-08-25 17:33:30 - INFO - === 开始执行合并分析任务 ===
2025-08-25 17:33:30 - WARNING - ⚠️ TaskInfo路径丢失，尝试重新构建...
2025-08-25 17:33:30 - INFO - 📍 重新构建TaskInfo路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:30 - INFO - 📝 更新字段: status = 进行中
2025-08-25 17:33:30 - INFO - 📝 更新字段: message = 开始AI语义分割...
2025-08-25 17:33:30 - INFO - 📝 更新字段: progress = 5
2025-08-25 17:33:30 - INFO - 📝 TaskInfo.json已更新: ['status', 'message', 'progress']
2025-08-25 17:33:30 - INFO - 🔍 验证更新结果:
2025-08-25 17:33:30 - INFO -   状态: 进行中
2025-08-25 17:33:30 - INFO -   进度: 5%
2025-08-25 17:33:30 - INFO -   AI处理时间: None
2025-08-25 17:33:30 - INFO -   成功状态: None
2025-08-25 17:33:30 - INFO -   空间统计:
2025-08-25 17:33:30 - INFO -     area_threshold: 200.0
2025-08-25 17:33:30 - INFO - 🤖 开始AI语义分割...
2025-08-25 17:33:30 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:33:30 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:33:30 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\constructionLand\20250705171599_1_1756114409.shp
2025-08-25 17:33:30 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-25 17:33:30 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-25 17:33:30 - INFO - 🔍 检测到合并分析任务: image_id=20250705171599, category=constructionLand
2025-08-25 17:33:30 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171599/TaskInfo.json
2025-08-25 17:33:30 - INFO - ✅ 使用真实任务ID: 71d765d8-dfdc-4c4e-bf9c-cb826069c708
2025-08-25 17:33:30 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-25 17:33:30 - INFO - 📄 读取现有TaskInfo文件...
2025-08-25 17:33:30 - INFO - 📋 读取到数组格式，包含 10 个历史任务
2025-08-25 17:33:30 - INFO - ➕ 添加新任务记录: 71d765d8-dfdc-4c4e-bf9c-cb826069c708
2025-08-25 17:33:30 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-25 17:33:30 - INFO - 📊 文件验证: 大小=23710字节，任务总数: 11
