2025-08-28 17:24:55,885 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_172455.log
2025-08-28 17:24:55,887 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,887 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,909 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,913 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,914 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,927 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,929 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,929 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,941 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,944 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,944 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,953 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,955 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,956 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,969 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:55,972 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:24:55,973 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:24:55,984 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:24:56,003 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:24:56,013 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:25:00,729 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:25:00,743 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:25:00,747 - analysis_executor - INFO - 加载了 28 个任务状态
