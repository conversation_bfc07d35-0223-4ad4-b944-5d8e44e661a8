#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试GeoServer发布修复
验证增加超时时间和重试机制后的发布效果
"""

import requests
import json
import time
from config import DJANGO_BASE_URL

def test_geoserver_publish_fix():
    """测试GeoServer发布修复"""
    print("🧪 测试GeoServer发布修复")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    # 提交测试任务
    task_params = {
        'id': '20250705171599',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print("📤 提交测试任务...")
        response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                              params=task_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                task_id = result['data']['task_id']
                print(f"✅ 任务提交成功: {task_id[:8]}...")
                
                # 监控任务执行过程
                monitor_task_execution(task_id, task_params['id'])
                
            else:
                print(f"❌ 任务提交失败: {result['message']}")
        else:
            print(f"❌ 任务提交HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def monitor_task_execution(task_id, image_id, max_wait=600):
    """监控任务执行过程"""
    print(f"\n👀 监控任务执行过程...")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    start_time = time.time()
    last_status = None
    
    while time.time() - start_time < max_wait:
        try:
            # 检查队列状态
            response = requests.get(f"{base_url}/queue-status/", 
                                  params={'task_id': task_id}, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    data = result['data']
                    status = data.get('status', 'unknown')
                    message = data.get('message', '')
                    
                    # 只在状态变化时打印
                    if status != last_status:
                        timestamp = time.strftime('%H:%M:%S')
                        print(f"   [{timestamp}] 状态: {status} - {message}")
                        last_status = status
                    
                    if status in ['完成', '失败']:
                        print(f"   任务已结束: {status}")
                        
                        # 获取详细的TaskInfo
                        check_task_results(image_id, task_id)
                        return status == '完成'
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"   检查状态异常: {e}")
            time.sleep(10)
    
    print(f"   ⚠️ 监控超时")
    return False

def check_task_results(image_id, task_id):
    """检查任务结果详情"""
    print(f"\n🔍 检查任务结果详情...")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis"
    
    try:
        # 获取TaskInfo
        response = requests.get(f"{base_url}/taskinfo/", 
                              params={'id': image_id}, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['status'] == 'success' and result['data']:
                tasks = result['data']
                
                # 查找指定的任务
                target_task = None
                for task in tasks:
                    if task.get('task_id') == task_id:
                        target_task = task
                        break
                
                if target_task:
                    print(f"   📋 找到任务: {task_id[:8]}...")
                    
                    # 检查GeoServer发布结果
                    geoserver_publish = target_task.get('geoserver_publish', {})
                    
                    print(f"   🌐 GeoServer发布结果:")
                    
                    # AI结果发布
                    ai_result = geoserver_publish.get('ai_result', {})
                    ai_success = ai_result.get('success', False)
                    ai_message = ai_result.get('message', 'N/A')
                    ai_layer = ai_result.get('layer_name', 'N/A')
                    
                    status_icon = "✅" if ai_success else "❌"
                    print(f"     AI结果: {status_icon} {ai_layer}")
                    print(f"       消息: {ai_message}")
                    
                    # 最终结果发布
                    final_result = geoserver_publish.get('final_result', {})
                    final_success = final_result.get('success', False)
                    final_message = final_result.get('message', 'N/A')
                    final_layer = final_result.get('layer_name', 'N/A')
                    
                    status_icon = "✅" if final_success else "❌"
                    print(f"     最终结果: {status_icon} {final_layer}")
                    print(f"       消息: {final_message}")
                    
                    # 总体状态
                    overall_success = geoserver_publish.get('overall_success', False)
                    status_icon = "✅" if overall_success else "❌"
                    print(f"     总体状态: {status_icon} {'成功' if overall_success else '部分失败'}")
                    
                    # 分析修复效果
                    analyze_fix_effectiveness(ai_result, final_result)
                    
                else:
                    print(f"   ❌ 未找到指定任务: {task_id}")
            else:
                print(f"   ❌ 获取TaskInfo失败: {result.get('message', 'unknown')}")
        else:
            print(f"   ❌ 获取TaskInfo HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 检查任务结果异常: {e}")

def analyze_fix_effectiveness(ai_result, final_result):
    """分析修复效果"""
    print(f"\n📊 修复效果分析:")
    
    ai_success = ai_result.get('success', False)
    final_success = final_result.get('success', False)
    
    ai_message = ai_result.get('message', '')
    final_message = final_result.get('message', '')
    
    # 检查是否还有超时问题
    ai_timeout = 'timeout' in ai_message.lower() or 'timed out' in ai_message.lower()
    final_timeout = 'timeout' in final_message.lower() or 'timed out' in final_message.lower()
    
    print(f"   🎯 修复目标: 解决GeoServer发布超时问题")
    print(f"   📈 修复措施:")
    print(f"     - 超时时间: 60秒 → 120秒")
    print(f"     - 重试机制: 无 → 3次重试")
    print(f"     - 重试间隔: 5秒")
    
    print(f"   📊 发布结果:")
    if ai_success and final_success:
        print(f"     ✅ 完全成功: 所有图层都发布成功")
    elif ai_success or final_success:
        print(f"     ⚠️ 部分成功: {'AI结果' if ai_success else '最终结果'}发布成功")
        if ai_timeout or final_timeout:
            print(f"     ❌ 仍有超时: 需要进一步优化")
        else:
            print(f"     💡 非超时问题: 可能是其他原因导致失败")
    else:
        print(f"     ❌ 完全失败: 所有图层发布都失败")
        if ai_timeout and final_timeout:
            print(f"     ❌ 超时问题未解决: 需要进一步增加超时时间")
        else:
            print(f"     💡 非超时问题: 可能是GeoServer配置或网络问题")
    
    # 提供建议
    print(f"   💡 建议:")
    if ai_timeout or final_timeout:
        print(f"     - 进一步增加超时时间到180秒或更多")
        print(f"     - 检查GeoServer性能和配置")
        print(f"     - 考虑异步发布机制")
    elif not (ai_success and final_success):
        print(f"     - 检查GeoServer连接配置")
        print(f"     - 验证文件路径和权限")
        print(f"     - 查看GeoServer日志")
    else:
        print(f"     - 修复成功，继续监控后续任务")

def test_geoserver_connection():
    """测试GeoServer连接"""
    print(f"\n🌐 测试GeoServer连接...")
    
    try:
        from config import GEOSERVER_URL, GEOSERVER_USER, GEOSERVER_PASSWORD
        
        # 测试GeoServer版本接口
        version_url = f"{GEOSERVER_URL}/rest/about/version"
        response = requests.get(version_url, 
                              auth=(GEOSERVER_USER, GEOSERVER_PASSWORD), 
                              timeout=30)
        
        if response.status_code == 200:
            print(f"   ✅ GeoServer连接正常")
            print(f"   📋 GeoServer URL: {GEOSERVER_URL}")
        else:
            print(f"   ❌ GeoServer连接异常: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ GeoServer连接测试失败: {e}")

def main():
    """主函数"""
    print("🧪 GeoServer发布修复测试")
    print("=" * 50)
    
    print("📝 修复内容:")
    print("1. 增加超时时间: 60秒 → 120秒")
    print("2. 添加重试机制: 最多3次重试")
    print("3. 重试间隔: 失败后等待5秒再重试")
    print("4. 详细日志: 记录每次重试的结果")
    
    print("\n🎯 测试目标:")
    print("- 验证AI结果发布不再超时")
    print("- 验证最终结果发布稳定性")
    print("- 确认重试机制正常工作")
    
    # 测试GeoServer连接
    test_geoserver_connection()
    
    # 执行发布测试
    choice = input("\n是否执行发布测试? (y/n): ").strip().lower()
    
    if choice == 'y':
        test_geoserver_publish_fix()
    else:
        print("跳过发布测试")
    
    print(f"\n🎉 测试完成！")
    
    print(f"\n📚 修复说明:")
    print(f"- 超时时间从60秒增加到120秒")
    print(f"- 添加了3次重试机制，每次重试间隔5秒")
    print(f"- 成功发布后立即跳出重试循环")
    print(f"- 详细记录每次重试的结果")
    
    print(f"\n💡 如果问题仍然存在:")
    print(f"1. 进一步增加超时时间到180秒或更多")
    print(f"2. 检查GeoServer性能和内存配置")
    print(f"3. 考虑实现异步发布机制")
    print(f"4. 优化SHP文件大小")

if __name__ == "__main__":
    main()
