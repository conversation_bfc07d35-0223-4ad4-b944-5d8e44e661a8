2025-08-20 11:25:18 - INFO - === 合并分析任务开始 ===
2025-08-20 11:25:18 - INFO - 任务ID: 16124f57-e48f-40d8-a322-265f96068b4d
2025-08-20 11:25:18 - INFO - 🔧 立即创建TaskInfo.json...
2025-08-20 11:25:18 - INFO - 🔧 开始创建TaskInfo.json模板...
2025-08-20 11:25:18 - ERROR - ❌ 创建TaskInfo.json模板失败: 'task_dir'
2025-08-20 11:25:18 - ERROR - 错误详情: Traceback (most recent call last):
  File "D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\combined_analysis_executor.py", line 460, in _create_taskinfo_immediately
    task_info_file = os.path.join(task_info['task_dir'], 'TaskInfo.json')
KeyError: 'task_dir'

2025-08-20 11:25:18 - INFO - ✅ TaskInfo.json创建完成
2025-08-20 11:25:18 - INFO - 影像ID: 20250705171601
2025-08-20 11:25:18 - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:18 - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 11:25:18 - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 11:25:18 - INFO - 分析类别: arableLand
2025-08-20 11:25:18 - INFO - AI输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:25:18 - INFO - 最终输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_2_1755660318.shp
2025-08-20 11:25:18 - INFO - 任务目录: D:/Drone_Project/nginxData\ODM\AI\20250705171601
2025-08-20 11:25:18 - INFO - === 开始执行合并分析任务 ===
2025-08-20 11:25:18 - INFO - 🤖 开始AI语义分割...
2025-08-20 11:25:18 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:18 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 11:25:18 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:25:18 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-20 11:25:18 - INFO - 🚀 使用新版AI分析引擎进行处理...
2025-08-20 11:25:18 - INFO - 🔍 检测到合并分析任务: image_id=20250705171601, category=arableLand
2025-08-20 11:25:18 - INFO - 📁 TaskInfo路径: D:/Drone_Project/nginxData/ODM/AI/20250705171601/TaskInfo.json
2025-08-20 11:25:18 - INFO - ✅ 使用真实任务ID: 16124f57-e48f-40d8-a322-265f96068b4d
2025-08-20 11:25:18 - INFO - 🔧 创建合并分析TaskInfo.json模板...
2025-08-20 11:25:18 - INFO - 📄 读取现有TaskInfo文件...
2025-08-20 11:25:19 - INFO - 📋 读取到数组格式，包含 2 个历史任务
2025-08-20 11:25:19 - INFO - ➕ 添加新任务记录: 16124f57-e48f-40d8-a322-265f96068b4d
2025-08-20 11:25:19 - INFO - ✅ TaskInfo.json文件已写入完成
2025-08-20 11:25:19 - INFO - 📊 文件验证: 大小=4135字节，任务总数: 3
2025-08-20 11:25:22 - INFO - ✅ 新版AI分析引擎加载成功
2025-08-20 11:25:22 - INFO - 🔧 创建处理参数:
2025-08-20 11:25:22 - INFO -   模型类型: deeplabv3_plus
2025-08-20 11:25:22 - INFO -   类别数量: 2
2025-08-20 11:25:22 - INFO -   目标类别: [1]
2025-08-20 11:25:22 - INFO -   窗口大小: 512
2025-08-20 11:25:22 - INFO -   批处理大小: 16
2025-08-20 11:25:22 - INFO -   重叠比例: 0.5
2025-08-20 11:25:22 - INFO - 🎮 使用GPU设备: cuda:0
2025-08-20 11:25:22 - INFO - 📂 输入图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:22 - INFO - 🤖 模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 11:25:22 - INFO - 📁 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:25:22 - INFO - 🔧 模型类型: deeplabv3_plus
2025-08-20 11:25:23 - INFO - 🔄 开始AI模型推理...
2025-08-20 11:25:23 - INFO - 
==================================================
2025-08-20 11:25:23 - INFO - 开始处理图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:23 - INFO - 输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:25:23 - INFO - ==================================================
2025-08-20 11:25:23 - INFO - 📖 读取图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:23 - INFO - 📏 图像大小: 17007x20364, 波段数: 3
2025-08-20 11:25:23 - INFO - 🚫 NoData值: [-9999.0, -9999.0, -9999.0]
2025-08-20 11:25:23 - INFO - 🤖 模型类型: deeplabv3_plus
2025-08-20 11:25:23 - INFO - 🔢 类别数量: 2
2025-08-20 11:25:23 - INFO - 🔄 重叠区域: 37 像素
2025-08-20 11:25:23 - INFO - 📦 批处理大小: 16
2025-08-20 11:25:23 - INFO - 💻 计算设备: cuda:0
2025-08-20 11:25:58 - INFO - 读取tif并裁剪预处理完毕,目前耗时间: 35s
2025-08-20 11:25:58 - INFO - 🔧 创建模型...
2025-08-20 11:26:02 - INFO - ✅ 模型创建完成
2025-08-20 11:26:02 - INFO - 🧠 开始模型预测...
2025-08-20 11:29:32 - INFO - ✅ AI处理成功完成，耗时: 248.00秒
2025-08-20 11:29:32 - INFO - 🔄 更新TaskInfo.json: AI处理完成，成功=True
2025-08-20 11:29:32 - INFO - ✅ 找到并更新任务记录: 16124f57-e48f-40d8-a322-265f96068b4d
2025-08-20 11:29:32 - INFO - 📝 TaskInfo.json已更新: AI处理时间=248秒
2025-08-20 11:29:32 - INFO - AI语义分割完成: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:29:32 - INFO - 🗺️ 开始提取影像有效范围...
2025-08-20 11:29:32 - INFO - 范围文件输出路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\20250705171601_area_1755660572.shp
2025-08-20 11:29:32 - INFO - 调用影像范围提取: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif -> D:/Drone_Project/nginxData\ODM\AI\20250705171601\20250705171601_area_1755660572.shp
2025-08-20 11:30:02 - INFO - ✅ 影像范围提取成功
2025-08-20 11:30:02 - INFO - 输出文件: D:/Drone_Project/nginxData\ODM\AI\20250705171601\20250705171601_area_1755660572.shp
2025-08-20 11:30:02 - INFO - 有效区域数量: 1
2025-08-20 11:30:02 - INFO - 坐标系: WGS 84 / UTM zone 48N (EPSG:32648)
2025-08-20 11:30:02 - INFO - 影像范围提取完成: D:/Drone_Project/nginxData\ODM\AI\20250705171601\20250705171601_area_1755660572.shp
2025-08-20 11:30:02 - INFO - 📊 开始空间变化分析...
2025-08-20 11:30:02 - INFO - 📁 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 11:30:02 - INFO - 📁 新数据: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:30:02 - INFO - 📏 面积阈值: 400.0 平方米
2025-08-20 11:30:02 - INFO - 使用裁剪范围: D:/Drone_Project/nginxData\ODM\AI\20250705171601\20250705171601_area_1755660572.shp
2025-08-20 11:36:39 - INFO - 空间变化分析完成: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_2_1755660318.shp
2025-08-20 11:36:39 - INFO - === 合并分析任务完成 ===
2025-08-20 11:36:39 - INFO - AI分析结果: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_1_1755660318.shp
2025-08-20 11:36:39 - INFO - 最终分析结果: D:/Drone_Project/nginxData\ODM\AI\20250705171601\arableLand\20250705171601_2_1755660318.shp
2025-08-20 11:36:39 - INFO - TaskInfo.json已更新完成
