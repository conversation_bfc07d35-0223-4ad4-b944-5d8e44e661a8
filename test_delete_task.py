#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试删除任务接口
验证不同状态任务的删除功能
"""

import requests
import json
import time

def test_delete_task():
    """测试删除任务功能"""
    print("🧪 测试删除任务接口")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试参数
    test_cases = [
        {
            'name': '删除已完成任务',
            'image_id': '20250705171601',
            'task_id': '60a6d2e1-f8c5-40e2-84e6-62957b38783f',  # 请替换为实际的task_id
            'expected_status': '完成'
        },
        {
            'name': '删除不存在的任务',
            'image_id': '20250705171601',
            'task_id': 'non-existent-task-id',
            'expected_status': None
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🎯 {test_case['name']}")
        print(f"   影像ID: {test_case['image_id']}")
        print(f"   任务ID: {test_case['task_id']}")
        
        try:
            # 调用删除接口
            response = requests.get(
                f"{base_url}/delete-task/",
                params={
                    'id': test_case['image_id'],
                    'task_id': test_case['task_id']
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result['success']:
                    print(f"   ✅ 删除成功: {result['message']}")
                    
                    # 显示删除详情
                    details = result.get('details', {})
                    actions = details.get('actions_performed', [])
                    
                    print(f"   📋 执行的操作:")
                    for action in actions:
                        print(f"     - {action}")
                    
                    # 显示删除的资源
                    if 'deleted_geoserver_layers' in details:
                        layers = details['deleted_geoserver_layers']
                        print(f"   🌐 删除的GeoServer图层: {len(layers)}个")
                        for layer in layers:
                            print(f"     - {layer}")
                    
                    if 'deleted_local_files' in details:
                        files = details['deleted_local_files']
                        print(f"   📁 删除的本地文件: {len(files)}个")
                        for file in files[:5]:  # 只显示前5个
                            print(f"     - {file}")
                        if len(files) > 5:
                            print(f"     - ... 还有{len(files)-5}个文件")
                    
                else:
                    print(f"   ❌ 删除失败: {result['message']}")
                    
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def get_taskinfo_for_testing():
    """获取TaskInfo用于测试"""
    print("\n📋 获取TaskInfo用于测试...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    test_image_ids = ['20250705171599', '20250705171600', '20250705171601']
    
    available_tasks = []
    
    for image_id in test_image_ids:
        try:
            response = requests.get(
                f"{base_url}/taskinfo/",
                params={'id': image_id},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result['status'] == 'success' and result['data']:
                    tasks = result['data']
                    print(f"\n📁 影像 {image_id}: {len(tasks)} 个任务")
                    
                    for i, task in enumerate(tasks):
                        task_id = task.get('task_id', 'N/A')
                        status = task.get('status', 'N/A')
                        category = task.get('analysis_category', 'N/A')
                        
                        print(f"   任务{i+1}: {task_id[:8]}... | {status} | {category}")
                        
                        available_tasks.append({
                            'image_id': image_id,
                            'task_id': task_id,
                            'status': status,
                            'category': category
                        })
                else:
                    print(f"📁 影像 {image_id}: 无任务")
                    
        except Exception as e:
            print(f"📁 影像 {image_id}: 获取失败 - {e}")
    
    return available_tasks

def test_delete_by_status():
    """按状态测试删除功能"""
    print("\n🔍 按状态测试删除功能...")
    
    # 获取可用任务
    available_tasks = get_taskinfo_for_testing()
    
    if not available_tasks:
        print("❌ 没有可用的任务进行测试")
        return
    
    # 按状态分组
    tasks_by_status = {}
    for task in available_tasks:
        status = task['status']
        if status not in tasks_by_status:
            tasks_by_status[status] = []
        tasks_by_status[status].append(task)
    
    print(f"\n📊 任务状态分布:")
    for status, tasks in tasks_by_status.items():
        print(f"   {status}: {len(tasks)} 个任务")
    
    # 选择测试任务
    test_tasks = []
    
    # 优先选择已完成的任务
    if '完成' in tasks_by_status:
        test_tasks.append(tasks_by_status['完成'][0])
        print(f"✅ 选择完成任务进行测试: {test_tasks[-1]['task_id'][:8]}...")
    
    # 选择失败的任务
    if '失败' in tasks_by_status:
        test_tasks.append(tasks_by_status['失败'][0])
        print(f"❌ 选择失败任务进行测试: {test_tasks[-1]['task_id'][:8]}...")
    
    # 选择已取消的任务
    if '已取消' in tasks_by_status:
        test_tasks.append(tasks_by_status['已取消'][0])
        print(f"⏹️ 选择已取消任务进行测试: {test_tasks[-1]['task_id'][:8]}...")
    
    if not test_tasks:
        print("⚠️ 没有合适的任务进行删除测试")
        return
    
    # 执行删除测试
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    for task in test_tasks:
        print(f"\n🗑️ 删除任务: {task['status']} - {task['task_id'][:8]}...")
        
        try:
            response = requests.get(
                f"{base_url}/delete-task/",
                params={
                    'id': task['image_id'],
                    'task_id': task['task_id']
                },
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result['success']:
                    print(f"   ✅ 删除成功")
                    
                    details = result.get('details', {})
                    actions = details.get('actions_performed', [])
                    
                    for action in actions:
                        print(f"     📋 {action}")
                        
                else:
                    print(f"   ❌ 删除失败: {result['message']}")
                    
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 删除异常: {e}")
        
        # 等待一下再处理下一个
        time.sleep(2)

def test_delete_running_task():
    """测试删除正在运行的任务"""
    print("\n⚡ 测试删除正在运行的任务...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 先提交一个任务
    task_params = {
        'id': '20250705171599',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print("📤 提交测试任务...")
        response = requests.get(f"{base_url}/queued-combined-ai-spatial-analysis/", 
                              params=task_params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result['success']:
                task_id = result['data']['task_id']
                print(f"✅ 任务提交成功: {task_id[:8]}...")
                
                # 等待任务开始执行
                print("⏰ 等待10秒让任务开始执行...")
                time.sleep(10)
                
                # 尝试删除正在执行的任务
                print("🗑️ 尝试删除正在执行的任务...")
                delete_response = requests.get(
                    f"{base_url}/delete-task/",
                    params={
                        'id': task_params['id'],
                        'task_id': task_id
                    },
                    timeout=30
                )
                
                if delete_response.status_code == 200:
                    delete_result = delete_response.json()
                    
                    if delete_result['success']:
                        print(f"   ⚠️ 意外成功删除了执行中的任务")
                    else:
                        print(f"   ✅ 正确拒绝删除执行中的任务: {delete_result['message']}")
                else:
                    print(f"   ❌ 删除请求HTTP错误: {delete_response.status_code}")
                    
            else:
                print(f"❌ 任务提交失败: {result['message']}")
        else:
            print(f"❌ 任务提交HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def verify_geoserver_config():
    """验证GeoServer配置"""
    print("🔧 验证GeoServer配置...")

    try:
        from config import GEOSERVER_URL, GEOSERVER_USER, GEOSERVER_PASSWORD
        from geoserver_api.core.task_deletion_manager import TaskDeletionManager

        print(f"   GeoServer URL: {GEOSERVER_URL}")
        print(f"   GeoServer User: {GEOSERVER_USER}")

        # 创建删除管理器并验证配置
        deletion_manager = TaskDeletionManager()
        config_info = deletion_manager.get_geoserver_info()

        print(f"   ✅ 配置验证成功")
        return True

    except Exception as e:
        print(f"   ❌ 配置验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 删除任务接口测试")

    # 首先验证配置
    if not verify_geoserver_config():
        print("❌ GeoServer配置验证失败，请检查config.py文件")
        return

    print(f"\n📝 测试内容:")
    print(f"1. 获取现有任务信息")
    print(f"2. 按状态测试删除功能")
    print(f"3. 测试删除正在运行的任务")
    print(f"4. 验证删除结果")
    
    # 选择测试模式
    print(f"\n🔧 测试模式:")
    print(f"1. 按状态自动测试（推荐）")
    print(f"2. 手动指定任务测试")
    print(f"3. 测试删除运行中任务")
    
    choice = input("请选择测试模式 (1/2/3): ").strip()
    
    if choice == "1":
        test_delete_by_status()
    elif choice == "2":
        test_delete_task()
    elif choice == "3":
        test_delete_running_task()
    else:
        print("❌ 无效选择，执行按状态自动测试")
        test_delete_by_status()
    
    print(f"\n🎉 测试完成！")
    
    print(f"\n📚 接口说明:")
    print(f"- 删除任务: GET /api/analysis/delete-task/?id=<image_id>&task_id=<task_id>")
    
    print(f"\n💡 删除策略:")
    print(f"1. 已取消: 直接删除TaskInfo记录")
    print(f"2. 等待中: 取消任务 → 删除记录")
    print(f"3. 完成/失败: 删除GeoServer图层 → 删除文件 → 删除记录")
    print(f"4. 进行中: 检查是否真正执行 → 拒绝或按失败处理")

if __name__ == "__main__":
    main()
