#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试批量下载接口的readme.txt功能
验证压缩包中是否包含详细的数据说明文档
"""

import requests
import json
import os
import zipfile
import tempfile
from config import DJ<PERSON><PERSON><PERSON>_BASE_URL

def test_download_with_readme():
    """测试带readme.txt的批量下载功能"""
    print("🧪 测试批量下载接口的readme.txt功能")
    
    base_url = f"{DJANGO_BASE_URL}/api/analysis/download-data/"
    
    # 测试数据 - 使用多个不同类型的文件
    test_data = {
        "file_paths": [
            "D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand/20250705171599_1_1756085988.shp",
            "D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand/20250705171599_2_1756085988.shp",
            "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756086000.shp",
            "D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756086000.shp"
        ]
    }
    
    try:
        print(f"\n📤 发送批量下载请求...")
        print(f"   文件数量: {len(test_data['file_paths'])}")
        
        for i, path in enumerate(test_data['file_paths'], 1):
            filename = os.path.basename(path)
            print(f"   文件{i}: {filename}")
        
        response = requests.post(
            base_url,
            json=test_data,
            timeout=120  # 增加超时时间
        )
        
        if response.status_code == 200:
            # 检查是否是ZIP文件
            content_type = response.headers.get('content-type', '')
            if 'application/zip' in content_type:
                print(f"✅ 成功获取ZIP文件")
                
                # 保存ZIP文件到临时位置
                temp_dir = tempfile.mkdtemp()
                zip_path = os.path.join(temp_dir, "downloaded_analysis_data_with_readme.zip")
                
                with open(zip_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"💾 ZIP文件已保存: {zip_path}")
                print(f"📊 文件大小: {len(response.content)} 字节")
                
                # 验证ZIP文件内容，特别是readme.txt
                verify_zip_with_readme(zip_path)
                
                return True
            else:
                print(f"❌ 响应不是ZIP文件: {content_type}")
                print(f"响应内容: {response.text[:200]}")
                return False
        else:
            print(f"❌ 下载失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def verify_zip_with_readme(zip_path):
    """验证ZIP文件内容，特别检查readme.txt"""
    print(f"\n🔍 验证ZIP文件内容...")
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            file_list = zipf.namelist()
            
            print(f"📁 ZIP文件包含 {len(file_list)} 个文件:")
            
            # 分类文件
            readme_files = []
            shp_files = []
            other_files = []
            
            for file_name in file_list:
                if file_name.endswith('readme.txt'):
                    readme_files.append(file_name)
                elif file_name.endswith('.shp'):
                    shp_files.append(file_name)
                else:
                    other_files.append(file_name)
            
            print(f"   📋 readme文件: {len(readme_files)} 个")
            print(f"   🗺️ SHP文件: {len(shp_files)} 个")
            print(f"   📄 其他文件: {len(other_files)} 个")
            
            # 检查readme.txt文件
            if readme_files:
                print(f"\n✅ 找到readme.txt文件:")
                for readme_file in readme_files:
                    print(f"   📋 {readme_file}")
                    
                    # 读取readme.txt内容
                    readme_content = zipf.read(readme_file).decode('utf-8')
                    analyze_readme_content(readme_content, readme_file)
            else:
                print(f"\n❌ 未找到readme.txt文件!")
                return False
            
            # 检查SHP文件
            if shp_files:
                print(f"\n🗺️ SHP文件列表:")
                for shp_file in shp_files:
                    print(f"   - {shp_file}")
            
            # 检查文件结构
            print(f"\n📂 文件结构:")
            folders = set()
            for file_name in file_list:
                if '/' in file_name:
                    folder = file_name.split('/')[0]
                    folders.add(folder)
            
            for folder in sorted(folders):
                folder_files = [f for f in file_list if f.startswith(folder + '/')]
                print(f"   📁 {folder}/ ({len(folder_files)} 个文件)")
                
                # 显示前几个文件
                for file_name in sorted(folder_files)[:5]:
                    print(f"     - {os.path.basename(file_name)}")
                if len(folder_files) > 5:
                    print(f"     - ... 还有 {len(folder_files) - 5} 个文件")
            
            return True
            
    except Exception as e:
        print(f"❌ 验证ZIP文件失败: {e}")
        return False

def analyze_readme_content(content, filename):
    """分析readme.txt文件内容"""
    print(f"\n📖 分析readme.txt内容: {filename}")
    
    lines = content.split('\n')
    
    # 统计信息
    print(f"   📊 文件统计:")
    print(f"     总行数: {len(lines)}")
    print(f"     文件大小: {len(content)} 字符")
    
    # 检查关键内容
    key_sections = {
        '下载时间': False,
        '下载统计': False,
        '数据说明': False,
        '文件详细信息': False,
        '使用说明': False,
        '技术支持': False
    }
    
    for line in lines:
        for section in key_sections:
            if section in line:
                key_sections[section] = True
    
    print(f"   📋 内容检查:")
    for section, found in key_sections.items():
        status = "✅" if found else "❌"
        print(f"     {status} {section}")
    
    # 显示前几行内容
    print(f"   📄 内容预览:")
    for i, line in enumerate(lines[:10]):
        if line.strip():
            print(f"     {i+1:2d}: {line}")
    
    if len(lines) > 10:
        print(f"     ... 还有 {len(lines) - 10} 行")
    
    # 检查文件信息解析
    file_info_section = False
    file_count = 0
    
    for line in lines:
        if '文件详细信息' in line:
            file_info_section = True
        elif file_info_section and line.strip().startswith(('1.', '2.', '3.', '4.', '5.')):
            file_count += 1
    
    print(f"   📁 解析到的文件信息: {file_count} 个")
    
    return all(key_sections.values())

def test_readme_generation():
    """测试readme生成功能"""
    print(f"\n🔧 测试readme生成功能...")
    
    # 模拟下载信息
    mock_download_info = [
        {
            'original_path': 'D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand/20250705171599_1_1756085988.shp',
            'filename': '20250705171599_1_1756085988.shp',
            'files': ['20250705171599_1_1756085988.shp', '20250705171599_1_1756085988.shx', '20250705171599_1_1756085988.dbf'],
            'status': 'success'
        },
        {
            'original_path': 'D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand/20250705171599_2_1756085988.shp',
            'filename': '20250705171599_2_1756085988.shp',
            'files': ['20250705171599_2_1756085988.shp', '20250705171599_2_1756085988.shx'],
            'status': 'success'
        },
        {
            'original_path': 'D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756086000.shp',
            'filename': '20250705171600_1_1756086000.shp',
            'files': [],
            'status': 'failed'
        }
    ]
    
    # 这里我们无法直接调用内部函数，但可以通过实际API测试来验证
    print(f"   📋 模拟数据准备完成")
    print(f"   📊 成功文件: 2 个")
    print(f"   📊 失败文件: 1 个")
    
    return True

def main():
    """主函数"""
    print("🧪 批量下载接口readme.txt功能测试")
    print("=" * 60)
    
    print("📝 新增功能:")
    print("1. 📋 自动生成readme.txt文件")
    print("2. 📊 包含下载统计信息")
    print("3. 📁 详细的文件对应关系")
    print("4. 📖 完整的使用说明")
    print("5. 🔧 技术支持信息")
    
    print("\n🎯 测试目标:")
    print("- 验证readme.txt文件是否生成")
    print("- 检查文件内容是否完整")
    print("- 确认数据对应关系清晰")
    print("- 验证使用说明详细")
    
    # 测试readme生成功能
    test_readme_generation()
    
    # 执行实际下载测试
    choice = input("\n是否执行实际下载测试? (y/n): ").strip().lower()
    
    if choice == 'y':
        success = test_download_with_readme()
        
        if success:
            print(f"\n🎉 批量下载readme.txt功能测试通过！")
            
            print(f"\n📚 readme.txt文件包含:")
            print(f"1. 📅 下载时间和基本信息")
            print(f"2. 📊 下载统计(成功/失败数量)")
            print(f"3. 📋 数据说明(文件类型和命名规则)")
            print(f"4. 📁 详细文件信息(路径、类型、状态)")
            print(f"5. 📖 使用说明(导入方法、数据格式)")
            print(f"6. 🔧 技术支持信息")
            
            print(f"\n💡 用户体验改进:")
            print(f"- 用户可以快速了解数据内容")
            print(f"- 清楚知道每个文件的来源和用途")
            print(f"- 获得详细的使用指导")
            print(f"- 遇到问题时有明确的支持渠道")
            
        else:
            print(f"\n❌ 批量下载readme.txt功能测试失败")
            print(f"请检查:")
            print(f"1. Django服务器是否正常运行")
            print(f"2. 测试文件路径是否存在")
            print(f"3. readme.txt生成逻辑是否正确")
    else:
        print("跳过实际下载测试")
    
    print(f"\n📋 功能说明:")
    print(f"- 接口: POST /api/analysis/download-data/")
    print(f"- 新增: 压缩包中自动包含readme.txt")
    print(f"- 内容: 详细的数据说明和使用指导")
    print(f"- 格式: UTF-8编码的纯文本文件")

if __name__ == "__main__":
    main()
