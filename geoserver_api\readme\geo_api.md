# GeoServer发布API文档

## 概述

GeoServer发布API提供将Shapefile和GeoTIFF文件异步发布到GeoServer的功能，支持单文件发布、批量发布、特定结构发布、自动切片、批量样式上传等多种模式。

## 基础信息

- **服务地址**: `http://127.0.0.1:8091`
- **数据格式**: JSON

## API端点

### 1. 异步发布Shapefile文件

**接口地址**: `GET /api/geo/shapefile/execute/`

**接口说明**: 异步发布单个Shapefile文件到GeoServer

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| path | string | 是 | Shapefile文件路径 | D:/data/boundary.shp |
| workspace | string | 是 | GeoServer工作区名称 | testodm |
| store_name | string | 否 | 存储名称，默认使用文件名 | boundary_store |
| layer_name | string | 否 | 图层名称，默认使用文件名 | boundary_layer |
| charset | string | 否 | 字符集，默认UTF-8 | UTF-8 |

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "Shapefile发布任务已启动",
  "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

错误响应 (400):
```json
{
  "status": "error",
  "message": "缺少必要参数: path, workspace"
}
```

**调用示例**:
```
# 基础发布
GET http://127.0.0.1:8091/api/geo/shapefile/execute/?path=D:/data/boundary.shp&workspace=testodm

# 指定存储和图层名称
GET http://127.0.0.1:8091/api/geo/shapefile/execute/?path=D:/data/boundary.shp&workspace=testodm&store_name=boundary_store&layer_name=boundary_layer&charset=UTF-8
```

### 2. 同步发布Shapefile文件

**接口地址**: `GET|POST /api/geo/shapefile/publish/`

**接口说明**: 同步发布单个Shapefile文件到GeoServer（立即返回结果）

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| file_path | string | 是 | Shapefile文件路径 | D:/data/boundary.shp |
| workspace | string | 是 | GeoServer工作区名称 | testodm |
| store | string | 否 | 存储名称，默认使用文件名 | boundary_store |
| layer | string | 否 | 图层名称，默认使用文件名 | boundary_layer |
| charset | string | 否 | 字符集，默认UTF-8 | UTF-8 |

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "Shapefile发布成功 (已覆盖现有资源)",
  "details": {
    "file_path": "D:/data/boundary.shp",
    "workspace": "testodm",
    "store": "boundary_store",
    "layer": "boundary_layer",
    "charset": "UTF-8",
    "crs": "EPSG:4326",
    "replaced_existing": true
  }
}
```

**调用示例**:
```bash
# GET方式
GET http://127.0.0.1:8091/api/geo/shapefile/publish/?file_path=D:/data/boundary.shp&workspace=testodm

# POST方式
curl -X POST http://127.0.0.1:8091/api/geo/shapefile/publish/ \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "D:/data/boundary.shp",
    "workspace": "testodm",
    "store": "boundary_store",
    "layer": "boundary_layer"
  }'
```

### 3. 发布Shapefile（支持指定坐标系）

**接口地址**: `GET|POST /api/geo/shapefile/publish-with-crs/`

**接口说明**: 发布Shapefile文件到GeoServer，支持指定EPSG坐标系

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| file_path | string | 是 | Shapefile文件路径 | D:/data/boundary.shp |
| workspace | string | 是 | GeoServer工作区名称 | testodm |
| store | string | 否 | 存储名称，默认使用文件名 | boundary_store |
| layer | string | 否 | 图层名称，默认使用文件名 | boundary_layer |
| charset | string | 否 | 字符集，默认UTF-8 | UTF-8 |
| epsg | string | 否 | EPSG坐标系代码，默认4326 | 32648 |

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "Shapefile发布成功 (EPSG:32648)",
  "details": {
    "file_path": "D:/data/boundary.shp",
    "workspace": "testodm",
    "store": "boundary_store",
    "layer": "boundary_layer",
    "charset": "UTF-8",
    "crs": "EPSG:32648",
    "replaced_existing": false
  }
}
```

**调用示例**:
```bash
# GET方式 - 使用默认4326坐标系
GET http://127.0.0.1:8091/api/geo/shapefile/publish-with-crs/?file_path=D:/data/boundary.shp&workspace=testodm

# GET方式 - 指定32648坐标系
GET http://127.0.0.1:8091/api/geo/shapefile/publish-with-crs/?file_path=D:/data/boundary.shp&workspace=testodm&epsg=32648

# POST方式
curl -X POST http://127.0.0.1:8091/api/geo/shapefile/publish-with-crs/ \
  -H "Content-Type: application/json" \
  -d '{
    "file_path": "D:/data/boundary.shp",
    "workspace": "testodm",
    "store": "boundary_store",
    "layer": "boundary_layer",
    "epsg": "32648"
  }'
```

### 4. 批量发布Shapefile目录

**接口地址**: `GET /api/geo/shapefile/directory/`

**接口说明**: 批量发布指定目录中的所有Shapefile文件

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| directory | string | 是 | Shapefile目录路径 | D:/data/shapefiles/ |
| workspace | string | 是 | GeoServer工作区名称 | testodm |
| store | string | 否 | 存储名称前缀 | shp_store |
| charset | string | 否 | 字符集，默认UTF-8 | UTF-8 |

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "发布了 3 个Shapefile，失败 0 个",
  "details": {
    "success": ["boundary.shp", "roads.shp", "buildings.shp"],
    "failed": []
  }
}
```

**调用示例**:
```bash
GET http://127.0.0.1:8091/api/geo/shapefile/directory/?directory=D:/data/shapefiles/&workspace=testodm&charset=UTF-8
```

### 4. 单文件GeoTIFF发布

**接口地址**: `GET /api/geo/geotiff/execute/`

**接口说明**: 将单个GeoTIFF文件发布到GeoServer

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| file_path | string | 是 | GeoTIFF文件路径 | D:/data/image.tif |
| workspace | string | 是 | GeoServer工作区名称 | testodm |
| store_name | string | 否 | 存储名称，默认使用文件名 | my_store |
| layer_name | string | 否 | 图层名称，默认使用文件名 | my_layer |

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "GeoTIFF发布任务已启动",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "file_path": "D:/data/image.tif",
  "workspace": "testodm",
  "store_name": "image",
  "layer_name": "image"
}
```

错误响应 (400):
```json
{
  "status": "error",
  "message": "缺少必要参数: file_path, workspace"
}
```

**调用示例**:
```
# 基础发布
GET http://127.0.0.1:8091/api/geo/geotiff/execute/?file_path=D:/data/image.tif&workspace=testodm

# 指定存储和图层名称
GET http://127.0.0.1:8091/api/geo/geotiff/execute/?file_path=D:/data/image.tif&workspace=testodm&store_name=my_store&layer_name=my_layer
```



### 5. 特定结构GeoTIFF发布

**接口地址**: `GET /api/geo/structured-geotiff/execute/`

**接口说明**: 发布特定目录结构中的GeoTIFF文件（扫描子目录，发布与子目录同名的GeoTIFF文件）

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| root_directory | string | 是 | 根目录路径 | D:/Drone_Project/nginxData/ODM/Output |
| workspace | string | 是 | GeoServer工作区名称 | testodm |
| store_name | string | 否 | 存储名称前缀 | odm_output |

**目录结构示例**:
```
D:/Drone_Project/nginxData/ODM/Output/
├── 20250705171600/
│   ├── 20250705171600.tif  ← 会被发布
│   └── other_files...
├── 20250706080000/
│   ├── 20250706080000.tif  ← 会被发布
│   └── other_files...
└── other_directories...
```

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "特定结构GeoTIFF发布任务已启动",
  "task_id": "660e8400-e29b-41d4-a716-446655440001",
  "root_directory": "D:/Drone_Project/nginxData/ODM/Output",
  "workspace": "testodm",
  "expected_files": [
    "D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600.tif",
    "D:/Drone_Project/nginxData/ODM/Output/20250706080000/20250706080000.tif"
  ]
}
```

**调用示例**:
```
GET http://127.0.0.1:8091/api/geo/structured-geotiff/execute/?root_directory=D:/Drone_Project/nginxData/ODM/Output&workspace=testodm
```

### 6. 获取发布任务状态

**接口地址**: `GET /api/geo/status/`

**接口说明**: 查询GeoServer发布任务的状态

**请求参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| task_id | string | 是 | 任务ID | 550e8400-e29b-41d4-a716-446655440000 |

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "task": {
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "type": "geotiff_publish",
    "status": "正在发布",
    "progress": 75,
    "start_time": "2025-08-07 10:30:15",
    "estimated_completion": "2025-08-07 10:35:00",
    "file_path": "D:/data/image.tif",
    "workspace": "testodm",
    "store_name": "image",
    "layer_name": "image",
    "current_step": "创建图层样式",
    "geoserver_url": "http://localhost:8083/geoserver/testodm/wms?service=WMS&version=1.1.0&request=GetMap&layers=testodm:image",
    "log_file": "D:/logs/geotiff_publish_550e8400.log"
  }
}
```

**任务状态说明**:

| 状态 | 说明 |
|------|------|
| 准备中 | 任务初始化 |
| 正在发布 | 正在发布到GeoServer |
| 发布完成 | 发布成功完成 |
| 发布失败 | 发布过程中出现错误 |
| 已取消 | 任务被取消 |

**调用示例**:
```
GET http://127.0.0.1:8091/api/geo/status/?task_id=550e8400-e29b-41d4-a716-446655440000
```

### 7. 批量样式上传

**接口地址**: `GET /api/geo/styles/batch-upload/`

**接口说明**: 批量上传文件夹中的GeoServer样式文件到GeoServer

**查询参数**:

| 参数名 | 类型 | 必需 | 说明 | 示例 |
|--------|------|------|------|------|
| folder_path | string | 是 | 样式文件夹路径 | D:/styles/ |
| workspace | string | 否 | 工作区名称，空表示全局样式 | my_workspace |
| overwrite | string | 否 | 是否覆盖已存在的样式，默认true | true |

**支持的文件格式**:
- `.txt` - 文本格式的SLD样式文件
- `.sld` - 标准SLD样式文件
- `.xml` - XML格式的SLD样式文件

**样式文件格式示例**:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<StyledLayerDescriptor version="1.0.0"
  xmlns="http://www.opengis.net/sld"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.opengis.net/sld http://schemas.opengis.net/sld/1.0.0/StyledLayerDescriptor.xsd">
  <NamedLayer>
    <Name>Aquamarine</Name>
    <UserStyle>
      <Name>Aquamarine</Name>
      <Title>Aquamarine polygon 0.2 opacity</Title>
      <FeatureTypeStyle>
        <Rule>
          <PolygonSymbolizer>
            <Fill>
              <CssParameter name="fill">#7FFFD4</CssParameter>
              <CssParameter name="fill-opacity">0.2</CssParameter>
            </Fill>
            <Stroke>
              <CssParameter name="stroke">#000000</CssParameter>
              <CssParameter name="stroke-width">1</CssParameter>
              <CssParameter name="stroke-opacity">1.0</CssParameter>
            </Stroke>
          </PolygonSymbolizer>
        </Rule>
      </FeatureTypeStyle>
    </UserStyle>
  </NamedLayer>
</StyledLayerDescriptor>
```

**返回数据**:

成功响应 (200):
```json
{
  "status": "success",
  "message": "成功上传所有 5 个样式文件",
  "data": {
    "folder_path": "D:/styles/",
    "workspace": "my_workspace",
    "overwrite": true,
    "summary": {
      "total_files": 5,
      "success_count": 5,
      "error_count": 0,
      "supported_extensions": [".txt", ".sld", ".xml"]
    },
    "results": [
      {
        "filename": "Aquamarine.txt",
        "style_name": "Aquamarine",
        "status": "success",
        "message": "样式上传成功",
        "workspace": "my_workspace"
      }
    ]
  }
}
```

部分成功响应 (200):
```json
{
  "status": "partial_success",
  "message": "部分成功：成功上传 3 个，失败 2 个样式文件",
  "data": {
    "folder_path": "D:/styles/",
    "workspace": "global",
    "overwrite": true,
    "summary": {
      "total_files": 5,
      "success_count": 3,
      "error_count": 2,
      "supported_extensions": [".txt", ".sld", ".xml"]
    },
    "results": [
      {
        "filename": "Blue.txt",
        "style_name": "Blue",
        "status": "success",
        "message": "样式上传成功",
        "workspace": "global"
      },
      {
        "filename": "Invalid.txt",
        "style_name": "Invalid",
        "status": "error",
        "message": "样式文件内容为空"
      }
    ]
  }
}
```

错误响应 (400):
```json
{
  "status": "error",
  "message": "缺少必需参数: folder_path"
}
```

错误响应 (404):
```json
{
  "status": "error",
  "message": "指定的文件夹不存在: D:/nonexistent/"
}
```

**调用示例**:

1. 上传到全局样式:
```bash
GET http://127.0.0.1:8091/api/geo/styles/batch-upload/?folder_path=D:/geoserver_styles/&overwrite=true
```

2. 上传到指定工作区:
```bash
GET http://127.0.0.1:8091/api/geo/styles/batch-upload/?folder_path=D:/my_styles/&workspace=my_workspace&overwrite=false
```

3. 使用默认参数:
```bash
GET http://127.0.0.1:8091/api/geo/styles/batch-upload/?folder_path=D:/styles/
```

**使用场景**:
- 批量导入预定义的样式文件到GeoServer
- 样式库迁移和备份恢复
- 为新项目快速配置标准样式集
- 团队间共享样式配置

## 错误处理

**常见错误**:

| 错误类型 | HTTP状态码 | 说明 |
|----------|------------|------|
| 文件不存在 | 404 | GeoTIFF文件不存在 |
| 工作区不存在 | 404 | GeoServer工作区不存在 |
| 权限不足 | 403 | 没有GeoServer操作权限 |
| 文件格式错误 | 400 | 不是有效的GeoTIFF文件 |
| GeoServer连接失败 | 500 | 无法连接到GeoServer |
| 存储名称冲突 | 409 | 存储名称已存在 |
| 样式文件夹不存在 | 404 | 指定的样式文件夹不存在 |
| 样式文件格式错误 | 400 | 样式文件不是有效的SLD格式 |
| 样式名称冲突 | 409 | 样式名称已存在且未启用覆盖模式 |
| 样式文件编码错误 | 400 | 样式文件编码格式不正确 |

## 注意事项

- 确保GeoServer可以访问文件路径
- 确保GeoTIFF文件包含正确的坐标系信息
- 大文件发布可能需要较长时间
- 避免同时发布过多文件
- 样式文件必须是有效的SLD格式（XML）
- 样式文件编码必须是UTF-8
- 批量上传样式时建议先备份现有样式
- 全局样式对所有工作区可见，工作区样式仅对指定工作区可见
