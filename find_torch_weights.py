#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找PyTorch预训练权重文件位置
"""

import os
import torch
import torchvision.models as models
from pathlib import Path

def find_torch_cache_dir():
    """查找torch缓存目录"""
    # 获取torch hub目录
    hub_dir = torch.hub.get_dir()
    print(f"Torch Hub目录: {hub_dir}")
    
    # 检查checkpoints目录
    checkpoints_dir = os.path.join(hub_dir, 'checkpoints')
    print(f"Checkpoints目录: {checkpoints_dir}")
    
    if os.path.exists(checkpoints_dir):
        print(f"✅ Checkpoints目录存在")
        files = os.listdir(checkpoints_dir)
        print(f"包含文件: {files}")
        
        # 查找ResNet101权重
        resnet_files = [f for f in files if 'resnet101' in f.lower()]
        if resnet_files:
            print(f"🎯 找到ResNet101权重文件: {resnet_files}")
            for f in resnet_files:
                full_path = os.path.join(checkpoints_dir, f)
                size = os.path.getsize(full_path) / (1024*1024)  # MB
                print(f"  文件: {f}")
                print(f"  路径: {full_path}")
                print(f"  大小: {size:.1f} MB")
        else:
            print("❌ 未找到ResNet101权重文件")
    else:
        print(f"❌ Checkpoints目录不存在")
    
    return checkpoints_dir

def download_resnet101_weights():
    """下载ResNet101权重（如果网络可用）"""
    try:
        print("\n=== 尝试下载ResNet101权重 ===")
        model = models.resnet101(weights=models.ResNet101_Weights.IMAGENET1K_V1)
        print("✅ ResNet101权重下载成功")
        
        # 查找下载的文件
        checkpoints_dir = find_torch_cache_dir()
        return True
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False

def check_existing_weights():
    """检查现有权重文件"""
    print("\n=== 检查现有权重文件 ===")
    
    # 常见的缓存位置
    possible_locations = [
        os.path.expanduser("~/.cache/torch/hub/checkpoints/"),
        os.path.expanduser("~/.torch/models/"),
        os.path.join(os.environ.get('TORCH_HOME', ''), 'hub', 'checkpoints') if os.environ.get('TORCH_HOME') else None,
        "C:/Users/" + os.environ.get('USERNAME', '') + "/.cache/torch/hub/checkpoints/",
        "C:/Users/" + os.environ.get('USERNAME', '') + "/.torch/models/",
    ]
    
    # 移除None值
    possible_locations = [loc for loc in possible_locations if loc]
    
    found_files = []
    
    for location in possible_locations:
        if os.path.exists(location):
            print(f"✅ 检查目录: {location}")
            try:
                files = os.listdir(location)
                resnet_files = [f for f in files if 'resnet101' in f.lower()]
                if resnet_files:
                    for f in resnet_files:
                        full_path = os.path.join(location, f)
                        size = os.path.getsize(full_path) / (1024*1024)
                        found_files.append({
                            'file': f,
                            'path': full_path,
                            'size': size
                        })
                        print(f"  🎯 找到: {f} ({size:.1f} MB)")
            except Exception as e:
                print(f"  ❌ 无法访问: {e}")
        else:
            print(f"❌ 目录不存在: {location}")
    
    return found_files

def create_copy_instructions(found_files):
    """创建复制指令"""
    if not found_files:
        print("\n❌ 未找到任何ResNet101权重文件")
        return
    
    print(f"\n=== 复制指令 ===")
    print("请将以下文件复制到断网电脑的相同位置：")
    
    for file_info in found_files:
        print(f"\n源文件: {file_info['path']}")
        print(f"目标位置: 断网电脑的 {os.path.dirname(file_info['path'])}")
        print(f"复制命令: ")
        print(f"  mkdir -p \"{os.path.dirname(file_info['path'])}\"")
        print(f"  copy \"{file_info['path']}\" \"{file_info['path']}\"")

def main():
    """主函数"""
    print("=== PyTorch预训练权重查找工具 ===")
    
    # 1. 查找torch缓存目录
    find_torch_cache_dir()
    
    # 2. 检查现有权重
    found_files = check_existing_weights()
    
    # 3. 如果没有找到，尝试下载
    if not found_files:
        print("\n未找到现有权重，尝试下载...")
        if download_resnet101_weights():
            found_files = check_existing_weights()
    
    # 4. 创建复制指令
    create_copy_instructions(found_files)
    
    # 5. 显示权重文件的预期名称
    print(f"\n=== 预期的权重文件名 ===")
    print("ResNet101 ImageNet权重通常命名为:")
    print("- resnet101-63fe2227.pth")
    print("- resnet101-5d3b4d8f.pth")
    print("具体名称可能因PyTorch版本而异")

if __name__ == '__main__':
    main()
