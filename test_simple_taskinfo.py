#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的TaskInfo.json创建测试
"""

import requests
import time
import json
import os

def test_simple_taskinfo():
    """简化的TaskInfo.json创建测试"""
    print("🧪 简化的TaskInfo.json创建测试...")
    
    # 测试数据
    image_id = "20250705171601"
    expected_taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    print(f"📁 预期TaskInfo路径: {expected_taskinfo_path}")
    
    # 记录启动前状态
    before_exists = os.path.exists(expected_taskinfo_path)
    before_count = 0
    
    if before_exists:
        try:
            with open(expected_taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if isinstance(data, list):
                before_count = len(data)
            print(f"📋 启动前: 文件存在，包含 {before_count} 个任务")
        except:
            print(f"📋 启动前: 文件存在但格式错误")
    else:
        print(f"📋 启动前: 文件不存在")
    
    # 发送请求
    print(f"\n🚀 发送合并分析请求...")
    
    url = "http://127.0.0.1:8091/api/analysis/combined-ai-spatial-analysis/"
    params = {
        'id': image_id,
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        response = requests.get(url, params=params, timeout=30)
        print(f"📡 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功，ID: {task_id}")
            
            # 立即检查文件
            for i in range(10):  # 检查10次，每次间隔1秒
                time.sleep(1)
                print(f"\n🔍 第{i+1}次检查...")
                
                if os.path.exists(expected_taskinfo_path):
                    try:
                        with open(expected_taskinfo_path, 'r', encoding='utf-8') as f:
                            current_data = json.load(f)
                        
                        if isinstance(current_data, list):
                            current_count = len(current_data)
                            print(f"📊 当前任务数: {current_count}")
                            
                            if current_count > before_count:
                                print(f"✅ 发现新任务！从 {before_count} 增加到 {current_count}")
                                
                                # 查找新任务
                                for task in current_data:
                                    if task.get('task_id') == task_id:
                                        print(f"🎯 找到目标任务:")
                                        print(f"  ID: {task.get('task_id')}")
                                        print(f"  状态: {task.get('status')}")
                                        print(f"  消息: {task.get('message')}")
                                        print(f"  进度: {task.get('progress')}%")
                                        return True
                                
                                print(f"❌ 未找到目标任务ID")
                            else:
                                print(f"⏳ 任务数量未变化")
                        else:
                            print(f"⚠️ 文件格式不正确")
                            
                    except Exception as e:
                        print(f"❌ 读取文件失败: {e}")
                else:
                    print(f"❌ 文件仍不存在")
            
            print(f"\n❌ 10次检查后仍未发现新任务")
            return False
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_log_files():
    """检查最新的日志文件"""
    print(f"\n📋 检查最新日志文件...")
    
    log_dir = "geoserver_api/logs/analysislog"
    if not os.path.exists(log_dir):
        print(f"❌ 日志目录不存在: {log_dir}")
        return
    
    try:
        # 获取最新的日志文件
        log_files = [f for f in os.listdir(log_dir) if f.endswith('.log')]
        if not log_files:
            print(f"❌ 没有找到日志文件")
            return
        
        # 按修改时间排序
        log_files.sort(key=lambda x: os.path.getmtime(os.path.join(log_dir, x)), reverse=True)
        latest_log = log_files[0]
        latest_log_path = os.path.join(log_dir, latest_log)
        
        print(f"📄 最新日志文件: {latest_log}")
        
        # 读取最后20行
        with open(latest_log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📝 最后20行日志:")
        for line in lines[-20:]:
            print(f"  {line.strip()}")
            
    except Exception as e:
        print(f"❌ 读取日志失败: {e}")

if __name__ == "__main__":
    print("🚀 开始简化的TaskInfo.json创建测试")
    
    success = test_simple_taskinfo()
    
    if not success:
        print(f"\n❌ TaskInfo.json创建测试失败")
        check_log_files()
    else:
        print(f"\n🎉 TaskInfo.json创建测试成功")
    
    print(f"\n📖 如果测试失败，请检查:")
    print(f"1. 服务器是否正常运行")
    print(f"2. 日志文件中是否有错误信息")
    print(f"3. 文件路径和权限是否正确")
    print(f"4. 合并分析执行器是否正确初始化")
