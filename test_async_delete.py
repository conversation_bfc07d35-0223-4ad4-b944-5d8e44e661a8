#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试异步删除GeoServer图层功能
验证删除任务接口的响应速度和异步执行效果
"""

import requests
import time
import threading
from datetime import datetime

def test_delete_response_time():
    """测试删除任务的响应时间"""
    print("⏱️ 测试删除任务响应时间...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试参数（请替换为实际存在的任务）
    test_cases = [
        {
            'name': '删除已完成任务（有GeoServer图层）',
            'image_id': '20250705171601',
            'task_id': '60a6d2e1-f8c5-40e2-84e6-62957b38783f',  # 请替换为实际的task_id
        }
    ]
    
    for test_case in test_cases:
        print(f"\n🎯 {test_case['name']}")
        print(f"   影像ID: {test_case['image_id']}")
        print(f"   任务ID: {test_case['task_id'][:8]}...")
        
        try:
            # 记录开始时间
            start_time = time.time()
            
            # 调用删除接口
            response = requests.get(
                f"{base_url}/delete-task/",
                params={
                    'id': test_case['image_id'],
                    'task_id': test_case['task_id']
                },
                timeout=30  # 设置较短的超时时间
            )
            
            # 记录结束时间
            end_time = time.time()
            response_time = end_time - start_time
            
            print(f"   ⏱️ 响应时间: {response_time:.2f} 秒")
            
            if response.status_code == 200:
                result = response.json()
                
                if result['success']:
                    print(f"   ✅ 删除成功: {result['message']}")
                    
                    # 检查是否是异步删除
                    details = result.get('details', {})
                    actions = details.get('actions_performed', [])
                    
                    print(f"   📋 执行的操作:")
                    for action in actions:
                        print(f"     - {action}")
                    
                    # 检查GeoServer删除是否是异步的
                    geoserver_actions = [action for action in actions if 'GeoServer' in action and '异步' in action]
                    if geoserver_actions:
                        print(f"   🚀 确认GeoServer删除是异步执行")
                    else:
                        print(f"   ⚠️ 未检测到异步GeoServer删除")
                    
                    # 评估响应时间
                    if response_time < 5.0:
                        print(f"   ✅ 响应时间良好（< 5秒）")
                    elif response_time < 10.0:
                        print(f"   ⚠️ 响应时间一般（5-10秒）")
                    else:
                        print(f"   ❌ 响应时间过长（> 10秒）")
                        
                else:
                    print(f"   ❌ 删除失败: {result['message']}")
                    
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")
                print(f"   响应内容: {response.text}")
                
        except requests.Timeout:
            print(f"   ❌ 请求超时（30秒）")
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def test_concurrent_deletes():
    """测试并发删除任务"""
    print(f"\n🔄 测试并发删除任务...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 准备多个测试任务（请替换为实际存在的任务）
    test_tasks = [
        {'image_id': '20250705171599', 'task_id': 'task-id-1'},
        {'image_id': '20250705171600', 'task_id': 'task-id-2'},
        {'image_id': '20250705171601', 'task_id': 'task-id-3'},
    ]
    
    results = []
    threads = []
    
    def delete_task(task_info, results_list):
        """删除单个任务"""
        try:
            start_time = time.time()
            
            response = requests.get(
                f"{base_url}/delete-task/",
                params={
                    'id': task_info['image_id'],
                    'task_id': task_info['task_id']
                },
                timeout=30
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            result = {
                'task_info': task_info,
                'response_time': response_time,
                'success': response.status_code == 200 and response.json().get('success', False) if response.status_code == 200 else False,
                'status_code': response.status_code
            }
            
            results_list.append(result)
            
        except Exception as e:
            results_list.append({
                'task_info': task_info,
                'response_time': None,
                'success': False,
                'error': str(e)
            })
    
    print(f"   启动 {len(test_tasks)} 个并发删除任务...")
    
    # 启动并发删除
    start_time = time.time()
    
    for task_info in test_tasks:
        thread = threading.Thread(target=delete_task, args=(task_info, results))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    total_time = time.time() - start_time
    
    print(f"   📊 并发删除结果:")
    print(f"     总耗时: {total_time:.2f} 秒")
    print(f"     任务数量: {len(test_tasks)}")
    print(f"     平均响应时间: {total_time/len(test_tasks):.2f} 秒")
    
    successful_count = sum(1 for r in results if r['success'])
    print(f"     成功数量: {successful_count}/{len(test_tasks)}")
    
    for i, result in enumerate(results):
        task_id = result['task_info']['task_id'][:8]
        if result['success']:
            print(f"     任务{i+1} ({task_id}...): ✅ {result['response_time']:.2f}s")
        else:
            error = result.get('error', f"HTTP {result.get('status_code', 'unknown')}")
            print(f"     任务{i+1} ({task_id}...): ❌ {error}")

def monitor_geoserver_deletion():
    """监控GeoServer删除过程"""
    print(f"\n👀 监控GeoServer删除过程...")
    
    print(f"   💡 提示: 由于GeoServer删除是异步的，需要通过日志观察删除过程")
    print(f"   📋 可以观察的内容:")
    print(f"     1. 删除接口快速返回（< 5秒）")
    print(f"     2. 后台线程继续执行GeoServer删除")
    print(f"     3. 日志中显示异步删除的进度")
    
    # 这里可以添加日志监控逻辑，但需要访问日志文件
    print(f"   📝 建议查看日志文件:")
    print(f"     - 应用日志: 查看TaskDeletionManager的日志输出")
    print(f"     - GeoServer日志: 查看图层删除的详细过程")

def test_async_vs_sync_comparison():
    """对比异步和同步删除的性能"""
    print(f"\n📊 异步vs同步删除性能对比...")
    
    print(f"   🔧 异步删除的优势:")
    print(f"     ✅ 接口响应快速（通常 < 5秒）")
    print(f"     ✅ 不阻塞主进程")
    print(f"     ✅ 支持并发删除多个任务")
    print(f"     ✅ 用户体验更好")
    
    print(f"   ⚠️ 异步删除的注意事项:")
    print(f"     - GeoServer删除结果不在接口响应中")
    print(f"     - 需要通过日志查看删除状态")
    print(f"     - 删除失败不会影响其他操作")
    
    print(f"   📈 预期性能提升:")
    print(f"     - 同步删除: 10-30秒（取决于GeoServer响应）")
    print(f"     - 异步删除: 2-5秒（只包含本地操作）")
    print(f"     - 性能提升: 3-10倍")

def main():
    """主函数"""
    print("🧪 异步删除GeoServer图层功能测试")
    
    print(f"\n📝 测试内容:")
    print(f"1. 测试删除任务的响应时间")
    print(f"2. 验证GeoServer删除是否异步执行")
    print(f"3. 测试并发删除任务")
    print(f"4. 监控异步删除过程")
    print(f"5. 性能对比分析")
    
    # 选择测试模式
    print(f"\n🔧 测试模式:")
    print(f"1. 响应时间测试")
    print(f"2. 并发删除测试")
    print(f"3. 监控和分析")
    print(f"4. 全部测试")
    
    choice = input("请选择测试模式 (1/2/3/4): ").strip()
    
    if choice == "1":
        test_delete_response_time()
    elif choice == "2":
        test_concurrent_deletes()
    elif choice == "3":
        monitor_geoserver_deletion()
        test_async_vs_sync_comparison()
    elif choice == "4":
        test_delete_response_time()
        test_concurrent_deletes()
        monitor_geoserver_deletion()
        test_async_vs_sync_comparison()
    else:
        print("❌ 无效选择，执行响应时间测试")
        test_delete_response_time()
    
    print(f"\n🎉 测试完成！")
    
    print(f"\n📚 异步删除说明:")
    print(f"- 删除接口立即返回，不等待GeoServer删除完成")
    print(f"- GeoServer图层在后台线程中异步删除")
    print(f"- 本地文件和TaskInfo记录同步删除")
    print(f"- 大幅提升接口响应速度和用户体验")
    
    print(f"\n💡 使用建议:")
    print(f"1. 删除大量任务时，异步执行避免超时")
    print(f"2. 通过日志监控GeoServer删除状态")
    print(f"3. 删除失败不影响其他操作的完成")

if __name__ == "__main__":
    main()
