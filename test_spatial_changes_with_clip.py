#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试修改后的空间数据变化分析功能（支持裁剪范围）
"""

import requests
import time
import json
import os

def test_spatial_changes_with_clip():
    """测试使用裁剪范围的空间数据变化分析"""
    print("🧪 测试空间数据变化分析（使用裁剪范围）...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    old_data_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp"
    new_data_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp"
    clip_area_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_area_1755596377.shp"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  老数据: {old_data_path}")
        print(f"  新数据: {new_data_path}")
        print(f"  裁剪范围: {clip_area_path}")
        
        # 1. 测试使用裁剪范围的空间变化分析
        print("\n1️⃣ 启动空间变化分析任务（使用裁剪范围）...")
        params = {
            'old_data_path': old_data_path,
            'new_data_path': new_data_path,
            'area_threshold': 200.0,
            'clip_area_path': clip_area_path
        }
        
        response = requests.get(f"{base_url}/spatial-changes/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务执行
            success = monitor_task(base_url, task_id, "空间变化分析（裁剪范围）")
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_spatial_changes_without_clip():
    """测试不使用裁剪范围的空间数据变化分析"""
    print("\n🧪 测试空间数据变化分析（不使用裁剪范围）...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    old_data_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp"
    new_data_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171602/20250705171602_out.shp"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  老数据: {old_data_path}")
        print(f"  新数据: {new_data_path}")
        print(f"  裁剪范围: 使用新数据覆盖区域")
        
        # 2. 测试不使用裁剪范围的空间变化分析
        print("\n2️⃣ 启动空间变化分析任务（不使用裁剪范围）...")
        params = {
            'old_data_path': old_data_path,
            'new_data_path': new_data_path,
            'area_threshold': 200.0
        }
        
        response = requests.get(f"{base_url}/spatial-changes/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务执行
            success = monitor_task(base_url, task_id, "空间变化分析（无裁剪）")
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_combined_analysis():
    """测试合并分析功能"""
    print("\n🧪 测试合并分析功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据路径
    image_id = "20250705171601"
    image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    model_path = "D:/Drone_Project/models/deeplabv3_plus_best.pth"
    old_data_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.shp"
    
    try:
        print(f"\n📁 测试数据:")
        print(f"  影像ID: {image_id}")
        print(f"  影像路径: {image_path}")
        print(f"  模型路径: {model_path}")
        print(f"  历史数据: {old_data_path}")
        
        # 3. 测试合并分析
        print("\n3️⃣ 启动合并分析任务...")
        params = {
            'image_id': image_id,
            'image': image_path,
            'model': model_path,
            'old_data': old_data_path,
            'area_threshold': 400.0
        }
        
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务执行
            success = monitor_task(base_url, task_id, "合并分析", max_wait_time=300)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_task(base_url, task_id, test_name, max_wait_time=180):
    """监控任务执行"""
    print(f"\n📊 监控 {test_name} 任务执行...")
    
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
            
            if status_response.status_code == 200:
                status_data = status_response.json()['data']
                task_status = status_data['task_status']
                message = status_data.get('message', '')
                progress = status_data.get('progress', 0)
                
                print(f"📈 {test_name}: {task_status} ({progress}%) - {message}")
                
                if task_status == '完成':
                    print(f"\n✅ {test_name} 任务完成！")
                    
                    # 显示结果信息
                    if 'result' in status_data:
                        result_info = status_data['result']
                        print(f"📊 结果统计:")
                        if 'statistics' in result_info:
                            stats = result_info['statistics']
                            print(f"  流出图斑: {stats.get('outflow_count', 0)}")
                            print(f"  流入图斑: {stats.get('inflow_count', 0)}")
                            print(f"  总图斑数: {stats.get('total_count', 0)}")
                            print(f"  流出面积: {stats.get('outflow_area', 0)} 平方米")
                            print(f"  流入面积: {stats.get('inflow_area', 0)} 平方米")
                        
                        if 'output_files' in result_info:
                            output_files = result_info['output_files']
                            print(f"📂 输出文件:")
                            for key, path in output_files.items():
                                print(f"  {key}: {path}")
                    
                    return True
                    
                elif task_status == '失败':
                    print(f"\n❌ {test_name} 任务失败: {message}")
                    return False
                
                time.sleep(5)
            else:
                print(f"❌ 状态查询失败: {status_response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 状态查询异常: {e}")
            time.sleep(5)
            continue
    
    print(f"\n⏰ {test_name} 任务超时")
    return False

if __name__ == "__main__":
    print("🚀 开始测试修改后的空间数据变化分析功能")
    
    # 测试1: 使用裁剪范围的空间变化分析
    success1 = test_spatial_changes_with_clip()
    
    if success1:
        print("\n⏳ 等待5秒后进行下一个测试...")
        time.sleep(5)
        
        # 测试2: 不使用裁剪范围的空间变化分析
        success2 = test_spatial_changes_without_clip()
        
        if success2:
            print("\n⏳ 等待5秒后进行下一个测试...")
            time.sleep(5)
            
            # 测试3: 合并分析功能
            success3 = test_combined_analysis()
            
            if success1 and success2 and success3:
                print("\n🎉 所有测试通过！")
            else:
                print("\n❌ 部分测试失败")
        else:
            print("\n❌ 第二个测试失败，跳过后续测试")
    else:
        print("\n❌ 第一个测试失败，跳过后续测试")
    
    print("\n📖 功能说明:")
    print("1. 空间数据变化分析现在支持clip_area_path参数")
    print("2. 如果提供clip_area_path，将在指定范围内进行对比")
    print("3. 如果不提供clip_area_path，使用新数据覆盖区域进行对比")
    print("4. 合并分析会自动提取影像范围，并用作空间变化分析的裁剪区域")
    print("5. 影像范围文件命名格式: {image_id}_area_{timestamp}.shp")
