#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试TaskInfo.json重复记录最终修复
验证合并分析流程中不会创建重复的task_id记录
"""

import requests
import json
import os
import time

def test_combined_analysis_no_duplicates():
    """测试合并分析不会创建重复记录"""
    print("🧪 测试合并分析不会创建重复记录...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试数据
    image_id = "20250705171599"
    params = {
        'id': image_id,
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp',
        'area_threshold': 200.0,
        'model_type': 'deeplabv3_plus'
    }
    
    try:
        print(f"\n🚀 启动合并分析任务...")
        print(f"   影像ID: {image_id}")
        print(f"   分析类别: constructionLand")
        
        # 启动任务
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            task_id = result['data']['task_id']
            print(f"✅ 任务启动成功: {task_id}")
            
            # 监控TaskInfo.json，确保不会创建重复记录
            success = monitor_taskinfo_for_duplicates(image_id, task_id)
            return success
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def monitor_taskinfo_for_duplicates(image_id, task_id, max_wait=600):
    """监控TaskInfo.json，检查是否会创建重复记录"""
    print(f"\n📊 监控TaskInfo.json变化，检查重复记录...")
    
    taskinfo_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
    
    start_time = time.time()
    check_count = 0
    duplicate_detected = False
    
    while time.time() - start_time < max_wait:
        try:
            check_count += 1
            
            if os.path.exists(taskinfo_path):
                with open(taskinfo_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list):
                    # 检查是否有重复的task_id
                    task_ids = [task.get('task_id') for task in data]
                    unique_task_ids = set(task_ids)
                    
                    if len(task_ids) != len(unique_task_ids):
                        print(f"❌ 第{check_count}次检查：检测到重复的task_id!")
                        duplicate_detected = True
                        
                        # 分析重复情况
                        from collections import Counter
                        task_id_counts = Counter(task_ids)
                        duplicates = {tid: count for tid, count in task_id_counts.items() if count > 1}
                        
                        print(f"   重复的task_id: {duplicates}")
                        
                        # 显示重复记录的详细信息
                        for tid, count in duplicates.items():
                            print(f"   task_id {tid[:8]}... 出现 {count} 次:")
                            for i, task in enumerate(data):
                                if task.get('task_id') == tid:
                                    timestamp = task.get('timestamp', 'N/A')
                                    datetime_str = task.get('datetime', 'N/A')
                                    status = task.get('status', 'N/A')
                                    has_model_name = 'model_name' in task.get('parameters', {})
                                    has_geoserver = 'geoserver_publish' in task
                                    
                                    print(f"     记录{i+1}: {datetime_str}, status={status}, model_name={has_model_name}, geoserver={has_geoserver}")
                        
                        return False
                    
                    # 检查当前任务的状态
                    target_task = None
                    for task in data:
                        if task.get('task_id') == task_id:
                            target_task = task
                            break
                    
                    if target_task:
                        status = target_task.get('status', 'N/A')
                        progress = target_task.get('progress', 'N/A')
                        message = target_task.get('message', 'N/A')
                        has_model_name = 'model_name' in target_task.get('parameters', {})
                        has_geoserver = 'geoserver_publish' in target_task
                        
                        if check_count % 6 == 0:  # 每分钟显示一次状态
                            print(f"📊 第{check_count}次检查: {status} ({progress}%) - model_name={has_model_name}, geoserver={has_geoserver}")
                        
                        # 检查任务是否完成
                        if status in ['完成', '失败']:
                            print(f"🏁 任务结束: {status}")
                            
                            # 最终检查是否有重复记录
                            final_task_ids = [task.get('task_id') for task in data]
                            final_unique_task_ids = set(final_task_ids)
                            
                            if len(final_task_ids) == len(final_unique_task_ids):
                                print(f"✅ 任务完成，无重复记录")
                                print(f"📊 最终记录数量: {len(data)}")
                                print(f"📊 唯一task_id数量: {len(final_unique_task_ids)}")
                                return True
                            else:
                                print(f"❌ 任务完成，但仍有重复记录")
                                return False
                    else:
                        print(f"⚠️ 第{check_count}次检查：未找到目标任务记录")
            
            time.sleep(10)  # 每10秒检查一次
            
        except Exception as e:
            print(f"❌ 第{check_count}次检查异常: {e}")
            time.sleep(10)
            continue
    
    print(f"⏰ 监控超时")
    return not duplicate_detected

def analyze_duplicate_creation_points():
    """分析重复记录的创建点"""
    print(f"\n🔍 分析重复记录的创建点...")
    
    print(f"📋 修复前的问题:")
    print(f"1. combined_analysis_executor.py 第143行:")
    print(f"   self._create_taskinfo_immediately(task_id, task_info, task_logger)")
    print(f"   → 创建第一个记录（基础信息）")
    
    print(f"\n2. analysis_executor.py 第971行:")
    print(f"   self._check_and_create_combined_taskinfo(config, task_logger)")
    print(f"   → 创建第二个记录（包含model_name和geoserver_publish）")
    
    print(f"\n📋 修复后的逻辑:")
    print(f"1. 第一个创建点：创建基础记录")
    print(f"2. 第二个创建点：检查是否已存在，如果存在则更新，否则创建")
    print(f"3. 更新时会合并信息，保留最完整的数据")

def verify_fix_logic():
    """验证修复逻辑"""
    print(f"\n🧪 验证修复逻辑...")
    
    # 模拟现有任务列表
    existing_tasks = [
        {
            "task_id": "test-task-123",
            "parameters": {
                "model_type": "deeplabv3_plus",
                "area_threshold": 200.0
            },
            "status": "进行中"
        }
    ]
    
    # 模拟新的任务信息（包含额外字段）
    new_task_info = {
        "task_id": "test-task-123",  # 相同的task_id
        "parameters": {
            "model_type": "deeplabv3_plus",
            "model_name": "deeplabv3_plus_best_20250811-162215.pth",  # 新字段
            "area_threshold": 200.0
        },
        "geoserver_publish": {  # 新字段
            "overall_success": False
        },
        "status": "进行中"
    }
    
    print(f"📊 模拟场景:")
    print(f"  现有记录数量: {len(existing_tasks)}")
    print(f"  新记录task_id: {new_task_info['task_id']}")
    print(f"  是否有model_name: {'model_name' in new_task_info['parameters']}")
    print(f"  是否有geoserver_publish: {'geoserver_publish' in new_task_info}")
    
    # 应用修复逻辑
    task_id = new_task_info['task_id']
    task_found = False
    
    for i, task in enumerate(existing_tasks):
        if task.get('task_id') == task_id:
            # 更新现有记录，合并信息
            existing_task = existing_tasks[i]
            
            # 保留原有的基础信息，但更新更详细的信息
            if 'model_name' not in existing_task.get('parameters', {}):
                existing_task['parameters']['model_name'] = new_task_info['parameters']['model_name']
            
            if 'geoserver_publish' not in existing_task:
                existing_task['geoserver_publish'] = new_task_info['geoserver_publish']
            
            existing_tasks[i] = existing_task
            task_found = True
            break

    if not task_found:
        existing_tasks.append(new_task_info)
    
    print(f"\n📊 修复后结果:")
    print(f"  记录数量: {len(existing_tasks)} (应该仍为1)")
    print(f"  是否有model_name: {'model_name' in existing_tasks[0]['parameters']}")
    print(f"  是否有geoserver_publish: {'geoserver_publish' in existing_tasks[0]}")
    
    return len(existing_tasks) == 1

def main():
    """主函数"""
    print("🚀 测试TaskInfo.json重复记录最终修复")
    
    print(f"\n📝 问题根因:")
    print(f"合并分析流程中有两个地方创建TaskInfo记录:")
    print(f"1. combined_analysis_executor._create_taskinfo_immediately")
    print(f"2. analysis_executor._create_combined_taskinfo_template")
    print(f"第二个地方没有检查重复，导致创建了两个相同task_id的记录")
    
    print(f"\n📝 修复方案:")
    print(f"在 analysis_executor._create_combined_taskinfo_template 中:")
    print(f"1. 检查是否已存在相同task_id的记录")
    print(f"2. 如果存在，更新现有记录并合并信息")
    print(f"3. 如果不存在，才创建新记录")
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 验证修复逻辑
    if verify_fix_logic():
        success_count += 1
        print(f"✅ 测试1通过: 修复逻辑验证")
    else:
        print(f"❌ 测试1失败: 修复逻辑验证")
    
    # 分析创建点
    analyze_duplicate_creation_points()
    
    # 测试2: 实际接口测试
    if test_combined_analysis_no_duplicates():
        success_count += 1
        print(f"✅ 测试2通过: 实际接口无重复")
    else:
        print(f"❌ 测试2失败: 实际接口仍有重复")
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print(f"🎉 TaskInfo.json重复记录问题已彻底修复！")
        
        print(f"\n✅ 修复确认:")
        print(f"1. ✅ 识别了两个TaskInfo创建点")
        print(f"2. ✅ 在第二个创建点添加了重复检查")
        print(f"3. ✅ 实现了信息合并而不是重复创建")
        print(f"4. ✅ 保持了数据的完整性和一致性")
        
    else:
        print(f"❌ 部分测试失败，请检查修复是否完整")

if __name__ == "__main__":
    main()
