#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
任务删除管理器
根据任务状态删除任务信息、GeoServer图层和相关文件
"""

import os
import json
import glob
import requests
import logging
from datetime import datetime
from typing import Dict, Any, List

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TaskDeletionManager:
    """任务删除管理器"""
    
    def __init__(self):
        self.geoserver_base_url = "http://127.0.0.1:8080/geoserver"
        self.geoserver_auth = ("admin", "geoserver")
        
    def delete_task(self, image_id: str, task_id: str) -> Dict[str, Any]:
        """
        删除指定的分析任务
        
        Args:
            image_id: 影像ID
            task_id: 任务ID
            
        Returns:
            Dict: 删除结果
        """
        try:
            logger.info(f"🗑️ 开始删除任务: image_id={image_id}, task_id={task_id}")
            
            # 第一步：获取TaskInfo.json
            taskinfo_result = self._get_taskinfo(image_id)
            if not taskinfo_result['success']:
                return taskinfo_result
            
            taskinfo_data = taskinfo_result['data']
            
            # 第二步：查找指定的任务
            task_info = None
            task_index = -1
            
            for i, task in enumerate(taskinfo_data):
                if task.get('task_id') == task_id:
                    task_info = task
                    task_index = i
                    break
            
            if not task_info:
                return {
                    'success': False,
                    'message': f'未找到指定的任务: {task_id}'
                }
            
            task_status = task_info.get('status', 'unknown')
            logger.info(f"📋 找到任务，状态: {task_status}")
            
            # 第三步：根据状态执行相应的删除操作
            deletion_details = {
                'task_id': task_id,
                'image_id': image_id,
                'status': task_status,
                'actions_performed': []
            }
            
            if task_status == '已取消':
                # 直接删除TaskInfo记录
                result = self._delete_taskinfo_record(image_id, task_index, taskinfo_data)
                deletion_details['actions_performed'].append('删除TaskInfo记录')
                
            elif task_status == '等待中':
                # 先取消任务，再删除记录
                cancel_result = self._cancel_waiting_task(task_id)
                if cancel_result['success']:
                    deletion_details['actions_performed'].append('取消等待中的任务')
                
                result = self._delete_taskinfo_record(image_id, task_index, taskinfo_data)
                deletion_details['actions_performed'].append('删除TaskInfo记录')
                
            elif task_status in ['完成', '失败']:
                # 删除GeoServer图层、文件和记录
                result = self._delete_completed_or_failed_task(task_info, image_id, task_index, taskinfo_data, deletion_details)
                
            elif task_status == '进行中':
                # 检查是否真正在进行中
                result = self._handle_running_task(task_id, task_info, image_id, task_index, taskinfo_data, deletion_details)
                
            else:
                return {
                    'success': False,
                    'message': f'未知的任务状态: {task_status}'
                }
            
            if result['success']:
                logger.info(f"✅ 任务删除成功: {task_id}")
                return {
                    'success': True,
                    'message': '任务删除成功',
                    'details': deletion_details
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"❌ 删除任务异常: {str(e)}")
            return {
                'success': False,
                'message': f'删除任务异常: {str(e)}'
            }
    
    def _get_taskinfo(self, image_id: str) -> Dict[str, Any]:
        """获取TaskInfo.json内容"""
        try:
            # 获取配置文件中的window_data_path
            config_result = self._get_odm_config()
            if config_result['success']:
                window_data_path = config_result['config'].get('PATHS', {}).get('window_data_path', 'D:/Drone_Project/nginxData')
            else:
                window_data_path = 'D:/Drone_Project/nginxData'
                logger.warning(f"使用默认window_data_path: {window_data_path}")
            
            # 构建TaskInfo.json路径
            taskinfo_path = os.path.join(window_data_path, 'ODM', 'AI', image_id, 'TaskInfo.json')
            
            if not os.path.exists(taskinfo_path):
                return {
                    'success': False,
                    'message': f'TaskInfo.json文件不存在: {taskinfo_path}'
                }
            
            # 读取文件
            with open(taskinfo_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                data = [data] if isinstance(data, dict) else []
            
            return {
                'success': True,
                'data': data,
                'path': taskinfo_path
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'读取TaskInfo.json失败: {str(e)}'
            }
    
    def _get_odm_config(self) -> Dict[str, Any]:
        """获取ODM配置文件"""
        try:
            config_url = "http://127.0.0.1:81/ODM/Task.cfg"
            response = requests.get(config_url, timeout=10)
            response.raise_for_status()
            
            # 解析配置文件
            config = {}
            current_section = None
            
            for line in response.text.splitlines():
                line = line.strip()
                if not line or line.startswith("#"):
                    continue
                
                if line.startswith("[") and line.endswith("]"):
                    current_section = line[1:-1]
                    config[current_section] = {}
                    continue
                
                if "=" in line and current_section:
                    key, value = line.split("=", 1)
                    config[current_section][key.strip()] = value.strip()
            
            return {
                'success': True,
                'config': config
            }
            
        except Exception as e:
            return {
                'success': False,
                'config': {},
                'message': f'读取ODM配置失败: {str(e)}'
            }
    
    def _cancel_waiting_task(self, task_id: str) -> Dict[str, Any]:
        """取消等待中的任务"""
        try:
            from .task_queue_manager import task_queue_manager
            return task_queue_manager.cancel_task(task_id)
        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}")
            return {
                'success': False,
                'message': f'取消任务失败: {str(e)}'
            }
    
    def _delete_taskinfo_record(self, image_id: str, task_index: int, taskinfo_data: List[Dict]) -> Dict[str, Any]:
        """删除TaskInfo.json中的记录"""
        try:
            # 删除指定索引的记录
            if 0 <= task_index < len(taskinfo_data):
                deleted_task = taskinfo_data.pop(task_index)
                logger.info(f"📝 从TaskInfo中删除记录: {deleted_task.get('task_id')}")
            
            # 保存更新后的TaskInfo.json
            config_result = self._get_odm_config()
            window_data_path = config_result['config'].get('PATHS', {}).get('window_data_path', 'D:/Drone_Project/nginxData') if config_result['success'] else 'D:/Drone_Project/nginxData'
            
            taskinfo_path = os.path.join(window_data_path, 'ODM', 'AI', image_id, 'TaskInfo.json')
            
            with open(taskinfo_path, 'w', encoding='utf-8') as f:
                json.dump(taskinfo_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 TaskInfo.json已更新，剩余 {len(taskinfo_data)} 个记录")
            
            return {
                'success': True,
                'message': 'TaskInfo记录删除成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'删除TaskInfo记录失败: {str(e)}'
            }
    
    def _delete_completed_or_failed_task(self, task_info: Dict, image_id: str, task_index: int, taskinfo_data: List[Dict], deletion_details: Dict) -> Dict[str, Any]:
        """删除完成或失败的任务"""
        try:
            # 删除GeoServer图层
            geoserver_result = self._delete_geoserver_layers(task_info, deletion_details)
            
            # 删除本地文件
            files_result = self._delete_local_files(task_info, deletion_details)
            
            # 删除TaskInfo记录
            taskinfo_result = self._delete_taskinfo_record(image_id, task_index, taskinfo_data)
            deletion_details['actions_performed'].append('删除TaskInfo记录')
            
            # 汇总结果
            all_success = geoserver_result['success'] and files_result['success'] and taskinfo_result['success']
            
            return {
                'success': all_success,
                'message': '任务删除完成' if all_success else '任务删除部分失败',
                'geoserver_result': geoserver_result,
                'files_result': files_result,
                'taskinfo_result': taskinfo_result
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'删除完成/失败任务异常: {str(e)}'
            }
    
    def _handle_running_task(self, task_id: str, task_info: Dict, image_id: str, task_index: int, taskinfo_data: List[Dict], deletion_details: Dict) -> Dict[str, Any]:
        """处理进行中的任务"""
        try:
            # 检查是否真正在进行中
            queue_status = self._get_current_running_task()
            
            if queue_status['success']:
                current_task_id = queue_status['data'].get('current_task')
                
                if current_task_id == task_id:
                    # 任务真正在进行中，不能删除
                    return {
                        'success': False,
                        'message': '任务正在执行中，无法删除'
                    }
                else:
                    # 任务实际已中断，按失败处理
                    logger.info(f"任务 {task_id} 状态为进行中但实际已中断，按失败处理")
                    deletion_details['actions_performed'].append('检测到中断的任务，按失败处理')
                    return self._delete_completed_or_failed_task(task_info, image_id, task_index, taskinfo_data, deletion_details)
            else:
                # 无法获取队列状态，按失败处理
                logger.warning(f"无法获取队列状态，按失败处理任务: {task_id}")
                deletion_details['actions_performed'].append('无法获取队列状态，按失败处理')
                return self._delete_completed_or_failed_task(task_info, image_id, task_index, taskinfo_data, deletion_details)
                
        except Exception as e:
            return {
                'success': False,
                'message': f'处理进行中任务异常: {str(e)}'
            }
    
    def _get_current_running_task(self) -> Dict[str, Any]:
        """获取当前正在运行的任务"""
        try:
            from .task_queue_manager import task_queue_manager
            return task_queue_manager.get_queue_status()
        except Exception as e:
            return {
                'success': False,
                'message': f'获取队列状态失败: {str(e)}'
            }

    def _delete_geoserver_layers(self, task_info: Dict, deletion_details: Dict) -> Dict[str, Any]:
        """删除GeoServer中发布的图层"""
        try:
            output_files = task_info.get('output_files', {})
            geoserver_publish = task_info.get('geoserver_publish', {})

            deleted_layers = []
            failed_layers = []

            # 处理AI结果图层
            ai_output_path = output_files.get('ai_output_path')
            if ai_output_path:
                layer_result = self._delete_single_geoserver_layer(ai_output_path, geoserver_publish.get('ai_result', {}))
                if layer_result['success']:
                    deleted_layers.append(layer_result['layer_name'])
                else:
                    failed_layers.append(layer_result['layer_name'])

            # 处理最终结果图层
            final_output_path = output_files.get('final_output_path')
            if final_output_path:
                layer_result = self._delete_single_geoserver_layer(final_output_path, geoserver_publish.get('final_result', {}))
                if layer_result['success']:
                    deleted_layers.append(layer_result['layer_name'])
                else:
                    failed_layers.append(layer_result['layer_name'])

            deletion_details['actions_performed'].append(f'删除GeoServer图层: 成功{len(deleted_layers)}个, 失败{len(failed_layers)}个')
            deletion_details['deleted_geoserver_layers'] = deleted_layers
            deletion_details['failed_geoserver_layers'] = failed_layers

            return {
                'success': len(failed_layers) == 0,
                'message': f'GeoServer图层删除完成: 成功{len(deleted_layers)}个, 失败{len(failed_layers)}个',
                'deleted_layers': deleted_layers,
                'failed_layers': failed_layers
            }

        except Exception as e:
            logger.error(f"删除GeoServer图层异常: {str(e)}")
            return {
                'success': False,
                'message': f'删除GeoServer图层异常: {str(e)}'
            }

    def _delete_single_geoserver_layer(self, file_path: str, layer_info: Dict) -> Dict[str, Any]:
        """删除单个GeoServer图层"""
        try:
            # 从文件路径提取workspace和layer_name
            path_parts = file_path.replace('\\', '/').split('/')

            # 查找workspace（文件名的上一层文件夹）
            workspace = None
            layer_name = None

            for i, part in enumerate(path_parts):
                if part.endswith('.shp'):
                    layer_name = os.path.splitext(part)[0]  # 去掉.shp扩展名
                    if i > 0:
                        workspace = path_parts[i-1]
                    break

            if not workspace or not layer_name:
                logger.warning(f"无法从路径提取workspace和layer_name: {file_path}")
                return {
                    'success': False,
                    'layer_name': layer_name or 'unknown',
                    'message': '无法提取图层信息'
                }

            store_name = f"{layer_name}_store"

            logger.info(f"🗑️ 删除GeoServer图层: workspace={workspace}, layer={layer_name}, store={store_name}")

            # 检查图层是否存在
            if not layer_info.get('success', False):
                logger.info(f"图层未发布或发布失败，跳过删除: {layer_name}")
                return {
                    'success': True,
                    'layer_name': layer_name,
                    'message': '图层未发布，跳过删除'
                }

            # 删除图层
            layer_url = f"{self.geoserver_base_url}/rest/workspaces/{workspace}/layers/{layer_name}"
            layer_response = requests.delete(layer_url, auth=self.geoserver_auth, timeout=30)

            # 删除数据存储
            store_url = f"{self.geoserver_base_url}/rest/workspaces/{workspace}/datastores/{store_name}"
            store_response = requests.delete(f"{store_url}?recurse=true", auth=self.geoserver_auth, timeout=30)

            # 检查删除结果
            layer_success = layer_response.status_code in [200, 404]  # 404表示已经不存在
            store_success = store_response.status_code in [200, 404]

            if layer_success and store_success:
                logger.info(f"✅ GeoServer图层删除成功: {layer_name}")
                return {
                    'success': True,
                    'layer_name': layer_name,
                    'message': '图层删除成功'
                }
            else:
                logger.warning(f"⚠️ GeoServer图层删除部分失败: {layer_name} (layer: {layer_response.status_code}, store: {store_response.status_code})")
                return {
                    'success': False,
                    'layer_name': layer_name,
                    'message': f'图层删除部分失败 (layer: {layer_response.status_code}, store: {store_response.status_code})'
                }

        except Exception as e:
            logger.error(f"删除单个GeoServer图层异常: {str(e)}")
            return {
                'success': False,
                'layer_name': layer_name if 'layer_name' in locals() else 'unknown',
                'message': f'删除图层异常: {str(e)}'
            }

    def _delete_local_files(self, task_info: Dict, deletion_details: Dict) -> Dict[str, Any]:
        """删除本地SHP文件"""
        try:
            output_files = task_info.get('output_files', {})

            deleted_files = []
            failed_files = []

            # 删除AI输出文件
            ai_output_path = output_files.get('ai_output_path')
            if ai_output_path:
                file_result = self._delete_shapefile_complete(ai_output_path)
                deleted_files.extend(file_result['deleted_files'])
                failed_files.extend(file_result['failed_files'])

            # 删除最终输出文件
            final_output_path = output_files.get('final_output_path')
            if final_output_path:
                file_result = self._delete_shapefile_complete(final_output_path)
                deleted_files.extend(file_result['deleted_files'])
                failed_files.extend(file_result['failed_files'])

            deletion_details['actions_performed'].append(f'删除本地文件: 成功{len(deleted_files)}个, 失败{len(failed_files)}个')
            deletion_details['deleted_local_files'] = deleted_files
            deletion_details['failed_local_files'] = failed_files

            return {
                'success': len(failed_files) == 0,
                'message': f'本地文件删除完成: 成功{len(deleted_files)}个, 失败{len(failed_files)}个',
                'deleted_files': deleted_files,
                'failed_files': failed_files
            }

        except Exception as e:
            logger.error(f"删除本地文件异常: {str(e)}")
            return {
                'success': False,
                'message': f'删除本地文件异常: {str(e)}'
            }

    def _delete_shapefile_complete(self, shp_path: str) -> Dict[str, Any]:
        """删除完整的SHP文件组"""
        try:
            if not shp_path or not shp_path.endswith('.shp'):
                return {
                    'deleted_files': [],
                    'failed_files': []
                }

            # SHP文件相关扩展名
            shp_extensions = ['.shp', '.shx', '.dbf', '.prj', '.cpg', '.sbn', '.sbx', '.shp.xml']

            base_path = shp_path[:-4]  # 去掉.shp扩展名
            deleted_files = []
            failed_files = []

            for ext in shp_extensions:
                file_path = base_path + ext

                try:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        deleted_files.append(os.path.basename(file_path))
                        logger.debug(f"删除文件: {file_path}")
                    else:
                        logger.debug(f"文件不存在，跳过: {file_path}")

                except Exception as e:
                    failed_files.append(os.path.basename(file_path))
                    logger.error(f"删除文件失败: {file_path}, 错误: {str(e)}")

            logger.info(f"📁 SHP文件组删除完成: {os.path.basename(base_path)} (成功{len(deleted_files)}个, 失败{len(failed_files)}个)")

            return {
                'deleted_files': deleted_files,
                'failed_files': failed_files
            }

        except Exception as e:
            logger.error(f"删除SHP文件组异常: {str(e)}")
            return {
                'deleted_files': [],
                'failed_files': [os.path.basename(shp_path) if shp_path else 'unknown']
            }
