{"f3a7abf5-36ef-4774-8f7b-f9fed688a758": {"task_id": "f3a7abf5-36ef-4774-8f7b-f9fed688a758", "status": "completed", "message": "空间数据变化分析完成", "start_time": "2025-08-13T11:03:16.285978", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\f3a7abf5-36ef-4774-8f7b-f9fed688a758.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T11:06:48.541128", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T11:06:48.275433", "statistics": {"outflow_count": 0, "inflow_count": 0, "outflow_area": 0, "inflow_area": 0, "area_threshold": 200.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "f014dd67-0907-45d2-81f5-7940530b2898": {"task_id": "f014dd67-0907-45d2-81f5-7940530b2898", "status": "completed", "message": "空间数据变化分析完成", "start_time": "2025-08-13T11:08:43.604004", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\f014dd67-0907-45d2-81f5-7940530b2898.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 20.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T11:12:27.517512", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T11:12:27.242418", "statistics": {"outflow_count": 0, "inflow_count": 0, "outflow_area": 0, "inflow_area": 0, "area_threshold": 20.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "a4515178-8cc1-46a1-b8a8-3321e1b00f21": {"task_id": "a4515178-8cc1-46a1-b8a8-3321e1b00f21", "status": "completed", "message": "空间数据变化分析完成", "start_time": "2025-08-13T11:14:33.147598", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\a4515178-8cc1-46a1-b8a8-3321e1b00f21.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 2.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T11:18:13.393464", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T11:18:13.104415", "statistics": {"outflow_count": 0, "inflow_count": 0, "outflow_area": 0, "inflow_area": 0, "area_threshold": 2.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "fcf0638a-6257-4448-bf32-c395e39e8611": {"task_id": "fcf0638a-6257-4448-bf32-c395e39e8611", "status": "completed", "message": "空间数据变化分析完成", "start_time": "2025-08-13T11:28:08.806777", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\fcf0638a-6257-4448-bf32-c395e39e8611.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 2.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T11:31:42.244289", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T11:31:41.978519", "statistics": {"outflow_count": 0, "inflow_count": 0, "outflow_area": 0, "inflow_area": 0, "area_threshold": 2.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "c7448c3a-d87f-4079-b82d-b616735dba15": {"task_id": "c7448c3a-d87f-4079-b82d-b616735dba15", "status": "completed", "message": "空间数据变化分析完成", "start_time": "2025-08-13T11:32:33.688947", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\c7448c3a-d87f-4079-b82d-b616735dba15.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 2.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T11:36:15.011825", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T11:36:14.722919", "statistics": {"outflow_count": 0, "inflow_count": 0, "outflow_area": 0, "inflow_area": 0, "area_threshold": 2.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "62921e5f-38f1-44ba-8d0c-a1e888d9855c": {"task_id": "62921e5f-38f1-44ba-8d0c-a1e888d9855c", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-13T12:01:59.356840", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\62921e5f-38f1-44ba-8d0c-a1e888d9855c.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 2.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T12:05:36.177873", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T12:05:35.911231", "statistics": {"outflow_count": 110, "inflow_count": 132, "outflow_area": 153654.96, "inflow_area": 43443.11, "area_threshold": 2.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "759a231d-4219-495c-9ba2-1e6e0963c6c3": {"task_id": "759a231d-4219-495c-9ba2-1e6e0963c6c3", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-13T15:07:36.202956", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\759a231d-4219-495c-9ba2-1e6e0963c6c3.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T15:11:40.368395", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T15:11:40.083693", "statistics": {"outflow_count": 72, "inflow_count": 40, "outflow_area": 150069.39, "inflow_area": 38748.65, "area_threshold": 200.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "c4d27fd0-7dcf-40cc-a0d0-55335102f494": {"task_id": "c4d27fd0-7dcf-40cc-a0d0-55335102f494", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-13T15:22:10.543893", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\c4d27fd0-7dcf-40cc-a0d0-55335102f494.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T15:26:44.532315", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T15:26:44.257321", "statistics": {"outflow_count": 71, "inflow_count": 40, "outflow_area": 144203.95, "inflow_area": 36278.95, "area_threshold": 200.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "a74cc310-a240-4412-a7c5-9963b56e601d": {"task_id": "a74cc310-a240-4412-a7c5-9963b56e601d", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-13T15:37:23.129917", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\a74cc310-a240-4412-a7c5-9963b56e601d.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "outflow_filename": "outflow_shapefile", "inflow_filename": "inflow_shapefile"}, "end_time": "2025-08-13T15:41:49.910162", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-13T15:41:49.597911", "statistics": {"outflow_count": 112, "inflow_count": 55, "outflow_area": 131038.81, "inflow_area": 27801.15, "area_threshold": 200.0}, "output_files": {"outflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/outflow_shapefile.shp", "inflow_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/inflow_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "e922f38e-592e-4dad-a335-e04fc30aefd8": {"task_id": "e922f38e-592e-4dad-a335-e04fc30aefd8", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-15T11:23:25.359754", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\e922f38e-592e-4dad-a335-e04fc30aefd8.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "shp_filename": "changes_shapefile"}, "end_time": "2025-08-15T11:27:29.552938", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-15T11:27:29.286691", "statistics": {"outflow_count": 112, "inflow_count": 55, "total_count": 167, "outflow_area": 131038.81, "inflow_area": 27801.15, "area_threshold": 200.0}, "output_files": {"combined_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/changes_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "f28302b8-9eff-4a33-a661-838137ade38d": {"task_id": "f28302b8-9eff-4a33-a661-838137ade38d", "status": "运行失败", "message": "分析执行失败: 'NoneType' object has no attribute 'CreateLayer'", "start_time": "2025-08-15T15:02:20.392979", "progress": 85, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\f28302b8-9eff-4a33-a661-838137ade38d.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "shp_filename": "changes_shapefile"}, "end_time": "2025-08-15T15:06:29.583016", "error": "'NoneType' object has no attribute 'CreateLayer'"}, "27f302c6-afea-4de5-b694-92aa70ad7138": {"task_id": "27f302c6-afea-4de5-b694-92aa70ad7138", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-15T15:15:47.648445", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\27f302c6-afea-4de5-b694-92aa70ad7138.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "shp_filename": "test_changes"}, "end_time": "2025-08-15T15:22:41.751244", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-15T15:22:41.496216", "statistics": {"outflow_count": 112, "inflow_count": 55, "total_count": 167, "outflow_area": 131038.81, "inflow_area": 27801.15, "area_threshold": 200.0}, "output_files": {"combined_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/test_changes.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "812f17d2-fca6-4485-a40f-448ea292f86a": {"task_id": "812f17d2-fca6-4485-a40f-448ea292f86a", "status": "运行成功", "message": "空间数据变化分析完成", "start_time": "2025-08-15T15:16:05.841379", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\812f17d2-fca6-4485-a40f-448ea292f86a.log", "parameters": {"old_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data_path": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp", "area_threshold": 200.0, "output_dir": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验", "shp_filename": "changes_shapefile"}, "end_time": "2025-08-15T15:22:56.200273", "result": {"success": true, "message": "空间数据变化分析完成", "analysis_time": "2025-08-15T15:22:55.790632", "statistics": {"outflow_count": 112, "inflow_count": 55, "total_count": 167, "outflow_area": 131038.81, "inflow_area": 27801.15, "area_threshold": 200.0}, "output_files": {"combined_shapefile": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/changes_shapefile.shp"}, "input_files": {"old_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp", "new_data": "D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp"}}}, "544c62dd-c379-4b42-91a7-c92d51c331ae": {"task_id": "544c62dd-c379-4b42-91a7-c92d51c331ae", "status": "失败", "message": "AI语义分割失败: '>' not supported between instances of 'NoneType' and 'int'", "start_time": "2025-08-18T17:21:55.753332", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\544c62dd-c379-4b42-91a7-c92d51c331ae.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2, "save_raw": false}}, "87eeecae-8299-449a-bac9-52ddf4103fb3": {"task_id": "87eeecae-8299-449a-bac9-52ddf4103fb3", "status": "失败", "message": "AI语义分割失败: cannot unpack non-iterable bool object", "start_time": "2025-08-18T17:28:47.156847", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\87eeecae-8299-449a-bac9-52ddf4103fb3.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2, "save_raw": false}}, "3f7b90b3-2291-4369-9615-c365b429d59f": {"task_id": "3f7b90b3-2291-4369-9615-c365b429d59f", "status": "失败", "message": "AI语义分割失败: 'torch.device' object has no attribute 'ignore_nodata'", "start_time": "2025-08-18T17:40:12.319614", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\3f7b90b3-2291-4369-9615-c365b429d59f.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2, "save_raw": false}}, "05f6823d-c0f7-4360-a1d2-0df332f924df": {"task_id": "05f6823d-c0f7-4360-a1d2-0df332f924df", "status": "正在运行", "message": "正在执行AI语义分割...", "start_time": "2025-08-18T17:58:48.850980", "progress": 30, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\05f6823d-c0f7-4360-a1d2-0df332f924df.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2, "save_raw": false}}, "f4411045-15e2-427d-8aa6-2c2f3e2da8b5": {"task_id": "f4411045-15e2-427d-8aa6-2c2f3e2da8b5", "status": "失败", "message": "AI语义分割失败: 'PerformanceMonitor' object has no attribute 'get_stats'", "start_time": "2025-08-19T09:22:35.818954", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\f4411045-15e2-427d-8aa6-2c2f3e2da8b5.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "e6faa30e-d492-4a4d-96e8-7ca9f9c20aa9": {"task_id": "e6faa30e-d492-4a4d-96e8-7ca9f9c20aa9", "status": "已完成", "message": "AI语义分割任务完成", "start_time": "2025-08-19T09:49:15.906976", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\e6faa30e-d492-4a4d-96e8-7ca9f9c20aa9.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "bb56e834-49dc-4d47-987b-74bf26de51b8": {"task_id": "bb56e834-49dc-4d47-987b-74bf26de51b8", "status": "正在运行", "message": "正在执行AI语义分割...", "start_time": "2025-08-19T15:05:53.397757", "progress": 30, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\bb56e834-49dc-4d47-987b-74bf26de51b8.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp", "model_type": "deeplabv3_plus", "target_classes": "1", "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "3de79e28-afbd-453a-bfa7-db836bf9dba9": {"task_id": "3de79e28-afbd-453a-bfa7-db836bf9dba9", "status": "失败", "message": "AI语义分割失败: 未知错误", "start_time": "2025-08-19T15:47:25.727232", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\3de79e28-afbd-453a-bfa7-db836bf9dba9.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out4.shp", "model_type": "deeplabv3_plus", "target_classes": null, "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "4645a19d-f6fc-4b48-bb98-6daf89d40b88": {"task_id": "4645a19d-f6fc-4b48-bb98-6daf89d40b88", "status": "失败", "message": "AI语义分割失败: 未知错误", "start_time": "2025-08-19T15:54:49.225559", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\4645a19d-f6fc-4b48-bb98-6daf89d40b88.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out4.shp", "model_type": "deeplabv3_plus", "target_classes": null, "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "109d6696-ddac-4ffe-b00f-721643d98fd6": {"task_id": "109d6696-ddac-4ffe-b00f-721643d98fd6", "status": "失败", "message": "AI语义分割失败: 未知错误", "start_time": "2025-08-19T15:59:13.002303", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\109d6696-ddac-4ffe-b00f-721643d98fd6.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/test_backbone_fix.shp", "model_type": "deeplabv3_plus", "target_classes": null, "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "9a0b4563-2621-4e38-a6c0-e9b31e01c36a": {"task_id": "9a0b4563-2621-4e38-a6c0-e9b31e01c36a", "status": "失败", "message": "AI语义分割失败: 未知错误", "start_time": "2025-08-19T16:03:27.726850", "progress": 0, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\9a0b4563-2621-4e38-a6c0-e9b31e01c36a.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/final_test.shp", "model_type": "deeplabv3_plus", "target_classes": null, "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "1dc2ac2e-cf54-40d9-bfad-0a1fb916c296": {"task_id": "1dc2ac2e-cf54-40d9-bfad-0a1fb916c296", "status": "已完成", "message": "AI语义分割任务完成", "start_time": "2025-08-19T16:18:19.281689", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\1dc2ac2e-cf54-40d9-bfad-0a1fb916c296.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/success_test.shp", "model_type": "deeplabv3_plus", "target_classes": null, "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}, "end_time": "2025-08-19T16:27:00.956816", "result": {"success": true, "message": "AI语义分割完成", "processing_time": 516, "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/success_test.shp"}}, "4a4a73de-c936-4292-9ca1-7210a85e6202": {"task_id": "4a4a73de-c936-4292-9ca1-7210a85e6202", "status": "已完成", "message": "AI语义分割任务完成", "start_time": "2025-08-19T16:20:46.664197", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\4a4a73de-c936-4292-9ca1-7210a85e6202.log", "parameters": {"image_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif", "model_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out4.shp", "model_type": "deeplabv3_plus", "target_classes": null, "window_size": 512, "batch_size": 16, "simplify": 0.0, "min_area": 0.0, "num_classes": 2}}, "4884f255-61bb-4139-8ac6-8ba7080c3efa": {"task_id": "4884f255-61bb-4139-8ac6-8ba7080c3efa", "status": "完成", "message": "影像有效范围提取完成", "start_time": "2025-08-20T09:07:49.210969", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\4884f255-61bb-4139-8ac6-8ba7080c3efa.log", "parameters": {"image_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp", "simplify_tolerance": 1.0, "min_area": 1000.0}, "result": {"success": true, "message": "影像有效范围提取完成", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp", "valid_area": 6.06953001802464e-05, "contour_count": 1, "coordinate_system": "WGS84 (EPSG:4326)"}, "end_time": "2025-08-20T09:09:30.329369"}, "70f11078-b4d8-4c74-ae58-4b2a182d7905": {"task_id": "70f11078-b4d8-4c74-ae58-4b2a182d7905", "status": "完成", "message": "影像有效范围提取完成", "start_time": "2025-08-20T09:18:34.343734", "progress": 100, "log_file": "D:\\Drone_Project\\geoserverAPIDJV2\\geoserver_api\\logs\\analysislog\\70f11078-b4d8-4c74-ae58-4b2a182d7905.log", "parameters": {"image_path": "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp", "simplify_tolerance": 1.0, "min_area": 10.0, "keep_original_crs": true}, "result": {"success": true, "message": "影像有效范围提取完成", "output_path": "D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp", "valid_area": 692292.7516226985, "contour_count": 1, "coordinate_system": "WGS 84 / UTM zone 48N (EPSG:32648)"}, "end_time": "2025-08-20T09:20:22.102668"}}