#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试队列功能
验证排队机制是否正常工作
"""

import requests
import json
import time
import threading

def test_queue_functionality():
    """测试队列功能"""
    print("🔍 调试队列功能...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 首先检查队列状态
    print(f"\n📊 检查初始队列状态...")
    check_queue_status(base_url)
    
    # 测试参数
    test_params_1 = {
        'id': '20250705171598',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0
    }
    
    test_params_2 = {
        'id': '20250705171597',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171597/20250705171597_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0
    }
    
    # 提交第一个任务
    print(f"\n📤 提交第一个排队任务...")
    task_id_1 = submit_queued_task(base_url, test_params_1, "任务1")
    
    # 等待2秒
    time.sleep(2)
    
    # 检查队列状态
    print(f"\n📊 提交第一个任务后的队列状态...")
    check_queue_status(base_url)
    
    # 提交第二个任务
    print(f"\n📤 提交第二个排队任务...")
    task_id_2 = submit_queued_task(base_url, test_params_2, "任务2")
    
    # 等待2秒
    time.sleep(2)
    
    # 检查队列状态
    print(f"\n📊 提交两个任务后的队列状态...")
    check_queue_status(base_url)
    
    # 监控任务执行
    if task_id_1 and task_id_2:
        monitor_queue_execution(base_url, [task_id_1, task_id_2])

def submit_queued_task(base_url, params, task_name):
    """提交排队任务"""
    try:
        print(f"   {task_name}: {params['id']}")
        
        # 确保使用排队接口
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis-queued/", params=params, timeout=30)
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result['status'] == 'success':
                task_id = result['data']['task_id']
                queue_position = result['data']['queue_position']
                
                print(f"   ✅ {task_name}提交成功")
                print(f"      任务ID: {task_id[:8]}...")
                print(f"      队列位置: {queue_position}")
                
                return task_id
            else:
                print(f"   ❌ {task_name}提交失败: {result['message']}")
                return None
        else:
            print(f"   ❌ {task_name}请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ {task_name}异常: {e}")
        return None

def check_queue_status(base_url):
    """检查队列状态"""
    try:
        response = requests.get(f"{base_url}/queue-status/", timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                queue_data = result['data']
                
                print(f"   📊 队列状态:")
                print(f"      队列长度: {queue_data['queue_size']}")
                print(f"      等待任务: {queue_data['waiting_tasks']}")
                print(f"      执行任务: {queue_data['executing_task'] or '无'}")
                print(f"      已完成: {queue_data['completed_tasks']}")
                print(f"      已取消: {queue_data['cancelled_tasks']}")
                print(f"      运行状态: {'运行中' if queue_data['is_running'] else '已停止'}")
                
                return queue_data
            else:
                print(f"   ❌ 队列状态查询失败: {result['message']}")
                return None
        else:
            print(f"   ❌ 队列状态请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ 队列状态查询异常: {e}")
        return None

def check_task_status(base_url, task_id, task_name):
    """检查任务状态"""
    try:
        response = requests.get(f"{base_url}/queued-task-status/", params={'task_id': task_id}, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            
            if result['status'] == 'success':
                task_data = result['data']
                
                print(f"   📋 {task_name}状态:")
                print(f"      状态: {task_data['status']}")
                print(f"      队列位置: {task_data.get('queue_position', 'N/A')}")
                
                return task_data
            else:
                print(f"   ❌ {task_name}状态查询失败: {result['message']}")
                return None
        else:
            print(f"   ❌ {task_name}状态请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"   ❌ {task_name}状态查询异常: {e}")
        return None

def monitor_queue_execution(base_url, task_ids, max_monitor_time=300):
    """监控队列执行"""
    print(f"\n🔍 监控队列执行情况...")
    
    start_time = time.time()
    last_status = {}
    
    while time.time() - start_time < max_monitor_time:
        try:
            print(f"\n⏰ 监控时间: {int(time.time() - start_time)}秒")
            
            # 检查队列状态
            queue_status = check_queue_status(base_url)
            
            # 检查每个任务状态
            all_completed = True
            for i, task_id in enumerate(task_ids):
                task_name = f"任务{i+1}"
                task_status = check_task_status(base_url, task_id, task_name)
                
                if task_status:
                    current_status = task_status['status']
                    
                    # 检查状态变化
                    if task_id not in last_status or last_status[task_id] != current_status:
                        print(f"   🔄 {task_name}状态变化: {current_status}")
                        last_status[task_id] = current_status
                    
                    # 检查是否完成
                    if current_status not in ['完成', '失败', '已取消']:
                        all_completed = False
                else:
                    # 任务可能已完成并从队列中移除
                    print(f"   ℹ️ {task_name}可能已完成")
            
            # 验证排队机制
            if queue_status:
                executing_task = queue_status['executing_task']
                queue_size = queue_status['queue_size']
                
                if executing_task and queue_size > 0:
                    print(f"   ✅ 排队机制正常: 执行{executing_task[:8]}..., 队列中还有{queue_size}个任务")
                elif executing_task and queue_size == 0:
                    print(f"   ✅ 排队机制正常: 执行{executing_task[:8]}..., 队列为空")
                elif not executing_task and queue_size > 0:
                    print(f"   ⚠️ 异常状态: 无执行任务但队列中有{queue_size}个任务")
                else:
                    print(f"   ℹ️ 队列空闲状态")
            
            # 如果所有任务都完成了，退出监控
            if all_completed and queue_status and queue_status['queue_size'] == 0:
                print(f"   🎉 所有任务执行完成！")
                break
            
            time.sleep(10)  # 每10秒检查一次
            
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断监控")
            break
        except Exception as e:
            print(f"   ❌ 监控异常: {e}")
            time.sleep(10)
    
    print(f"\n📊 监控结束")

def test_wrong_interface():
    """测试错误的接口调用（非排队接口）"""
    print(f"\n⚠️ 测试非排队接口（用于对比）...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    test_params = {
        'id': '20250705171596',
        'image': 'D:/Drone_Project/nginxData/ODM/Output/20250705171596/20250705171596_out.tif',
        'model': 'D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth',
        'old_data_path': 'D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp',
        'area_threshold': 400.0
    }
    
    try:
        # 调用原始接口（非排队）
        response = requests.get(f"{base_url}/combined-ai-spatial-analysis/", params=test_params, timeout=30)
        
        print(f"   原始接口响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   ⚠️ 原始接口直接执行，不通过队列")
            print(f"   任务ID: {result['data']['task_id']}")
        else:
            print(f"   原始接口调用失败: {response.text}")
            
    except Exception as e:
        print(f"   原始接口调用异常: {e}")

def main():
    """主函数"""
    print("🚀 开始调试队列功能")
    
    print(f"\n📝 调试目标:")
    print(f"1. 验证排队接口是否正确使用队列")
    print(f"2. 确认任务是否按顺序执行")
    print(f"3. 检查是否存在并发执行问题")
    print(f"4. 对比排队接口和原始接口的行为")
    
    # 测试排队功能
    test_queue_functionality()
    
    # 测试非排队接口（对比）
    test_wrong_interface()
    
    print(f"\n📖 调试结论:")
    print(f"1. 如果看到两个任务同时执行，说明排队机制有问题")
    print(f"2. 正确的行为应该是：第一个任务执行时，第二个任务等待")
    print(f"3. 确保使用 /combined-ai-spatial-analysis-queued/ 而不是 /combined-ai-spatial-analysis/")

if __name__ == "__main__":
    main()
