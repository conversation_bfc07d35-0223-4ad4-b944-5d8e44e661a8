2025-08-20 09:03:30,084 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250820_090330.log
2025-08-20 09:03:30,085 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:03:30,087 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:03:30,107 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:03:30,111 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:03:30,112 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:03:30,125 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:03:30,127 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:03:30,127 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:03:30,138 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:03:30,139 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:03:30,140 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:03:30,150 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:03:30,154 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:03:30,154 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:03:30,165 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:03:30,167 - root - INFO - 已连接到GeoServer：http://localhost:8083/geoserver
2025-08-20 09:03:30,169 - root - INFO - 正在检查GeoServer连接: http://localhost:8083/geoserver/rest/about/version.json
2025-08-20 09:03:30,181 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-20 09:03:30,197 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-20 09:03:30,202 - geo_publisher - INFO - 加载了 60 个任务状态
2025-08-20 09:03:33,886 - tif_executor - INFO - 加载了 77 个任务状态
2025-08-20 09:03:33,917 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-20 09:03:33,928 - analysis_executor - INFO - 加载了 26 个任务状态
