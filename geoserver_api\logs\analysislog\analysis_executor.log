2025-08-13 11:03:16,235 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 11:03:16,245 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 11:03:16,256 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 11:03:16,258 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-13 11:03:16,272 - analysis_executor - INFO - 开始空间数据变化分析任务: f3a7abf5-36ef-4774-8f7b-f9fed688a758
2025-08-13 11:03:16,288 - analysis_executor - INFO - 空间数据变化分析任务已启动: f3a7abf5-36ef-4774-8f7b-f9fed688a758
2025-08-13 11:03:16,301 - analysis_executor - INFO - 开始执行分析任务: f3a7abf5-36ef-4774-8f7b-f9fed688a758
2025-08-13 11:06:48,550 - analysis_executor - INFO - 空间数据变化分析任务完成: f3a7abf5-36ef-4774-8f7b-f9fed688a758
2025-08-13 11:08:43,573 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 11:08:43,578 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 11:08:43,580 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 11:08:43,582 - analysis_executor - INFO - 面积阈值: 20.0 平方米
2025-08-13 11:08:43,587 - analysis_executor - INFO - 开始空间数据变化分析任务: f014dd67-0907-45d2-81f5-7940530b2898
2025-08-13 11:08:43,606 - analysis_executor - INFO - 空间数据变化分析任务已启动: f014dd67-0907-45d2-81f5-7940530b2898
2025-08-13 11:08:43,636 - analysis_executor - INFO - 开始执行分析任务: f014dd67-0907-45d2-81f5-7940530b2898
2025-08-13 11:12:27,528 - analysis_executor - INFO - 空间数据变化分析任务完成: f014dd67-0907-45d2-81f5-7940530b2898
2025-08-13 11:14:33,107 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 11:14:33,110 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 11:14:33,112 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 11:14:33,116 - analysis_executor - INFO - 面积阈值: 2.0 平方米
2025-08-13 11:14:33,120 - analysis_executor - INFO - 开始空间数据变化分析任务: a4515178-8cc1-46a1-b8a8-3321e1b00f21
2025-08-13 11:14:33,152 - analysis_executor - INFO - 空间数据变化分析任务已启动: a4515178-8cc1-46a1-b8a8-3321e1b00f21
2025-08-13 11:14:33,159 - analysis_executor - INFO - 开始执行分析任务: a4515178-8cc1-46a1-b8a8-3321e1b00f21
2025-08-13 11:18:13,405 - analysis_executor - INFO - 空间数据变化分析任务完成: a4515178-8cc1-46a1-b8a8-3321e1b00f21
2025-08-13 11:25:55,052 - analysis_executor - INFO - 加载了 3 个任务状态
2025-08-13 11:28:08,757 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 11:28:08,763 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 11:28:08,769 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 11:28:08,771 - analysis_executor - INFO - 面积阈值: 2.0 平方米
2025-08-13 11:28:08,774 - analysis_executor - INFO - 开始空间数据变化分析任务: fcf0638a-6257-4448-bf32-c395e39e8611
2025-08-13 11:28:08,808 - analysis_executor - INFO - 空间数据变化分析任务已启动: fcf0638a-6257-4448-bf32-c395e39e8611
2025-08-13 11:28:08,834 - analysis_executor - INFO - 开始执行分析任务: fcf0638a-6257-4448-bf32-c395e39e8611
2025-08-13 11:31:42,254 - analysis_executor - INFO - 空间数据变化分析任务完成: fcf0638a-6257-4448-bf32-c395e39e8611
2025-08-13 11:32:33,642 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 11:32:33,645 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 11:32:33,650 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 11:32:33,654 - analysis_executor - INFO - 面积阈值: 2.0 平方米
2025-08-13 11:32:33,659 - analysis_executor - INFO - 开始空间数据变化分析任务: c7448c3a-d87f-4079-b82d-b616735dba15
2025-08-13 11:32:33,690 - analysis_executor - INFO - 空间数据变化分析任务已启动: c7448c3a-d87f-4079-b82d-b616735dba15
2025-08-13 11:32:33,704 - analysis_executor - INFO - 开始执行分析任务: c7448c3a-d87f-4079-b82d-b616735dba15
2025-08-13 11:36:15,021 - analysis_executor - INFO - 空间数据变化分析任务完成: c7448c3a-d87f-4079-b82d-b616735dba15
2025-08-13 11:38:09,696 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:38:42,627 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:39:09,424 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:39:32,003 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:39:47,441 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:40:15,081 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:40:37,151 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:41:19,644 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:41:38,649 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 11:41:57,748 - analysis_executor - INFO - 加载了 5 个任务状态
2025-08-13 12:01:59,325 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 12:01:59,329 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 12:01:59,336 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 12:01:59,339 - analysis_executor - INFO - 面积阈值: 2.0 平方米
2025-08-13 12:01:59,342 - analysis_executor - INFO - 开始空间数据变化分析任务: 62921e5f-38f1-44ba-8d0c-a1e888d9855c
2025-08-13 12:01:59,362 - analysis_executor - INFO - 空间数据变化分析任务已启动: 62921e5f-38f1-44ba-8d0c-a1e888d9855c
2025-08-13 12:01:59,369 - analysis_executor - INFO - 开始执行分析任务: 62921e5f-38f1-44ba-8d0c-a1e888d9855c
2025-08-13 12:05:36,189 - analysis_executor - INFO - 空间数据变化分析任务完成: 62921e5f-38f1-44ba-8d0c-a1e888d9855c
2025-08-13 15:07:36,164 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 15:07:36,167 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 15:07:36,167 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 15:07:36,174 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-13 15:07:36,182 - analysis_executor - INFO - 开始空间数据变化分析任务: 759a231d-4219-495c-9ba2-1e6e0963c6c3
2025-08-13 15:07:36,209 - analysis_executor - INFO - 空间数据变化分析任务已启动: 759a231d-4219-495c-9ba2-1e6e0963c6c3
2025-08-13 15:07:36,215 - analysis_executor - INFO - 开始执行分析任务: 759a231d-4219-495c-9ba2-1e6e0963c6c3
2025-08-13 15:11:40,380 - analysis_executor - INFO - 空间数据变化分析任务完成: 759a231d-4219-495c-9ba2-1e6e0963c6c3
2025-08-13 15:18:02,591 - analysis_executor - INFO - 加载了 7 个任务状态
2025-08-13 15:18:23,162 - analysis_executor - INFO - 加载了 7 个任务状态
2025-08-13 15:18:44,319 - analysis_executor - INFO - 加载了 7 个任务状态
2025-08-13 15:19:06,498 - analysis_executor - INFO - 加载了 7 个任务状态
2025-08-13 15:19:28,700 - analysis_executor - INFO - 加载了 7 个任务状态
2025-08-13 15:20:12,108 - analysis_executor - INFO - 加载了 7 个任务状态
2025-08-13 15:22:10,501 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 15:22:10,506 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 15:22:10,514 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 15:22:10,520 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-13 15:22:10,524 - analysis_executor - INFO - 开始空间数据变化分析任务: c4d27fd0-7dcf-40cc-a0d0-55335102f494
2025-08-13 15:22:10,546 - analysis_executor - INFO - 空间数据变化分析任务已启动: c4d27fd0-7dcf-40cc-a0d0-55335102f494
2025-08-13 15:22:10,556 - analysis_executor - INFO - 开始执行分析任务: c4d27fd0-7dcf-40cc-a0d0-55335102f494
2025-08-13 15:26:44,582 - analysis_executor - INFO - 空间数据变化分析任务完成: c4d27fd0-7dcf-40cc-a0d0-55335102f494
2025-08-13 15:33:50,585 - analysis_executor - INFO - 加载了 8 个任务状态
2025-08-13 15:34:08,477 - analysis_executor - INFO - 加载了 8 个任务状态
2025-08-13 15:34:24,922 - analysis_executor - INFO - 加载了 8 个任务状态
2025-08-13 15:35:58,448 - analysis_executor - INFO - 加载了 8 个任务状态
2025-08-13 15:37:23,036 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-13 15:37:23,052 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-13 15:37:23,075 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-13 15:37:23,097 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-13 15:37:23,102 - analysis_executor - INFO - 开始空间数据变化分析任务: a74cc310-a240-4412-a7c5-9963b56e601d
2025-08-13 15:37:23,139 - analysis_executor - INFO - 空间数据变化分析任务已启动: a74cc310-a240-4412-a7c5-9963b56e601d
2025-08-13 15:37:23,183 - analysis_executor - INFO - 开始执行分析任务: a74cc310-a240-4412-a7c5-9963b56e601d
2025-08-13 15:41:49,934 - analysis_executor - INFO - 空间数据变化分析任务完成: a74cc310-a240-4412-a7c5-9963b56e601d
2025-08-14 09:54:11,812 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 09:54:25,553 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:03:34,793 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:05:30,913 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:05:50,634 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:09:16,472 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:17:14,939 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:17:59,554 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:18:33,883 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:22:53,012 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:23:17,271 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:26:58,167 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:31:45,316 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:32:16,794 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:35:57,602 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:54:20,758 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:56:06,000 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:56:26,972 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:56:46,525 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:57:14,134 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:57:31,820 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-14 17:58:01,302 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:35:36,524 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:35:50,917 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:42:30,473 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:42:42,138 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:43:11,636 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:43:50,996 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:47:33,374 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:47:47,270 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:48:37,969 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:48:52,702 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:57:37,740 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:59:09,026 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:59:37,300 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 08:59:48,258 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 09:01:17,984 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 09:01:30,657 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 09:01:55,312 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 09:03:17,435 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:04:36,290 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:04:59,575 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:06:21,298 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:07:07,119 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:07:25,750 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:07:42,619 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:08:34,626 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:16:02,688 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:16:50,003 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:22:40,785 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:22:53,118 - analysis_executor - INFO - 加载了 9 个任务状态
2025-08-15 11:23:25,338 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-15 11:23:25,343 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-15 11:23:25,348 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-15 11:23:25,351 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-15 11:23:25,353 - analysis_executor - INFO - 开始空间数据变化分析任务: e922f38e-592e-4dad-a335-e04fc30aefd8
2025-08-15 11:23:25,379 - analysis_executor - INFO - 空间数据变化分析任务已启动: e922f38e-592e-4dad-a335-e04fc30aefd8
2025-08-15 11:23:25,390 - analysis_executor - INFO - 开始执行分析任务: e922f38e-592e-4dad-a335-e04fc30aefd8
2025-08-15 11:27:29,557 - analysis_executor - INFO - 空间数据变化分析任务完成: e922f38e-592e-4dad-a335-e04fc30aefd8
2025-08-15 14:56:08,759 - analysis_executor - INFO - 加载了 10 个任务状态
2025-08-15 14:56:25,597 - analysis_executor - INFO - 加载了 10 个任务状态
2025-08-15 14:56:41,578 - analysis_executor - INFO - 加载了 10 个任务状态
2025-08-15 14:57:05,570 - analysis_executor - INFO - 加载了 10 个任务状态
2025-08-15 14:58:06,599 - analysis_executor - INFO - 加载了 10 个任务状态
2025-08-15 15:02:20,377 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-15 15:02:20,379 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-15 15:02:20,380 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-15 15:02:20,381 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-15 15:02:20,383 - analysis_executor - INFO - 开始空间数据变化分析任务: f28302b8-9eff-4a33-a661-838137ade38d
2025-08-15 15:02:20,396 - analysis_executor - INFO - 空间数据变化分析任务已启动: f28302b8-9eff-4a33-a661-838137ade38d
2025-08-15 15:02:20,405 - analysis_executor - INFO - 开始执行分析任务: f28302b8-9eff-4a33-a661-838137ade38d
2025-08-15 15:06:29,585 - analysis_executor - ERROR - 空间数据变化分析任务失败: f28302b8-9eff-4a33-a661-838137ade38d - 分析执行失败: 'NoneType' object has no attribute 'CreateLayer'
2025-08-15 15:13:45,725 - analysis_executor - INFO - 加载了 11 个任务状态
2025-08-15 15:14:29,431 - analysis_executor - INFO - 加载了 11 个任务状态
2025-08-15 15:14:55,997 - analysis_executor - INFO - 加载了 11 个任务状态
2025-08-15 15:15:47,639 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-15 15:15:47,640 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-15 15:15:47,641 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-15 15:15:47,642 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-15 15:15:47,644 - analysis_executor - INFO - 开始空间数据变化分析任务: 27f302c6-afea-4de5-b694-92aa70ad7138
2025-08-15 15:15:47,650 - analysis_executor - INFO - 空间数据变化分析任务已启动: 27f302c6-afea-4de5-b694-92aa70ad7138
2025-08-15 15:15:47,733 - analysis_executor - INFO - 开始执行分析任务: 27f302c6-afea-4de5-b694-92aa70ad7138
2025-08-15 15:16:05,823 - analysis_executor - INFO - 收到空间数据变化分析请求
2025-08-15 15:16:05,824 - analysis_executor - INFO - 老数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2024_84.shp
2025-08-15 15:16:05,825 - analysis_executor - INFO - 新数据: D:/Drone_Project/dataset/迁移流出耕地/耕地实验/耕地2025部分_84.shp
2025-08-15 15:16:05,827 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-15 15:16:05,829 - analysis_executor - INFO - 开始空间数据变化分析任务: 812f17d2-fca6-4485-a40f-448ea292f86a
2025-08-15 15:16:05,845 - analysis_executor - INFO - 空间数据变化分析任务已启动: 812f17d2-fca6-4485-a40f-448ea292f86a
2025-08-15 15:16:05,858 - analysis_executor - INFO - 开始执行分析任务: 812f17d2-fca6-4485-a40f-448ea292f86a
2025-08-15 15:22:41,755 - analysis_executor - INFO - 空间数据变化分析任务完成: 27f302c6-afea-4de5-b694-92aa70ad7138
2025-08-15 15:22:56,206 - analysis_executor - INFO - 空间数据变化分析任务完成: 812f17d2-fca6-4485-a40f-448ea292f86a
2025-08-15 17:40:11,839 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-15 17:41:28,432 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-15 17:48:56,655 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 08:38:49,664 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 08:39:12,034 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 08:44:31,759 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 08:45:17,615 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 09:29:45,646 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 09:34:35,420 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 09:34:52,282 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 10:58:06,190 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:03:49,533 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:05:14,542 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:20:56,777 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:21:14,545 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:21:57,216 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:22:25,440 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:24:11,040 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:24:39,402 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:25:08,766 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:25:27,047 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:26:01,098 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:26:19,661 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:26:39,173 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 11:27:24,094 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:00:44,569 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:01:22,251 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:01:45,095 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:02:50,460 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:03:22,537 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:06:38,270 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:07:01,045 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:07:38,708 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:08:16,637 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 15:50:05,017 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 16:55:58,737 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 16:56:27,730 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 16:56:46,438 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:06:02,825 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:06:33,242 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:07:18,419 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:07:52,165 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:08:35,734 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:10:21,278 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:12:39,919 - analysis_executor - ERROR - 启动AI语义分割任务失败: 'AnalysisExecutor' object has no attribute 'log_dir'
2025-08-18 17:14:36,155 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:15:44,723 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:17:20,159 - analysis_executor - ERROR - 启动AI语义分割任务失败: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-18 17:19:28,279 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:20:16,594 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:20:49,537 - analysis_executor - INFO - 加载了 13 个任务状态
2025-08-18 17:21:55,754 - analysis_executor - INFO - AI语义分割任务已启动: 544c62dd-c379-4b42-91a7-c92d51c331ae
2025-08-18 17:26:11,408 - analysis_executor - INFO - 加载了 14 个任务状态
2025-08-18 17:26:54,725 - analysis_executor - INFO - 加载了 14 个任务状态
2025-08-18 17:27:59,108 - analysis_executor - INFO - 加载了 14 个任务状态
2025-08-18 17:28:47,157 - analysis_executor - INFO - AI语义分割任务已启动: 87eeecae-8299-449a-bac9-52ddf4103fb3
2025-08-18 17:32:13,188 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:32:42,019 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:33:01,998 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:33:22,542 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:34:16,667 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:34:37,404 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:34:58,686 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:35:35,651 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:36:15,728 - analysis_executor - INFO - 加载了 15 个任务状态
2025-08-18 17:40:12,320 - analysis_executor - INFO - AI语义分割任务已启动: 3f7b90b3-2291-4369-9615-c365b429d59f
2025-08-18 17:53:09,749 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:53:31,386 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:53:53,019 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:54:13,966 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:54:59,564 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:56:02,560 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:56:55,331 - analysis_executor - INFO - 加载了 16 个任务状态
2025-08-18 17:58:48,852 - analysis_executor - INFO - AI语义分割任务已启动: 05f6823d-c0f7-4360-a1d2-0df332f924df
2025-08-19 08:58:00,627 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 08:58:16,878 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:02:30,196 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:02:57,404 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:03:19,092 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:03:42,769 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:03:59,976 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:04:28,440 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:04:44,918 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:05:08,674 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:05:38,304 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:06:42,259 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:10:56,464 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:11:14,693 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:11:34,357 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:11:58,191 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:12:22,585 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:12:41,484 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:13:03,520 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:13:29,390 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:14:20,085 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:14:55,064 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:15:27,345 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:16:01,681 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:16:19,882 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:17:07,657 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:20:24,218 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:21:03,677 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:21:21,459 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:22:08,527 - analysis_executor - INFO - 加载了 17 个任务状态
2025-08-19 09:22:35,820 - analysis_executor - INFO - AI语义分割任务已启动: f4411045-15e2-427d-8aa6-2c2f3e2da8b5
2025-08-19 09:22:39,986 - analysis_executor - INFO - 🚀 开始处理图像: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out.tif
2025-08-19 09:22:40,000 - analysis_executor - INFO - 📁 模型路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth
2025-08-19 09:22:40,074 - analysis_executor - INFO - 📄 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp
2025-08-19 09:22:40,293 - analysis_executor - INFO - 🔧 设备: cuda
2025-08-19 09:22:40,393 - analysis_executor - INFO - ⚙️ 参数: window_size=512, batch_size=16
2025-08-19 09:22:40,406 - analysis_executor - INFO - 📊 启动性能监控...
2025-08-19 09:22:40,467 - analysis_executor - INFO - 📖 读取图像数据...
2025-08-19 09:22:40,843 - analysis_executor - INFO - 🌍 获取地理信息...
2025-08-19 09:22:40,997 - analysis_executor - INFO - 地理变换: (759547.6575424257, 0.07390315427927786, 0.0, 2488550.2657344304, 0.0, -0.07390169802147338)
2025-08-19 09:22:41,000 - analysis_executor - INFO - 投影信息: PROJCS["WGS 84 / UTM zone 48N",GEOGCS["WGS 84",DATUM["WGS_1984",SPHEROID["WGS 84",6378137,298.257223...
2025-08-19 09:22:41,002 - analysis_executor - INFO - 📊 读取图像数据...
2025-08-19 09:22:41,052 - analysis_executor - INFO - ✅ 图像尺寸: 17007x20364, 波段数: 3
2025-08-19 09:22:41,100 - analysis_executor - INFO - 🔍 检查Nodata值...
2025-08-19 09:22:41,148 - analysis_executor - INFO - 发现Nodata值: [-9999.0, -9999.0, -9999.0]
2025-08-19 09:22:41,149 - analysis_executor - INFO - 💾 读取图像数据到内存...
2025-08-19 09:22:41,196 - analysis_executor - INFO - 处理RGB图像...
2025-08-19 09:22:41,256 - analysis_executor - INFO -   读取波段 1/3...
2025-08-19 09:22:50,185 - analysis_executor - INFO -   读取波段 2/3...
2025-08-19 09:22:54,725 - analysis_executor - INFO -   读取波段 3/3...
2025-08-19 09:22:59,043 - analysis_executor - INFO - ✅ 图像数据读取完成，形状: (20364, 17007, 3), 数据类型: float32
2025-08-19 09:22:59,356 - analysis_executor - INFO - ⚠️ 未启用Nodata掩码
2025-08-19 09:22:59,437 - analysis_executor - INFO - 🔢 图像数据归一化...
2025-08-19 09:23:00,020 - analysis_executor - INFO - 归一化前: min=-9999.000, max=255.000, dtype=float32
2025-08-19 09:23:00,528 - analysis_executor - INFO - 使用8位归一化 (除以255)
2025-08-19 09:23:02,398 - analysis_executor - INFO - ✅ 归一化后: min=-39.212, max=1.000
2025-08-19 09:23:02,428 - analysis_executor - INFO - 🤖 创建模型: deeplabv3_plus
2025-08-19 09:23:02,569 - analysis_executor - INFO - 模型路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/models/farm.pth
2025-08-19 09:23:04,227 - analysis_executor - INFO - ✅ 模型创建完成
2025-08-19 09:23:04,477 - analysis_executor - INFO - 🧠 开始模型推理...
2025-08-19 09:23:04,662 - analysis_executor - INFO - ⚙️ 推理参数:
2025-08-19 09:23:04,951 - analysis_executor - INFO -   - 窗口大小: 512
2025-08-19 09:23:04,953 - analysis_executor - INFO -   - 重叠率: 0.5
2025-08-19 09:23:04,954 - analysis_executor - INFO -   - 批处理大小: 16
2025-08-19 09:23:04,955 - analysis_executor - INFO -   - 并行线程: 4
2025-08-19 09:23:04,957 - analysis_executor - INFO -   - 并行处理: 启用
2025-08-19 09:23:04,964 - analysis_executor - INFO - 🔲 生成 5070 个窗口，使用 4 个CPU线程进行推理
2025-08-19 09:27:42,962 - analysis_executor - INFO - ✅ 模型推理完成，预测结果形状: (2, 20364, 17007)
2025-08-19 09:27:43,044 - analysis_executor - INFO - 🗺️ 开始矢量化...
2025-08-19 09:27:43,076 - analysis_executor - INFO - 📄 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp
2025-08-19 09:27:43,160 - analysis_executor - INFO - 🎯 目标类别: 1
2025-08-19 09:27:43,234 - analysis_executor - INFO - ⚙️ 矢量化参数:
2025-08-19 09:27:43,296 - analysis_executor - INFO -   - 简化阈值: 0.0
2025-08-19 09:27:43,302 - analysis_executor - INFO -   - 最小面积: 0.0
2025-08-19 09:27:43,304 - analysis_executor - INFO -   - 移除空洞: True
2025-08-19 09:27:43,311 - analysis_executor - INFO -   - 空洞阈值: 1000.0
2025-08-19 09:27:43,315 - analysis_executor - INFO -   - 合并相邻: True
2025-08-19 09:27:43,318 - analysis_executor - INFO -   - 合并距离: 10.0
2025-08-19 09:27:43,320 - analysis_executor - INFO - 🔄 开始矢量化处理，输出到: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp
2025-08-19 09:27:43,376 - analysis_executor - INFO - 📊 多类别预测，取最大概率类别
2025-08-19 09:27:51,753 - analysis_executor - INFO - 🎯 目标类别: [1]
2025-08-19 09:27:54,185 - analysis_executor - INFO - 🔍 处理类别 1...
2025-08-19 09:27:55,439 - analysis_executor - INFO - ⚡ 使用并行轮廓处理（掩码尺寸: (20364, 17007)）
2025-08-19 09:29:58,824 - analysis_executor - INFO - ✅ 矢量化完成
2025-08-19 09:29:58,847 - analysis_executor - INFO - 🎉 处理完成: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out2.shp
2025-08-19 09:32:46,129 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:34:14,070 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:39:02,966 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:39:33,318 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:39:54,047 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:40:21,754 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:40:47,377 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:41:13,407 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:41:36,989 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:41:55,554 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:42:51,432 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:43:07,725 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:44:02,298 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:44:21,357 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:45:00,580 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:45:37,681 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:45:59,862 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:46:16,410 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:46:36,021 - analysis_executor - INFO - 加载了 18 个任务状态
2025-08-19 09:49:15,907 - analysis_executor - INFO - AI语义分割任务已启动: e6faa30e-d492-4a4d-96e8-7ca9f9c20aa9
2025-08-19 10:41:45,750 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:42:34,995 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:42:53,926 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:43:13,501 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:48:35,052 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-19 10:48:35,055 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-19 10:48:35,061 - analysis_executor - INFO - 配置文件读取成功
2025-08-19 10:48:35,064 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-19 10:48:35,067 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-19 10:48:35,068 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-19 10:48:35,070 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-19 10:48:35,071 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 10:48:35,073 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-19 10:48:35,084 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-19 10:48:35,087 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 10:48:35,090 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-19 10:48:35,092 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-19 10:48:35,093 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-19 10:48:48,648 - analysis_executor - INFO - 开始获取权重配置信息
2025-08-19 10:48:48,655 - analysis_executor - INFO - 成功获取权重配置信息
2025-08-19 10:48:59,163 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-19 10:48:59,168 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-19 10:48:59,189 - analysis_executor - INFO - 配置文件读取成功
2025-08-19 10:48:59,192 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-19 10:48:59,193 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-19 10:48:59,196 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-19 10:48:59,199 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-19 10:48:59,201 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 10:48:59,202 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-19 10:48:59,209 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-19 10:48:59,210 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 10:48:59,212 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-19 10:48:59,214 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-19 10:48:59,218 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-19 10:50:00,628 - analysis_executor - INFO - 开始获取权重配置信息
2025-08-19 10:50:00,672 - analysis_executor - INFO - 成功获取权重配置信息
2025-08-19 10:58:05,969 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:58:28,582 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:58:52,156 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:59:12,504 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:59:37,692 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 10:59:59,885 - analysis_executor - INFO - 加载了 19 个任务状态
2025-08-19 11:11:21,723 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-19 11:11:21,750 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-19 11:11:21,763 - analysis_executor - INFO - 配置文件读取成功
2025-08-19 11:11:21,768 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-19 11:11:21,770 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-19 11:11:21,773 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-19 11:11:21,775 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-19 11:11:21,777 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 11:11:21,788 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-19 11:11:21,791 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-19 11:11:21,793 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 11:11:21,795 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-19 11:11:21,797 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-19 11:11:21,798 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-19 15:05:53,399 - analysis_executor - INFO - AI语义分割任务已启动: bb56e834-49dc-4d47-987b-74bf26de51b8
2025-08-19 15:26:43,114 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:29:21,819 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:30:06,824 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:30:37,946 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:39:00,425 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:39:27,162 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:40:35,265 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:42:11,740 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:43:51,333 - analysis_executor - INFO - 加载了 20 个任务状态
2025-08-19 15:47:25,728 - analysis_executor - INFO - AI语义分割任务已启动: 3de79e28-afbd-453a-bfa7-db836bf9dba9
2025-08-19 15:50:32,901 - analysis_executor - INFO - 加载了 21 个任务状态
2025-08-19 15:50:55,485 - analysis_executor - INFO - 加载了 21 个任务状态
2025-08-19 15:51:59,326 - analysis_executor - INFO - 加载了 21 个任务状态
2025-08-19 15:52:40,294 - analysis_executor - INFO - 加载了 21 个任务状态
2025-08-19 15:52:59,820 - analysis_executor - INFO - 加载了 21 个任务状态
2025-08-19 15:54:49,226 - analysis_executor - INFO - AI语义分割任务已启动: 4645a19d-f6fc-4b48-bb98-6daf89d40b88
2025-08-19 15:57:02,348 - analysis_executor - INFO - 加载了 22 个任务状态
2025-08-19 15:58:02,509 - analysis_executor - INFO - 加载了 22 个任务状态
2025-08-19 15:59:10,890 - analysis_executor - INFO - 加载了 22 个任务状态
2025-08-19 15:59:10,891 - analysis_executor - INFO - 加载了 22 个任务状态
2025-08-19 15:59:13,005 - analysis_executor - INFO - AI语义分割任务已启动: 109d6696-ddac-4ffe-b00f-721643d98fd6
2025-08-19 16:02:29,800 - analysis_executor - INFO - 加载了 23 个任务状态
2025-08-19 16:03:27,727 - analysis_executor - INFO - AI语义分割任务已启动: 9a0b4563-2621-4e38-a6c0-e9b31e01c36a
2025-08-19 16:16:53,578 - analysis_executor - INFO - 加载了 24 个任务状态
2025-08-19 16:18:19,282 - analysis_executor - INFO - AI语义分割任务已启动: 1dc2ac2e-cf54-40d9-bfad-0a1fb916c296
2025-08-19 16:20:46,665 - analysis_executor - INFO - AI语义分割任务已启动: 4a4a73de-c936-4292-9ca1-7210a85e6202
2025-08-19 17:04:50,698 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-19 17:05:28,351 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-19 17:06:07,390 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-19 17:06:07,394 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-19 17:06:07,398 - analysis_executor - INFO - 配置文件读取成功
2025-08-19 17:06:07,399 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-19 17:06:07,401 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-19 17:06:07,403 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-19 17:06:07,405 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-19 17:06:07,413 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 17:06:07,413 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-19 17:06:07,414 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-19 17:06:07,414 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 17:06:07,415 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-19 17:06:07,416 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-19 17:06:08,660 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-19 17:06:08,661 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-19 17:06:08,662 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-19 17:06:08,662 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-19 17:06:08,664 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-19 17:06:08,665 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-19 17:06:08,666 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-19 17:06:08,667 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-19 17:06:08,668 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-19 17:06:17,714 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-19 17:06:17,717 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-19 17:06:17,723 - analysis_executor - INFO - 配置文件读取成功
2025-08-19 17:06:17,724 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-19 17:06:17,725 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-19 17:06:17,726 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-19 17:06:17,733 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-19 17:06:17,734 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 17:06:17,736 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-19 17:06:17,737 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-19 17:06:17,738 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 17:06:17,739 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-19 17:06:17,740 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-19 17:06:17,740 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-19 17:06:17,741 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-19 17:06:17,751 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-19 17:06:17,752 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-19 17:06:17,753 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-19 17:06:17,754 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-19 17:06:17,755 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-19 17:06:17,756 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-19 17:06:17,756 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-19 17:06:48,041 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-19 17:06:48,044 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-19 17:06:48,072 - analysis_executor - INFO - 配置文件读取成功
2025-08-19 17:06:48,074 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-19 17:06:48,075 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-19 17:06:48,076 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-19 17:06:48,146 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-19 17:06:48,146 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 17:06:48,147 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-19 17:06:48,147 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-19 17:06:48,148 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-19 17:06:48,148 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-19 17:06:48,150 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-19 17:06:48,151 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-19 17:06:48,151 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-19 17:06:48,152 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-19 17:06:48,152 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-19 17:06:48,153 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-19 17:06:48,154 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-19 17:06:48,154 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-19 17:06:48,155 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-19 17:06:48,155 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-19 17:06:50,217 - analysis_executor - INFO - 开始获取权重配置信息
2025-08-19 17:06:50,225 - analysis_executor - INFO - 成功获取权重配置信息
2025-08-19 17:27:46,465 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-19 17:28:20,720 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-19 17:28:39,748 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-19 17:29:11,258 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-19 17:39:37,622 - analysis_executor - INFO - 收到合并分析请求
2025-08-19 17:39:37,627 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-19 17:39:37,629 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-19 17:39:37,630 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-19 17:39:37,632 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-19 17:39:37,634 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-19 17:39:37,636 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-19 17:39:37,643 - analysis_executor - INFO - 开始合并分析任务: fa4a2561-4f16-402e-8e5e-3dbb44366020
2025-08-19 17:39:37,669 - analysis_executor - INFO - 合并分析任务已启动: fa4a2561-4f16-402e-8e5e-3dbb44366020
2025-08-19 17:39:37,773 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:43:44,456 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:43:56,555 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:45:42,601 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:46:41,867 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:47:00,021 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:47:20,356 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:47:36,911 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:48:01,264 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:48:54,137 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:49:32,442 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:49:46,000 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:49:59,449 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:56:32,516 - analysis_executor - INFO - 收到影像范围提取请求
2025-08-20 08:56:32,518 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 08:56:32,519 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 08:56:32,520 - analysis_executor - INFO - 简化容差: 1.0米
2025-08-20 08:56:32,521 - analysis_executor - INFO - 最小面积: 1000.0平方米
2025-08-20 08:56:32,526 - analysis_executor - INFO - 影像范围提取任务已启动: 25e4462e-493f-4aab-9e79-b3f692b77bc7
2025-08-20 08:56:32,539 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 08:56:32,547 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 08:59:22,307 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 08:59:38,647 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:00:13,023 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:00:51,412 - analysis_executor - INFO - 收到影像范围提取请求
2025-08-20 09:00:51,413 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:00:51,414 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 09:00:51,417 - analysis_executor - INFO - 简化容差: 1.0米
2025-08-20 09:00:51,420 - analysis_executor - INFO - 最小面积: 1000.0平方米
2025-08-20 09:00:51,426 - analysis_executor - INFO - 影像范围提取任务已启动: 555342a3-62f1-47ee-97f0-99ab4d42f2b9
2025-08-20 09:00:51,435 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:00:51,442 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 09:02:14,817 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:02:30,630 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:02:49,590 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:03:04,738 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:03:33,928 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:03:58,438 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:04:17,433 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:06:32,183 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:06:52,336 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:07:06,930 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:07:25,053 - analysis_executor - INFO - 加载了 26 个任务状态
2025-08-20 09:07:49,202 - analysis_executor - INFO - 收到影像范围提取请求
2025-08-20 09:07:49,203 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:07:49,205 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 09:07:49,206 - analysis_executor - INFO - 简化容差: 1.0米
2025-08-20 09:07:49,208 - analysis_executor - INFO - 最小面积: 1000.0平方米
2025-08-20 09:07:49,216 - analysis_executor - INFO - 影像范围提取任务已启动: 4884f255-61bb-4139-8ac6-8ba7080c3efa
2025-08-20 09:07:49,227 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:07:49,234 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 09:13:07,044 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:13:40,773 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:13:57,366 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:14:21,343 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:14:36,584 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:14:49,122 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:15:15,588 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:15:30,269 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:15:51,189 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:16:03,971 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:16:21,650 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:16:33,790 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:16:46,754 - analysis_executor - INFO - 加载了 27 个任务状态
2025-08-20 09:18:34,332 - analysis_executor - INFO - 收到影像范围提取请求
2025-08-20 09:18:34,334 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:18:34,335 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 09:18:34,336 - analysis_executor - INFO - 简化容差: 1.0米
2025-08-20 09:18:34,338 - analysis_executor - INFO - 最小面积: 10.0平方米
2025-08-20 09:18:34,340 - analysis_executor - INFO - 保持原始坐标系: 是
2025-08-20 09:18:34,349 - analysis_executor - INFO - 影像范围提取任务已启动: 70f11078-b4d8-4c74-ae58-4b2a182d7905
2025-08-20 09:18:34,357 - analysis_executor - INFO - 输入影像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:18:34,364 - analysis_executor - INFO - 输出路径: D:/Drone_Project/geoserverAPIDJV2/geoserver_api/core/AIChangeShp/tif/20250705171601_out6.shp
2025-08-20 09:29:21,866 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:29:34,441 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:29:49,188 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:30:22,929 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:30:55,029 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:31:17,963 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:31:48,111 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:32:10,658 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:32:24,032 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:33:07,893 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:33:43,304 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:36:05,927 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:39:21,956 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:41:46,997 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:44:43,521 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:45:22,148 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:45:34,813 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:46:05,058 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:47:38,922 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:48:11,556 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:48:41,553 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:48:54,452 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:49:15,201 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 09:55:54,392 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 09:55:54,393 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 09:55:54,393 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 09:55:54,394 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 09:55:54,395 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 09:55:54,396 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 09:55:54,397 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 09:55:54,513 - analysis_executor - INFO - 开始合并分析任务: 6d2431c4-6791-43dd-8932-bc221ae6aeba
2025-08-20 09:55:54,520 - analysis_executor - INFO - 合并分析任务已启动: 6d2431c4-6791-43dd-8932-bc221ae6aeba
2025-08-20 09:55:54,566 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:02:21,306 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:02:36,371 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:05:24,951 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:05:43,743 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:05:59,673 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:06:31,518 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:07:06,071 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:07:19,048 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:07:33,962 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:08:04,141 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:09:46,610 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:09:46,612 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:09:46,612 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:09:46,613 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:09:46,614 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:09:46,615 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:09:46,615 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:09:46,618 - analysis_executor - INFO - 开始合并分析任务: c58bca09-a054-47da-b2ad-7ca625512b72
2025-08-20 10:09:46,624 - analysis_executor - INFO - 合并分析任务已启动: c58bca09-a054-47da-b2ad-7ca625512b72
2025-08-20 10:09:46,669 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:12:50,444 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:13:21,150 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:13:54,348 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:15:31,147 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:15:31,153 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:15:31,155 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:15:31,156 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:15:31,158 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:15:31,158 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:15:31,159 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:15:31,255 - analysis_executor - INFO - 开始合并分析任务: 30633333-4b84-45b8-a988-f713b56e6d19
2025-08-20 10:15:31,265 - analysis_executor - INFO - 合并分析任务已启动: 30633333-4b84-45b8-a988-f713b56e6d19
2025-08-20 10:15:31,483 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:18:42,873 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:19:04,325 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:19:25,702 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:19:55,040 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:20:24,296 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:20:48,813 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:21:02,735 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:22:42,650 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:22:42,651 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:22:42,654 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:22:42,655 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:22:42,655 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:22:42,656 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:22:42,658 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:22:42,803 - analysis_executor - INFO - 开始合并分析任务: 85e90ef1-8ba5-4304-b45f-8e35006b3e10
2025-08-20 10:22:42,817 - analysis_executor - INFO - 合并分析任务已启动: 85e90ef1-8ba5-4304-b45f-8e35006b3e10
2025-08-20 10:22:42,847 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:24:59,212 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:25:11,604 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:26:42,151 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:27:05,314 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:27:49,301 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:28:47,221 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:28:47,223 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:28:47,224 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:28:47,225 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:28:47,226 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:28:47,227 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:28:47,228 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:28:47,505 - analysis_executor - INFO - 开始合并分析任务: b44138e3-284f-40ea-987b-15df396236b0
2025-08-20 10:28:47,756 - analysis_executor - INFO - 合并分析任务已启动: b44138e3-284f-40ea-987b-15df396236b0
2025-08-20 10:28:48,111 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:30:22,277 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:30:22,280 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:30:22,281 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:30:22,282 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:30:22,283 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:30:22,285 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:30:22,286 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:30:22,373 - analysis_executor - INFO - 开始合并分析任务: 04409c37-7379-4c02-9048-8d4197f5d3f4
2025-08-20 10:30:22,384 - analysis_executor - INFO - 合并分析任务已启动: 04409c37-7379-4c02-9048-8d4197f5d3f4
2025-08-20 10:30:22,438 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:31:37,597 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:32:09,805 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:32:23,986 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:32:23,987 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:32:23,988 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:32:23,988 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:32:23,988 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:32:23,989 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:32:23,989 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:32:23,990 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:32:24,076 - analysis_executor - INFO - 开始合并分析任务: bb090725-071d-4055-a195-3ad638176242
2025-08-20 10:32:24,105 - analysis_executor - INFO - 合并分析任务已启动: bb090725-071d-4055-a195-3ad638176242
2025-08-20 10:32:24,116 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:33:55,849 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:34:07,961 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:34:10,923 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:34:10,926 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:34:10,927 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:34:10,929 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:34:10,930 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:34:10,933 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:34:10,934 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:34:10,936 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:34:11,092 - analysis_executor - INFO - 开始合并分析任务: a04e9c59-f72a-41da-9917-e104bd7c14fc
2025-08-20 10:34:11,414 - analysis_executor - INFO - 合并分析任务已启动: a04e9c59-f72a-41da-9917-e104bd7c14fc
2025-08-20 10:34:11,588 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:35:49,557 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:36:20,710 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:36:20,712 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:36:20,713 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:36:20,719 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:36:20,720 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:36:20,721 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:36:20,734 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:36:20,736 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:36:20,845 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:36:20,987 - analysis_executor - INFO - 开始合并分析任务: 63cae558-7a71-45fb-8d92-b38ab7fdd85e
2025-08-20 10:36:21,502 - analysis_executor - INFO - 合并分析任务已启动: 63cae558-7a71-45fb-8d92-b38ab7fdd85e
2025-08-20 10:36:21,789 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:37:16,436 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:38:15,884 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:38:27,356 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:38:44,676 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:38:44,680 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:38:44,684 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:38:44,685 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:38:44,687 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:38:44,689 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:38:44,691 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:38:44,692 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:38:44,798 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:38:44,914 - analysis_executor - INFO - 开始合并分析任务: 479652da-5345-4d50-b4a1-6d9b34ab2508
2025-08-20 10:38:45,187 - analysis_executor - INFO - 合并分析任务已启动: 479652da-5345-4d50-b4a1-6d9b34ab2508
2025-08-20 10:38:45,438 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:44:42,535 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:45:33,288 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:46:38,065 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:46:38,069 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:46:38,073 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:46:38,079 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:46:38,080 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:46:38,081 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:46:38,082 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:46:38,090 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:46:38,297 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:46:38,370 - analysis_executor - INFO - 开始合并分析任务: 3f8bdbf4-08ae-4d1b-9816-4b6140e18f84
2025-08-20 10:46:38,719 - analysis_executor - INFO - 合并分析任务已启动: 3f8bdbf4-08ae-4d1b-9816-4b6140e18f84
2025-08-20 10:46:38,949 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:46:55,836 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:48:04,725 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:48:35,523 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:50:34,564 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:50:34,568 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:50:34,575 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:50:34,577 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:50:34,578 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:50:34,579 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:50:34,579 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:50:34,580 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:50:34,704 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:50:34,819 - analysis_executor - INFO - 开始合并分析任务: 77561507-3d1a-44d1-83e9-3c69158be488
2025-08-20 10:50:35,340 - analysis_executor - INFO - 合并分析任务已启动: 77561507-3d1a-44d1-83e9-3c69158be488
2025-08-20 10:50:35,575 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:51:26,934 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:51:26,937 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:51:26,938 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:26,941 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:51:26,943 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:51:26,945 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:51:26,947 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:51:26,948 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:51:27,030 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:51:27,141 - analysis_executor - INFO - 开始合并分析任务: b38ef849-57b0-4b34-94bc-b0b85a6e345f
2025-08-20 10:51:27,575 - analysis_executor - INFO - 合并分析任务已启动: b38ef849-57b0-4b34-94bc-b0b85a6e345f
2025-08-20 10:51:27,856 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:52:08,488 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:52:19,856 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:52:20,105 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:52:20,109 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:52:20,111 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:52:20,112 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:52:20,114 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:52:20,116 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:52:20,118 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:52:20,121 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:52:20,261 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:52:20,313 - analysis_executor - INFO - 开始合并分析任务: d0b0f56b-fb01-42a9-8164-4720d2190e60
2025-08-20 10:52:20,615 - analysis_executor - INFO - 合并分析任务已启动: d0b0f56b-fb01-42a9-8164-4720d2190e60
2025-08-20 10:52:20,863 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:53:12,542 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:53:24,443 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:54:24,978 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:54:42,153 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:55:22,881 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:55:22,886 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:55:22,891 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:55:22,893 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:55:22,895 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:55:22,896 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:55:22,897 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:55:22,909 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:55:23,019 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:55:23,200 - analysis_executor - INFO - 开始合并分析任务: 2741276c-693d-4963-9d31-e7cac18b29d9
2025-08-20 10:55:23,753 - analysis_executor - INFO - 合并分析任务已启动: 2741276c-693d-4963-9d31-e7cac18b29d9
2025-08-20 10:55:24,072 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:55:57,610 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:56:08,536 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 10:56:25,879 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 10:56:25,883 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 10:56:25,887 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:56:25,891 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 10:56:25,893 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 10:56:25,894 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 10:56:25,895 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 10:56:25,896 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 10:56:26,046 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 10:56:26,213 - analysis_executor - INFO - 开始合并分析任务: 246d9465-b05c-45d6-b159-84ceab21eae3
2025-08-20 10:56:26,623 - analysis_executor - INFO - 合并分析任务已启动: 246d9465-b05c-45d6-b159-84ceab21eae3
2025-08-20 10:56:26,883 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:10:33,866 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:11:34,610 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:12:33,175 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:13:34,603 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:15:02,243 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:23:37,506 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:23:53,985 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:24:16,859 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-20 11:25:17,596 - analysis_executor - INFO - 收到合并分析请求
2025-08-20 11:25:17,599 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-20 11:25:17,600 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:17,603 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-20 11:25:17,606 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-20 11:25:17,608 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-20 11:25:17,608 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-20 11:25:17,609 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-20 11:25:17,975 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-20 11:25:18,104 - analysis_executor - INFO - 开始合并分析任务: 16124f57-e48f-40d8-a322-265f96068b4d
2025-08-20 11:25:18,465 - analysis_executor - INFO - 合并分析任务已启动: 16124f57-e48f-40d8-a322-265f96068b4d
2025-08-20 11:25:18,703 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 08:15:30,297 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 08:15:43,063 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 09:52:46,419 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 09:52:46,539 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 09:52:46,630 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 09:52:46,632 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 09:52:46,634 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 09:52:46,636 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 09:52:46,637 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 09:52:46,638 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 09:52:46,640 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 09:52:46,642 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 09:52:46,644 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 09:52:46,645 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 09:52:46,646 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 09:52:46,648 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 09:52:46,651 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 09:52:46,653 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 09:52:46,653 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 09:52:46,655 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 09:52:46,658 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 09:52:46,738 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 09:52:46,739 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 09:52:46,741 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 10:00:40,726 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:00:54,405 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:01:08,153 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:01:55,567 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:02:09,023 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:02:27,745 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:02:42,451 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:03:37,511 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 10:03:37,512 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 10:03:37,518 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 10:03:37,520 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 10:03:37,522 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 10:03:37,523 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 10:03:37,525 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 10:03:37,527 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 10:03:37,528 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 10:03:37,529 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 10:03:37,529 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 10:03:37,531 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 10:03:37,532 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 10:03:37,532 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 10:03:37,533 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 10:03:37,534 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 10:03:37,537 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 10:03:37,538 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 10:03:37,540 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 10:03:37,542 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 10:03:37,543 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 10:03:37,544 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 10:03:37,544 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 10:19:25,420 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:21:28,843 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:21:43,205 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:22:02,408 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:24:47,535 - analysis_executor - INFO - 收到合并分析请求
2025-08-21 10:24:47,537 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-21 10:24:47,538 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 10:24:47,539 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 10:24:47,540 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 10:24:47,541 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-21 10:24:47,543 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-21 10:24:47,543 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-21 10:24:47,588 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 10:24:47,665 - analysis_executor - INFO - 开始合并分析任务: 62435a75-3482-4608-b585-f0d8506098e1
2025-08-21 10:24:47,791 - analysis_executor - INFO - 合并分析任务已启动: 62435a75-3482-4608-b585-f0d8506098e1
2025-08-21 10:24:47,850 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:45:31,484 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:45:57,900 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:46:20,470 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:46:39,111 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 10:54:40,232 - analysis_executor - INFO - 收到合并分析请求
2025-08-21 10:54:40,234 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-21 10:54:40,235 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 10:54:40,235 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 10:54:40,237 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 10:54:40,238 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-21 10:54:40,238 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-21 10:54:40,240 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-21 10:54:40,271 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 10:54:40,314 - analysis_executor - INFO - 开始合并分析任务: 71a6bceb-687e-45de-864a-a6ec8e5668b7
2025-08-21 10:54:40,419 - analysis_executor - INFO - 合并分析任务已启动: 71a6bceb-687e-45de-864a-a6ec8e5668b7
2025-08-21 10:54:40,517 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:17:08,085 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:17:33,763 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:17:46,929 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:18:01,069 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:21:46,829 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 11:21:46,934 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 11:21:46,940 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-21 11:23:05,582 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-21 11:23:05,591 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-21 11:23:05,594 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-21 11:23:11,073 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 11:23:11,079 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 11:23:11,221 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-21 11:28:28,084 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:29:03,902 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 11:37:00,881 - analysis_executor - INFO - 收到合并分析请求
2025-08-21 11:37:00,883 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-21 11:37:00,884 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 11:37:00,885 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 11:37:00,886 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 11:37:00,890 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-21 11:37:00,892 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-21 11:37:00,893 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-21 11:37:00,939 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 11:37:00,971 - analysis_executor - INFO - 开始合并分析任务: b5159c0f-1c95-4242-bb2d-b54bc89ee88f
2025-08-21 11:37:01,095 - analysis_executor - INFO - 合并分析任务已启动: b5159c0f-1c95-4242-bb2d-b54bc89ee88f
2025-08-21 11:37:01,162 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:16:41,709 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 15:16:41,758 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 15:16:41,759 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-21 15:22:58,342 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:22:58,343 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:22:58,349 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:22:58,350 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:22:58,350 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:22:58,351 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:22:58,351 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:22:58,352 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:22:58,353 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:22:58,353 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:22:58,373 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:22:58,374 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:22:58,375 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:22:58,376 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:22:58,377 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:22:58,378 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:22:58,379 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:22:58,380 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:22:58,381 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:22:58,389 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:22:58,389 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:22:58,390 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:22:58,390 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:42:38,680 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:43:05,464 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:43:33,286 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:43:49,022 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:44:05,301 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:44:22,975 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:44:25,261 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:44:25,263 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:44:25,270 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:44:25,272 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:44:25,273 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:44:25,274 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:44:25,275 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:44:25,276 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:44:25,277 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:44:25,278 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:44:25,279 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:44:25,281 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:44:25,282 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:44:25,283 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:44:25,283 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:44:25,284 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:44:25,285 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:44:25,291 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:44:25,292 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:44:25,294 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:44:25,295 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:44:25,295 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:44:25,296 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:44:25,296 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:44:38,313 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 15:51:54,563 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:51:54,564 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:51:54,569 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:51:54,569 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:51:54,570 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:51:54,571 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:51:54,572 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:51:54,573 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:51:54,574 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:51:54,575 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:51:54,575 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:51:54,576 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:51:54,579 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:51:54,580 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:51:54,580 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:51:54,580 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:51:54,581 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:51:54,582 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:51:54,582 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:51:54,583 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:51:54,583 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:51:54,584 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:51:54,584 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:51:54,585 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:52:17,404 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:52:17,404 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:52:17,410 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:52:17,411 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:52:17,414 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:52:17,415 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:52:17,416 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:52:17,417 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:52:17,418 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:52:17,418 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:52:17,419 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:52:17,421 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:52:17,422 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:52:17,423 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:52:17,425 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:52:17,426 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:52:17,426 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:52:17,427 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:52:17,430 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:52:17,431 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:52:17,432 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:52:17,433 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:52:17,434 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:52:17,434 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:54:08,031 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:54:08,063 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:54:08,086 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:54:08,110 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:54:08,111 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:54:08,112 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:54:08,220 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:54:08,266 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:54:08,382 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:54:08,434 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:54:08,493 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:54:08,581 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:54:08,758 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:54:08,921 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:54:09,038 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:54:09,120 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:54:09,278 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:54:09,396 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:54:09,528 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:54:09,645 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:54:09,746 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:54:09,881 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:54:10,041 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:54:10,268 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:54:10,595 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:54:10,740 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:54:10,915 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:54:10,959 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:54:10,960 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:54:10,978 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:54:10,991 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:54:10,991 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:54:10,994 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:54:10,995 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:54:11,063 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:54:11,116 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:54:11,177 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:54:11,243 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:54:11,263 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:54:11,276 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:54:11,309 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:54:11,427 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:54:11,605 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:54:11,688 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:54:11,775 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:54:11,799 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:54:11,837 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:54:11,916 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:57:25,245 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:57:25,246 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:57:25,268 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:57:25,270 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:57:25,272 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:57:25,273 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:57:25,273 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:57:25,274 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:57:25,275 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:57:25,275 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:57:25,276 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:57:25,277 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:57:25,284 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:57:25,285 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:57:25,286 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:57:25,287 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:57:25,288 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:57:25,290 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:57:25,290 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:57:25,291 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:57:25,292 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:57:25,293 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:57:25,294 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:57:25,294 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 15:59:28,726 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 15:59:28,727 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 15:59:28,732 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 15:59:28,733 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 15:59:28,734 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 15:59:28,737 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 15:59:28,742 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 15:59:28,743 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 15:59:28,744 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 15:59:28,745 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:59:28,745 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 15:59:28,746 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 15:59:28,747 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 15:59:28,747 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 15:59:28,748 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 15:59:28,749 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 15:59:28,749 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 15:59:28,751 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 15:59:28,774 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 15:59:28,776 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 15:59:28,778 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 15:59:28,778 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 15:59:28,779 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 15:59:28,779 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:17:20,116 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:17:20,117 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:17:20,124 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:17:20,124 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:17:20,126 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:17:20,127 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:17:20,127 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 16:17:20,128 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:17:20,129 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:17:20,130 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:17:20,130 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:17:20,131 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:17:20,132 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:17:20,133 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:17:20,133 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:17:20,134 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:17:20,135 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:17:20,136 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:17:20,137 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:17:20,138 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:17:20,139 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:17:20,140 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:17:20,142 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:17:20,144 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:18:19,366 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:18:19,367 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:18:19,373 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:18:19,375 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:18:19,376 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:18:19,377 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:18:19,377 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 16:18:19,378 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:18:19,379 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:18:19,380 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:18:19,381 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:18:19,382 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:18:19,383 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:18:19,383 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:18:19,384 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:18:19,384 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:18:19,385 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:18:19,386 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:18:19,386 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:18:19,387 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:18:19,388 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:18:19,390 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:18:19,392 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:18:19,394 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:19:21,444 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:19:21,445 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:19:21,464 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:19:21,465 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:19:21,466 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:19:21,466 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:19:21,467 - analysis_executor - INFO - 获取到默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 16:19:21,467 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:19:21,469 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:19:21,470 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:19:21,471 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:19:21,472 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:19:21,473 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:19:21,474 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:19:21,475 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:19:21,476 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:19:21,477 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:19:21,478 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:19:21,479 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:19:21,479 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:19:21,480 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:19:21,481 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:19:21,481 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:19:21,482 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:22:32,137 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 16:22:40,303 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:22:40,306 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:22:40,316 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:22:40,317 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:22:40,317 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:22:40,318 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:22:40,319 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:22:40,319 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:22:40,320 - analysis_executor - INFO - 处理默认面积配置: arableland = '400' (类型: <class 'str'>)
2025-08-21 16:22:40,320 - analysis_executor - INFO - 默认面积转换成功: arableland = 400.0
2025-08-21 16:22:40,321 - analysis_executor - INFO - 处理默认面积配置: constructionland = '200' (类型: <class 'str'>)
2025-08-21 16:22:40,322 - analysis_executor - INFO - 默认面积转换成功: constructionland = 200.0
2025-08-21 16:22:40,323 - analysis_executor - INFO - 最终默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 16:22:40,324 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:22:40,325 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:22:40,325 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:22:40,326 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:22:40,327 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:22:40,327 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:22:40,328 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:22:40,329 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:22:40,329 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:22:40,330 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:22:40,331 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:22:40,332 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:22:40,332 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:22:40,333 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:22:40,333 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:22:40,334 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:22:40,334 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:23:18,942 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:23:18,943 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:23:18,974 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:23:18,976 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:23:18,977 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:23:18,978 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:23:18,979 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:23:18,980 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:23:18,981 - analysis_executor - INFO - 处理默认面积配置: arableland = '400' (类型: <class 'str'>)
2025-08-21 16:23:18,981 - analysis_executor - INFO - 默认面积转换成功: arableland = 400.0
2025-08-21 16:23:18,982 - analysis_executor - INFO - 处理默认面积配置: constructionland = '200' (类型: <class 'str'>)
2025-08-21 16:23:18,983 - analysis_executor - INFO - 默认面积转换成功: constructionland = 200.0
2025-08-21 16:23:18,984 - analysis_executor - INFO - 最终默认面积配置: {'arableland': 400.0, 'constructionland': 200.0}
2025-08-21 16:23:18,984 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:23:18,986 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:23:18,987 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:23:18,988 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:23:18,989 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:23:18,990 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:23:18,991 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:23:18,991 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:23:18,992 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:23:18,993 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:23:18,995 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:23:18,995 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:23:18,996 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:23:18,997 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:23:18,997 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:23:18,998 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:23:18,998 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:24:22,723 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 16:24:38,486 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 16:25:29,444 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:25:29,466 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:25:29,476 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:25:29,480 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:25:29,512 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:25:29,513 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:25:29,514 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:25:29,527 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:25:29,618 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-21 16:25:29,666 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-21 16:25:29,667 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-21 16:25:29,670 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-21 16:25:29,672 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-21 16:25:29,672 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:25:29,673 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:25:29,693 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:25:29,696 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:25:29,711 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:25:29,713 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:25:29,716 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:25:29,719 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:25:29,721 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:25:29,723 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:25:29,725 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:25:29,726 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:25:29,728 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:25:29,730 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:25:29,731 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:25:29,734 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:25:29,739 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:25:29,756 - analysis_executor - INFO - 开始获取权重配置信息
2025-08-21 16:25:29,771 - analysis_executor - INFO - 成功获取权重配置信息
2025-08-21 16:26:30,599 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:26:30,599 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:26:30,604 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:26:30,605 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:26:30,605 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:26:30,606 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:26:30,606 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:26:30,608 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:26:30,608 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-21 16:26:30,609 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-21 16:26:30,609 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-21 16:26:30,610 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-21 16:26:30,611 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-21 16:26:30,611 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:26:30,613 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:26:30,614 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:30,614 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:26:30,615 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:26:30,616 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:30,616 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:26:30,617 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:30,617 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:26:30,618 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:26:30,619 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:26:30,620 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:26:30,620 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:26:30,621 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:26:30,622 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:26:30,622 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:30,623 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:26:34,752 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:26:34,752 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:26:34,774 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:26:34,775 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:26:34,776 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:26:34,777 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:26:34,779 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:26:34,781 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:26:34,781 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-21 16:26:34,781 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-21 16:26:34,782 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-21 16:26:34,782 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-21 16:26:34,782 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-21 16:26:34,783 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:26:34,783 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:26:34,784 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:34,785 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:26:34,786 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:26:34,786 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:34,787 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:26:34,787 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:34,787 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:26:34,788 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:26:34,789 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:26:34,789 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:26:34,789 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:26:34,790 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:26:34,790 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:26:34,791 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:34,791 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:26:44,374 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:26:44,375 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:26:44,380 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:26:44,381 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:26:44,381 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:26:44,382 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:26:44,382 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:26:44,382 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:26:44,383 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-21 16:26:44,384 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-21 16:26:44,385 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-21 16:26:44,386 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-21 16:26:44,386 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-21 16:26:44,387 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:26:44,388 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:26:44,391 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:44,393 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:26:44,393 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:26:44,394 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:44,395 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:26:44,396 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:44,396 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:26:44,397 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:26:44,398 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:26:44,400 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:26:44,401 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:26:44,403 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:26:44,404 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:26:44,404 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:44,408 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:26:54,085 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 16:26:54,086 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 16:26:54,090 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 16:26:54,091 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 16:26:54,092 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 16:26:54,092 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 16:26:54,092 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 16:26:54,093 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 16:26:54,093 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-21 16:26:54,094 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-21 16:26:54,094 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-21 16:26:54,095 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-21 16:26:54,095 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-21 16:26:54,096 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 16:26:54,097 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 16:26:54,098 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:54,098 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 16:26:54,098 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 16:26:54,099 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 16:26:54,099 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 16:26:54,100 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:54,100 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 16:26:54,101 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 16:26:54,102 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 16:26:54,102 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 16:26:54,103 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 16:26:54,104 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 16:26:54,104 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 16:26:54,105 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 16:26:54,105 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 16:33:23,747 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:33:23,752 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:33:23,754 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:48:03,546 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-21 16:48:03,556 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-21 16:48:03,563 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-21 16:48:07,005 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:48:07,013 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:48:07,014 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:48:19,837 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:48:19,844 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:48:19,846 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:48:21,213 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:48:21,234 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:48:21,236 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:50:06,766 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:50:06,793 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:50:06,794 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:50:17,304 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:50:17,336 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:50:17,337 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:53:58,505 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:53:58,515 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:53:58,516 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 16:58:13,862 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 16:58:13,869 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 16:58:13,871 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 17:01:42,935 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 17:01:42,944 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 17:01:42,945 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 17:01:47,414 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-21 17:01:47,415 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-21 17:01:47,443 - analysis_executor - INFO - 配置文件读取成功
2025-08-21 17:01:47,444 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-21 17:01:47,444 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-21 17:01:47,445 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-21 17:01:47,445 - analysis_executor - INFO - 发现Default_area配置节
2025-08-21 17:01:47,446 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-21 17:01:47,446 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-21 17:01:47,447 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-21 17:01:47,447 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-21 17:01:47,449 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-21 17:01:47,449 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-21 17:01:47,449 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-21 17:01:47,450 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-21 17:01:47,451 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 17:01:47,452 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-21 17:01:47,453 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-21 17:01:47,453 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-21 17:01:47,455 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-21 17:01:47,456 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-21 17:01:47,456 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-21 17:01:47,458 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-21 17:01:47,459 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-21 17:01:47,459 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-21 17:01:47,460 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-21 17:01:47,461 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-21 17:01:47,462 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-21 17:01:47,462 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-21 17:01:47,463 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-21 17:02:04,357 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-21 17:02:04,395 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-21 17:02:04,432 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-21 17:02:07,864 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-21 17:02:07,872 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-21 17:02:07,874 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-21 17:20:34,110 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:30:21,861 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:30:44,554 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:31:20,036 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:34:50,770 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:35:03,440 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:36:47,020 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:39:56,491 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:41:25,325 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:41:52,245 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:43:24,368 - analysis_executor - INFO - 收到合并分析请求
2025-08-21 17:43:24,369 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-21 17:43:24,370 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:43:24,371 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 17:43:24,373 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 17:43:24,374 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-21 17:43:24,375 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-21 17:43:24,376 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-21 17:43:24,412 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:43:24,441 - analysis_executor - INFO - 开始合并分析任务: 8211d345-7110-4d4d-876d-cb94aae3acee
2025-08-21 17:43:24,518 - analysis_executor - INFO - 合并分析任务已启动: 8211d345-7110-4d4d-876d-cb94aae3acee
2025-08-21 17:43:24,593 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:43:55,676 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:44:34,002 - analysis_executor - INFO - 收到合并分析请求
2025-08-21 17:44:34,003 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-21 17:44:34,004 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:44:34,004 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 17:44:34,005 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 17:44:34,006 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-21 17:44:34,007 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-21 17:44:34,007 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-21 17:44:34,038 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:44:34,062 - analysis_executor - INFO - 开始合并分析任务: 77e1e6f9-406d-4c51-a003-9d7991a16b5e
2025-08-21 17:44:34,154 - analysis_executor - INFO - 合并分析任务已启动: 77e1e6f9-406d-4c51-a003-9d7991a16b5e
2025-08-21 17:44:34,207 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:47:22,571 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:49:28,438 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:49:40,378 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:52:41,424 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:52:55,277 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:53:08,949 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 17:54:32,031 - analysis_executor - INFO - 收到合并分析请求
2025-08-21 17:54:32,040 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-21 17:54:32,043 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:54:32,045 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-21 17:54:32,046 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-21 17:54:32,047 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-21 17:54:32,047 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-21 17:54:32,050 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-21 17:54:32,184 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-21 17:54:32,369 - analysis_executor - INFO - 开始合并分析任务: 5c7960a5-6e6d-4975-ada7-36ddbfc5c109
2025-08-21 17:54:33,142 - analysis_executor - INFO - 合并分析任务已启动: 5c7960a5-6e6d-4975-ada7-36ddbfc5c109
2025-08-21 17:54:33,684 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 18:01:34,643 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 18:02:00,172 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 18:04:05,103 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-21 18:04:22,969 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 08:19:33,031 - analysis_executor - INFO - 收到合并分析请求
2025-08-22 08:19:33,034 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-22 08:19:33,035 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-22 08:19:33,036 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-22 08:19:33,040 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-22 08:19:33,042 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-22 08:19:33,052 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-22 08:19:33,054 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-22 08:19:33,184 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-22 08:19:33,373 - analysis_executor - INFO - 开始合并分析任务: 8144f232-fb2b-430b-9f82-51535c2158b7
2025-08-22 08:19:33,895 - analysis_executor - INFO - 合并分析任务已启动: 8144f232-fb2b-430b-9f82-51535c2158b7
2025-08-22 08:19:34,247 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 08:42:38,736 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 08:42:38,842 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 08:42:38,847 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 08:42:47,489 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 08:42:47,526 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 08:42:47,533 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 08:46:37,905 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 08:46:37,915 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 08:46:37,920 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 08:47:09,801 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 08:47:09,830 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 08:47:09,833 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 08:53:13,329 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 08:53:13,345 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 08:53:13,349 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 08:59:29,855 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 08:59:29,858 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 08:59:29,866 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 08:59:29,869 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 08:59:29,873 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 08:59:29,882 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 08:59:29,884 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 08:59:29,885 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 08:59:29,886 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 08:59:29,888 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 08:59:29,889 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 08:59:29,891 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 08:59:29,893 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 08:59:29,894 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 08:59:29,908 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 08:59:29,912 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 08:59:29,919 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 08:59:29,946 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 08:59:29,947 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 08:59:29,953 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 08:59:29,957 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 08:59:29,961 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 08:59:29,963 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 08:59:29,969 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 08:59:29,974 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 08:59:29,976 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 08:59:29,984 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 08:59:29,986 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 08:59:29,990 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 08:59:29,991 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 09:01:55,063 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 09:01:55,073 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:01:55,075 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:01:57,816 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:01:57,824 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:01:57,828 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:03:07,731 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 09:03:07,768 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:03:07,793 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:04:32,359 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 09:04:32,390 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:04:32,394 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:04:36,808 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:04:36,818 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:04:36,820 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:05:20,208 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 09:05:20,220 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:05:20,223 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:05:27,998 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:05:28,026 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:05:28,029 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:08:09,999 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 09:08:10,010 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:08:10,012 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:10:36,069 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:10:36,083 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:10:36,087 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:11:47,809 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:11:47,819 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:11:47,822 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:16:12,986 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:16:13,010 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:16:13,016 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:20:35,858 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:20:35,888 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:20:35,894 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:20:59,968 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 09:20:59,979 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:20:59,981 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 09:21:03,082 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:21:03,091 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:21:03,093 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:30:22,029 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 09:30:43,709 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 09:31:37,774 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 09:34:23,310 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 09:51:31,991 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:51:32,155 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:51:32,168 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:54:22,429 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 09:54:22,444 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 09:54:22,446 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 09:54:38,204 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 09:54:38,212 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 09:54:38,223 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 09:54:38,225 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 09:54:38,227 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 09:54:38,229 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 09:54:38,231 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 09:54:38,246 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 09:54:38,249 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 09:54:38,253 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 09:54:38,256 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 09:54:38,258 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 09:54:38,260 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 09:54:38,262 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 09:54:38,272 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 09:54:38,279 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 09:54:38,280 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 09:54:38,282 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 09:54:38,284 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 09:54:38,288 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 09:54:38,291 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 09:54:38,293 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 09:54:38,294 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 09:54:38,312 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 09:54:38,314 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 09:54:38,315 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 09:54:38,381 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 09:54:38,384 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 09:54:38,387 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 09:54:38,389 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 09:54:42,074 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 09:54:42,077 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 09:54:42,090 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 09:54:42,092 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 09:54:42,094 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 09:54:42,106 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 09:54:42,110 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 09:54:42,112 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 09:54:42,113 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 09:54:42,117 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 09:54:42,120 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 09:54:42,126 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 09:54:42,142 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 09:54:42,145 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 09:54:42,149 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 09:54:42,153 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 09:54:42,155 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 09:54:42,157 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 09:54:42,173 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 09:54:42,180 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 09:54:42,181 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 09:54:42,183 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 09:54:42,185 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 09:54:42,192 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 09:54:42,196 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 09:54:42,199 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 09:54:42,202 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 09:54:42,207 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 09:54:42,212 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 09:54:42,214 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 10:02:44,134 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:02:44,149 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:02:44,151 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-22 10:02:47,684 - analysis_executor - INFO - 收到合并分析请求
2025-08-22 10:02:47,689 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-22 10:02:47,695 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-22 10:02:47,702 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-22 10:02:47,704 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-22 10:02:47,705 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-22 10:02:47,707 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-22 10:02:47,717 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-22 10:02:47,841 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-22 10:02:48,028 - analysis_executor - INFO - 开始合并分析任务: 60a6d2e1-f8c5-40e2-84e6-62957b38783f
2025-08-22 10:02:48,348 - analysis_executor - INFO - 合并分析任务已启动: 60a6d2e1-f8c5-40e2-84e6-62957b38783f
2025-08-22 10:02:48,464 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 10:04:38,776 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:04:39,663 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:04:40,161 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:04:40,170 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:04:40,177 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:04:40,179 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:04:42,942 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:04:42,984 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:04:42,986 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:08:18,096 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 10:08:18,312 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 10:08:18,322 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 10:08:20,478 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 10:08:20,497 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 10:08:20,503 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 10:08:25,850 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:08:25,864 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:08:25,995 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:09:15,602 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:09:15,809 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:09:16,199 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:11:53,771 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 10:11:53,776 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 10:11:53,801 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 10:11:53,805 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 10:11:53,807 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 10:11:53,807 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 10:11:53,808 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 10:11:53,809 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 10:11:53,810 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 10:11:53,810 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 10:11:53,811 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 10:11:53,812 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 10:11:53,813 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 10:11:53,813 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 10:11:53,824 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 10:11:53,825 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 10:11:53,826 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 10:11:53,846 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 10:11:53,852 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 10:11:53,854 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 10:11:53,854 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 10:11:53,855 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 10:11:53,875 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 10:11:53,877 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 10:11:53,878 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 10:11:53,883 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 10:11:53,905 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 10:11:53,906 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 10:11:53,907 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 10:11:53,907 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 10:28:45,845 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:28:45,854 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:28:45,855 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:29:05,548 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 10:29:05,552 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 10:29:05,556 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 10:29:05,557 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 10:29:05,558 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 10:29:05,559 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 10:29:05,559 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 10:29:05,559 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 10:29:05,560 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 10:29:05,560 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 10:29:05,561 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 10:29:05,562 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 10:29:05,562 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 10:29:05,563 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 10:29:05,564 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 10:29:05,566 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 10:29:05,580 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 10:29:05,581 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 10:29:05,582 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 10:29:05,582 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 10:29:05,583 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 10:29:05,585 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 10:29:05,585 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 10:29:05,587 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 10:29:05,588 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 10:29:05,588 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 10:29:05,589 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 10:29:05,590 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 10:29:05,591 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 10:29:05,592 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 10:31:45,382 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:31:45,389 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:31:45,390 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:31:49,002 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 10:31:49,005 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 10:31:49,031 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 10:31:49,034 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 10:31:49,035 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 10:31:49,037 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 10:31:49,038 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 10:31:49,039 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 10:31:49,039 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 10:31:49,040 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 10:31:49,041 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 10:31:49,042 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 10:31:49,042 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 10:31:49,043 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 10:31:49,054 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 10:31:49,055 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 10:31:49,056 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 10:31:49,056 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 10:31:49,057 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 10:31:49,057 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 10:31:49,058 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 10:31:49,058 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 10:31:49,059 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 10:31:49,060 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 10:31:49,060 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 10:31:49,061 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 10:31:49,063 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 10:31:49,064 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 10:31:49,065 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 10:31:49,065 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 10:44:20,679 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:44:21,046 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:44:21,335 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:44:56,135 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:44:56,150 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:44:56,152 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:46:31,592 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:46:31,605 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:46:31,607 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 10:48:53,223 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 10:48:53,237 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 10:48:53,238 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:14:28,560 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:14:28,573 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:14:28,575 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:15:25,160 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:15:25,171 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:15:25,173 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:16:15,910 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:16:15,947 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:16:15,955 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:20:05,024 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:20:05,033 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:20:05,034 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:25:36,862 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:25:36,872 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:25:36,874 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:25:53,583 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:25:53,594 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:25:53,597 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:28:03,948 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:28:03,960 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:28:03,962 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:29:40,223 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:29:40,235 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:29:40,239 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:30:04,231 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:30:04,254 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:30:04,259 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:33:22,002 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:33:22,064 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:33:22,067 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:35:12,122 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:35:12,132 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:35:12,136 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 11:36:08,972 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 11:36:08,982 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 11:36:08,984 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:02:12,273 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:02:12,285 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:02:12,287 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:15:37,775 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:15:37,787 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:15:37,789 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:21:04,596 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:21:04,610 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:21:04,611 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:27:21,726 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:27:21,738 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:27:21,745 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:29:57,727 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:29:57,738 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:29:57,740 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:35:46,814 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:35:46,828 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:35:46,830 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:36:45,555 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:36:45,569 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:36:45,571 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:38:26,383 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:38:26,408 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:38:26,410 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:40:36,200 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:40:36,215 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:40:36,216 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 12:40:51,179 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 12:40:51,192 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 12:40:51,194 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 14:56:45,200 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 14:56:45,213 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 14:56:45,214 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 14:57:18,178 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 14:57:18,191 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 14:57:18,193 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 14:58:58,351 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 14:58:58,366 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 14:58:58,368 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 14:59:05,877 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 14:59:05,890 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 14:59:05,891 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 15:00:21,874 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:00:21,882 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:00:21,883 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 15:05:08,848 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:05:08,860 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:05:08,862 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 15:05:37,849 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 15:05:37,853 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 15:05:37,861 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 15:05:37,862 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 15:05:37,863 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 15:05:37,864 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 15:05:37,864 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 15:05:37,865 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 15:05:37,867 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 15:05:37,867 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 15:05:37,868 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 15:05:37,869 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 15:05:37,869 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 15:05:37,870 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 15:05:37,871 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 15:05:37,871 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:05:37,872 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 15:05:37,872 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 15:05:37,873 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:05:37,873 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 15:05:37,907 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 15:05:37,918 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 15:05:37,919 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 15:05:37,921 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 15:05:37,925 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 15:05:37,927 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 15:05:37,929 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 15:05:37,931 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 15:05:37,931 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 15:05:37,932 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 15:07:42,887 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 15:07:42,890 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 15:07:42,898 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 15:07:42,899 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 15:07:42,899 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 15:07:42,900 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 15:07:42,900 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 15:07:42,901 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 15:07:42,901 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 15:07:42,901 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 15:07:42,902 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 15:07:42,902 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 15:07:42,902 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 15:07:42,902 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 15:07:42,903 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 15:07:42,903 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:07:42,904 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 15:07:42,904 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 15:07:42,904 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:07:42,905 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 15:07:42,905 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 15:07:42,906 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 15:07:42,906 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 15:07:42,907 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 15:07:42,907 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 15:07:42,907 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 15:07:42,908 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 15:07:42,909 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 15:07:42,910 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 15:07:42,921 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 15:15:46,808 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:15:46,822 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:15:46,824 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 15:15:49,312 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 15:15:49,317 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 15:15:49,342 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 15:15:49,343 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 15:15:49,344 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 15:15:49,344 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 15:15:49,345 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 15:15:49,346 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 15:15:49,346 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 15:15:49,347 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 15:15:49,348 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 15:15:49,350 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 15:15:49,351 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 15:15:49,351 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 15:15:49,352 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 15:15:49,352 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:15:49,353 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 15:15:49,353 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 15:15:49,354 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:15:49,354 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 15:15:49,354 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 15:15:49,355 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 15:15:49,355 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 15:15:49,357 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 15:15:49,368 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 15:15:49,368 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 15:15:49,369 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 15:15:49,370 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 15:15:49,371 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 15:15:49,371 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 15:18:06,362 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:18:06,369 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:18:06,370 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 15:18:07,505 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 15:18:07,509 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 15:18:07,515 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 15:18:07,517 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 15:18:07,518 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 15:18:07,519 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 15:18:07,521 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 15:18:07,522 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 15:18:07,523 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 15:18:07,524 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 15:18:07,525 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 15:18:07,526 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 15:18:07,526 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 15:18:07,527 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 15:18:07,529 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 15:18:07,530 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:18:07,531 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 15:18:07,532 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 15:18:07,532 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:18:07,533 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 15:18:07,533 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 15:18:07,534 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 15:18:07,535 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 15:18:07,536 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 15:18:07,537 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 15:18:07,538 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 15:18:07,540 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 15:18:07,541 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 15:18:07,542 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 15:18:07,542 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 15:22:10,759 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:22:10,766 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:22:10,768 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-22 15:22:12,992 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-22 15:22:12,993 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-22 15:22:12,998 - analysis_executor - INFO - 配置文件读取成功
2025-08-22 15:22:12,999 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-22 15:22:12,999 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-22 15:22:13,000 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-22 15:22:13,000 - analysis_executor - INFO - 发现Default_area配置节
2025-08-22 15:22:13,001 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-22 15:22:13,001 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-22 15:22:13,002 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-22 15:22:13,002 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-22 15:22:13,004 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-22 15:22:13,004 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-22 15:22:13,004 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-22 15:22:13,005 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-22 15:22:13,006 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:22:13,007 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-22 15:22:13,007 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-22 15:22:13,008 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-22 15:22:13,009 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-22 15:22:13,010 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-22 15:22:13,010 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-22 15:22:13,011 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-22 15:22:13,012 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-22 15:22:13,013 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-22 15:22:13,013 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-22 15:22:13,014 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-22 15:22:13,015 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-22 15:22:13,015 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-22 15:22:13,016 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-22 15:22:15,547 - analysis_executor - INFO - 收到合并分析请求
2025-08-22 15:22:15,548 - analysis_executor - INFO - 影像ID: 20250705171601
2025-08-22 15:22:15,548 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-22 15:22:15,548 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-22 15:22:15,549 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-22 15:22:15,551 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-22 15:22:15,552 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-22 15:22:15,552 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-22 15:22:15,564 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171601, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
2025-08-22 15:22:15,583 - analysis_executor - INFO - 开始合并分析任务: 34918de2-ed0a-4907-815b-ed9954d654e4
2025-08-22 15:22:15,641 - analysis_executor - INFO - 合并分析任务已启动: 34918de2-ed0a-4907-815b-ed9954d654e4
2025-08-22 15:22:15,762 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 15:23:23,147 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:23:23,245 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:23:23,246 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-22 15:27:01,417 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:27:01,479 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:27:01,502 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-22 15:30:09,699 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:30:09,733 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:30:09,735 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-22 15:30:11,081 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:30:11,104 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:30:11,106 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-22 15:30:13,002 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:30:13,008 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:30:13,010 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-22 15:33:01,408 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:33:01,434 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:33:01,436 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 15:46:49,313 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:46:49,322 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:46:49,323 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 15:47:41,326 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:47:41,337 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:47:41,339 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 15:49:16,352 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 15:49:31,005 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-22 15:49:45,741 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:49:45,757 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:49:45,761 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 15:53:36,946 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-22 15:53:36,972 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 15:53:36,976 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-22 15:55:04,984 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:55:05,002 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:55:05,008 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 15:55:51,338 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 15:55:51,369 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 15:55:51,377 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 16:25:16,145 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 16:25:16,210 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 16:25:16,215 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 16:27:34,097 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 16:27:34,106 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 16:27:34,109 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 16:29:46,701 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 16:29:47,136 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 16:29:47,228 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 16:29:49,663 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 16:29:49,681 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 16:29:49,690 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 16:34:02,519 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 16:34:02,530 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 16:34:02,532 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-22 16:34:08,213 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-22 16:34:08,222 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-22 16:34:08,224 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:42:16,854 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 08:42:30,203 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 08:47:27,041 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:47:27,441 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:47:27,514 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:47:31,236 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:47:31,266 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:47:31,270 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:47:34,952 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:47:34,974 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:47:34,977 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:48:28,945 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:48:28,965 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:48:28,970 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:48:30,306 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:48:30,337 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:48:30,344 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:48:33,505 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:48:33,534 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:48:33,536 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:48:34,226 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:48:34,247 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:48:34,251 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:48:36,202 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:48:36,220 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:48:36,222 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 08:56:53,680 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 08:56:53,713 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 08:56:53,718 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:00:44,297 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:00:44,326 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:00:44,328 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:00:48,073 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:00:48,098 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:00:48,102 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:00:50,954 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:00:50,962 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:00:50,966 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:00:52,705 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:00:52,724 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:00:52,726 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:01:35,850 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:01:35,873 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:01:35,877 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:01:38,850 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:01:38,869 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:01:38,874 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:01:46,080 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:01:46,111 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:01:46,116 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:01:48,048 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:01:48,072 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:01:48,075 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:03:20,217 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:03:20,253 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:03:20,258 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:03:23,000 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:03:23,028 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:03:23,031 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:03:28,489 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:03:28,511 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:03:28,516 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:06:53,778 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:06:53,794 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:06:53,797 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:06:57,392 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:06:57,434 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:06:57,454 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:07:12,186 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:07:12,196 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:07:12,198 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:07:15,667 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:07:15,677 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:07:15,679 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:08:24,667 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:08:24,689 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:08:24,692 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:08:38,769 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:08:38,777 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:08:38,779 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:08:41,490 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:08:41,511 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:08:41,516 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:13:10,113 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:13:10,135 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:13:10,139 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:13:15,257 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:13:15,269 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:13:15,271 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:13:41,961 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:13:41,970 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:13:41,973 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:15:29,529 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:15:29,547 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:15:29,549 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:15:32,044 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:15:32,056 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:15:32,059 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:15:43,449 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:15:43,463 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:15:43,465 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:15:46,153 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:15:46,162 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:15:46,165 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:15:52,768 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:15:52,788 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:15:52,791 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:16:19,825 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:16:19,856 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:16:19,861 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:16:22,816 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 09:16:22,838 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 09:16:22,840 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 09:16:31,552 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:16:31,572 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:16:31,576 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:16:33,587 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 09:16:33,599 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 09:16:33,602 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 09:16:43,488 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:16:43,497 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:16:43,500 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:16:47,632 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:16:47,643 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:16:47,648 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:17:27,821 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:17:27,863 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:17:27,886 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:21:06,057 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:21:06,084 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:21:06,085 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:21:07,983 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:21:07,993 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:21:07,995 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:21:24,672 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 09:21:24,675 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 09:21:24,701 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 09:21:24,704 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 09:21:24,708 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 09:21:24,709 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 09:21:24,712 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 09:21:24,714 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 09:21:24,726 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 09:21:24,737 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 09:21:24,738 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 09:21:24,740 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 09:21:24,742 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 09:21:24,743 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 09:21:24,746 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 09:21:24,761 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 09:21:24,763 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 09:21:24,769 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 09:21:24,771 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 09:21:24,772 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 09:21:24,775 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 09:21:24,776 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 09:21:25,762 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 09:21:25,766 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 09:21:25,770 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 09:21:25,772 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 09:21:25,776 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 09:21:25,779 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 09:21:25,785 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 09:21:25,790 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 09:23:24,393 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:23:24,414 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:23:24,419 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:24:17,089 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:24:17,122 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:24:17,128 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:25:32,394 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:25:32,403 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:25:32,405 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:25:39,946 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:25:39,977 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:25:39,982 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:27:07,432 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:27:07,456 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:27:07,460 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:38:26,096 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:38:26,109 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:38:26,120 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:38:29,898 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:38:29,919 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:38:29,921 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:38:45,450 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:38:45,459 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:38:45,463 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:39:26,161 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:39:26,182 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:39:26,187 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:39:29,858 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:39:29,881 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:29,883 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:31,977 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:39:31,999 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:39:32,001 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:39:34,225 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:39:34,248 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:34,252 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:42,907 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 09:39:42,909 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 09:39:42,918 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 09:39:42,920 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 09:39:42,921 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 09:39:42,923 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 09:39:42,924 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 09:39:42,926 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 09:39:42,928 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 09:39:42,929 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 09:39:42,930 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 09:39:42,942 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 09:39:42,953 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 09:39:42,956 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 09:39:42,958 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 09:39:42,960 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 09:39:42,961 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 09:39:42,963 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 09:39:42,971 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 09:39:42,976 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 09:39:42,977 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 09:39:42,980 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 09:39:42,982 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 09:39:42,986 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 09:39:42,987 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 09:39:42,988 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 09:39:42,990 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 09:39:42,991 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 09:39:42,992 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 09:39:43,003 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 09:39:48,051 - analysis_executor - INFO - 收到合并分析请求
2025-08-25 09:39:48,052 - analysis_executor - INFO - 影像ID: 20250705171600
2025-08-25 09:39:48,053 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600_out.tif
2025-08-25 09:39:48,058 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-25 09:39:48,059 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-25 09:39:48,060 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-25 09:39:48,061 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-25 09:39:48,062 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 09:39:48,439 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171600, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171600/20250705171600_out.tif
2025-08-25 09:39:48,584 - analysis_executor - INFO - 开始合并分析任务: f885326b-6f79-4f3f-901b-4f1071a6044f
2025-08-25 09:39:48,958 - analysis_executor - INFO - 合并分析任务已启动: f885326b-6f79-4f3f-901b-4f1071a6044f
2025-08-25 09:39:49,062 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:39:49,113 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:49,141 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:49,374 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 09:39:50,506 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:39:50,515 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:39:50,517 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:05,999 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:41:06,108 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:41:06,130 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:13,456 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:41:13,471 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:41:13,487 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:21,065 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:41:21,095 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:41:21,099 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:28,033 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:41:28,063 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:41:28,065 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:41:30,034 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:41:30,046 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:41:30,051 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:35,608 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:41:35,668 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:41:35,818 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:41:38,434 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:41:38,447 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:41:38,450 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:52,715 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:41:52,727 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:41:52,730 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:41:55,358 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:41:55,373 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:41:55,386 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:42:01,913 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:42:01,937 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:42:01,940 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:43:15,515 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:43:15,822 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:43:15,843 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:43:17,533 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:43:17,563 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:43:17,566 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:44:06,415 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:44:06,505 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:44:06,532 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:44:23,725 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:44:23,754 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:44:23,756 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:44:37,854 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:44:37,889 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:44:37,893 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:44:39,994 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:44:40,007 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:44:40,016 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:44:56,175 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:44:56,187 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:44:56,193 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:46:04,339 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:46:04,373 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:46:04,402 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:46:06,354 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:46:06,367 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:46:06,390 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:49:34,225 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:49:34,299 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:49:34,380 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:49:43,414 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:49:43,438 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:49:43,443 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:49:49,445 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:49:49,458 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:49:49,470 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:49:51,369 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 09:49:51,381 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:49:51,383 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 09:49:53,673 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:49:53,686 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:49:53,782 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 09:49:56,698 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 09:49:56,713 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 09:49:56,716 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 09:59:07,930 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 09:59:07,994 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 09:59:08,039 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:01:19,582 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:01:19,610 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:01:19,618 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:01:28,315 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:01:28,328 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:01:28,331 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:02:18,858 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:02:18,886 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:02:18,905 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:02:20,795 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:02:20,810 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:02:20,817 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:03:28,754 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:03:28,922 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:03:28,939 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:06:19,994 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:06:20,068 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:06:20,094 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:06:28,810 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:06:28,838 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:06:28,860 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:07:32,417 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:07:32,426 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:07:32,428 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:11:27,937 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:11:27,946 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:11:27,948 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:11:31,417 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:11:31,426 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:11:31,429 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:11:59,505 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:11:59,515 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:11:59,517 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:12:08,553 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:12:08,567 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:12:08,569 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:12:11,225 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:12:11,237 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:12:11,240 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:14:20,697 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 10:14:20,707 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 10:14:20,709 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 10:14:23,281 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:14:23,290 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:14:23,292 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:14:28,241 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:14:28,266 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:14:28,269 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:14:29,512 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:14:29,520 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:14:29,522 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:16:17,681 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:16:17,701 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:16:17,705 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:16:19,270 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:16:19,294 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:16:19,296 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:21:05,860 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:21:05,887 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:21:05,892 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:21:09,629 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:21:09,637 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:21:09,639 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:21:13,955 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:21:13,964 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:21:13,968 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:23:56,110 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:23:56,123 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:23:56,128 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:24:06,020 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:24:06,040 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:24:06,044 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:26:59,332 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:26:59,361 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:26:59,366 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:27:09,136 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:27:09,158 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:27:09,164 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:31:02,160 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:31:02,171 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:31:02,173 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:31:38,543 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:31:38,556 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:31:38,558 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:31:48,868 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:31:48,891 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:31:48,893 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:32:30,891 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:32:30,899 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:32:30,902 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:32:35,779 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:32:35,809 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:32:35,813 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:47:07,118 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:47:07,128 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:47:07,131 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:47:49,687 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 10:48:51,770 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 10:49:11,503 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 10:49:32,582 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 10:50:10,228 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 10:56:06,820 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 10:56:06,847 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 10:56:06,849 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 10:56:09,612 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:56:09,621 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:56:09,622 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:56:52,518 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 10:56:52,530 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 10:56:52,533 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 10:57:12,171 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:57:12,202 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:57:12,207 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 10:57:15,803 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 10:57:15,817 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 10:57:15,819 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:02:23,060 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 11:02:23,070 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 11:02:23,073 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:02:24,545 - analysis_executor - INFO - 开始处理数据下载请求，文件数量: 2
2025-08-25 11:02:24,546 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp -> http://127.0.0.1:81/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp
2025-08-25 11:02:24,548 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp -> http://127.0.0.1:81/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp
2025-08-25 11:02:24,551 - analysis_executor - INFO - 创建数据包: C:\Users\<USER>\AppData\Local\Temp\tmp695942z8\analysis_data_1756090944.zip
2025-08-25 11:02:24,911 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171600_1_1756085988.shp (4 个文件)
2025-08-25 11:02:25,061 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171600_2_1756085988.shp (4 个文件)
2025-08-25 11:02:25,064 - analysis_executor - INFO - 数据打包完成: C:\Users\<USER>\AppData\Local\Temp\tmp695942z8\analysis_data_1756090944.zip
2025-08-25 11:02:25,069 - analysis_executor - INFO - 开始下载文件: analysis_data_1756090944.zip
2025-08-25 11:04:32,552 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:04:32,579 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:04:32,580 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:05:06,488 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:05:06,516 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:05:06,523 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:14:52,886 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:14:52,895 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:14:52,899 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:15:02,773 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:15:02,786 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:15:02,788 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:15:03,756 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:15:03,789 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:15:03,796 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:15:07,261 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:15:07,282 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:15:07,284 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:21:08,594 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 11:21:08,606 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:21:08,608 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:21:08,635 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:21:08,669 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:21:08,674 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:21:08,692 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 11:21:08,700 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 11:21:08,702 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:21:08,717 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:21:08,726 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:21:08,729 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:21:32,258 - analysis_executor - INFO - 开始处理数据下载请求，文件数量: 6
2025-08-25 11:21:32,265 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand/20250705171601_1_1755828168.shp -> http://127.0.0.1:81/ODM/AI/20250705171601/arableLand/20250705171601_1_1755828168.shp
2025-08-25 11:21:32,267 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand/20250705171601_2_1755828168.shp -> http://127.0.0.1:81/ODM/AI/20250705171601/arableLand/20250705171601_2_1755828168.shp
2025-08-25 11:21:32,272 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171601/constructionLand/20250705171601_1_1755847335.shp -> http://127.0.0.1:81/ODM/AI/20250705171601/constructionLand/20250705171601_1_1755847335.shp
2025-08-25 11:21:32,273 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171601/constructionLand/20250705171601_2_1755847335.shp -> http://127.0.0.1:81/ODM/AI/20250705171601/constructionLand/20250705171601_2_1755847335.shp
2025-08-25 11:21:32,276 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp -> http://127.0.0.1:81/ODM/AI/20250705171600/arableLand/20250705171600_1_1756085988.shp
2025-08-25 11:21:32,278 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp -> http://127.0.0.1:81/ODM/AI/20250705171600/arableLand/20250705171600_2_1756085988.shp
2025-08-25 11:21:32,281 - analysis_executor - INFO - 创建数据包: C:\Users\<USER>\AppData\Local\Temp\tmpufdfrcfi\analysis_data_1756092092.zip
2025-08-25 11:21:32,552 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171601_1_1755828168.shp (4 个文件)
2025-08-25 11:21:32,743 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171601_2_1755828168.shp (4 个文件)
2025-08-25 11:21:32,942 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171601_1_1755847335.shp (4 个文件)
2025-08-25 11:21:33,097 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171601_2_1755847335.shp (4 个文件)
2025-08-25 11:21:33,384 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171600_1_1756085988.shp (4 个文件)
2025-08-25 11:21:33,613 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171600_2_1756085988.shp (4 个文件)
2025-08-25 11:21:33,617 - analysis_executor - INFO - 数据打包完成: C:\Users\<USER>\AppData\Local\Temp\tmpufdfrcfi\analysis_data_1756092092.zip
2025-08-25 11:21:33,619 - analysis_executor - INFO - 开始下载文件: analysis_data_1756092092.zip
2025-08-25 11:22:14,077 - analysis_executor - INFO - 开始处理数据下载请求，文件数量: 2
2025-08-25 11:22:14,080 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171601/constructionLand/20250705171601_1_1755847335.shp -> http://127.0.0.1:81/ODM/AI/20250705171601/constructionLand/20250705171601_1_1755847335.shp
2025-08-25 11:22:14,084 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171601/constructionLand/20250705171601_2_1755847335.shp -> http://127.0.0.1:81/ODM/AI/20250705171601/constructionLand/20250705171601_2_1755847335.shp
2025-08-25 11:22:14,089 - analysis_executor - INFO - 创建数据包: C:\Users\<USER>\AppData\Local\Temp\tmpa0h8kuok\analysis_data_1756092134.zip
2025-08-25 11:22:14,219 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171601_1_1755847335.shp (4 个文件)
2025-08-25 11:22:14,363 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171601_2_1755847335.shp (4 个文件)
2025-08-25 11:22:14,372 - analysis_executor - INFO - 数据打包完成: C:\Users\<USER>\AppData\Local\Temp\tmpa0h8kuok\analysis_data_1756092134.zip
2025-08-25 11:22:14,375 - analysis_executor - INFO - 开始下载文件: analysis_data_1756092134.zip
2025-08-25 11:26:15,092 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 11:26:15,126 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:26:15,130 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:26:15,138 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:26:15,145 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:26:15,147 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:26:15,159 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 11:26:15,188 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 11:26:15,193 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:26:15,201 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:26:15,235 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:26:15,240 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:27:48,786 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 11:27:48,800 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:27:48,808 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:27:48,840 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:27:48,868 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:27:48,871 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:27:48,897 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 11:27:48,930 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 11:27:48,935 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:27:48,942 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:27:48,952 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:27:48,953 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:30:11,158 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 11:30:11,171 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:30:11,174 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:30:11,187 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:30:11,212 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:30:11,216 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:30:11,226 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 11:30:11,236 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 11:30:11,238 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:30:11,251 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:30:11,261 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:30:11,263 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:31:04,712 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 11:31:04,721 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:31:04,728 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 11:31:04,740 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 11:31:04,752 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 11:31:04,755 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 11:31:04,762 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 11:31:04,767 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 11:31:04,774 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 11:31:04,780 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 11:31:04,786 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 11:31:04,789 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:02:16,209 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:02:16,216 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:02:16,219 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:02:19,663 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:02:19,671 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:02:19,675 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:02:41,661 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:02:41,696 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:02:41,702 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:02:57,453 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:02:57,464 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:02:57,466 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:03:25,082 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:03:25,090 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:03:25,092 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:04:23,278 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:04:23,288 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:04:23,292 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:05:04,903 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 15:05:04,937 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 15:05:04,938 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 15:05:04,984 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:05:04,993 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:05:04,996 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:05:05,014 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 15:05:05,025 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 15:05:05,027 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:05:05,047 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:05:05,080 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:05:05,084 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:31:31,826 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 15:31:31,836 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 15:31:31,838 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 15:31:36,589 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:31:36,596 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:31:36,599 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:31:40,590 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:31:40,611 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:31:40,612 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:31:44,127 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:31:44,144 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:31:44,146 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:31:47,118 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 15:31:47,139 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 15:31:47,141 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:31:52,021 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 15:31:52,027 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 15:31:52,032 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:33:31,102 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171602
2025-08-25 15:33:31,109 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 15:33:31,110 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171602\TaskInfo.json
2025-08-25 15:33:34,462 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:33:34,469 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:33:34,471 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:33:37,765 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 15:33:37,766 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 15:33:37,782 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 15:33:37,784 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 15:33:37,784 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 15:33:37,786 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 15:33:37,787 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 15:33:37,788 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 15:33:37,789 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 15:33:37,790 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 15:33:37,791 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 15:33:37,792 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 15:33:37,794 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 15:33:37,796 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 15:33:37,817 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 15:33:37,828 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 15:33:37,830 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 15:33:37,832 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 15:33:37,833 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 15:33:37,835 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 15:33:37,836 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 15:33:37,838 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 15:33:37,840 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 15:33:37,842 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 15:33:37,844 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 15:33:37,846 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 15:33:37,848 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 15:33:37,850 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 15:33:37,850 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 15:33:37,851 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 15:33:41,037 - analysis_executor - INFO - 收到合并分析请求
2025-08-25 15:33:41,038 - analysis_executor - INFO - 影像ID: 20250705171599
2025-08-25 15:33:41,040 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 15:33:41,041 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 15:33:41,043 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 15:33:41,044 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-25 15:33:41,045 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-25 15:33:41,047 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 15:33:41,081 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 15:33:41,113 - analysis_executor - INFO - 开始合并分析任务: 45414019-899e-4d89-94b6-ac61e794ea7c
2025-08-25 15:33:41,274 - analysis_executor - INFO - 合并分析任务已启动: 45414019-899e-4d89-94b6-ac61e794ea7c
2025-08-25 15:33:41,295 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:33:41,307 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:33:41,313 - analysis_executor - INFO - TaskInfo文件不存在: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:33:41,338 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 15:33:43,796 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:33:43,803 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:33:43,804 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:35:34,432 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:35:34,437 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:35:34,439 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:36:22,846 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:36:22,872 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:36:22,874 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:36:26,524 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:36:26,531 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:36:26,532 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:39:48,647 - analysis_executor - INFO - 开始处理数据下载请求，文件数量: 2
2025-08-25 15:39:48,648 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171599/constructionLand/20250705171599_1_1756107221.shp -> http://127.0.0.1:81/ODM/AI/20250705171599/constructionLand/20250705171599_1_1756107221.shp
2025-08-25 15:39:48,650 - analysis_executor - INFO - 路径转换: D:/Drone_Project/nginxData/ODM/AI/20250705171599/constructionLand/20250705171599_2_1756107221.shp -> http://127.0.0.1:81/ODM/AI/20250705171599/constructionLand/20250705171599_2_1756107221.shp
2025-08-25 15:39:48,652 - analysis_executor - INFO - 创建数据包: C:\Users\<USER>\AppData\Local\Temp\tmpsz3dmrae\analysis_data_1756107588.zip
2025-08-25 15:39:48,698 - analysis_executor - INFO - 成功下载SHP文件组: 20250705171599_1_1756107221.shp (4 个文件)
2025-08-25 15:39:48,703 - analysis_executor - WARNING - 必需文件下载失败: http://127.0.0.1:81/ODM/AI/20250705171599/constructionLand/20250705171599_2_1756107221.shp (HTTP 404)
2025-08-25 15:39:48,709 - analysis_executor - WARNING - 必需文件下载失败: http://127.0.0.1:81/ODM/AI/20250705171599/constructionLand/20250705171599_2_1756107221.shx (HTTP 404)
2025-08-25 15:39:48,716 - analysis_executor - WARNING - 必需文件下载失败: http://127.0.0.1:81/ODM/AI/20250705171599/constructionLand/20250705171599_2_1756107221.dbf (HTTP 404)
2025-08-25 15:39:48,747 - analysis_executor - ERROR - 缺少必需的SHP文件: ['20250705171599_2_1756107221.shp', '20250705171599_2_1756107221.shx', '20250705171599_2_1756107221.dbf']
2025-08-25 15:39:48,748 - analysis_executor - WARNING - SHP文件下载失败: http://127.0.0.1:81/ODM/AI/20250705171599/constructionLand/20250705171599_2_1756107221.shp
2025-08-25 15:39:48,749 - analysis_executor - INFO - 数据打包完成: C:\Users\<USER>\AppData\Local\Temp\tmpsz3dmrae\analysis_data_1756107588.zip
2025-08-25 15:39:48,749 - analysis_executor - INFO - 开始下载文件: analysis_data_1756107588.zip
2025-08-25 15:50:23,644 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 15:51:32,947 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 15:52:10,617 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 15:52:40,009 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 15:54:44,417 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:54:44,461 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:54:44,501 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:54:49,738 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:54:49,762 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:54:49,763 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:54:53,159 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:54:53,166 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:54:53,168 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:54:57,377 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:54:57,383 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:54:57,384 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:55:00,553 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:55:00,559 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:55:00,561 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:55:04,736 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 15:55:04,744 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 15:55:04,745 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:55:38,527 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:55:38,533 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:55:38,534 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:55:39,804 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 15:55:39,805 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 15:55:39,809 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 15:55:39,810 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 15:55:39,810 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 15:55:39,811 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 15:55:39,811 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 15:55:39,811 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 15:55:39,811 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 15:55:39,812 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 15:55:39,812 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 15:55:39,812 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 15:55:39,812 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 15:55:39,813 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 15:55:39,813 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 15:55:39,814 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 15:55:39,814 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 15:55:39,815 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 15:55:39,815 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 15:55:39,815 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 15:55:39,816 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 15:55:39,816 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 15:55:39,816 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 15:55:39,817 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 15:55:39,818 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 15:55:39,818 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 15:55:39,819 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 15:55:39,819 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 15:55:39,819 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 15:55:39,819 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 15:55:42,752 - analysis_executor - INFO - 收到合并分析请求
2025-08-25 15:55:42,753 - analysis_executor - INFO - 影像ID: 20250705171599
2025-08-25 15:55:42,753 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 15:55:42,754 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 15:55:42,754 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 15:55:42,755 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-25 15:55:42,756 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-25 15:55:42,756 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 15:55:42,773 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 15:55:42,796 - analysis_executor - INFO - 开始合并分析任务: 1e1f805d-664c-42dc-9c0d-49b2c9d34b95
2025-08-25 15:55:42,847 - analysis_executor - INFO - 合并分析任务已启动: 1e1f805d-664c-42dc-9c0d-49b2c9d34b95
2025-08-25 15:55:42,859 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:55:42,868 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:55:42,872 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 15:55:42,890 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 15:55:46,750 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:55:46,780 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:55:46,782 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:56:38,303 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:56:38,309 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:56:38,312 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:58:23,926 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:58:23,954 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:58:23,956 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 15:58:25,617 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 15:58:25,626 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 15:58:25,628 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:07:30,475 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:08:07,239 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:08:24,129 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:08:24,147 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:08:24,147 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:08:57,637 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:08:57,655 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:08:57,655 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:09:13,661 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:09:13,678 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:09:13,678 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:09:44,803 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:09:44,822 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:09:44,823 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:10:10,800 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:10:10,815 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:10:10,816 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:10:46,512 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:10:46,517 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:10:46,517 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:10:59,787 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:10:59,831 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:10:59,832 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:11:31,550 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:11:31,568 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:11:31,568 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:19:51,352 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 16:19:51,374 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 16:19:51,376 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:19:54,166 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:19:54,173 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:19:54,174 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:19:56,759 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:19:56,759 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:19:56,781 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:19:56,782 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:19:56,783 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:19:56,783 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:19:56,784 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:19:56,784 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:19:56,784 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:19:56,785 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:19:56,785 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:19:56,785 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:19:56,785 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:19:56,786 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:19:56,786 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:19:56,787 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:19:56,787 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:19:56,787 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:19:56,788 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:19:56,788 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:19:56,789 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:19:56,789 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:19:56,789 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:19:56,790 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:19:56,791 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:19:56,792 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:19:56,793 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:19:56,793 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:19:56,794 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:19:56,795 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:19:59,837 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:19:59,839 - analysis_executor - ERROR - 创建排队合并分析任务失败: 'CombinedAnalysisExecutor' object has no attribute '_infer_analysis_category'
2025-08-25 16:21:02,550 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:21:02,568 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:21:02,568 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:21:34,212 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:21:34,233 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:21:34,233 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:23:19,813 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:23:19,832 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:23:19,832 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:24:32,628 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:24:32,646 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:24:32,646 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:24:52,485 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:24:52,506 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:24:52,506 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:25:54,257 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:25:54,276 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:25:54,276 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:26:07,485 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:26:07,503 - analysis_executor - INFO - 📋 队列工作线程开始运行
2025-08-25 16:26:07,503 - analysis_executor - INFO - 🔄 合并分析队列工作线程已启动
2025-08-25 16:26:12,817 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171601
2025-08-25 16:26:12,819 - analysis_executor - INFO - 从模型路径推断分析类别: arableLand (匹配关键词: arableland)
2025-08-25 16:26:12,820 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:26:12,821 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\20250705171601_1_1756110372.shp
2025-08-25 16:26:12,821 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\20250705171601_2_1756110372.shp
2025-08-25 16:26:12,822 - analysis_executor - INFO - 🔄 创建排队合并分析任务: 89cbd6c8-a0be-405b-8da1-7e63807f04ee
2025-08-25 16:26:12,848 - analysis_executor - INFO -    影像ID: 20250705171601
2025-08-25 16:26:12,849 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 16:26:12,851 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\TaskInfo.json
2025-08-25 16:26:12,867 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: 89cbd6c8-a0be-405b-8da1-7e63807f04ee
2025-08-25 16:26:12,867 - analysis_executor - INFO - 🚀 开始执行队列任务: 89cbd6c8-a0be-405b-8da1-7e63807f04ee
2025-08-25 16:26:12,880 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:26:12,883 - analysis_executor - ERROR - ❌ 执行队列任务失败: 89cbd6c8-a0be-405b-8da1-7e63807f04ee, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:26:12,884 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:26:12,890 - analysis_executor - INFO - ✅ 队列任务执行完成: 89cbd6c8-a0be-405b-8da1-7e63807f04ee
2025-08-25 16:26:12,906 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171601
2025-08-25 16:26:12,907 - analysis_executor - INFO - 从模型路径推断分析类别: arableLand (匹配关键词: arableland)
2025-08-25 16:26:12,908 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:26:12,909 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\20250705171601_1_1756110372.shp
2025-08-25 16:26:12,909 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\20250705171601_2_1756110372.shp
2025-08-25 16:26:12,910 - analysis_executor - INFO - 🔄 创建排队合并分析任务: f9aa3c5b-0153-4996-a3db-c1c20607ee80
2025-08-25 16:26:12,921 - analysis_executor - INFO -    影像ID: 20250705171601
2025-08-25 16:26:12,922 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 16:26:12,924 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\TaskInfo.json
2025-08-25 16:26:12,935 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: f9aa3c5b-0153-4996-a3db-c1c20607ee80
2025-08-25 16:26:12,935 - analysis_executor - INFO - 🚀 开始执行队列任务: f9aa3c5b-0153-4996-a3db-c1c20607ee80
2025-08-25 16:26:12,943 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:26:12,944 - analysis_executor - ERROR - ❌ 执行队列任务失败: f9aa3c5b-0153-4996-a3db-c1c20607ee80, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:26:12,944 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:26:12,948 - analysis_executor - INFO - ✅ 队列任务执行完成: f9aa3c5b-0153-4996-a3db-c1c20607ee80
2025-08-25 16:26:12,955 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171602
2025-08-25 16:26:12,956 - analysis_executor - INFO - 从模型路径推断分析类别: arableLand (匹配关键词: arableland)
2025-08-25 16:26:12,957 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:26:12,957 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171602/arableLand\20250705171602_1_1756110372.shp
2025-08-25 16:26:12,957 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171602/arableLand\20250705171602_2_1756110372.shp
2025-08-25 16:26:12,958 - analysis_executor - INFO - 🔄 创建排队合并分析任务: 67d98dba-f004-413d-b2bf-3b8865bb9b85
2025-08-25 16:26:12,968 - analysis_executor - INFO -    影像ID: 20250705171602
2025-08-25 16:26:12,968 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 16:26:12,970 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171602/arableLand\TaskInfo.json
2025-08-25 16:26:12,976 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: 67d98dba-f004-413d-b2bf-3b8865bb9b85
2025-08-25 16:26:12,977 - analysis_executor - INFO - 🚀 开始执行队列任务: 67d98dba-f004-413d-b2bf-3b8865bb9b85
2025-08-25 16:26:12,986 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:26:12,988 - analysis_executor - ERROR - ❌ 执行队列任务失败: 67d98dba-f004-413d-b2bf-3b8865bb9b85, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:26:12,988 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:26:12,990 - analysis_executor - INFO - ✅ 队列任务执行完成: 67d98dba-f004-413d-b2bf-3b8865bb9b85
2025-08-25 16:26:12,998 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171603
2025-08-25 16:26:12,998 - analysis_executor - INFO - 从模型路径推断分析类别: arableLand (匹配关键词: arableland)
2025-08-25 16:26:13,000 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:26:13,000 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171603/arableLand\20250705171603_1_1756110372.shp
2025-08-25 16:26:13,000 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171603/arableLand\20250705171603_2_1756110372.shp
2025-08-25 16:26:13,001 - analysis_executor - INFO - 🔄 创建排队合并分析任务: 178a383d-5546-41fe-bb5c-4afd8b9d5506
2025-08-25 16:26:13,007 - analysis_executor - INFO -    影像ID: 20250705171603
2025-08-25 16:26:13,007 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 16:26:13,008 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171603/arableLand\TaskInfo.json
2025-08-25 16:26:13,018 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: 178a383d-5546-41fe-bb5c-4afd8b9d5506
2025-08-25 16:26:13,018 - analysis_executor - INFO - 🚀 开始执行队列任务: 178a383d-5546-41fe-bb5c-4afd8b9d5506
2025-08-25 16:26:13,028 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:26:13,032 - analysis_executor - ERROR - ❌ 执行队列任务失败: 178a383d-5546-41fe-bb5c-4afd8b9d5506, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:26:13,033 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:26:13,037 - analysis_executor - INFO - ✅ 队列任务执行完成: 178a383d-5546-41fe-bb5c-4afd8b9d5506
2025-08-25 16:26:13,053 - analysis_executor - INFO - 收到取消任务请求: 178a383d-5546-41fe-bb5c-4afd8b9d5506
2025-08-25 16:26:13,070 - analysis_executor - INFO - 收到取消任务请求: fake-task-id-12345
2025-08-25 16:28:12,195 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171601
2025-08-25 16:28:12,196 - analysis_executor - INFO - 从模型路径推断分析类别: arableLand (匹配关键词: arableland)
2025-08-25 16:28:12,197 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:28:12,198 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\20250705171601_1_1756110492.shp
2025-08-25 16:28:12,199 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\20250705171601_2_1756110492.shp
2025-08-25 16:28:12,200 - analysis_executor - INFO - 🔄 创建排队合并分析任务: 80ad7175-2277-4788-abc6-0ab7f2469378
2025-08-25 16:28:12,210 - analysis_executor - INFO -    影像ID: 20250705171601
2025-08-25 16:28:12,211 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 16:28:12,214 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171601/arableLand\TaskInfo.json
2025-08-25 16:28:12,228 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: 80ad7175-2277-4788-abc6-0ab7f2469378
2025-08-25 16:28:12,228 - analysis_executor - INFO - 🚀 开始执行队列任务: 80ad7175-2277-4788-abc6-0ab7f2469378
2025-08-25 16:28:12,239 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:28:12,241 - analysis_executor - ERROR - ❌ 执行队列任务失败: 80ad7175-2277-4788-abc6-0ab7f2469378, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:28:12,242 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:28:12,245 - analysis_executor - INFO - ✅ 队列任务执行完成: 80ad7175-2277-4788-abc6-0ab7f2469378
2025-08-25 16:28:30,649 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 16:28:30,660 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 16:28:30,661 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:31,960 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 16:28:31,972 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 16:28:31,974 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:47,521 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:47,550 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:47,551 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:48,893 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:28:48,896 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:28:48,903 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:28:48,904 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:28:48,905 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:28:48,906 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:28:48,907 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:28:48,907 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:28:48,914 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:28:48,915 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:28:48,916 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:28:48,918 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:28:48,918 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:28:48,918 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:28:48,919 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:28:48,921 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:28:48,921 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:28:48,921 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:28:48,922 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:28:48,923 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:28:48,923 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:28:48,924 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:28:48,935 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:28:48,936 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:28:48,937 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:28:48,937 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:28:48,938 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:28:48,939 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:28:48,939 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:28:48,939 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:28:51,636 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:28:51,639 - analysis_executor - INFO - 从模型路径推断分析类别: arableLand (匹配关键词: arableland)
2025-08-25 16:28:51,640 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:28:51,641 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand\20250705171599_1_1756110531.shp
2025-08-25 16:28:51,642 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand\20250705171599_2_1756110531.shp
2025-08-25 16:28:51,642 - analysis_executor - INFO - 🔄 创建排队合并分析任务: a107d9e3-7415-44ee-92a9-30f15ed21175
2025-08-25 16:28:51,666 - analysis_executor - INFO -    影像ID: 20250705171599
2025-08-25 16:28:51,667 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 16:28:51,668 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171599/arableLand\TaskInfo.json
2025-08-25 16:28:51,684 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: a107d9e3-7415-44ee-92a9-30f15ed21175
2025-08-25 16:28:51,684 - analysis_executor - INFO - 🚀 开始执行队列任务: a107d9e3-7415-44ee-92a9-30f15ed21175
2025-08-25 16:28:51,737 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:28:51,741 - analysis_executor - ERROR - ❌ 执行队列任务失败: a107d9e3-7415-44ee-92a9-30f15ed21175, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:28:51,741 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:28:51,745 - analysis_executor - INFO - ✅ 队列任务执行完成: a107d9e3-7415-44ee-92a9-30f15ed21175
2025-08-25 16:28:51,768 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:51,799 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:51,801 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:53,687 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:53,695 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:53,697 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:54,783 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:54,806 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:54,808 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:55,846 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:55,854 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:55,855 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:56,174 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:56,199 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:56,202 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:28:56,624 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:28:56,651 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:28:56,653 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:29:18,271 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:29:18,280 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:29:18,282 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:29:18,879 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:29:18,908 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:29:18,911 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:29:19,360 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:29:19,391 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:29:19,394 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:29:21,479 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:29:21,482 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:29:21,488 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:29:21,490 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:29:21,490 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:29:21,491 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:29:21,492 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:29:21,492 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:29:21,494 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:29:21,502 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:29:21,502 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:29:21,504 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:29:21,504 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:29:21,505 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:29:21,506 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:29:21,507 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:29:21,508 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:29:21,510 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:29:21,512 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:29:21,513 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:29:21,513 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:29:21,514 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:29:21,516 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:29:21,517 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:29:21,517 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:29:21,518 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:29:21,519 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:29:21,520 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:29:21,521 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:29:21,521 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:29:24,082 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:29:24,083 - analysis_executor - INFO - 从模型路径推断分析类别: constructionLand (匹配关键词: constructionland)
2025-08-25 16:29:24,085 - analysis_executor - INFO - 构建输出路径:
2025-08-25 16:29:24,085 - analysis_executor - INFO -   AI分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171599/constructionLand\20250705171599_1_1756110564.shp
2025-08-25 16:29:24,086 - analysis_executor - INFO -   最终分析结果: D:/Drone_Project/nginxData/ODM/AI/20250705171599/constructionLand\20250705171599_2_1756110564.shp
2025-08-25 16:29:24,087 - analysis_executor - INFO - 🔄 创建排队合并分析任务: 29739ed7-e21f-4724-a0ee-e582596dc09a
2025-08-25 16:29:24,098 - analysis_executor - INFO -    影像ID: 20250705171599
2025-08-25 16:29:24,098 - analysis_executor - INFO -    分析类别: constructionLand
2025-08-25 16:29:24,100 - analysis_executor - INFO - ✅ 等待中TaskInfo.json模板已创建: D:/Drone_Project/nginxData/ODM/AI/20250705171599/constructionLand\TaskInfo.json
2025-08-25 16:29:24,156 - analysis_executor - INFO - ✅ 排队合并分析任务已创建: 29739ed7-e21f-4724-a0ee-e582596dc09a
2025-08-25 16:29:24,156 - analysis_executor - INFO - 🚀 开始执行队列任务: 29739ed7-e21f-4724-a0ee-e582596dc09a
2025-08-25 16:29:24,166 - analysis_executor - INFO -    队列位置: 1
2025-08-25 16:29:24,180 - analysis_executor - ERROR - ❌ 执行队列任务失败: 29739ed7-e21f-4724-a0ee-e582596dc09a, 错误: _create_task_logger() takes 2 positional arguments but 3 were given
2025-08-25 16:29:24,181 - analysis_executor - INFO -    当前执行: 无
2025-08-25 16:29:24,190 - analysis_executor - INFO - ✅ 队列任务执行完成: 29739ed7-e21f-4724-a0ee-e582596dc09a
2025-08-25 16:29:24,200 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:29:24,232 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:29:24,234 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:30:40,675 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:34:30,966 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:35:14,687 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:35:51,311 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:36:06,237 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:43:48,098 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:43:48,111 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:43:48,113 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:43:49,267 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:43:49,269 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:43:49,277 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:43:49,278 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:43:49,279 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:43:49,280 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:43:49,281 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:43:49,281 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:43:49,282 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:43:49,283 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:43:49,283 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:43:49,285 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:43:49,286 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:43:49,286 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:43:49,288 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:43:49,289 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:43:49,291 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:43:49,292 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:43:49,294 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:43:49,295 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:43:49,296 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:43:49,296 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:43:49,297 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:43:49,299 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:43:49,303 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:43:49,304 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:43:49,306 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:43:49,307 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:43:49,308 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:43:49,308 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:43:55,234 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:43:55,296 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:43:55,302 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:43:55,304 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:43:56,903 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:43:56,912 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:43:56,913 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:43:57,783 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:43:57,790 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:43:57,791 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:43:59,703 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:43:59,731 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:43:59,732 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:45:30,161 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:45:43,757 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:47:38,953 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:47:38,959 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:47:38,966 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:47:38,967 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:47:38,967 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:47:38,968 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:47:38,969 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:47:38,969 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:47:38,970 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:47:38,971 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:47:38,973 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:47:38,974 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:47:38,980 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:47:38,994 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:47:38,995 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:47:38,995 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:47:38,996 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:47:38,996 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:47:38,996 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:47:38,997 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:47:38,997 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:47:38,998 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:47:38,998 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:47:38,999 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:47:39,000 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:47:39,000 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:47:39,001 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:47:39,002 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:47:39,002 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:47:39,002 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:47:42,956 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:47:42,976 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 16:47:42,984 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 16:47:43,005 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:47:43,062 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:47:43,063 - analysis_executor - INFO - 开始合并分析任务: 86008431-11ee-4589-99fe-0299e460076e
2025-08-25 16:47:43,126 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:47:43,249 - analysis_executor - INFO - 合并分析任务已启动: 86008431-11ee-4589-99fe-0299e460076e
2025-08-25 16:47:43,358 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:47:44,318 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:47:44,327 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:47:44,328 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 3 个任务
2025-08-25 16:47:47,441 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:47:47,465 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:47:47,467 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 3 个任务
2025-08-25 16:47:53,175 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:47:53,178 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:47:53,199 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:47:53,203 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:47:53,204 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:47:53,208 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:47:53,209 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:47:53,209 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:47:53,210 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:47:53,210 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:47:53,211 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:47:53,211 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:47:53,212 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:47:53,212 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:47:53,213 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:47:53,225 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:47:53,297 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:47:53,300 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:47:53,302 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:47:53,303 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:47:53,304 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:47:53,305 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:47:53,306 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:47:53,308 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:47:53,309 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:47:53,310 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:47:53,311 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:47:53,311 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:47:53,311 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:47:53,312 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:47:56,427 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:47:56,479 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 16:47:56,486 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 16:47:56,489 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:47:56,581 - analysis_executor - INFO - 开始合并分析任务: 5fa78617-756d-4da8-aac9-63f51226d24d
2025-08-25 16:47:56,581 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:47:56,583 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 3 个任务
2025-08-25 16:47:56,662 - analysis_executor - INFO - 合并分析任务已启动: 5fa78617-756d-4da8-aac9-63f51226d24d
2025-08-25 16:47:56,790 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:47:57,972 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:47:57,991 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:47:58,001 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:47:59,127 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:47:59,138 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:47:59,139 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:48:10,128 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:48:10,142 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:48:10,145 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:48:11,287 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:48:11,300 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:48:11,302 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:51:17,266 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:51:19,674 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:51:19,688 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:51:19,691 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:51:24,243 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:51:24,252 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:51:24,253 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:53:12,382 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:53:12,394 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:53:12,396 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:53:13,680 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:53:13,687 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:53:13,689 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:53:19,876 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 16:53:19,885 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 16:53:19,912 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 16:53:23,281 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:53:23,290 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:53:23,291 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:54:18,344 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:54:18,344 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:54:18,371 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:54:18,373 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:54:18,374 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:54:18,375 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:54:18,376 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:54:18,376 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:54:18,376 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:54:18,377 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:54:18,377 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:54:18,377 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:54:18,378 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:54:18,378 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:54:18,379 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:54:18,379 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:54:18,380 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:54:18,381 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:54:18,382 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:54:18,383 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:54:18,392 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:54:18,393 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:54:18,393 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:54:18,394 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:54:18,395 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:54:18,396 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:54:18,397 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:54:18,398 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:54:18,399 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:54:18,400 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:54:24,245 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:54:24,457 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 16:54:24,465 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 16:54:24,527 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:54:24,605 - analysis_executor - INFO - 开始合并分析任务: 41a89329-2f72-405a-b9ba-0e84e55f0b6e
2025-08-25 16:54:24,607 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:54:24,615 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 16:54:24,712 - analysis_executor - INFO - 合并分析任务已启动: 41a89329-2f72-405a-b9ba-0e84e55f0b6e
2025-08-25 16:54:24,830 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:54:29,480 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:54:29,499 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:54:29,501 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 16:54:31,992 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:54:31,996 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:54:32,013 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:54:32,015 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:54:32,016 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:54:32,017 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:54:32,017 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:54:32,018 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:54:32,018 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:54:32,019 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:54:32,020 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:54:32,020 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:54:32,021 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:54:32,021 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:54:32,022 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:54:32,023 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:54:32,025 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:54:32,038 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:54:32,039 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:54:32,040 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:54:32,041 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:54:32,042 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:54:32,043 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:54:32,044 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:54:32,045 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:54:32,046 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:54:32,047 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:54:32,047 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:54:32,048 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:54:32,048 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:54:35,492 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:54:35,722 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 16:54:35,728 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 16:54:35,760 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:54:35,878 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:54:35,880 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 16:54:35,885 - analysis_executor - INFO - 开始合并分析任务: 991c84e4-a8ea-4987-85d5-29f4f97eab6b
2025-08-25 16:54:35,978 - analysis_executor - INFO - 合并分析任务已启动: 991c84e4-a8ea-4987-85d5-29f4f97eab6b
2025-08-25 16:54:36,105 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:54:36,583 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:54:36,593 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:54:36,594 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 16:56:06,690 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:56:06,706 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:56:06,716 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 16:56:21,937 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:56:28,314 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 16:56:28,323 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 16:56:28,324 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 16:56:31,946 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:56:31,954 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:56:31,956 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 16:56:52,139 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:57:08,114 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:57:21,068 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:57:43,757 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:58:00,105 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:58:45,395 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:58:45,415 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:58:45,416 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 16:59:34,764 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:34,773 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:34,775 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 16:59:39,976 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:59:40,019 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:59:40,070 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:59:40,105 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:59:40,161 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:59:40,208 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:59:40,287 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:59:40,288 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:59:40,289 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:59:40,290 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:59:40,291 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:59:40,292 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:59:40,293 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:59:40,294 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:59:40,299 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:59:40,300 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:59:40,301 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:59:40,302 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:59:40,303 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:59:40,304 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:59:40,304 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:59:40,305 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:59:40,306 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:59:40,308 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:59:40,326 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:59:40,328 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:59:40,329 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:59:40,330 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:59:40,332 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:59:40,332 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:59:43,694 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:59:43,983 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:43,995 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:44,096 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 16:59:44,098 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 16:59:44,100 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 16:59:44,168 - analysis_executor - INFO - 开始合并分析任务: ca080027-bc4e-4c4c-b055-7e8b9fa8f924
2025-08-25 16:59:44,274 - analysis_executor - INFO - 合并分析任务已启动: ca080027-bc4e-4c4c-b055-7e8b9fa8f924
2025-08-25 16:59:44,367 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:59:45,174 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 16:59:45,177 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 16:59:45,185 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 16:59:45,190 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 16:59:45,191 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 16:59:45,194 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 16:59:45,197 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 16:59:45,199 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 16:59:45,200 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 16:59:45,202 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 16:59:45,212 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 16:59:45,213 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 16:59:45,214 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 16:59:45,216 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 16:59:45,217 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 16:59:45,219 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:59:45,220 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 16:59:45,221 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 16:59:45,222 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 16:59:45,223 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 16:59:45,224 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 16:59:45,231 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 16:59:45,233 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 16:59:45,243 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 16:59:45,244 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 16:59:45,245 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 16:59:45,249 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 16:59:45,250 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 16:59:45,251 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 16:59:45,251 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 16:59:47,884 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 16:59:48,161 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:48,282 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:48,394 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 16:59:48,397 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 7 个任务
2025-08-25 16:59:48,402 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 16:59:48,409 - analysis_executor - INFO - 开始合并分析任务: 352b73b7-89bb-45ff-8df9-ec4780fa97c2
2025-08-25 16:59:48,641 - analysis_executor - INFO - 合并分析任务已启动: 352b73b7-89bb-45ff-8df9-ec4780fa97c2
2025-08-25 16:59:48,777 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:48,789 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:48,894 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 16:59:48,895 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 7 个任务
2025-08-25 16:59:50,082 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:50,097 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:50,099 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 8 个任务
2025-08-25 16:59:50,921 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:50,932 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:50,934 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 8 个任务
2025-08-25 16:59:52,175 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 16:59:52,185 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 16:59:52,190 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 8 个任务
2025-08-25 17:01:00,360 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:01:00,364 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:01:00,370 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:01:00,373 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:01:00,376 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:01:00,377 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:01:00,378 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:01:00,378 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:01:00,378 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:01:00,379 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:01:00,379 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:01:00,381 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:01:00,381 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:01:00,382 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:01:00,383 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:01:00,384 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:01:00,385 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:01:00,386 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:01:00,387 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:01:00,389 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:01:00,401 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:01:00,402 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:01:00,403 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:01:00,404 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:01:00,405 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:01:00,405 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:01:00,407 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:01:00,407 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:01:00,407 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:01:00,408 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:01:03,524 - analysis_executor - INFO - 收到排队合并分析请求: 20250705171599
2025-08-25 17:01:03,635 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:01:03,739 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:01:03,743 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:01:03,745 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 8 个任务
2025-08-25 17:01:03,846 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:01:03,860 - analysis_executor - INFO - 开始合并分析任务: 99f079ea-f889-4f35-988b-819446fc0466
2025-08-25 17:01:03,957 - analysis_executor - INFO - 合并分析任务已启动: 99f079ea-f889-4f35-988b-819446fc0466
2025-08-25 17:01:04,199 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:01:12,985 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:01:12,998 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:01:13,000 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:02:28,486 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:02:28,491 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:02:28,500 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:02:28,502 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:02:28,503 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:02:28,504 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:02:28,504 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:02:28,514 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:02:28,515 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:02:28,517 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:02:28,519 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:02:28,521 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:02:28,522 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:02:28,523 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:02:28,525 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:02:28,526 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:02:28,529 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:02:28,529 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:02:28,534 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:02:28,535 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:02:28,536 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:02:28,554 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:02:28,555 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:02:28,558 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:02:28,559 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:02:28,560 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:02:28,562 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:02:28,564 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:02:28,566 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:02:28,568 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:05:03,781 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:06:37,331 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:06:37,360 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:06:37,361 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 4 个任务
2025-08-25 17:07:37,551 - analysis_executor - INFO - 🚨🚨🚨 QUEUED_INTERFACE_CALLED 🚨🚨🚨
2025-08-25 17:07:37,581 - analysis_executor - INFO - 📞 排队合并分析接口被调用
2025-08-25 17:07:37,595 - analysis_executor - INFO - 📋 接收到的参数:
2025-08-25 17:07:37,609 - analysis_executor - INFO -    影像ID: None
2025-08-25 17:07:37,610 - analysis_executor - INFO -    影像路径: None
2025-08-25 17:07:37,610 - analysis_executor - INFO -    模型路径: None
2025-08-25 17:07:37,611 - analysis_executor - INFO -    老数据路径: None
2025-08-25 17:07:37,612 - analysis_executor - INFO -    面积阈值: 400.0
2025-08-25 17:07:37,612 - analysis_executor - INFO -    模型类型: deeplabv3_plus
2025-08-25 17:07:37,613 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 17:07:37,614 - analysis_executor - ERROR - ❌ 缺少必要参数
2025-08-25 17:07:37,655 - analysis_executor - INFO - 🚨🚨🚨 QUEUED_INTERFACE_CALLED 🚨🚨🚨
2025-08-25 17:07:38,825 - analysis_executor - INFO - 📞 排队合并分析接口被调用
2025-08-25 17:07:41,360 - analysis_executor - INFO - 📋 接收到的参数:
2025-08-25 17:07:44,002 - analysis_executor - INFO -    影像ID: 20250705171599
2025-08-25 17:07:44,040 - analysis_executor - INFO -    影像路径: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:07:44,080 - analysis_executor - INFO -    模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:07:44,118 - analysis_executor - INFO -    老数据路径: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:07:44,217 - analysis_executor - INFO -    面积阈值: 200.0
2025-08-25 17:07:44,392 - analysis_executor - INFO -    模型类型: deeplabv3_plus
2025-08-25 17:07:44,477 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 17:07:44,502 - analysis_executor - INFO - ✅ 参数验证通过，开始处理排队合并分析请求: 20250705171599
2025-08-25 17:07:46,175 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:07:46,180 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:07:46,238 - analysis_executor - INFO - 开始合并分析任务: 12cb680a-0308-4f70-a48f-7ce2b23d7204
2025-08-25 17:07:46,571 - analysis_executor - INFO - 合并分析任务已启动: 12cb680a-0308-4f70-a48f-7ce2b23d7204
2025-08-25 17:07:46,642 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:07:48,083 - analysis_executor - INFO - 🚨🚨🚨 QUEUED_INTERFACE_CALLED 🚨🚨🚨
2025-08-25 17:07:48,295 - analysis_executor - INFO - 📞 排队合并分析接口被调用
2025-08-25 17:07:48,314 - analysis_executor - INFO - 📋 接收到的参数:
2025-08-25 17:07:48,335 - analysis_executor - INFO -    影像ID: 20250705171598
2025-08-25 17:07:48,336 - analysis_executor - INFO -    影像路径: D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:48,338 - analysis_executor - INFO -    模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:07:48,339 - analysis_executor - INFO -    老数据路径: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:07:48,340 - analysis_executor - INFO -    面积阈值: 200.0
2025-08-25 17:07:48,341 - analysis_executor - INFO -    模型类型: deeplabv3_plus
2025-08-25 17:07:48,343 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 17:07:48,344 - analysis_executor - INFO - ✅ 参数验证通过，开始处理排队合并分析请求: 20250705171598
2025-08-25 17:07:48,847 - analysis_executor - INFO - 收到队列状态查询请求
2025-08-25 17:07:48,945 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:07:48,960 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171598, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171598/20250705171598_out.tif
2025-08-25 17:07:49,012 - analysis_executor - INFO - 开始合并分析任务: 817b1e4d-8cc9-41d2-9667-607cae3de451
2025-08-25 17:07:49,311 - analysis_executor - INFO - 合并分析任务已启动: 817b1e4d-8cc9-41d2-9667-607cae3de451
2025-08-25 17:07:49,629 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:08:48,258 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:09:37,147 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:09:37,164 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:09:37,515 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:09:37,537 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:09:37,554 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:09:37,697 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:09:37,727 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:09:37,739 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:09:37,766 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:09:37,794 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:09:37,815 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:10:16,242 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:10:16,445 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:10:16,569 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:10:29,466 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:10:54,474 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171600
2025-08-25 17:10:54,483 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171600\TaskInfo.json
2025-08-25 17:10:54,485 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 1 个任务
2025-08-25 17:10:57,483 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:10:57,497 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:10:57,498 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:11:07,856 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:11:07,858 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:11:07,888 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:11:07,891 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:11:07,892 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:11:07,892 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:11:07,893 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:11:07,893 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:11:07,894 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:11:07,894 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:11:07,895 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:11:07,895 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:11:07,896 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:11:07,896 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:11:07,897 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:11:07,898 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:11:07,898 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:11:07,899 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:11:07,899 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:11:07,900 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:11:07,900 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:11:07,911 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:11:07,912 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:11:07,913 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:11:07,914 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:11:07,914 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:11:07,917 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:11:07,917 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:11:07,918 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:11:07,918 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:11:10,894 - analysis_executor - INFO - 🚨🚨🚨 QUEUED_INTERFACE_CALLED 🚨🚨🚨
2025-08-25 17:11:10,949 - analysis_executor - INFO - 📞 排队合并分析接口被调用
2025-08-25 17:11:11,002 - analysis_executor - INFO - 📋 接收到的参数:
2025-08-25 17:11:11,024 - analysis_executor - INFO -    影像ID: 20250705171599
2025-08-25 17:11:11,025 - analysis_executor - INFO -    影像路径: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:11:11,025 - analysis_executor - INFO -    模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:11:11,027 - analysis_executor - INFO -    老数据路径: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:11:11,027 - analysis_executor - INFO -    面积阈值: 200.0
2025-08-25 17:11:11,029 - analysis_executor - INFO -    模型类型: deeplabv3_plus
2025-08-25 17:11:11,031 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 17:11:11,031 - analysis_executor - INFO - ✅ 参数验证通过，开始处理排队合并分析请求: 20250705171599
2025-08-25 17:11:11,173 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:11:11,252 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:11:11,398 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:11:11,399 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 5 个任务
2025-08-25 17:11:11,405 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:11:11,416 - analysis_executor - INFO - 开始合并分析任务: 4c8c570b-1d94-42f5-9fc8-d65373644fed
2025-08-25 17:11:11,525 - analysis_executor - INFO - 合并分析任务已启动: 4c8c570b-1d94-42f5-9fc8-d65373644fed
2025-08-25 17:11:11,664 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:11:12,521 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:11:12,531 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:11:12,533 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 17:11:13,504 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:11:13,508 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:11:13,515 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:11:13,516 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:11:13,517 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:11:13,518 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:11:13,520 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:11:13,520 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:11:13,522 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:11:13,523 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:11:13,525 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:11:13,536 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:11:13,537 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:11:13,538 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:11:13,540 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:11:13,542 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:11:13,545 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:11:13,546 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:11:13,548 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:11:13,550 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:11:13,551 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:11:13,552 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:11:13,553 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:11:13,558 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:11:13,568 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:11:13,570 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:11:13,574 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:11:13,576 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:11:13,577 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:11:13,578 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:11:16,487 - analysis_executor - INFO - 🚨🚨🚨 QUEUED_INTERFACE_CALLED 🚨🚨🚨
2025-08-25 17:11:16,542 - analysis_executor - INFO - 📞 排队合并分析接口被调用
2025-08-25 17:11:16,678 - analysis_executor - INFO - 📋 接收到的参数:
2025-08-25 17:11:16,757 - analysis_executor - INFO -    影像ID: 20250705171599
2025-08-25 17:11:16,757 - analysis_executor - INFO -    影像路径: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:11:16,758 - analysis_executor - INFO -    模型路径: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:11:16,758 - analysis_executor - INFO -    老数据路径: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:11:16,760 - analysis_executor - INFO -    面积阈值: 200.0
2025-08-25 17:11:16,760 - analysis_executor - INFO -    模型类型: deeplabv3_plus
2025-08-25 17:11:16,760 - analysis_executor - INFO -    分析类别: arableLand
2025-08-25 17:11:16,761 - analysis_executor - INFO - ✅ 参数验证通过，开始处理排队合并分析请求: 20250705171599
2025-08-25 17:11:16,890 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:11:16,903 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:11:16,987 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 17:11:17,078 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:11:17,437 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:11:17,502 - analysis_executor - INFO - 开始合并分析任务: 827240ee-b546-4dba-8f7e-8c4cf4dd36fe
2025-08-25 17:11:17,529 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:11:17,781 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:11:17,828 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 6 个任务
2025-08-25 17:11:17,979 - analysis_executor - INFO - 合并分析任务已启动: 827240ee-b546-4dba-8f7e-8c4cf4dd36fe
2025-08-25 17:11:18,243 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:11:18,925 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:11:18,935 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:11:18,938 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 7 个任务
2025-08-25 17:13:45,789 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:15:15,175 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:15:57,987 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:18:29,305 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 17:18:29,316 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 17:18:29,318 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 17:18:30,474 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-25 17:18:30,486 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-25 17:18:30,487 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-25 17:20:16,627 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:21:52,053 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:22:37,447 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:23:54,683 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:23:54,692 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:23:54,693 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 7 个任务
2025-08-25 17:24:09,808 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:24:09,810 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:24:09,836 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:24:09,838 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:24:09,838 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:24:09,839 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:24:09,840 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:24:09,841 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:24:09,841 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:24:09,842 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:24:09,842 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:24:09,843 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:24:09,843 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:24:09,844 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:24:09,844 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:24:09,845 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:24:09,846 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:24:09,846 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:24:09,847 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:24:09,847 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:24:09,859 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:24:09,860 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:24:09,861 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:24:09,862 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:24:09,862 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:24:09,863 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:24:09,864 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:24:09,865 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:24:09,865 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:24:09,866 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:24:14,660 - analysis_executor - INFO - 收到合并分析请求
2025-08-25 17:24:14,660 - analysis_executor - INFO - 影像ID: 20250705171599
2025-08-25 17:24:14,661 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:24:14,662 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:24:14,663 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
2025-08-25 17:24:14,663 - analysis_executor - INFO - 面积阈值: 400.0 平方米
2025-08-25 17:24:14,664 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-25 17:24:14,664 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:24:14,717 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:24:14,791 - analysis_executor - INFO - 开始合并分析任务: aaf53486-73fa-4f35-bf3b-403f35107f84
2025-08-25 17:24:15,106 - analysis_executor - INFO - 合并分析任务已启动: aaf53486-73fa-4f35-bf3b-403f35107f84
2025-08-25 17:24:15,185 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:24:15,192 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:24:15,254 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 8 个任务
2025-08-25 17:24:15,359 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:24:35,105 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:24:35,114 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:24:35,115 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:24:38,600 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:24:38,817 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:24:38,819 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:24:46,815 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:24:46,823 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:24:46,825 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:25:40,851 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:25:40,864 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:25:40,866 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:28:36,536 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:31:01,794 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:33:23,905 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:23,939 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:23,941 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:33:24,648 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:24,673 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:24,674 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:33:25,128 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:25,145 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:25,147 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:33:25,320 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:25,348 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:25,352 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 9 个任务
2025-08-25 17:33:25,791 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:33:25,795 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:33:25,801 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:33:25,803 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:33:25,803 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:33:25,804 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:33:25,804 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:33:25,804 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:33:25,805 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:33:25,805 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:33:25,805 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:33:25,806 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:33:25,806 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:33:25,807 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:33:25,808 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:33:25,808 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:33:25,808 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:33:25,810 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:33:25,811 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:33:25,812 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:33:25,813 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:33:25,813 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:33:25,814 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:33:25,816 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:33:25,827 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:33:25,827 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:33:25,829 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:33:25,829 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:33:25,830 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:33:25,831 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:33:29,844 - analysis_executor - INFO - 收到合并分析请求
2025-08-25 17:33:29,847 - analysis_executor - INFO - 影像ID: 20250705171599
2025-08-25 17:33:29,848 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:33:29,848 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:33:29,849 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:33:29,849 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-25 17:33:29,850 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-25 17:33:29,850 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:33:29,906 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:33:29,922 - analysis_executor - INFO - 开始合并分析任务: 71d765d8-dfdc-4c4e-bf9c-cb826069c708
2025-08-25 17:33:30,501 - analysis_executor - INFO - 合并分析任务已启动: 71d765d8-dfdc-4c4e-bf9c-cb826069c708
2025-08-25 17:33:30,524 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:30,533 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:30,593 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 10 个任务
2025-08-25 17:33:30,841 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:33:32,434 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:32,462 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:32,464 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:33:33,456 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:33,465 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:33,467 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:33:34,427 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:34,436 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:34,437 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:33:35,985 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:35,994 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:35,996 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:33:51,992 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:33:52,001 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:33:52,003 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:39:26,924 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:42:15,073 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:15,082 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:15,084 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:42:16,169 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:16,175 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:16,177 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:42:16,961 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:16,971 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:16,972 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 11 个任务
2025-08-25 17:42:18,801 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 17:42:18,803 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 17:42:18,808 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 17:42:18,808 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 17:42:18,810 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 17:42:18,811 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 17:42:18,812 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 17:42:18,812 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 17:42:18,813 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 17:42:18,813 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 17:42:18,814 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 17:42:18,814 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 17:42:18,815 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 17:42:18,815 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 17:42:18,816 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 17:42:18,816 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:42:18,817 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 17:42:18,827 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 17:42:18,828 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 17:42:18,829 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:42:18,830 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 17:42:18,830 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 17:42:18,832 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 17:42:18,833 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 17:42:18,834 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 17:42:18,834 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 17:42:18,835 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 17:42:18,835 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 17:42:18,836 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 17:42:18,836 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 17:42:21,348 - analysis_executor - INFO - 收到合并分析请求
2025-08-25 17:42:21,349 - analysis_executor - INFO - 影像ID: 20250705171599
2025-08-25 17:42:21,349 - analysis_executor - INFO - TIF图像: D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:42:21,350 - analysis_executor - INFO - 模型权重: D:/Drone_Project/nginxData/ODM/AIWeight/constructionLand/deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth
2025-08-25 17:42:21,351 - analysis_executor - INFO - 历史数据: D:/Drone_Project/nginxData/ODM/AIOLDSHP/constructionLand/建设2024_84.shp
2025-08-25 17:42:21,351 - analysis_executor - INFO - 面积阈值: 200.0 平方米
2025-08-25 17:42:21,352 - analysis_executor - INFO - 模型类型: deeplabv3_plus
2025-08-25 17:42:21,353 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 17:42:21,429 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 17:42:21,444 - analysis_executor - INFO - 开始合并分析任务: 86c53fb0-5e8d-4c4c-b3d3-039ded5ce27e
2025-08-25 17:42:21,832 - analysis_executor - INFO - 合并分析任务已启动: 86c53fb0-5e8d-4c4c-b3d3-039ded5ce27e
2025-08-25 17:42:21,845 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:21,901 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:21,903 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:22,129 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:42:23,811 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:23,819 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:23,821 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:24,808 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:24,817 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:24,819 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:25,680 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:25,699 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:25,701 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:26,610 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:26,639 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:26,646 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:28,648 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:28,657 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:28,659 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:29,329 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:29,338 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:29,342 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:42:36,768 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:42:36,776 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:42:36,778 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:43:16,617 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:43:16,645 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:43:16,649 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:43:59,837 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:43:59,844 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:43:59,847 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:44:06,171 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:44:06,178 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:44:06,181 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:44:08,914 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:44:08,923 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:44:08,924 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:44:35,690 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 17:44:35,700 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 17:44:35,702 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 17:57:52,237 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:58:13,854 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 17:58:57,247 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:07:36,550 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:08:15,752 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:10:25,630 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:10:25,638 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:25,642 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 12 个任务
2025-08-25 18:10:31,011 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 18:10:31,015 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 18:10:31,038 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 18:10:31,041 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 18:10:31,042 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 18:10:31,044 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 18:10:31,044 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 18:10:31,045 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 18:10:31,046 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 18:10:31,046 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 18:10:31,047 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 18:10:31,047 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 18:10:31,048 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 18:10:31,048 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 18:10:31,049 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 18:10:31,050 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:10:31,061 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 18:10:31,062 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 18:10:31,062 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:10:31,063 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:10:31,063 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 18:10:31,064 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 18:10:31,065 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 18:10:31,066 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 18:10:31,067 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 18:10:31,067 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 18:10:31,068 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 18:10:31,068 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 18:10:31,069 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 18:10:31,069 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 18:10:33,294 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-25 18:10:33,604 - analysis_executor - INFO - ✅ 任务已添加到队列: 04c17f98-1c76-42d7-866f-9b7dcd635319
2025-08-25 18:10:33,627 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 18:10:33,633 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:33,637 - analysis_executor - INFO - 开始合并分析任务: 04c17f98-1c76-42d7-866f-9b7dcd635319
2025-08-25 18:10:33,991 - analysis_executor - INFO - 合并分析任务已启动: 04c17f98-1c76-42d7-866f-9b7dcd635319
2025-08-25 18:10:34,323 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:10:37,482 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:10:37,550 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:37,552 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 13 个任务
2025-08-25 18:10:38,661 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 18:10:38,664 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 18:10:38,671 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 18:10:38,673 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 18:10:38,674 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 18:10:38,675 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 18:10:38,675 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 18:10:38,676 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 18:10:38,678 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 18:10:38,679 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 18:10:38,679 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 18:10:38,681 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 18:10:38,681 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 18:10:38,682 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 18:10:38,683 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 18:10:38,685 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:10:38,706 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 18:10:38,707 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 18:10:38,708 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:10:38,710 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:10:38,711 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 18:10:38,712 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 18:10:38,713 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 18:10:38,715 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 18:10:38,725 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 18:10:38,726 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 18:10:38,729 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 18:10:38,730 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 18:10:38,731 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 18:10:38,732 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 18:10:41,523 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-25 18:10:41,695 - analysis_executor - INFO - ✅ 任务已添加到队列: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:41,743 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 18:10:41,824 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:10:41,831 - analysis_executor - INFO - 开始合并分析任务: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:42,053 - analysis_executor - INFO - 合并分析任务已启动: a78b1bfe-e649-48f3-a586-d631a6c336d0
2025-08-25 18:10:42,183 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:10:42,976 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:10:42,992 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:43,001 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 14 个任务
2025-08-25 18:10:43,922 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:10:43,958 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:10:43,963 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 14 个任务
2025-08-25 18:11:21,595 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:11:21,604 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:11:21,606 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 14 个任务
2025-08-25 18:12:47,452 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:12:47,473 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:12:47,483 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 14 个任务
2025-08-25 18:12:49,328 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:12:49,338 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:12:49,339 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 14 个任务
2025-08-25 18:13:37,573 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:16:04,803 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:16:04,812 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:16:04,814 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 14 个任务
2025-08-25 18:16:06,986 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 18:16:06,991 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 18:16:06,997 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 18:16:07,000 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 18:16:07,000 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 18:16:07,001 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 18:16:07,001 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 18:16:07,002 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 18:16:07,002 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 18:16:07,003 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 18:16:07,005 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 18:16:07,018 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 18:16:07,019 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 18:16:07,022 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 18:16:07,023 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 18:16:07,024 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:16:07,025 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 18:16:07,027 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 18:16:07,028 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:16:07,029 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:16:07,030 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 18:16:07,031 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 18:16:07,032 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 18:16:07,034 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 18:16:07,035 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 18:16:07,036 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 18:16:07,060 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 18:16:07,061 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 18:16:07,062 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 18:16:07,063 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 18:16:10,027 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-25 18:16:10,750 - analysis_executor - INFO - ✅ 任务已添加到队列: 803cdb51-6b01-4918-afc8-ef2a6da5fa8a
2025-08-25 18:16:10,824 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-25 18:16:10,828 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-25 18:16:10,833 - analysis_executor - INFO - 开始合并分析任务: 803cdb51-6b01-4918-afc8-ef2a6da5fa8a
2025-08-25 18:16:11,001 - analysis_executor - INFO - 🔄 队列模式：同步执行合并分析任务: 803cdb51-6b01-4918-afc8-ef2a6da5fa8a
2025-08-25 18:16:11,226 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:16:11,284 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:16:11,287 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 15 个任务
2025-08-25 18:16:11,296 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-25 18:16:12,288 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-25 18:16:12,342 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-25 18:16:12,372 - analysis_executor - INFO - 配置文件读取成功
2025-08-25 18:16:12,394 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-25 18:16:12,416 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-25 18:16:12,422 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-25 18:16:12,428 - analysis_executor - INFO - 发现Default_area配置节
2025-08-25 18:16:12,432 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-25 18:16:12,433 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-25 18:16:12,436 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-25 18:16:12,454 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-25 18:16:12,462 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-25 18:16:12,463 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-25 18:16:12,465 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-25 18:16:12,472 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-25 18:16:12,475 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:16:12,477 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-25 18:16:12,478 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-25 18:16:12,480 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-25 18:16:12,482 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-25 18:16:12,483 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-25 18:16:12,491 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-25 18:16:12,495 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-25 18:16:12,499 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-25 18:16:12,503 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-25 18:16:12,507 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-25 18:16:12,510 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-25 18:16:12,511 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-25 18:16:12,513 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-25 18:16:12,514 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-25 18:16:15,395 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-25 18:16:15,477 - analysis_executor - INFO - ✅ 任务已添加到队列: 3e15dff4-cd3c-477e-a84f-cda230f35b0b
2025-08-25 18:16:16,531 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:16:16,542 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:16:16,545 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 16 个任务
2025-08-25 18:16:17,609 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:16:17,615 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:16:17,618 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 16 个任务
2025-08-25 18:16:24,299 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-25 18:16:24,306 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-25 18:16:24,309 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 16 个任务
2025-08-26 08:22:31,698 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:22:43,491 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:25:38,979 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-26 08:25:39,033 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-26 08:25:39,050 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-26 08:25:44,969 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:25:44,996 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:25:44,998 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 16 个任务
2025-08-26 08:26:09,329 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:26:09,331 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:26:09,360 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:26:09,361 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:26:09,362 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:26:09,363 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:26:09,365 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:26:09,366 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:26:09,367 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:26:09,368 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:26:09,370 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:26:09,371 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:26:09,372 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:26:09,372 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:26:09,373 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:26:09,374 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:26:09,375 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:26:09,375 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:26:09,376 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:26:09,377 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:26:09,377 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:26:09,378 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:26:09,379 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:26:09,380 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:26:09,381 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:26:09,382 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:26:09,384 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:26:09,384 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:26:09,385 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:26:09,385 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:26:18,048 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:26:18,868 - analysis_executor - INFO - ✅ 任务已添加到队列: ab1b158f-ed44-4b46-941b-fd49d04af341
2025-08-26 08:26:18,912 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-26 08:26:19,126 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 08:26:19,136 - analysis_executor - INFO - 开始合并分析任务: ab1b158f-ed44-4b46-941b-fd49d04af341
2025-08-26 08:26:19,151 - analysis_executor - INFO - 🔄 队列模式：同步执行合并分析任务: ab1b158f-ed44-4b46-941b-fd49d04af341
2025-08-26 08:26:19,316 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:26:19,323 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:26:19,325 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:26:19,332 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:26:19,491 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:26:19,501 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:26:19,509 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:26:19,518 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:26:19,527 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:26:19,528 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:26:19,538 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:26:19,539 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:26:19,548 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:26:19,558 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:26:19,561 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:26:19,563 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:26:19,571 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:26:19,581 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:26:19,582 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:26:19,583 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:26:19,584 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:26:19,585 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:26:19,586 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:26:19,587 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:26:19,588 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:26:19,589 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:26:19,590 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:26:19,592 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:26:19,593 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:26:19,594 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:26:19,595 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:26:22,462 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:26:22,651 - analysis_executor - INFO - ✅ 任务已添加到队列: adc3bae4-251a-4d60-ae16-0cf2313cf4f5
2025-08-26 08:26:23,961 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:26:23,969 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:26:23,971 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:26:46,008 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:26:46,013 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:26:46,015 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:27:24,944 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:27:24,974 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:27:24,977 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:31:39,008 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:31:39,010 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:31:39,017 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:31:39,019 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:31:39,020 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:31:39,021 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:31:39,022 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:31:39,023 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:31:39,024 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:31:39,025 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:31:39,027 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:31:39,028 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:31:39,031 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:31:39,032 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:31:39,033 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:31:39,035 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:31:39,036 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:31:39,037 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:31:39,039 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:31:39,040 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:31:39,041 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:31:39,042 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:31:39,043 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:31:39,046 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:31:39,049 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:31:39,050 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:31:39,053 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:31:39,054 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:31:39,055 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:31:39,056 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:31:47,641 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:31:47,659 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:31:47,662 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:31:52,968 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:31:52,993 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:31:52,997 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:31:53,552 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:31:53,578 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:31:53,580 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:33:23,036 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:33:23,041 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:33:23,044 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:33:35,912 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-26 08:33:35,918 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 08:33:35,927 - analysis_executor - INFO - 开始合并分析任务: adc3bae4-251a-4d60-ae16-0cf2313cf4f5
2025-08-26 08:33:36,193 - analysis_executor - INFO - 🔄 队列模式：同步执行合并分析任务: adc3bae4-251a-4d60-ae16-0cf2313cf4f5
2025-08-26 08:33:36,310 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:34:54,815 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:34:54,826 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:34:54,829 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:35:16,979 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:35:16,986 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:35:16,989 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:36:00,254 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:36:00,281 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:36:00,285 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:36:02,949 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:36:02,981 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:36:02,987 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:36:08,630 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:36:08,663 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:36:08,668 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:40:44,837 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:42:28,049 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:42:28,058 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:42:28,060 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 18 个任务
2025-08-26 08:42:30,638 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:42:30,639 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:42:30,657 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:42:30,658 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:42:30,658 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:42:30,659 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:42:30,659 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:42:30,660 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:42:30,661 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:42:30,661 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:42:30,661 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:42:30,662 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:42:30,662 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:42:30,663 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:42:30,664 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:42:30,665 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:42:30,665 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:42:30,666 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:42:30,667 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:42:30,668 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:42:30,668 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:42:30,669 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:42:30,669 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:42:30,670 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:42:30,672 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:42:30,672 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:42:30,673 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:42:30,674 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:42:30,674 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:42:30,674 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:42:33,736 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:42:33,820 - analysis_executor - INFO - ✅ 任务已添加到队列: 9420a641-f1f7-4e34-b382-fa94a5015143
2025-08-26 08:42:33,837 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-26 08:42:33,841 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 08:42:33,848 - analysis_executor - INFO - 开始合并分析任务: 9420a641-f1f7-4e34-b382-fa94a5015143
2025-08-26 08:42:33,854 - analysis_executor - INFO - 🔄 队列模式：同步执行合并分析任务: 9420a641-f1f7-4e34-b382-fa94a5015143
2025-08-26 08:42:33,905 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:42:34,958 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:42:34,961 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:42:34,966 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:42:34,967 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:42:34,969 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:42:34,970 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:42:34,971 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:42:34,972 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:42:34,973 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:42:34,974 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:42:34,974 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:42:34,975 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:42:34,976 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:42:34,976 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:42:34,978 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:42:34,980 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:42:34,982 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:42:34,983 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:42:34,984 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:42:34,985 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:42:34,986 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:42:34,987 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:42:34,988 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:42:34,990 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:42:34,990 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:42:34,991 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:42:34,995 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:42:34,997 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:42:34,998 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:42:34,999 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:42:38,553 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:42:38,593 - analysis_executor - INFO - ✅ 任务已添加到队列: 8aa0585d-cbea-4430-8a24-df6853387ac4
2025-08-26 08:42:39,352 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:42:39,359 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:42:39,361 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 20 个任务
2025-08-26 08:43:13,884 - analysis_executor - INFO - 📥 收到取消任务请求: 8aa0585d-cbea-4430-8a24-df6853387ac4
2025-08-26 08:43:14,294 - analysis_executor - INFO - ✅ 任务已取消: 8aa0585d-cbea-4430-8a24-df6853387ac4
2025-08-26 08:43:27,558 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:43:27,565 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:43:27,566 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 20 个任务
2025-08-26 08:43:28,646 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:43:28,666 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:43:28,667 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 20 个任务
2025-08-26 08:43:40,928 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:43:40,936 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:43:40,938 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 20 个任务
2025-08-26 08:46:51,893 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:48:55,891 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:48:55,921 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:48:55,926 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 20 个任务
2025-08-26 08:49:11,170 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:49:11,173 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:49:11,178 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:49:11,179 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:49:11,180 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:49:11,182 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:49:11,182 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:49:11,182 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:49:11,183 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:49:11,183 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:49:11,183 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:49:11,184 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:49:11,184 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:49:11,185 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:49:11,185 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:49:11,186 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:49:11,186 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:49:11,187 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:49:11,198 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:49:11,198 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:49:11,198 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:49:11,199 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:49:11,200 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:49:11,201 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:49:11,201 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:49:11,202 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:49:11,203 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:49:11,204 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:49:11,204 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:49:11,204 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:49:14,495 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:49:14,749 - analysis_executor - INFO - ✅ 任务已添加到队列: d54a9326-fe5d-46e3-a274-f5fb2deb2f87
2025-08-26 08:49:14,754 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-26 08:49:15,402 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 08:49:15,473 - analysis_executor - INFO - 开始合并分析任务: d54a9326-fe5d-46e3-a274-f5fb2deb2f87
2025-08-26 08:49:15,481 - analysis_executor - INFO - 🔄 队列模式：同步执行合并分析任务: d54a9326-fe5d-46e3-a274-f5fb2deb2f87
2025-08-26 08:49:15,611 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:49:15,614 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:49:15,623 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:49:15,674 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:49:15,675 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:49:15,676 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:49:15,677 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:49:15,679 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:49:15,679 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:49:15,680 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:49:15,680 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:49:15,681 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:49:15,681 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:49:15,681 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:49:15,682 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:49:15,683 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:49:15,683 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:49:15,684 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:49:15,684 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:49:15,685 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:49:15,685 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:49:15,685 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:49:15,686 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:49:15,687 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:49:15,687 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:49:15,688 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:49:15,699 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:49:15,700 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:49:15,700 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:49:15,701 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:49:15,852 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:49:17,829 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:49:17,938 - analysis_executor - INFO - ✅ 任务已添加到队列: 8a0ac0a1-99e3-4dcc-81fa-bbf991c93381
2025-08-26 08:49:18,554 - analysis_executor - INFO - 开始获取AI模型权重信息
2025-08-26 08:49:18,559 - analysis_executor - INFO - 读取配置文件: http://127.0.0.1:81/ODM/Task.cfg
2025-08-26 08:49:18,567 - analysis_executor - INFO - 配置文件读取成功
2025-08-26 08:49:18,569 - analysis_executor - INFO - 获取到window_data_path: D:/Drone_Project/nginxData
2025-08-26 08:49:18,569 - analysis_executor - INFO - 获取到默认权重配置: {'arableland': 'deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth', 'constructionland': 'deeplabv3_plus/deeplabv3_plus_best_20250811-162215.pth'}
2025-08-26 08:49:18,570 - analysis_executor - INFO - 使用默认字段映射配置: {'arableLand': '耕地', 'constructionLand': '建设用地'}
2025-08-26 08:49:18,570 - analysis_executor - INFO - 发现Default_area配置节
2025-08-26 08:49:18,571 - analysis_executor - INFO - 原始默认面积配置: {'arableland': '400', 'constructionland': '200'}
2025-08-26 08:49:18,572 - analysis_executor - INFO - 处理默认面积配置: 'arableland' -> 'arableLand' = '400' (类型: <class 'str'>)
2025-08-26 08:49:18,579 - analysis_executor - INFO - 默认面积转换成功: arableLand = 400.0
2025-08-26 08:49:18,580 - analysis_executor - INFO - 处理默认面积配置: 'constructionland' -> 'constructionLand' = '200' (类型: <class 'str'>)
2025-08-26 08:49:18,580 - analysis_executor - INFO - 默认面积转换成功: constructionLand = 200.0
2025-08-26 08:49:18,581 - analysis_executor - INFO - 最终默认面积配置: {'arableLand': 400.0, 'constructionLand': 200.0}
2025-08-26 08:49:18,581 - analysis_executor - INFO - 权重目录路径: D:/Drone_Project/nginxData\ODM\AIWeight
2025-08-26 08:49:18,582 - analysis_executor - INFO - 扫描地物类型: arableLand
2025-08-26 08:49:18,583 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:49:18,603 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250807-111949.pth
2025-08-26 08:49:18,607 - analysis_executor - INFO - 扫描地物类型: constructionLand
2025-08-26 08:49:18,610 - analysis_executor - INFO -   扫描模型类型: deeplabv3_plus
2025-08-26 08:49:18,626 - analysis_executor - INFO -     发现权重文件: deeplabv3_plus_best_20250811-162215.pth
2025-08-26 08:49:18,630 - analysis_executor - INFO - 权重目录扫描完成，共发现 2 个地物类型
2025-08-26 08:49:18,631 - analysis_executor - INFO - 开始扫描AIOLDSHP目录: D:/Drone_Project/nginxData\ODM\AIOLDSHP
2025-08-26 08:49:18,633 - analysis_executor - INFO - 扫描地物类型目录: arableLand
2025-08-26 08:49:18,638 - analysis_executor - INFO -   发现shp文件: 耕地2024_84.shp
2025-08-26 08:49:18,645 - analysis_executor - INFO -   地物类型 arableLand 共找到 1 个shp文件
2025-08-26 08:49:18,646 - analysis_executor - INFO - 扫描地物类型目录: constructionLand
2025-08-26 08:49:18,653 - analysis_executor - INFO -   发现shp文件: 建设2024_84.shp
2025-08-26 08:49:18,654 - analysis_executor - INFO -   地物类型 constructionLand 共找到 1 个shp文件
2025-08-26 08:49:18,656 - analysis_executor - INFO - AIOLDSHP目录扫描完成，共发现 2 个地物类型
2025-08-26 08:49:18,664 - analysis_executor - INFO - 成功获取权重信息，共 2 个地物类型
2025-08-26 08:49:21,005 - analysis_executor - INFO - 📥 收到队列化合并分析请求: 20250705171599
2025-08-26 08:49:21,117 - analysis_executor - INFO - ✅ 任务已添加到队列: 2487ef65-f327-4aff-a74a-239347bbcd9b
2025-08-26 08:49:22,108 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:49:22,118 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:49:22,120 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 08:50:01,652 - analysis_executor - INFO - 📥 收到取消任务请求: 8a0ac0a1-99e3-4dcc-81fa-bbf991c93381
2025-08-26 08:50:01,925 - analysis_executor - INFO - ✅ 任务已取消: 8a0ac0a1-99e3-4dcc-81fa-bbf991c93381
2025-08-26 08:52:50,748 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:52:50,758 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:52:50,760 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 08:52:55,990 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:52:56,019 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:52:56,022 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 08:53:00,649 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:53:00,679 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:53:00,682 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 08:55:51,616 - analysis_executor - INFO - 🚀🚀🚀 COMBINED_ANALYSIS_EXECUTOR_MODIFIED_VERSION_LOADED 🚀🚀🚀
2025-08-26 08:55:51,619 - analysis_executor - INFO - 🎯 开始执行合并分析，参数: image_id=20250705171599, image_path=D:/Drone_Project/nginxData/ODM/Output/20250705171599/20250705171599_out.tif
2025-08-26 08:55:51,625 - analysis_executor - INFO - 开始合并分析任务: 2487ef65-f327-4aff-a74a-239347bbcd9b
2025-08-26 08:55:51,684 - analysis_executor - INFO - 🔄 队列模式：同步执行合并分析任务: 2487ef65-f327-4aff-a74a-239347bbcd9b
2025-08-26 08:55:51,871 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 08:58:57,240 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:58:57,249 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:58:57,251 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 08:58:59,338 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 08:58:59,364 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 08:58:59,366 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 09:01:10,178 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171601
2025-08-26 09:01:10,205 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171601\TaskInfo.json
2025-08-26 09:01:10,206 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 2 个任务
2025-08-26 09:03:50,911 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 09:03:50,921 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 09:03:50,923 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 09:03:52,263 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 09:03:52,292 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 09:03:52,298 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 09:03:55,551 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 09:03:55,578 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 09:03:55,580 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 09:04:00,710 - analysis_executor - INFO - 获取TaskInfo请求: image_id=20250705171599
2025-08-26 09:04:00,717 - analysis_executor - INFO - TaskInfo文件路径: D:/Drone_Project/nginxData\ODM\AI\20250705171599\TaskInfo.json
2025-08-26 09:04:00,719 - analysis_executor - INFO - 成功读取TaskInfo文件，包含 23 个任务
2025-08-26 09:26:19,694 - analysis_executor - INFO - 加载了 28 个任务状态
2025-08-26 09:28:40,727 - analysis_executor - INFO - 加载了 28 个任务状态
