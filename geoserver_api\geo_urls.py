#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GeoServer 发布API URL配置
"""

from django.urls import path
from .views.geo import geo_views

urlpatterns = [
    # Shapefile发布 - 异步
    path('shapefile/execute/', geo_views.execute_publish_shapefile, name='execute_publish_shapefile'),

    # Shapefile发布 - 同步
    path('shapefile/publish/', geo_views.publish_shapefile_sync, name='publish_shapefile_sync'),

    # Shapefile发布 - 支持指定坐标系
    path('shapefile/publish-with-crs/', geo_views.publish_shapefile_with_crs, name='publish_shapefile_with_crs'),

    # Shapefile批量发布
    path('shapefile/directory/', geo_views.publish_shapefile_directory, name='publish_shapefile_directory'),

    # GeoTIFF异步发布
    path('geotiff/execute/', geo_views.execute_publish_geotiff, name='execute_publish_geotiff'),

    # 特定结构GeoTIFF异步发布
    path('structured-geotiff/execute/', geo_views.execute_publish_structured_geotiff, name='execute_publish_structured_geotiff'),

    # 任务状态查询
    path('status/', geo_views.get_publish_status, name='get_publish_status'),
    path('all/', geo_views.get_all_publish_status, name='get_all_publish_status'),

    # 任务日志查询
    path('log/', geo_views.get_geo_task_log, name='get_geo_task_log'),

    # 自动切片接口
    path('tile/auto/', geo_views.auto_tile_layer, name='auto_tile_layer'),

    # 切片状态查询
    path('tile/status/', geo_views.get_tile_status, name='get_tile_status'),

    # 批量样式上传
    path('styles/batch-upload/', geo_views.batch_upload_styles, name='batch_upload_styles'),
]
