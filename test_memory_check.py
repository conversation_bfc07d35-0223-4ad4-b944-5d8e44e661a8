#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的内存检查和分块计算测试
"""

import os
import sys

def check_dependencies():
    """检查依赖是否安装"""
    print("检查依赖...")
    
    try:
        import psutil
        print(f"✅ psutil: {psutil.__version__}")
        return True
    except ImportError:
        print("❌ psutil未安装")
        print("请运行: pip install psutil")
        return False

def simple_memory_check():
    """简单的内存检查"""
    try:
        import psutil
        
        memory = psutil.virtual_memory()
        print(f"\n=== 系统内存信息 ===")
        print(f"总内存: {memory.total / (1024**3):.1f} GB")
        print(f"可用内存: {memory.available / (1024**3):.1f} GB")
        print(f"已用内存: {memory.used / (1024**3):.1f} GB")
        print(f"内存使用率: {memory.percent:.1f}%")
        
        # 计算可用于处理的内存（80%）
        usable_memory = memory.available * 0.8
        print(f"可用于处理: {usable_memory / (1024**3):.1f} GB")
        
        return usable_memory
        
    except Exception as e:
        print(f"内存检查失败: {e}")
        return None

def calculate_image_memory(height, width, bands=3, dtype_size=8):
    """计算图像内存需求"""
    # 原始图像内存
    image_memory = height * width * bands * dtype_size
    
    # 处理过程中的临时数组（保守估计4倍）
    processing_memory = image_memory * 4
    
    total_memory = image_memory + processing_memory
    
    print(f"\n=== 图像内存需求 ===")
    print(f"图像尺寸: {height}x{width}x{bands}")
    print(f"原始图像: {image_memory / (1024**3):.2f} GB")
    print(f"处理内存: {processing_memory / (1024**3):.2f} GB")
    print(f"总需求: {total_memory / (1024**3):.2f} GB")
    
    return total_memory

def calculate_grid_size(height, width, available_memory, bands=3):
    """计算分块网格大小"""
    print(f"\n=== 计算分块网格 ===")
    
    overlap_pixels = 128
    
    for grid_size in range(1, 11):
        # 计算每个块的大小（包含重叠）
        block_height = (height // grid_size) + overlap_pixels * 2
        block_width = (width // grid_size) + overlap_pixels * 2
        
        # 计算单个块的内存需求
        block_memory = calculate_image_memory(block_height, block_width, bands, 8)
        
        print(f"网格 {grid_size}x{grid_size}:")
        print(f"  块大小: {block_height}x{block_width}")
        print(f"  块内存: {block_memory / (1024**3):.2f} GB")
        
        if block_memory <= available_memory:
            print(f"  ✅ 适合！")
            return grid_size, grid_size
        else:
            print(f"  ❌ 内存不足")
    
    print("需要更大的网格分割")
    return 10, 10

def test_problematic_image():
    """测试问题图像的分块方案"""
    print(f"\n{'='*60}")
    print("测试问题图像: 56283x67180")
    print(f"{'='*60}")
    
    height, width = 67180, 56283
    
    # 检查系统内存
    available_memory = simple_memory_check()
    if available_memory is None:
        return
    
    # 计算图像内存需求
    required_memory = calculate_image_memory(height, width)
    
    print(f"\n=== 内存对比 ===")
    print(f"需要内存: {required_memory / (1024**3):.2f} GB")
    print(f"可用内存: {available_memory / (1024**3):.2f} GB")
    
    if required_memory > available_memory:
        print("❌ 内存不足，需要分块处理")
        
        # 计算分块方案
        grid_rows, grid_cols = calculate_grid_size(height, width, available_memory)
        
        print(f"\n=== 推荐分块方案 ===")
        print(f"网格大小: {grid_rows}x{grid_cols}")
        print(f"总块数: {grid_rows * grid_cols}")
        
        # 计算每个块的实际大小
        base_block_height = height // grid_rows
        base_block_width = width // grid_cols
        
        print(f"基础块大小: {base_block_height}x{base_block_width}")
        print(f"扩展块大小: {base_block_height + 256}x{base_block_width + 256} (含重叠)")
        
    else:
        print("✅ 内存充足，可以直接处理")

def main():
    """主函数"""
    print("大图像内存分析工具")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 测试问题图像
    test_problematic_image()
    
    # 测试其他常见大小
    test_sizes = [
        (10000, 10000, "中等大图"),
        (20000, 20000, "大图"),
        (30000, 30000, "超大图"),
    ]
    
    available_memory = simple_memory_check()
    if available_memory:
        for height, width, desc in test_sizes:
            print(f"\n{'='*40}")
            print(f"测试 {desc}: {height}x{width}")
            print(f"{'='*40}")
            
            required_memory = calculate_image_memory(height, width)
            
            if required_memory > available_memory:
                print("需要分块处理")
                grid_rows, grid_cols = calculate_grid_size(height, width, available_memory)
                print(f"推荐网格: {grid_rows}x{grid_cols}")
            else:
                print("可以直接处理")

if __name__ == '__main__':
    main()
