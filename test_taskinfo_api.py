#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试TaskInfo获取API接口
"""

import requests
import json
import os

def test_taskinfo_api():
    """测试TaskInfo获取API接口"""
    print("🧪 测试TaskInfo获取API接口...")
    
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试用例
    test_cases = [
        {
            'name': '存在的TaskInfo文件',
            'id': '20250705171601',
            'expected_status': 'success',
            'description': '测试获取存在的TaskInfo.json文件'
        },
        {
            'name': '不存在的TaskInfo文件',
            'id': '20241231999999',
            'expected_status': 'success',
            'description': '测试获取不存在的TaskInfo.json文件，应返回空列表'
        },
        {
            'name': '缺少ID参数',
            'id': None,
            'expected_status': 'error',
            'description': '测试缺少id参数的情况'
        }
    ]
    
    print(f"\n📋 开始执行 {len(test_cases)} 个测试用例...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ 测试用例: {test_case['name']}")
        print(f"   描述: {test_case['description']}")
        
        try:
            # 构建请求参数
            params = {}
            if test_case['id'] is not None:
                params['id'] = test_case['id']
            
            print(f"   请求参数: {params}")
            
            # 发送请求
            response = requests.get(f"{base_url}/taskinfo/", params=params, timeout=30)
            
            print(f"   HTTP状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"   响应状态: {result.get('status', 'N/A')}")
                print(f"   响应消息: {result.get('message', 'N/A')}")
                
                data = result.get('data', [])
                print(f"   数据条数: {len(data)}")
                
                # 验证响应格式
                if result.get('status') == test_case['expected_status']:
                    print(f"   ✅ 状态验证通过")
                else:
                    print(f"   ❌ 状态验证失败，期望: {test_case['expected_status']}, 实际: {result.get('status')}")
                
                # 详细分析数据内容
                if data and isinstance(data, list):
                    print(f"   📊 数据详情:")
                    for j, task in enumerate(data):
                        if isinstance(task, dict):
                            print(f"     任务 {j+1}:")
                            print(f"       任务ID: {task.get('task_id', 'N/A')}")
                            print(f"       影像ID: {task.get('image_id', 'N/A')}")
                            print(f"       分析类别: {task.get('analysis_category', 'N/A')}")
                            print(f"       状态: {task.get('status', 'N/A')}")
                            print(f"       进度: {task.get('progress', 'N/A')}%")
                            
                            # 检查parameters
                            parameters = task.get('parameters', {})
                            if parameters:
                                model_name = parameters.get('model_name', 'N/A')
                                print(f"       模型名称: {model_name}")
                            
                            # 检查results
                            results = task.get('results', {})
                            if results:
                                ai_time = results.get('ai_processing_time', 'N/A')
                                success = results.get('success', 'N/A')
                                print(f"       AI处理时间: {ai_time}")
                                print(f"       成功状态: {success}")
                                
                                # 检查空间统计
                                spatial_stats = results.get('spatial_statistics', {})
                                if spatial_stats:
                                    non_null_count = sum(1 for v in spatial_stats.values() if v is not None)
                                    total_count = len(spatial_stats)
                                    print(f"       空间统计: {non_null_count}/{total_count} 字段有值")
                                    
                                    # 显示具体统计值
                                    for key, value in spatial_stats.items():
                                        if value is not None:
                                            print(f"         {key}: {value}")
                        else:
                            print(f"     ❌ 任务 {j+1} 格式错误")
                
                elif test_case['id'] and test_case['expected_status'] == 'success':
                    # 如果期望有数据但实际为空，检查文件是否真的存在
                    expected_path = f"D:/Drone_Project/nginxData/ODM/AI/{test_case['id']}/TaskInfo.json"
                    if os.path.exists(expected_path):
                        print(f"   ⚠️ 文件存在但API返回空数据: {expected_path}")
                        try:
                            with open(expected_path, 'r', encoding='utf-8') as f:
                                file_content = f.read()
                            print(f"   📄 文件内容长度: {len(file_content)} 字符")
                            if file_content.strip():
                                try:
                                    file_data = json.loads(file_content)
                                    print(f"   📊 文件数据类型: {type(file_data)}")
                                    if isinstance(file_data, list):
                                        print(f"   📊 文件包含任务数: {len(file_data)}")
                                    elif isinstance(file_data, dict):
                                        print(f"   📊 文件为单个任务对象")
                                except json.JSONDecodeError as e:
                                    print(f"   ❌ 文件JSON格式错误: {e}")
                            else:
                                print(f"   ⚠️ 文件为空")
                        except Exception as e:
                            print(f"   ❌ 读取文件失败: {e}")
                    else:
                        print(f"   ✅ 文件确实不存在: {expected_path}")
                
            else:
                print(f"   ❌ HTTP请求失败")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print(f"\n🎯 测试完成")

def test_direct_file_access():
    """直接测试文件访问"""
    print(f"\n🔍 直接测试文件访问...")
    
    test_ids = ['20250705171601', '20241231999999']
    
    for image_id in test_ids:
        print(f"\n📁 测试ID: {image_id}")
        
        # 构建文件路径
        file_path = f"D:/Drone_Project/nginxData/ODM/AI/{image_id}/TaskInfo.json"
        print(f"   文件路径: {file_path}")
        
        if os.path.exists(file_path):
            print(f"   ✅ 文件存在")
            
            try:
                file_size = os.path.getsize(file_path)
                print(f"   📊 文件大小: {file_size} 字节")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if content.strip():
                    try:
                        data = json.loads(content)
                        print(f"   ✅ JSON格式正确")
                        print(f"   📊 数据类型: {type(data)}")
                        
                        if isinstance(data, list):
                            print(f"   📊 包含任务数: {len(data)}")
                            if data:
                                first_task = data[0]
                                print(f"   📋 第一个任务:")
                                print(f"     任务ID: {first_task.get('task_id', 'N/A')}")
                                print(f"     状态: {first_task.get('status', 'N/A')}")
                        elif isinstance(data, dict):
                            print(f"   📊 单个任务对象")
                            print(f"     任务ID: {data.get('task_id', 'N/A')}")
                            print(f"     状态: {data.get('status', 'N/A')}")
                        
                    except json.JSONDecodeError as e:
                        print(f"   ❌ JSON格式错误: {e}")
                        print(f"   📄 文件内容前100字符: {content[:100]}")
                else:
                    print(f"   ⚠️ 文件为空")
                    
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
        else:
            print(f"   ❌ 文件不存在")

def test_odm_config_access():
    """测试ODM配置访问"""
    print(f"\n🔧 测试ODM配置访问...")
    
    try:
        config_url = "http://127.0.0.1:81/ODM/Task.cfg"
        print(f"   配置URL: {config_url}")
        
        response = requests.get(config_url, timeout=10)
        
        if response.status_code == 200:
            print(f"   ✅ 配置文件访问成功")
            
            config_content = response.text
            print(f"   📊 配置内容长度: {len(config_content)} 字符")
            
            # 查找window_data_path
            if 'window_data_path' in config_content:
                print(f"   ✅ 找到window_data_path配置")
                
                # 简单解析
                for line in config_content.split('\n'):
                    if 'window_data_path' in line and '=' in line:
                        path = line.split('=', 1)[1].strip()
                        print(f"   📁 window_data_path: {path}")
                        break
            else:
                print(f"   ⚠️ 未找到window_data_path配置")
                
        else:
            print(f"   ❌ 配置文件访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 测试ODM配置失败: {e}")

if __name__ == "__main__":
    print("🚀 开始测试TaskInfo获取API接口")
    
    # 测试API接口
    test_taskinfo_api()
    
    # 直接测试文件访问
    test_direct_file_access()
    
    # 测试ODM配置访问
    test_odm_config_access()
    
    print(f"\n🎉 所有测试完成！")
    
    print(f"\n📖 接口使用说明:")
    print(f"URL: GET /api/analysis/taskinfo/")
    print(f"参数: id (必需) - 影像ID，如 20250705171601")
    print(f"示例: http://127.0.0.1:8091/api/analysis/taskinfo/?id=20250705171601")
    print(f"返回: TaskInfo.json的完整内容，如果文件不存在则返回空列表[]")
