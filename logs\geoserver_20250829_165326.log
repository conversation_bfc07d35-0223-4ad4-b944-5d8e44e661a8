2025-08-29 16:53:26,924 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_165326.log
2025-08-29 16:53:26,927 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:53:26,927 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:53:26,979 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:53:26,985 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:53:26,986 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:53:26,998 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:53:27,001 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:53:27,005 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:53:27,021 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:53:27,023 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:53:27,024 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:53:27,039 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:53:27,041 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:53:27,042 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:53:27,058 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:53:27,062 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 16:53:27,065 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 16:53:27,082 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 16:53:27,106 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 16:53:27,116 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 16:53:31,509 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 16:53:31,530 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 16:53:31,537 - analysis_executor - INFO - 加载了 28 个任务状态
