2025-08-28 16:06:12,819 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_160612.log
2025-08-28 16:06:12,820 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:06:12,821 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:06:12,842 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:06:12,847 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:06:12,847 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:06:12,861 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:06:12,863 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:06:12,863 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:06:12,876 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:06:12,879 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:06:12,879 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:06:12,891 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:06:12,893 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:06:12,894 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:06:12,908 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:06:12,910 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:06:12,911 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:06:12,927 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:06:12,945 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 16:06:12,979 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 16:06:17,385 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 16:06:17,398 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 16:06:17,403 - analysis_executor - INFO - 加载了 28 个任务状态
