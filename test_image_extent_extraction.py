#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试影像范围提取功能

功能说明:
- 测试影像有效范围提取接口
- 验证参数处理和错误处理
- 测试任务状态查询
"""

import requests
import time
import json
import os

def test_image_extent_extraction():
    """测试影像范围提取接口"""
    print("\n🧪 测试影像范围提取接口...")
    
    # 测试参数
    base_url = "http://127.0.0.1:8091/api/analysis"
    
    # 测试用的TIF文件路径（需要根据实际情况修改）
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    # 检查测试文件是否存在
    if not os.path.exists(test_image_path):
        print(f"❌ 测试文件不存在: {test_image_path}")
        print("请修改test_image_path为实际存在的TIF文件路径")
        return False
    
    try:
        # 1. 测试基本功能
        print("\n1️⃣ 测试基本影像范围提取...")
        
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 接口调用成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            print(f"📁 输入影像: {result['data']['input_image']}")
            print(f"📂 输出路径: {result['data']['output_path']}")
            
            task_id = result['data']['task_id']
            
            # 2. 监控任务状态
            print(f"\n2️⃣ 监控任务执行状态...")
            
            max_wait_time = 300  # 最大等待5分钟
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id})
                
                if status_response.status_code == 200:
                    status_data = status_response.json()['data']
                    task_status = status_data['task_status']
                    progress = status_data.get('progress', 0)
                    message = status_data.get('message', '')
                    
                    print(f"📊 任务状态: {task_status} ({progress}%) - {message}")
                    
                    if task_status == '完成':
                        print("✅ 任务执行完成！")
                        
                        # 显示结果信息
                        if 'result' in status_data:
                            result_info = status_data['result']
                            print(f"📊 有效区域数量: {result_info.get('contour_count', 0)}")
                            print(f"📐 总面积: {result_info.get('valid_area', 0):.6f} 平方度")
                            print(f"🗺️ 坐标系: {result_info.get('coordinate_system', 'WGS84')}")
                            print(f"📂 输出文件: {result_info.get('output_path', '')}")
                        
                        return True
                        
                    elif task_status == '失败':
                        print(f"❌ 任务执行失败: {message}")
                        return False
                    
                    time.sleep(5)  # 等待5秒后再次查询
                else:
                    print(f"❌ 状态查询失败: {status_response.status_code}")
                    return False
            
            print("⏰ 任务执行超时")
            return False
            
        else:
            print(f"❌ 接口调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False

def test_parameter_validation():
    """测试参数验证"""
    print("\n🧪 测试参数验证...")
    
    base_url = "http://127.0.0.1:8091/api/analysis/image-extent-extraction/"
    
    # 测试缺少必需参数
    print("\n1️⃣ 测试缺少必需参数...")
    response = requests.get(base_url)
    
    if response.status_code == 400:
        result = response.json()
        print(f"✅ 正确返回400错误: {result['message']}")
    else:
        print(f"❌ 应该返回400错误，实际返回: {response.status_code}")
    
    # 测试文件不存在
    print("\n2️⃣ 测试文件不存在...")
    params = {'image': 'D:/nonexistent/file.tif'}
    response = requests.get(base_url, params=params)
    
    if response.status_code == 404:
        result = response.json()
        print(f"✅ 正确返回404错误: {result['message']}")
    else:
        print(f"❌ 应该返回404错误，实际返回: {response.status_code}")
    
    # 测试无效文件格式
    print("\n3️⃣ 测试无效文件格式...")
    params = {'image': 'D:/test/file.jpg'}
    response = requests.get(base_url, params=params)
    
    if response.status_code == 400:
        result = response.json()
        print(f"✅ 正确返回400错误: {result['message']}")
    else:
        print(f"❌ 应该返回400错误，实际返回: {response.status_code}")
    
    # 测试无效参数值
    print("\n4️⃣ 测试无效参数值...")
    params = {
        'image': 'D:/test/file.tif',
        'simplify_tolerance': -1.0
    }
    response = requests.get(base_url, params=params)
    
    if response.status_code == 400:
        result = response.json()
        print(f"✅ 正确返回400错误: {result['message']}")
    else:
        print(f"❌ 应该返回400错误，实际返回: {response.status_code}")

def test_api_documentation():
    """测试API文档示例"""
    print("\n🧪 测试API文档示例...")
    
    # 这里可以添加文档中示例的测试
    print("📖 API文档示例:")
    print("GET http://127.0.0.1:8091/api/analysis/image-extent-extraction/?image=D:/data/satellite.tif&simplify_tolerance=1.0&min_area=1000.0")
    print("✅ 文档示例格式正确")

if __name__ == "__main__":
    print("🚀 开始测试影像范围提取功能")
    
    # 测试参数验证
    test_parameter_validation()
    
    # 测试API文档
    test_api_documentation()
    
    # 测试实际功能（需要有效的TIF文件）
    success = test_image_extent_extraction()
    
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 部分测试失败，请检查日志")
