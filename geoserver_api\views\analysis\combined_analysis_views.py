#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
合并分析API视图

功能说明:
- 合并AI语义分割和空间变化分析功能
- 输入影像进行AI预测，然后与历史数据进行变化分析
- 统一的任务管理和日志记录
- 生成详细的任务信息JSON

接口列表:
- GET /api/analysis/combined-ai-spatial-analysis/ - 合并的AI语义分割和空间变化分析
"""

import os
import logging
import json
import configparser
from datetime import datetime
from rest_framework.decorators import api_view
from rest_framework.response import Response
from rest_framework import status

# 导入核心模块
from ...core.combined_analysis_executor import combined_analysis_executor, analysis_logger

@api_view(['GET'])
def combined_ai_spatial_analysis(request):
    """
    合并的AI语义分割和空间变化分析接口
    
    功能: 
    1. 对输入的TIF图像进行AI语义分割
    2. 将AI分析结果与历史SHP数据进行空间变化分析
    3. 输出最终的变化分析结果
    
    查询参数:
        id: 影像ID (必选) - 如: 20250705171601
        image: 用于预测的TIF路径 (必选) - 如: D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif
        model: 权重路径 (必选) - 如: D:/Drone_Project/nginxData/ODM/AIWeight/arableLand/deeplabv3_plus/deeplabv3_plus_best_20250807-111949.pth
        old_data_path: 老的SHP路径 (必选) - 如: D:/Drone_Project/nginxData/ODM/AIOLDSHP/arableLand/耕地2024_84.shp
        area_threshold: 面积阈值 (可选，默认400) - 单位平方米
        model_type: 模型类型 (可选，默认deeplabv3_plus)
        num_classes: 类别数量 (可选，默认2)
    
    返回: 合并分析任务ID和初始状态
    """
    try:
        # 获取参数
        image_id = request.GET.get("id")
        image_path = request.GET.get("image")
        model_path = request.GET.get("model")
        old_data_path = request.GET.get("old_data_path")
        area_threshold = float(request.GET.get("area_threshold", 400.0))
        model_type = request.GET.get("model_type", "deeplabv3_plus")
        num_classes = int(request.GET.get("num_classes", 2))
        
        # 参数验证
        if not image_id:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: id (影像ID)'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        if not image_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: image (TIF图像路径)'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        if not model_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: model (权重路径)'
            }, status=status.HTTP_400_BAD_REQUEST)
            
        if not old_data_path:
            return Response({
                'status': 'error',
                'message': '缺少必需参数: old_data_path (历史SHP路径)'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            return Response({
                'status': 'error',
                'message': f'TIF图像文件不存在: {image_path}'
            }, status=status.HTTP_404_NOT_FOUND)
            
        if not os.path.exists(model_path):
            return Response({
                'status': 'error',
                'message': f'模型权重文件不存在: {model_path}'
            }, status=status.HTTP_404_NOT_FOUND)
            
        if not os.path.exists(old_data_path):
            return Response({
                'status': 'error',
                'message': f'历史SHP文件不存在: {old_data_path}'
            }, status=status.HTTP_404_NOT_FOUND)
        
        analysis_logger.info(f"收到合并分析请求")
        analysis_logger.info(f"影像ID: {image_id}")
        analysis_logger.info(f"TIF图像: {image_path}")
        analysis_logger.info(f"模型权重: {model_path}")
        analysis_logger.info(f"历史数据: {old_data_path}")
        analysis_logger.info(f"面积阈值: {area_threshold} 平方米")
        analysis_logger.info(f"模型类型: {model_type}")
        
        # 执行合并分析任务
        result = combined_analysis_executor.execute_combined_analysis(
            image_id=image_id,
            image_path=image_path,
            model_path=model_path,
            old_data_path=old_data_path,
            area_threshold=area_threshold,
            model_type=model_type,
            num_classes=num_classes
        )
        
        if result['success']:
            return Response({
                'status': 'success',
                'message': '合并分析任务已启动',
                'data': {
                    'task_id': result['task_id'],
                    'task_status': result['status'],
                    'message': result['message'],
                    'image_id': image_id,
                    'estimated_time': '预计15-20分钟完成'
                }
            })
        else:
            return Response({
                'status': 'error',
                'message': result['message'],
                'error': result.get('error')
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    except ValueError as e:
        analysis_logger.error(f"参数错误: {str(e)}")
        return Response({
            'status': 'error',
            'message': f'参数错误: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        analysis_logger.error(f"合并分析API错误: {str(e)}")
        import traceback
        analysis_logger.error(traceback.format_exc())
        return Response({
            'status': 'error',
            'message': f'服务器内部错误: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
