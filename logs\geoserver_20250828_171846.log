2025-08-28 17:18:46,269 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_171846.log
2025-08-28 17:18:46,271 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:46,271 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:46,288 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:46,293 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:46,294 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:46,305 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:46,308 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:46,308 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:46,322 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:46,324 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:46,325 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:46,337 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:46,339 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:46,340 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:46,351 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:46,353 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 17:18:46,353 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 17:18:46,364 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 17:18:46,378 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 17:18:46,382 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 17:18:51,021 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 17:18:51,032 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 17:18:51,035 - analysis_executor - INFO - 加载了 28 个任务状态
