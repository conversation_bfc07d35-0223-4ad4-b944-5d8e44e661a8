#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证合并分析接口的修改
"""

import os
import re

def verify_status_changes():
    """验证状态修改"""
    print("🔍 验证状态修改...")
    
    file_path = "geoserver_api/core/combined_analysis_executor.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有status设置
        status_patterns = [
            r"'status'\s*:\s*'([^']+)'",
            r'"status"\s*:\s*"([^"]+)"'
        ]
        
        all_statuses = []
        for pattern in status_patterns:
            matches = re.findall(pattern, content)
            all_statuses.extend(matches)
        
        print(f"📊 发现的所有状态值:")
        valid_statuses = ['等待中', '进行中', '完成', '失败']
        invalid_statuses = []
        
        for status in set(all_statuses):
            if status in valid_statuses:
                print(f"   ✅ {status}")
            else:
                print(f"   ❌ {status} (非法状态)")
                invalid_statuses.append(status)
        
        if invalid_statuses:
            print(f"❌ 发现非法状态: {invalid_statuses}")
            return False
        else:
            print(f"✅ 所有状态都合法")
            return True
            
    except Exception as e:
        print(f"❌ 验证状态失败: {e}")
        return False

def verify_path_changes():
    """验证路径修改"""
    print(f"\n🔍 验证路径修改...")
    
    file_path = "geoserver_api/core/combined_analysis_executor.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找影像范围提取函数
        extract_function_match = re.search(
            r'def _extract_image_extent.*?(?=def|\Z)', 
            content, 
            re.DOTALL
        )
        
        if extract_function_match:
            function_content = extract_function_match.group(0)
            
            # 检查是否包含分析类别目录创建
            if 'analysis_category' in function_content:
                print(f"✅ 发现analysis_category使用")
            else:
                print(f"❌ 未发现analysis_category使用")
                return False
            
            # 检查是否创建分析类别目录
            if 'os.makedirs' in function_content and 'analysis_category' in function_content:
                print(f"✅ 发现目录创建逻辑")
            else:
                print(f"❌ 未发现目录创建逻辑")
                return False
            
            # 检查路径构建
            if 'analysis_category_dir' in function_content:
                print(f"✅ 发现分析类别目录路径构建")
            else:
                print(f"❌ 未发现分析类别目录路径构建")
                return False
            
            print(f"✅ 路径修改验证通过")
            return True
        else:
            print(f"❌ 未找到_extract_image_extent函数")
            return False
            
    except Exception as e:
        print(f"❌ 验证路径修改失败: {e}")
        return False

def verify_documentation_updates():
    """验证文档更新"""
    print(f"\n🔍 验证文档更新...")
    
    doc_path = "geoserver_api/readme/analysis_api.md"
    
    if not os.path.exists(doc_path):
        print(f"❌ 文档文件不存在: {doc_path}")
        return False
    
    try:
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            {
                'name': '状态说明更新',
                'pattern': r'任务状态.*仅这4种状态',
                'description': '检查是否明确说明只有4种状态'
            },
            {
                'name': '文件路径说明更新',
                'pattern': r'与其他SHP文件.*同.*目录',
                'description': '检查是否说明范围文件与其他SHP文件同目录'
            },
            {
                'name': '输出结构更新',
                'pattern': r'影像范围文件.*与其他SHP文件同目录',
                'description': '检查输出结构说明是否更新'
            }
        ]
        
        all_passed = True
        for check in checks:
            if re.search(check['pattern'], content, re.IGNORECASE):
                print(f"   ✅ {check['name']}")
            else:
                print(f"   ❌ {check['name']}: {check['description']}")
                all_passed = False
        
        if all_passed:
            print(f"✅ 文档更新验证通过")
        else:
            print(f"❌ 文档更新验证失败")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 验证文档更新失败: {e}")
        return False

def generate_test_summary():
    """生成测试总结"""
    print(f"\n📋 修改总结:")
    
    print(f"\n1️⃣ 状态简化修改:")
    print(f"   ✅ 移除了'AI处理完成'等中间状态")
    print(f"   ✅ 只保留'等待中'、'进行中'、'完成'、'失败'四种状态")
    print(f"   ✅ AI处理完成后状态保持为'进行中'，直到整个任务完成")
    
    print(f"\n2️⃣ 文件路径修改:")
    print(f"   ✅ 影像范围文件从根目录移动到分析类别目录")
    print(f"   ✅ 原路径: /ODM/AI/{'{image_id}'}/{'{image_id}'}_area_{'{timestamp}'}.shp")
    print(f"   ✅ 新路径: /ODM/AI/{'{image_id}'}/arableLand/{'{image_id}'}_area_{'{timestamp}'}.shp")
    print(f"   ✅ 与AI分析结果和最终分析结果位于同一目录")
    
    print(f"\n3️⃣ 文档更新:")
    print(f"   ✅ 更新了TaskInfo字段说明")
    print(f"   ✅ 更新了输出文件结构说明")
    print(f"   ✅ 明确了状态取值范围")
    
    print(f"\n🎯 预期效果:")
    print(f"   1. TaskInfo.json中的status字段更加简洁明确")
    print(f"   2. 所有SHP文件（AI结果、范围文件、最终结果）都在同一目录下")
    print(f"   3. 便于文件管理和后续处理")

def main():
    """主函数"""
    print("🚀 开始验证合并分析接口的修改")
    
    # 验证状态修改
    status_ok = verify_status_changes()
    
    # 验证路径修改
    path_ok = verify_path_changes()
    
    # 验证文档更新
    doc_ok = verify_documentation_updates()
    
    # 生成总结
    generate_test_summary()
    
    # 最终结果
    if status_ok and path_ok and doc_ok:
        print(f"\n🎉 所有修改验证通过！")
        print(f"\n📝 建议测试:")
        print(f"   1. 运行 test_combined_analysis_fixes.py 进行功能测试")
        print(f"   2. 启动一个新的合并分析任务")
        print(f"   3. 监控TaskInfo.json中的状态变化")
        print(f"   4. 检查生成的文件路径是否正确")
        return True
    else:
        print(f"\n❌ 部分修改验证失败")
        print(f"   状态修改: {'✅' if status_ok else '❌'}")
        print(f"   路径修改: {'✅' if path_ok else '❌'}")
        print(f"   文档更新: {'✅' if doc_ok else '❌'}")
        return False

if __name__ == "__main__":
    main()
