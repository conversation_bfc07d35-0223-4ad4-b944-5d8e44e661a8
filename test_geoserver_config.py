#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试GeoServer配置
验证TaskDeletionManager使用正确的GeoServer配置
"""

import requests
import json

def test_geoserver_config():
    """测试GeoServer配置"""
    print("🧪 测试GeoServer配置")
    
    try:
        # 导入配置
        from config import (
            GEOSERVER_URL,
            GEOSERVER_USER,
            GEOSERVER_PASSWORD,
            DEFAULT_WORKSPACE,
            DEFAULT_DATASTORE
        )
        
        print(f"\n📋 当前配置:")
        print(f"   GEOSERVER_URL: {GEOSERVER_URL}")
        print(f"   GEOSERVER_USER: {GEOSERVER_USER}")
        print(f"   GEOSERVER_PASSWORD: {'*' * len(GEOSERVER_PASSWORD) if GEOSERVER_PASSWORD else 'None'}")
        print(f"   DEFAULT_WORKSPACE: {DEFAULT_WORKSPACE}")
        print(f"   DEFAULT_DATASTORE: {DEFAULT_DATASTORE}")
        
        # 测试TaskDeletionManager配置
        print(f"\n🔧 测试TaskDeletionManager配置:")
        from geoserver_api.core.task_deletion_manager import TaskDeletionManager
        
        deletion_manager = TaskDeletionManager()
        geoserver_info = deletion_manager.get_geoserver_info()
        
        print(f"   TaskDeletionManager使用的配置:")
        for key, value in geoserver_info.items():
            if 'password' in key.lower():
                value = '*' * len(str(value)) if value else 'None'
            print(f"     {key}: {value}")
        
        # 验证配置一致性
        print(f"\n✅ 配置一致性检查:")
        config_matches = [
            ("GEOSERVER_URL", GEOSERVER_URL, deletion_manager.geoserver_base_url),
            ("GEOSERVER_USER", GEOSERVER_USER, deletion_manager.geoserver_auth[0]),
            ("GEOSERVER_PASSWORD", GEOSERVER_PASSWORD, deletion_manager.geoserver_auth[1])
        ]
        
        all_match = True
        for name, config_value, manager_value in config_matches:
            if config_value == manager_value:
                print(f"   ✅ {name}: 配置一致")
            else:
                print(f"   ❌ {name}: 配置不一致")
                print(f"      配置文件: {config_value}")
                print(f"      管理器: {manager_value}")
                all_match = False
        
        if all_match:
            print(f"\n🎉 所有配置都一致！")
        else:
            print(f"\n⚠️ 发现配置不一致的问题")
        
        return all_match
        
    except ImportError as e:
        print(f"❌ 导入配置失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试配置异常: {e}")
        return False

def test_geoserver_connection():
    """测试GeoServer连接"""
    print(f"\n🌐 测试GeoServer连接...")
    
    try:
        from config import GEOSERVER_URL, GEOSERVER_USER, GEOSERVER_PASSWORD
        
        # 测试版本信息接口
        version_url = f"{GEOSERVER_URL}/rest/about/version"
        print(f"   测试URL: {version_url}")
        
        response = requests.get(
            version_url,
            auth=(GEOSERVER_USER, GEOSERVER_PASSWORD),
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"   ✅ 连接成功: HTTP {response.status_code}")
            
            # 尝试解析版本信息
            try:
                if 'application/json' in response.headers.get('content-type', ''):
                    version_info = response.json()
                    print(f"   📋 GeoServer版本信息:")
                    if 'about' in version_info:
                        about = version_info['about']
                        for key, value in about.items():
                            print(f"     {key}: {value}")
                else:
                    print(f"   📋 响应内容: {response.text[:200]}...")
                    
            except Exception as e:
                print(f"   ⚠️ 解析版本信息失败: {e}")
            
            return True
            
        else:
            print(f"   ❌ 连接失败: HTTP {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 连接异常: {e}")
        return False

def test_workspace_access():
    """测试工作空间访问"""
    print(f"\n📁 测试工作空间访问...")
    
    try:
        from config import GEOSERVER_URL, GEOSERVER_USER, GEOSERVER_PASSWORD, DEFAULT_WORKSPACE
        
        # 测试工作空间列表
        workspaces_url = f"{GEOSERVER_URL}/rest/workspaces"
        print(f"   测试URL: {workspaces_url}")
        
        response = requests.get(
            workspaces_url,
            auth=(GEOSERVER_USER, GEOSERVER_PASSWORD),
            headers={'Accept': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"   ✅ 工作空间列表获取成功")
            
            try:
                workspaces_data = response.json()
                workspaces = workspaces_data.get('workspaces', {}).get('workspace', [])
                
                if isinstance(workspaces, dict):
                    workspaces = [workspaces]
                
                print(f"   📋 可用工作空间 ({len(workspaces)}个):")
                workspace_names = []
                for ws in workspaces:
                    name = ws.get('name', 'unknown')
                    workspace_names.append(name)
                    print(f"     - {name}")
                
                # 检查默认工作空间是否存在
                if DEFAULT_WORKSPACE in workspace_names:
                    print(f"   ✅ 默认工作空间 '{DEFAULT_WORKSPACE}' 存在")
                else:
                    print(f"   ⚠️ 默认工作空间 '{DEFAULT_WORKSPACE}' 不存在")
                    print(f"   💡 建议创建工作空间或修改配置")
                
                return True
                
            except Exception as e:
                print(f"   ⚠️ 解析工作空间信息失败: {e}")
                return False
                
        else:
            print(f"   ❌ 工作空间列表获取失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 工作空间访问异常: {e}")
        return False

def test_delete_task_config():
    """测试删除任务的配置"""
    print(f"\n🗑️ 测试删除任务配置...")
    
    try:
        # 创建TaskDeletionManager实例
        from geoserver_api.core.task_deletion_manager import TaskDeletionManager
        deletion_manager = TaskDeletionManager()
        
        print(f"   ✅ TaskDeletionManager创建成功")
        
        # 获取配置信息
        config_info = deletion_manager.get_geoserver_info()
        print(f"   📋 删除管理器配置:")
        for key, value in config_info.items():
            print(f"     {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 删除任务配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 GeoServer配置测试")
    
    print(f"\n📝 测试内容:")
    print(f"1. 验证配置文件导入")
    print(f"2. 检查TaskDeletionManager配置一致性")
    print(f"3. 测试GeoServer连接")
    print(f"4. 测试工作空间访问")
    print(f"5. 验证删除任务配置")
    
    results = []
    
    # 测试1: 配置一致性
    print(f"\n" + "="*50)
    result1 = test_geoserver_config()
    results.append(("配置一致性", result1))
    
    # 测试2: GeoServer连接
    print(f"\n" + "="*50)
    result2 = test_geoserver_connection()
    results.append(("GeoServer连接", result2))
    
    # 测试3: 工作空间访问
    print(f"\n" + "="*50)
    result3 = test_workspace_access()
    results.append(("工作空间访问", result3))
    
    # 测试4: 删除任务配置
    print(f"\n" + "="*50)
    result4 = test_delete_task_config()
    results.append(("删除任务配置", result4))
    
    # 汇总结果
    print(f"\n" + "="*50)
    print(f"📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print(f"🎉 所有测试通过！GeoServer配置正确。")
    else:
        print(f"⚠️ 部分测试失败，请检查配置。")
    
    print(f"\n💡 配置文件位置:")
    print(f"   config.py (项目根目录)")
    
    print(f"\n📚 相关配置项:")
    print(f"   GEOSERVER_URL - GeoServer服务地址")
    print(f"   GEOSERVER_USER - GeoServer用户名")
    print(f"   GEOSERVER_PASSWORD - GeoServer密码")
    print(f"   DEFAULT_WORKSPACE - 默认工作空间")
    print(f"   DEFAULT_DATASTORE - 默认数据存储")

if __name__ == "__main__":
    main()
