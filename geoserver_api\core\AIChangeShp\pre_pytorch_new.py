import os
import sys
import argparse
import tempfile
import subprocess
from osgeo import gdal, ogr, osr

"""
遥感图像分割处理工具 - 优化版本 v1.3 (直接输出Shapefile)
用于使用深度学习模型对大型遥感图像进行语义分割，支持多种模型类型和边界处理优化
新增支持批量预测功能，可同时处理单个文件、单个目录或多个目录中的影像
现在直接输出Shapefile矢量文件，无需手动转换

===================================
详细使用说明
===================================

基本使用方法:
python pre_pytorch_多线程.py [图像路径] [模型路径] [结果路径] [重叠区域百分比] [批处理大小]

或使用命名参数:
python pre_pytorch_多线程.py --image [图像路径] --model [模型路径] --output [结果路径]

基本参数:
- 图像路径(--image): 输入的遥感图像路径，支持GeoTIFF格式，可以是单个文件或目录
- 模型路径(--model): 模型权重文件路径(.pth文件)
- 结果路径(--output): 输出分割结果的路径(.shp文件)，当输入为目录时，此参数也应为目录
- 重叠区域百分比(--overlap): 图像块之间的重叠区域百分比，默认为0.5
- 批处理大小(--batch_size): 批处理大小，默认为16

注意: 现在程序直接输出Shapefile格式(.shp)，自动过滤背景类(值为0)，只保留目标对象

批量处理参数:
--batch_mode: 启用批量处理模式，处理整个目录中的图像
--image_dirs: 多个输入图像目录路径，用于批量处理多个文件夹中的图像
--output_dirs: 多个输出目录路径，与image_dirs一一对应
--image_ext: 批量模式下要处理的图像文件扩展名，默认为.tif
--recursive: 是否递归处理子目录中的图像

高级参数:
--model_type: 模型类型，支持以下选项:
    * deeplabv3_plus (默认): DeepLabV3+模型，适用于多种分割任务
    * deeplabv3_plus_improved: 改进版DeepLabV3+模型，添加了注意力机制和边界增强模块
    * unet: 经典U-Net模型，适用于医学和遥感图像分割
    * lunet: 轻量级U-Net变种，适用于资源有限的环境
    * unetgai: 改进版U-Net，添加了全局注意力机制
    * segformer: Vision Transformer架构的分割模型，性能优但需要更多资源
    * segformer_improved: 改进版SegFormer，增强了对边界和小目标的识别能力
    * unetplusplus: U-Net++模型，嵌套结构设计，可使用深度监督
    * segnext: 最新的分割模型，结合了Transformer和CNN的优点，性能优越
    * segnext_improved: 改进版SegNeXt，针对遥感图像优化，集成ASPP和注意力机制
    * vision_transformer: Vision Transformer模型，纯注意力机制，适用于高分辨率图像
    * vit: Vision Transformer的简称别名

--num_classes: 类别数量
    * 二分类任务: 设置为2 (背景为0，目标为1)
    * 多分类任务: 设置为N+1 (背景为0，N个目标类别从1到N)

--backbone: DeepLabV3+的骨干网络，可选:
    * resnet50: 较轻量级，适合一般任务
    * resnet101(默认): 平衡性能和计算资源
    * xception: 高性能，需要更多计算资源

--segformer_type: SegFormer模型的类型，可选b0(最小)到b5(最大)
--segnext_type: SegNeXt模型的类型，可选tiny、small、base(默认)、large
--segnext_improved_type: 改进版SegNeXt模型的类型，可选tiny、small、base(默认)、large
--vit_type: Vision Transformer模型的类型，可选vit_small、vit_base(默认)、vit_large、vit_huge
--vit_patch_size: ViT的patch大小，默认为16
--vit_img_size: ViT的输入图像尺寸，默认为224
--deep_supervision: 使用深度监督(用于UNet++)

--mask_shp: 可选的shapefile路径，用于限制预测范围，只预测shapefile内部的像素
--use_amp: 使用混合精度计算加速
--save_memory: 启用内存节省模式（可能会降低速度）
--gpu: 指定使用的GPU ID，默认为0
--num_workers: 数据加载的工作线程数，默认为0

===================================
使用场景示例
===================================

1. 基本二分类任务(单文件):
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/model.pth --output D:/results/result.shp

2. 批量处理单个目录中的所有图像:
python pre_pytorch_多线程.py --image D:/data/images --model D:/models/model.pth --output D:/results --batch_mode

3. 批量处理单个目录(自动检测目录):
python pre_pytorch_多线程.py --image D:/data/images --model D:/models/model.pth --output D:/results
(当--image参数指向目录时，自动进入批量处理模式，自动将.tif输出为.shp)

4. 批量处理多个目录:
python pre_pytorch_多线程.py --image_dirs D:/data/images1 D:/data/images2 --output_dirs D:/results1 D:/results2 --model D:/models/model.pth

5. 递归处理子目录中的图像:
python pre_pytorch_多线程.py --image D:/data/images --model D:/models/model.pth --output D:/results --batch_mode --recursive

6. 处理特定类型的图像文件:
python pre_pytorch_多线程.py --image D:/data/images --model D:/models/model.pth --output D:/results --batch_mode --image_ext .jpg

7. 多分类任务(5个类别):
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/model.pth --output D:/results/result.shp --num_classes 6

8. 使用U-Net模型:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/unet_model.pth --output D:/results/result.shp --model_type unet

9. 使用Shapefile限制预测区域:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/model.pth --output D:/results/result.shp --mask_shp D:/data/area.shp

10. 使用SegFormer模型进行多分类:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/segformer_model.pth --output D:/results/result.shp --model_type segformer --segformer_type b3 --num_classes 4

11. 在低内存环境下使用:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/model.pth --output D:/results/result.shp --save_memory --batch_size 4

12. 使用混合精度加速:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/model.pth --output D:/results/result.shp --use_amp

13. 在特定GPU上运行:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/model.pth --output D:/results/result.shp --gpu 1

14. 使用SegNeXt模型进行多分类:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/segnext_model.pth --output D:/results/result.shp --model_type segnext --segnext_type base --num_classes 4

15. 使用改进版DeepLabV3+模型:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/deeplabv3_plus_improved_model.pth --output D:/results/result.shp --model_type deeplabv3_plus_improved

16. 使用改进版SegFormer模型:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/segformer_improved_model.pth --output D:/results/result.shp --model_type segformer_improved --segformer_type b3 --num_classes 4

17. 使用改进版SegNeXt模型:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/segnext_improved.pth --output D:/results/result.shp --model_type segnext_improved --segnext_improved_type large

18. 使用Vision Transformer模型:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/vit_model.pth --output D:/results/result.shp --model_type vision_transformer --vit_type vit_base --vit_img_size 224

19. 使用ViT处理高分辨率图像:
python pre_pytorch_多线程.py --image D:/data/image.tif --model D:/models/vit_large.pth --output D:/results/result.shp --model_type vit --vit_type vit_large --vit_img_size 384 --vit_patch_size 16

注意事项:
1. 确保模型训练时的类别数量与预测时设置的num_classes一致
2. 对于不规则影像，推荐使用mask_shp参数限定预测区域
3. 大型影像处理可能需要较大内存，可以通过调整batch_size和启用save_memory来优化
4. 如果使用多分类，结果图像中的像素值将对应类别编号(0为背景，1-N为目标类别)
5. 批量处理时，输出目录结构将保持与输入目录结构一致
6. 当使用--image_dirs参数时，必须提供相同数量的--output_dirs参数
7. 递归模式下会保留子目录结构到输出目录中
"""

"""
中文使用说明：

该程序用于遥感影像语义分割，支持多种深度学习模型。新版本增加了批量预测功能，可以同时处理多个影像。
现在直接输出Shapefile矢量文件，自动过滤背景类，只保留目标对象。

使用方式：
1. 处理单个影像文件：
   python pre_pytorch_多线程.py --image 影像路径.tif --model 模型路径.pth --output 输出路径.shp

2. 处理单个文件夹中的所有影像：
   python pre_pytorch_多线程.py --image 输入文件夹 --model 模型路径.pth --output 输出文件夹

3. 同时处理多个文件夹中的影像：
   python pre_pytorch_多线程.py --image_dirs 文件夹1 文件夹2 --output_dirs 输出1 输出2 --model 模型路径.pth

主要参数：
--image：输入影像路径，可以是单个文件或目录
--model：模型权重文件路径
--output：输出结果路径（.shp文件）
--batch_mode：启用批量处理模式
--image_dirs：多个输入目录路径
--output_dirs：多个输出目录路径
--image_ext：要处理的文件扩展名（默认.tif）
--recursive：是否递归处理子目录
--model_type：模型类型（默认deeplabv3_plus）
--num_classes：类别数量（默认2）

说明：
- 现在直接输出Shapefile格式，自动过滤背景类(值为0)
- 批量处理时会保留原始目录结构，自动将.tif改为.shp
- 处理多个目录时，输入目录和输出目录数量必须相同
- 程序会自动检测输入路径类型，如果是目录则自动进入批量处理模式
- 输出的Shapefile包含class字段，表示分类结果
"""

# 添加父目录到系统路径，以便能够导入network模块
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 导入必要的库
import torch
import numpy as np
import datetime
import math
import gc
from tqdm import tqdm
from .large_image_processor import LargeImageProcessor

def process_large_image_with_tiling(image_path, model_path, output_path, args, device, model,
                                  processor, im_width, im_height, im_bands, im_geotrans, im_proj, nodata_values):
    """
    使用分块处理大图像

    Args:
        image_path: 输入图像路径
        model_path: 模型路径
        output_path: 输出路径
        args: 参数对象
        device: 计算设备
        model: 模型对象
        processor: 大图像处理器
        im_width, im_height, im_bands: 图像尺寸信息
        im_geotrans, im_proj: 地理信息
        nodata_values: NoData值列表

    Returns:
        (success, testtime, processing_time)
    """
    import tempfile
    import shutil

    log_info = print  # 使用print作为日志函数

    try:
        # 计算网格大小
        grid_rows, grid_cols = processor.calculate_grid_size(im_height, im_width, im_bands)
        log_info(f"🔧 分块网格: {grid_rows}x{grid_cols}")

        # 计算块边界
        blocks = processor.calculate_block_bounds(im_height, im_width, grid_rows, grid_cols)
        log_info(f"📦 生成 {len(blocks)} 个处理块")

        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="large_image_processing_")
        log_info(f"📁 临时目录: {temp_dir}")

        block_results = []
        temp_files = []

        try:
            # 处理每个块
            for i, block_info in enumerate(blocks):
                log_info(f"🔄 处理块 {i+1}/{len(blocks)}: {block_info['row']}_{block_info['col']}")

                # 提取块
                block_path = processor.extract_block_from_image(image_path, block_info, temp_dir)
                temp_files.append(block_path)

                # 生成块结果路径
                base_name = os.path.splitext(os.path.basename(output_path))[0]
                block_result_path = os.path.join(temp_dir, f"{base_name}_{block_info['row']}_{block_info['col']}.shp")

                # 处理单个块
                success, _, _ = process_single_image(
                    block_path, model_path, block_result_path, args, device, model
                )

                if success and os.path.exists(block_result_path):
                    block_results.append({
                        'block_info': block_info,
                        'result_path': block_result_path
                    })
                    temp_files.append(block_result_path)
                    log_info(f"✅ 块 {block_info['row']}_{block_info['col']} 处理完成")
                else:
                    log_info(f"❌ 块 {block_info['row']}_{block_info['col']} 处理失败")

            # 合并结果
            if block_results:
                log_info(f"🔧 合并 {len(block_results)} 个块的结果")
                success = processor.merge_block_results(
                    block_results, output_path, im_height, im_width
                )

                if success:
                    log_info(f"✅ 大图像分块处理完成: {output_path}")
                    return True, [], 0
                else:
                    log_info(f"❌ 结果合并失败")
                    return False, [], 0
            else:
                log_info(f"❌ 没有成功处理的块")
                return False, [], 0

        finally:
            # 清理临时文件
            processor.cleanup_temp_files(temp_files)
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
                log_info(f"🧹 清理临时目录: {temp_dir}")

    except Exception as e:
        log_info(f"❌ 大图像分块处理失败: {e}")
        import traceback
        log_info(f"错误详情: {traceback.format_exc()}")
        return False, [], 0

print("设置GDAL环境变量...")


# # 在导入GDAL之前设置环境变量
# dll_path = 'F:\\Anaconda\\anaconda3\\envs\\torch_gpu\\Library\\bin'
# if os.path.exists(dll_path):
#     # Python 3.8+ 使用os.add_dll_directory添加DLL搜索路径
#     if hasattr(os, 'add_dll_directory'):
#         os.add_dll_directory(dll_path)
#     # 更新PATH环境变量
#     os.environ['PATH'] = dll_path + ';' + os.environ['PATH']

# # 定义GDAL相关路径
# gdal_bin_path = 'F:\\Anaconda\\anaconda3\\envs\\torch_gpu\\Library\\bin'

# 确保GDAL_DATA和PROJ_LIB环境变量正确设置
# os.environ['USE_PATH_FOR_GDAL_PYTHON'] = 'YES'
# os.environ['PATH'] = os.environ['PATH'] + ';F:\\Anaconda\\anaconda3\\envs\\torch_gpu\\Library\\bin'
# os.environ['GDAL_DATA'] = 'F:\\Anaconda\\anaconda3\\envs\\torch_gpu\\Library\\share\\gdal'
# os.environ['PROJ_LIB'] = 'F:\\Anaconda\\anaconda3\\envs\\torch_gpu\\Library\\share\\proj'


# 检查路径是否存在
# print(f"检查GDAL路径: {gdal_bin_path}")
# if not os.path.exists(gdal_bin_path):
#     print(f"警告: GDAL路径不存在: {gdal_bin_path}")
#     print("请检查GDAL安装路径")

# # 对于Python 3.8+，需要显式添加DLL目录
# if hasattr(os, 'add_dll_directory'):
#     print("使用os.add_dll_directory添加DLL搜索路径")
#     try:
#         os.add_dll_directory(gdal_bin_path)
#         print("成功添加DLL目录")
#     except Exception as e:
#         print(f"添加DLL目录失败: {e}")

# 尝试使用ctypes直接加载GDAL DLL
# try:
#     import ctypes
#     print("尝试使用ctypes直接加载GDAL DLL")
#     gdal_dll_path = os.path.join(gdal_bin_path, 'gdal304.dll')  # 根据你的GDAL版本调整
#     if os.path.exists(gdal_dll_path):
#         ctypes.WinDLL(gdal_dll_path)
#         print(f"成功加载 {gdal_dll_path}")
#     else:
#         print(f"GDAL DLL不存在: {gdal_dll_path}")
#         # 尝试查找其他版本的GDAL DLL
#         import glob
#         gdal_dlls = glob.glob(os.path.join(gdal_bin_path, 'gdal*.dll'))
#         if gdal_dlls:
#             print(f"找到其他GDAL DLL: {gdal_dlls}")
#             for dll in gdal_dlls:
#                 try:
#                     ctypes.WinDLL(dll)
#                     print(f"成功加载 {dll}")
#                     break
#                 except Exception as e:
#                     print(f"加载 {dll} 失败: {e}")
# except Exception as e:
#     print(f"ctypes加载GDAL DLL失败: {e}")

# try:
#     print("尝试导入GDAL...")
#     from osgeo import gdal, ogr, osr
#     print("GDAL导入成功!")
# except ImportError as e:
#     print(f"GDAL导入错误: {e}")
#     print("请检查GDAL安装和环境变量设置")
#     sys.exit(1)

import gc
from tqdm import tqdm

# 尝试导入所有模型
try:
    print("尝试导入所有模型...")
    # 导入DeepLabV3+模型
    from network.deeplabv3_plus import deeplabv3_plus
    # 导入改进版DeepLabV3+模型
    from network.deeplabv3_plus_improved import deeplabv3_plus_improved
    # 导入UNet模型
    from network.unet import UNet
    # 导入LUnet模型
    from network.lunet_improve import LUnet
    # 导入UNetGAI模型
    from network.seg_unet_gai import UNetGAI
    # 导入SegFormer模型
    from network.segformer import segformer
    # 导入改进版SegFormer模型
    from network.segformer_improved import segformer_improved
    
    # 导入UNet++模型
    from network.unetplusplus import unetplusplus
    # 导入SegNeXt模型
    from network.segnext import segnext
    # 导入改进版SegNeXt模型
    from network.segnext_improved import segnext_improved

    print("基础模型导入成功!")
except ImportError as e:
    print(f"基础模型导入失败: {e}")
    print(f"当前系统路径: {sys.path}")
    print(f"检查network模块是否存在于父目录: {os.path.exists(os.path.join(parent_dir, 'network'))}")
    sys.exit(1)

# 单独导入Vision Transformer，便于调试
try:
    print("尝试导入Vision Transformer...")
    from network.vision_transformer import create_vit_model
    print("✅ Vision Transformer导入成功!")
    VIT_AVAILABLE = True
except ImportError as e:
    print(f"❌ Vision Transformer导入失败: {e}")
    print("Vision Transformer模型将不可用")
    VIT_AVAILABLE = False
    create_vit_model = None

def create_model(args, model_path, device):
    """
    统一的模型创建函数

    Args:
        args: 命令行参数
        model_path: 模型权重路径
        device: 设备

    Returns:
        model: 创建并加载权重的模型
    """
    import torch

    model_type = args.model_type
    num_classes = args.num_classes

    print(f"创建模型: {model_type}")

    if model_type == 'deeplabv3_plus':
        model = deeplabv3_plus(backbone_type=args.backbone, num_classes=num_classes)
    elif model_type == 'deeplabv3_plus_improved':
        model = deeplabv3_plus_improved(backbone_type=args.backbone, num_classes=num_classes)
    elif model_type == 'unet':
        model = UNet(in_channels=3, num_classes=num_classes)
    elif model_type == 'lunet':
        model = LUnet(input_channels=3, num_classes=num_classes)
    elif model_type == 'unetgai':
        model = UNetGAI(in_channels=3, num_classes=num_classes)
    elif model_type == 'segformer':
        model = segformer(num_classes=num_classes, in_chans=3, backbone=args.segformer_type)
    elif model_type == 'segformer_improved':
        model = segformer_improved(num_classes=num_classes, in_chans=3, backbone=args.segformer_type)
    elif model_type == 'unetplusplus':
        model = unetplusplus(num_classes=num_classes, in_chans=3, deep_supervision=args.deep_supervision)
    elif model_type == 'segnext':
        model = segnext(num_classes=num_classes, in_chans=3, model_type=args.segnext_type)
    elif model_type == 'segnext_improved':
        model = segnext_improved(num_classes=num_classes, in_chans=3, model_type=args.segnext_improved_type)
    elif model_type == 'vision_transformer' or model_type == 'vit':
        if not VIT_AVAILABLE:
            raise ValueError(f"Vision Transformer模型不可用，导入失败。请检查 network/vision_transformer.py 文件。")

        # 先加载检查点以检测位置编码尺寸
        checkpoint = torch.load(model_path, map_location='cpu')

        # 检测位置编码的尺寸来推断训练时的图像尺寸
        pos_embed_key = None
        for key in checkpoint.keys():
            if 'pos_embed' in key:
                pos_embed_key = key
                break

        if pos_embed_key:
            pos_embed_shape = checkpoint[pos_embed_key].shape
            num_patches = pos_embed_shape[1]
            # 根据patch数量推断图像尺寸
            grid_size = int(num_patches ** 0.5)
            detected_img_size = grid_size * args.vit_patch_size
            print(f"检测到模型训练时的图像尺寸: {detected_img_size}x{detected_img_size} ({grid_size}x{grid_size} patches)")
            img_size = detected_img_size
        else:
            print(f"未找到位置编码信息，使用默认图像尺寸: {args.vit_img_size}")
            img_size = args.vit_img_size

        model = create_vit_model(
            model_type=args.vit_type,
            img_size=img_size,
            in_channels=3,
            num_classes=num_classes,
            patch_size=args.vit_patch_size
        )
    else:
        raise ValueError(f"不支持的模型类型: {model_type}")

    # 加载模型权重
    model.load_state_dict(torch.load(model_path, map_location=device))
    print("模型权重加载成功")
    model.to(device)
    model.eval()  # 设置为评估模式

    return model

# Set CUDA device if available
def setup_device(gpu_id=0):
    import torch
    if torch.cuda.is_available():
        os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
        device = torch.device('cuda')
        print(f"使用GPU: {gpu_id}, 设备名称: {torch.cuda.get_device_name(0)}")
        print(f"总显存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
    else:
        device = torch.device('cpu')
        print("CUDA不可用，使用CPU")
    
    return device

# 设置PyTorch内存管理
def setup_torch_memory():
    import torch
    torch.backends.cudnn.benchmark = True  # 加速卷积神经网络计算
    if torch.cuda.is_available():
        # 设置为确定性算法，可能会降低性能但增加可重复性
        torch.backends.cudnn.deterministic = True
        
        # 尝试设置TF32精度（仅适用于Ampere及以上架构）
        if hasattr(torch.backends.cuda, 'matmul') and hasattr(torch.backends.cuda.matmul, 'allow_tf32'):
            torch.backends.cuda.matmul.allow_tf32 = True
        if hasattr(torch.backends.cudnn, 'allow_tf32'):
            torch.backends.cudnn.allow_tf32 = True

# 读取tif数据集，按需加载数据减少内存占用
def readTif(fileName, xoff=0, yoff=0, data_width=0, data_height=0):
    dataset = gdal.Open(fileName)
    if dataset == None:
        print(fileName + "文件无法打开")
        return None, None, None, None, None, None, None
    # 栅格矩阵的列数
    width = dataset.RasterXSize
    # 栅格矩阵的行数
    height = dataset.RasterYSize
    # 波段数
    bands = dataset.RasterCount
    # 获取数据
    if(data_width == 0 and data_height == 0):
        data_width = width
        data_height = height
    data = dataset.ReadAsArray(xoff, yoff, data_width, data_height)
    # 获取仿射矩阵信息
    geotrans = dataset.GetGeoTransform()
    # 获取投影信息
    proj = dataset.GetProjection()
    # 获取nodata值
    nodata_values = []
    for i in range(1, bands + 1):
        band = dataset.GetRasterBand(i)
        nodata = band.GetNoDataValue()
        nodata_values.append(nodata)
    return width, height, bands, data, geotrans, proj, nodata_values

# 保存tif文件函数
def writeTiff(im_data, im_geotrans, im_proj, path):
    if 'int8' in im_data.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'int16' in im_data.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    if len(im_data.shape) == 3:
        im_bands, im_height, im_width = im_data.shape
    elif len(im_data.shape) == 2:
        im_data = np.array([im_data])
        im_bands, im_height, im_width = im_data.shape

    # 创建文件
    driver = gdal.GetDriverByName("GTiff")
    dataset = driver.Create(path, int(im_width), int(im_height), int(im_bands), datatype)
    if(dataset != None):
        dataset.SetGeoTransform(im_geotrans) # 写入仿射变换参数
        dataset.SetProjection(im_proj) # 写入投影
    for i in range(im_bands):
        dataset.GetRasterBand(i+1).WriteArray(im_data[i])
    dataset.FlushCache()  # 确保数据写入磁盘
    del dataset

# 将分类结果转为矢量shapefile
def raster2vector(raster_path, vector_path, field_name="class", ignore_values=None):
    # 读取路径中的栅格数据
    raster = gdal.Open(raster_path)
    if raster is None:
        print(f"无法打开栅格文件: {raster_path}")
        return

    # 获取第一个波段
    band = raster.GetRasterBand(1)

    # 读取栅格的投影信息
    prj = osr.SpatialReference()
    prj.ImportFromWkt(raster.GetProjection())

    # 创建矢量数据驱动
    drv = ogr.GetDriverByName("ESRI Shapefile")
    # 若文件已经存在,删除
    if os.path.exists(vector_path):
        drv.DeleteDataSource(vector_path)

    # 创建目标文件
    polygon = drv.CreateDataSource(vector_path)
    # 创建面图层
    poly_layer = polygon.CreateLayer(os.path.basename(vector_path)[:-4], srs=prj, geom_type=ogr.wkbMultiPolygon)
    # 添加字段
    field = ogr.FieldDefn(field_name, ogr.OFTReal)
    poly_layer.CreateField(field)

    # 检查栅格数据是否有有效值（非0值）
    band_array = band.ReadAsArray()
    has_valid_data = False

    if band_array is not None:
        # 检查是否有非0值（排除背景）
        unique_values = np.unique(band_array)
        has_valid_data = len(unique_values) > 1 or (len(unique_values) == 1 and unique_values[0] != 0)

    if has_valid_data:
        # 执行栅格到矢量的转换
        print("正在进行栅格到矢量的转换...")
        gdal.FPolygonize(band, None, poly_layer, 0)

        # 删除忽略值的要素
        if ignore_values is not None:
            print(f"删除类别值为 {ignore_values} 的要素...")
            for feature in poly_layer:
                class_value = feature.GetField(field_name)
                for ignore_value in ignore_values:
                    if class_value == ignore_value:
                        # 通过FID删除要素
                        poly_layer.DeleteFeature(feature.GetFID())
                        break

        # 检查删除后是否还有要素
        poly_layer.ResetReading()
        feature_count = poly_layer.GetFeatureCount()

        if feature_count > 0:
            print(f"矢量转换完成，共生成 {feature_count} 个要素")
        else:
            print("⚠️ 删除背景类后没有有效要素，将创建空的SHP文件")
    else:
        print("⚠️ 栅格数据中没有有效的分类结果（只有背景类），创建空的SHP文件")

    # 提交更改（即使是空文件也要保存）
    polygon.SyncToDisk()

    # 获取最终的要素数量
    poly_layer.ResetReading()
    final_feature_count = poly_layer.GetFeatureCount()

    if final_feature_count == 0:
        print(f"✅ 空的矢量文件已保存到: {vector_path}")
        print("   这是一个有效的SHP文件，只是不包含任何要素")
    else:
        print(f"✅ 矢量数据已保存到: {vector_path}，包含 {final_feature_count} 个要素")

    polygon = None
    raster = None

# tif裁剪（tif像素数据，裁剪边长）- 优化内存使用和边缘处理
def TifCroppingArray(img, SideLength):
    # 获取图像尺寸
    height, width, _ = img.shape
    
    # 计算有效步长
    effective_stride = 256 - SideLength * 2
    
    # 计算列和行上的完整块数
    ColumnNum = int((height - SideLength * 2) / effective_stride)
    RowNum = int((width - SideLength * 2) / effective_stride)
    
    # 预分配内存以提高性能
    TifArrayReturn = []
    
    # 使用进度条显示裁剪进度
    print(f"开始裁剪图像，共 {(ColumnNum+1)*(RowNum+1)} 个切片...")
    progress = tqdm(total=(ColumnNum+1)*(RowNum+1))
    
    # 裁剪完整块
    for i in range(ColumnNum):
        row_start = i * effective_stride
        row_end = row_start + 256
        
        TifArray = []
        for j in range(RowNum):
            col_start = j * effective_stride
            col_end = col_start + 256
            
            # 直接切片而不是逐像素复制
            cropped = img[row_start:row_end, col_start:col_end].copy()
            TifArray.append(cropped)
            progress.update(1)
        
        # 裁剪最后一列（每行的最后一个块）
        col_start = width - 256
        col_end = width
        cropped = img[row_start:row_end, col_start:col_end].copy()
        TifArray.append(cropped)
        progress.update(1)
        
        TifArrayReturn.append(TifArray)
    
    # 裁剪最后一行
    row_start = height - 256
    row_end = height
    TifArray = []
    
    # 最后一行的完整块
    for j in range(RowNum):
        col_start = j * effective_stride
        col_end = col_start + 256
        cropped = img[row_start:row_end, col_start:col_end].copy()
        TifArray.append(cropped)
        progress.update(1)
    
    # 右下角块
    col_start = width - 256
    col_end = width
    cropped = img[row_start:row_end, col_start:col_end].copy()
    TifArray.append(cropped)
    progress.update(1)
    
    # 添加最后一行到结果
    TifArrayReturn.append(TifArray)
    
    progress.close()
    
    # 列上的剩余数
    ColumnOver = (height - SideLength * 2) % effective_stride + SideLength
    # 行上的剩余数
    RowOver = (width - SideLength * 2) % effective_stride + SideLength
    
    return TifArrayReturn, RowOver, ColumnOver

# 标签可视化，即为第n类赋上n值
def labelVisualize(img):
    # 使用numpy的argmax函数直接在最后一个维度上找最大值的索引
    # 这比嵌套循环快几十倍
    return np.argmax(img, axis=2).astype(np.uint8)

# 创建PyTorch数据集类，用于批量处理预测
class TifDataset(torch.utils.data.Dataset):
    def __init__(self, TifArray, nodata_mask=None):
        self.TifArray = TifArray
        self.nodata_mask = nodata_mask
        self.indexes = []
        for i in range(len(TifArray)):
            for j in range(len(TifArray[0])):
                self.indexes.append((i, j))
    
    def __len__(self):
        return len(self.indexes)
    
    def __getitem__(self, idx):
        i, j = self.indexes[idx]
        img = self.TifArray[i][j] / 255.0  # 归一化
        img = np.transpose(img, (2, 0, 1))  # 从(H,W,C)转为(C,H,W)
        img_tensor = torch.from_numpy(img).float()
        
        if self.nodata_mask is not None:
            mask = self.nodata_mask[i][j]
            mask_tensor = torch.from_numpy(mask).bool()
            return img_tensor, mask_tensor, (i, j)
        return img_tensor, (i, j)

# 创建nodata掩码，标记哪些像素是nodata
def create_nodata_mask(im_data, nodata_values):
    """改进的NoData掩码创建，更好地处理不规则边界"""
    if im_data is None:
        return None
    
    print("创建改进的nodata掩码...")
    
    # 创建掩码数组，初始化为False（非nodata）
    mask = np.zeros((im_data.shape[0], im_data.shape[1]), dtype=bool)
    
    # 1. 检查标准NoData值
    if nodata_values is not None:
        for b in range(min(len(nodata_values), im_data.shape[2])):
            nodata_value = nodata_values[b]
            if nodata_value is not None:
                mask = np.logical_or(mask, im_data[:, :, b] == nodata_value)
    
    # 2. 检测边缘的无效区域（关键改进）
    height, width = im_data.shape[:2]
    edge_buffer = 50  # 边缘缓冲区大小
    
    # 创建边缘检测掩码
    edge_mask = np.zeros((height, width), dtype=bool)
    edge_mask[:edge_buffer, :] = True  # 上边缘
    edge_mask[-edge_buffer:, :] = True  # 下边缘
    edge_mask[:, :edge_buffer] = True  # 左边缘
    edge_mask[:, -edge_buffer:] = True  # 右边缘
    
    # 3. 在边缘区域检测异常像素
    for b in range(im_data.shape[2]):
        band_data = im_data[:, :, b]
        
        # 检测纯黑色和纯白色
        edge_black = (band_data == 0) & edge_mask
        edge_white = (band_data == 255) & edge_mask
        
        # 检测异常低值和高值
        edge_low = (band_data < 10) & edge_mask
        edge_high = (band_data > 245) & edge_mask
        
        mask = np.logical_or(mask, edge_black | edge_white | edge_low | edge_high)
    
    # 4. 形态学处理，平滑边界（需要安装scipy）
    try:
        from scipy import ndimage
        
        # 先膨胀再腐蚀，填充小洞
        mask = ndimage.binary_dilation(mask, iterations=3)
        mask = ndimage.binary_erosion(mask, iterations=2)
        
        # 连通域分析，保留大的连通区域
        labeled_mask, num_features = ndimage.label(mask)
        
        if num_features > 0:
            # 计算每个连通域的大小
            sizes = ndimage.sum(mask, labeled_mask, range(num_features + 1))
            
            # 只保留大于阈值的连通域
            min_size = height * width * 0.001  # 至少占总面积的0.1%
            large_regions = sizes > min_size
            
            # 创建新的掩码
            new_mask = np.zeros_like(mask)
            for i in range(1, num_features + 1):
                if large_regions[i]:
                    new_mask[labeled_mask == i] = True
            
            mask = new_mask
    except ImportError:
        print("未安装scipy，跳过形态学处理")
    
    print(f"改进算法识别到 {np.sum(mask)} 个nodata像素")
    return mask

# 从shapefile创建掩码，用于限制预测范围
def create_shp_mask(shp_path, im_geotrans, im_proj, im_width, im_height):
    """
    从shapefile创建掩码，用于限制预测范围
    """
    import os  # 显式导入，避免PyCharm警告
    if shp_path is None or not os.path.exists(shp_path):
        print(f"未提供有效的shapefile路径或文件不存在: {shp_path}")
        return None
    
    try:
        print(f"从shapefile创建掩码: {shp_path}")
        
        # 打开shapefile检查
        driver = ogr.GetDriverByName("ESRI Shapefile")
        data_source = driver.Open(shp_path, 0)
        if data_source is None:
            print(f"无法打开shapefile: {shp_path}")
            return None
        
        layer = data_source.GetLayer()
        feature_count = layer.GetFeatureCount()
        print(f"Shapefile包含 {feature_count} 个要素")
        
        if feature_count == 0:
            print("Shapefile为空，返回None")
            data_source = None
            return None
        
        # 计算像素分辨率
        pixel_width = abs(im_geotrans[1])
        pixel_height = abs(im_geotrans[5])
        
        print(f"像素分辨率: {pixel_width} x {pixel_height}")
        
        # 方法1: 使用临时文件和命令行工具（最稳定）
        import tempfile
        import subprocess
        import os
        
        # 创建临时输出文件
        with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp_file:
            temp_raster_path = tmp_file.name
        
        # 构建gdal_rasterize命令
        cmd = [
            'gdal_rasterize',
            '-burn', '1',
            '-ot', 'Byte',
            '-tr', str(pixel_width), str(pixel_height),
            '-te', str(im_geotrans[0]), 
                   str(im_geotrans[3] + im_geotrans[5] * im_height),
                   str(im_geotrans[0] + im_geotrans[1] * im_width),
                   str(im_geotrans[3]),
            '-a_srs', im_proj,
            '-at',  # 所有接触的像素都烧录
            shp_path,
            temp_raster_path
        ]
        
        try:
            print("使用gdal_rasterize命令...")
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            print("gdal_rasterize命令执行成功")
            
            # 读取结果
            result_ds = gdal.Open(temp_raster_path)
            if result_ds:
                rasterized = result_ds.ReadAsArray()
                result_ds = None
                
                # 清理临时文件
                os.unlink(temp_raster_path)
                
                if rasterized is None:
                    print("栅格化结果为空")
                    return None
                
                # 转换为布尔掩码
                mask = rasterized.astype(bool)
                print(f"Shapefile掩码创建成功，共有 {np.sum(mask)} 个像素在范围内")
                return mask
            else:
                print("无法读取栅格化结果")
                os.unlink(temp_raster_path)
                return None
                
        except subprocess.CalledProcessError as e:
            print(f"gdal_rasterize命令失败: {e}")
            print(f"错误输出: {e.stderr}")
            if os.path.exists(temp_raster_path):
                os.unlink(temp_raster_path)
        except FileNotFoundError:
            print("找不到gdal_rasterize命令，尝试使用Python API...")
            if os.path.exists(temp_raster_path):
                os.unlink(temp_raster_path)
        
        # 方法2: 使用更兼容的Python API
        print("尝试使用兼容的Python API...")
        
        # 创建临时栅格文件
        with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp_file:
            temp_raster_path = tmp_file.name
        
        try:
            # 创建输出栅格
            gtiff_driver = gdal.GetDriverByName('GTiff')
            out_ds = gtiff_driver.Create(temp_raster_path, im_width, im_height, 1, gdal.GDT_Byte)
            out_ds.SetGeoTransform(im_geotrans)
            out_ds.SetProjection(im_proj)
            
            # 初始化为0
            out_band = out_ds.GetRasterBand(1)
            out_band.Fill(0)
            
            # 使用gdal.RasterizeLayer
            gdal.RasterizeLayer(out_ds, [1], layer, burn_values=[1])
            
            # 读取结果
            rasterized = out_band.ReadAsArray()
            
            # 关闭数据集
            out_ds = None
            
            # 清理临时文件
            os.unlink(temp_raster_path)
            
            if rasterized is None:
                print("Python API栅格化失败")
                return None
            
            # 转换为布尔掩码
            mask = rasterized.astype(bool)
            print(f"Shapefile掩码创建成功，共有 {np.sum(mask)} 个像素在范围内")
            return mask
            
        except Exception as api_error:
            print(f"Python API也失败了: {api_error}")
            if os.path.exists(temp_raster_path):
                os.unlink(temp_raster_path)
        
        # 方法3: 最后的备选方案 - 手动栅格化
        print("尝试手动栅格化...")
        return manual_rasterize_shapefile(data_source, layer, im_geotrans, im_width, im_height)
        
    except Exception as e:
        print(f"创建shapefile掩码时出错: {e}")
        import traceback
        traceback.print_exc()
        return None
    finally:
        if 'data_source' in locals() and data_source:
            data_source = None

def manual_rasterize_shapefile(data_source, layer, im_geotrans, im_width, im_height):
    """手动栅格化shapefile的备选方案"""
    try:
        print("使用手动栅格化方法...")
        
        # 创建空的掩码
        mask = np.zeros((im_height, im_width), dtype=bool)
        
        # 遍历所有要素
        layer.ResetReading()
        for feature in layer:
            geom = feature.GetGeometryRef()
            if geom is None:
                continue
            
            # 获取几何体的边界
            minx, maxx, miny, maxy = geom.GetEnvelope()
            
            # 转换为像素坐标
            inv_geotrans = gdal.InvGeoTransform(im_geotrans)
            
            # 计算像素范围
            px_minx, py_maxy = gdal.ApplyGeoTransform(inv_geotrans, minx, miny)
            px_maxx, py_miny = gdal.ApplyGeoTransform(inv_geotrans, maxx, maxy)
            
            # 确保在图像范围内
            px_minx = max(0, int(px_minx))
            px_maxx = min(im_width, int(px_maxx) + 1)
            py_miny = max(0, int(py_miny))
            py_maxy = min(im_height, int(py_maxy) + 1)
            
            # 对边界框内的每个像素进行点在多边形内测试
            for py in range(py_miny, py_maxy):
                for px in range(px_minx, px_maxx):
                    # 转换回地理坐标
                    geo_x, geo_y = gdal.ApplyGeoTransform(im_geotrans, px + 0.5, py + 0.5)
                    
                    # 创建点几何
                    point = ogr.Geometry(ogr.wkbPoint)
                    point.AddPoint(geo_x, geo_y)
                    
                    # 测试点是否在多边形内
                    if geom.Contains(point) or geom.Touches(point):
                        mask[py, px] = True
        
        print(f"手动栅格化完成，共有 {np.sum(mask)} 个像素在范围内")
        return mask
        
    except Exception as e:
        print(f"手动栅格化也失败了: {e}")
        return None

# 裁剪nodata掩码，与图像块对应
def crop_nodata_mask(mask, SideLength):
    if mask is None:
        return None
    
    MaskArrayReturn = []
    ColumnNum = int((mask.shape[0] - SideLength * 2) / (256 - SideLength * 2))
    RowNum = int((mask.shape[1] - SideLength * 2) / (256 - SideLength * 2))
    
    print("裁剪nodata掩码...")
    
    for i in range(ColumnNum):
        MaskArray = []
        for j in range(RowNum):
            cropped = mask[i * (256 - SideLength * 2) : i * (256 - SideLength * 2) + 256,
                       j * (256 - SideLength * 2) : j * (256 - SideLength * 2) + 256]
            MaskArray.append(cropped)
        MaskArrayReturn.append(MaskArray)
    
    # 向前裁剪最后一列
    for i in range(ColumnNum):
        cropped = mask[i * (256 - SideLength * 2) : i * (256 - SideLength * 2) + 256,
                    (mask.shape[1] - 256) : mask.shape[1]]
        MaskArrayReturn[i].append(cropped)
    
    # 向前裁剪最后一行
    MaskArray = []
    for j in range(RowNum):
        cropped = mask[(mask.shape[0] - 256) : mask.shape[0],
                   j * (256-SideLength*2) : j * (256 - SideLength * 2) + 256]
        MaskArray.append(cropped)
    
    # 向前裁剪右下角
    cropped = mask[(mask.shape[0] - 256) : mask.shape[0],
                (mask.shape[1] - 256) : mask.shape[1]]
    MaskArray.append(cropped)
    
    # 添加到结果中
    MaskArrayReturn.append(MaskArray)
    
    return MaskArrayReturn

# 加权平均融合边缘重叠区域 - 改进版本，更好地处理不规则影像边界
def weightedFusion(result_shape, TifArray, results, num_class, RepetitiveLength, RowOver, ColumnOver):
    import numpy as np
    
    # 创建结果数组，使用uint8类型减少内存占用
    result = np.zeros(result_shape, np.uint8)
    # 创建权重数组用于边缘融合
    weight_map = np.zeros(result_shape, np.float32)
    # 创建有效数据掩码，用于标记哪些区域有有效预测（非NoData）
    valid_data_mask = np.zeros(result_shape, dtype=bool)
    
    print("开始拼接并融合结果...")
    
    # 预计算权重图 - 只需计算一次
    weight = np.ones((256, 256), np.float32)
    
    # 在边缘区域创建过渡的权重
    for k in range(RepetitiveLength):
        # 上边缘
        weight[k, :] = (k + 1) / (RepetitiveLength + 1)
        # 下边缘
        weight[255-k, :] = (k + 1) / (RepetitiveLength + 1)
        # 左边缘
        weight[:, k] = (k + 1) / (RepetitiveLength + 1)
        # 右边缘
        weight[:, 255-k] = (k + 1) / (RepetitiveLength + 1)
    
    # 计算总块数
    total_blocks = len(TifArray) * len(TifArray[0])
    
    # 使用tqdm显示进度
    progress = tqdm(total=total_blocks, desc="拼接融合进度")
    
    # 处理每个分块预测结果
    for i in range(len(TifArray)):
        for j in range(len(TifArray[0])):
            # 生成当前块的索引范围
            if i < len(TifArray) - 1 and j < len(TifArray[0]) - 1:
                # 正常的中间块
                row_start = i * (256 - 2 * RepetitiveLength) 
                row_end = row_start + 256
                col_start = j * (256 - 2 * RepetitiveLength)
                col_end = col_start + 256
            elif i == len(TifArray) - 1 and j < len(TifArray[0]) - 1:
                # 最后一行的块
                row_start = result_shape[0] - 256
                row_end = result_shape[0]
                col_start = j * (256 - 2 * RepetitiveLength)
                col_end = col_start + 256
            elif i < len(TifArray) - 1 and j == len(TifArray[0]) - 1:
                # 最后一列的块
                row_start = i * (256 - 2 * RepetitiveLength)
                row_end = row_start + 256
                col_start = result_shape[1] - 256
                col_end = result_shape[1]
            else:
                # 右下角块
                row_start = result_shape[0] - 256
                row_end = result_shape[0]
                col_start = result_shape[1] - 256
                col_end = result_shape[1]
            
            # 确保索引不超出范围
            row_end = min(row_end, result_shape[0])
            col_end = min(col_end, result_shape[1])
            
            # 处理预测结果
            img = labelVisualize(results[i][j])
            img = img.astype(np.uint8)
            
            # 检查是否为NoData区域（如果预测结果中有大量背景类）
            # 这可能表明该区域是不规则影像的边界外区域
            actual_height = row_end - row_start
            actual_width = col_end - col_start
            
            nodata_ratio = np.sum(img == 0) / (actual_height * actual_width)
            is_likely_nodata = nodata_ratio > 0.95  # 如果95%以上是背景，可能是NoData区域
            
            # 确保不超出图像边界
            if actual_height > 256 or actual_width > 256:
                actual_height = min(actual_height, 256)
                actual_width = min(actual_width, 256)
            
            # 提取当前区域的结果、权重图和有效数据掩码
            result_region = result[row_start:row_end, col_start:col_end]
            weight_region = weight_map[row_start:row_end, col_start:col_end]
            valid_region = valid_data_mask[row_start:row_end, col_start:col_end]
            
            # 提取需要的权重部分
            block_weight = weight[:actual_height, :actual_width]
            
            # 检测当前块中可能的NoData区域（假设类别0为背景或NoData）
            # 我们通过检查块的边缘是否全为0来粗略估计是否为NoData区域
            edge_pixels = np.concatenate([
                img[0, :actual_width],                   # 上边缘
                img[actual_height-1, :actual_width],     # 下边缘
                img[1:actual_height-1, 0],               # 左边缘
                img[1:actual_height-1, actual_width-1]   # 右边缘
            ])
            edge_all_zero = (np.sum(edge_pixels) == 0)
            
            # 识别当前块中的有效数据区域（非NoData）
            # 对于不规则影像，我们假设大面积连续的0值区域可能是NoData
            # 这里使用简单启发式：如果像素值>0，则认为是有效数据
            img_valid_data = (img[:actual_height, :actual_width] > 0)
            
            # 使用NumPy操作进行高效处理
            # 1. 处理第一次赋值的区域 (weight_region == 0)
            first_assign = (weight_region == 0)
            if np.any(first_assign):
                result_region[first_assign] = img[:actual_height, :actual_width][first_assign]
                weight_region[first_assign] = block_weight[first_assign]
                # 标记有效数据区域
                valid_region[first_assign & img_valid_data] = True
            
            # 2. 处理需要更新的区域
            # 创建调整后的权重图
            adjusted_weight = block_weight.copy()
            
            # 非背景掩码（类别 > 0）
            img_non_bg = (img[:actual_height, :actual_width] > 0)
            result_non_bg = (result_region > 0)
            
            # 特别处理不规则影像边界：
            # 如果当前块有有效数据，且目标区域没有被标记为有效数据，
            # 则大幅提高当前块的权重以确保边界区域被正确预测
            if np.any(img_non_bg) and not edge_all_zero and not is_likely_nodata:
                # 如果新区域有有效数据而当前区域没有，大幅提高权重
                boost_mask = np.logical_and(img_non_bg, np.logical_not(valid_region))
                if np.any(boost_mask):
                    # 大幅提高权重，确保边界区域被正确预测
                    adjusted_weight[boost_mask] *= 3.0
            
            # 常规权重调整：如果新区域是非背景而当前是背景
            # 对于不规则影像边界区域，我们需要特别注意
            if not is_likely_nodata:  # 只有当区域不太可能是NoData时才应用这个调整
                boost_mask = np.logical_and(img_non_bg, np.logical_not(result_non_bg))
                adjusted_weight[boost_mask] *= 1.5
            
            # 如果当前区域已被标记为有效数据且有非背景值，而新区域是背景，
            # 则保留当前区域的值（相当于降低新区域的权重）
            reduce_mask = np.logical_and(
                np.logical_and(np.logical_not(img_non_bg), result_non_bg),
                valid_region
            )
            adjusted_weight[reduce_mask] *= 0.5  # 降低这些区域的权重
            
            # 找出需要更新的区域 - 权重比较
            update_mask = np.logical_and(
                np.logical_not(first_assign),  # 不是第一次赋值的区域
                adjusted_weight > weight_region  # 新权重大于旧权重
            )
            
            # 更新需要更新的区域
            if np.any(update_mask):
                result_region[update_mask] = img[:actual_height, :actual_width][update_mask]
                weight_region[update_mask] = adjusted_weight[update_mask]
                # 更新有效数据标记
                valid_region[update_mask & img_valid_data] = True
            
            # 更新进度条
            progress.update(1)
    
    progress.close()
    
    # 释放内存
    del weight_map, valid_data_mask
    gc.collect()
    
    return result

# 处理UNet++模型的深度监督输出
def process_unetplusplus_output(outputs, deep_supervision=False):
    """处理UNet++模型的输出，处理深度监督情况"""
    if deep_supervision:
        # 深度监督模式下，模型返回多个输出，我们只使用最后一个（最精细的）
        return outputs[-1]
    else:
        # 非深度监督模式，直接返回输出
        return outputs

# 解析命令行参数
def parse_args():
    parser = argparse.ArgumentParser(description='遥感图像分割处理工具 - 优化版本 v1.2 (支持批量预测)')
    parser.add_argument('--image', type=str, default=r"D:\rs\seg_code\data\data4\mask_all_0.2m_RGB.tif",
                        help='输入的遥感图像路径，可以是单个文件或目录')
    parser.add_argument('--model', type=str, default=r"D:\rs\seg_code\model\segnext_best_20250612-101455.pth",
                        help='模型权重文件路径')
    parser.add_argument('--output', type=str, default=r"D:\rs\seg_code\data\data4\2\pre1.shp",
                        help='输出分割结果的路径(.shp文件)，当输入为目录时，此参数也应为目录')
    parser.add_argument('--batch_mode', action='store_true',
                        help='启用批量处理模式，处理整个目录中的图像')
    parser.add_argument('--image_dirs', type=str, nargs='+', default=None,
                        help='多个输入图像目录路径，用于批量处理多个文件夹中的图像')
    parser.add_argument('--output_dirs', type=str, nargs='+', default=None,
                        help='多个输出目录路径，与image_dirs一一对应')
    parser.add_argument('--image_ext', type=str, default='.tif',
                        help='批量模式下要处理的图像文件扩展名，默认为.tif')
    parser.add_argument('--recursive', action='store_true',
                        help='是否递归处理子目录中的图像')
    parser.add_argument('--overlap', type=float, default=0.5,
                        help='图像块之间的重叠区域百分比，默认为0.5')
    parser.add_argument('--batch_size', type=int, default=16,
                        help='批处理大小，默认为16')
    parser.add_argument('--num_workers', type=int, default=0,
                        help='数据加载的工作线程数，默认为0')
    parser.add_argument('--use_amp', action='store_true',
                        help='是否使用混合精度计算')
    parser.add_argument('--save_memory', action='store_true',
                        help='是否启用内存节省模式（可能会降低速度）')
    parser.add_argument('--gpu', type=int, default=0,
                        help='使用的GPU ID，默认为0')
    parser.add_argument('--model_type', type=str, default='segnext',
                        choices=['deeplabv3_plus', 'deeplabv3_plus_improved', 'unet', 'lunet', 'unetgai',
                                'segformer', 'segformer_improved', 'unetplusplus', 'segnext',
                                'segnext_improved', 'vision_transformer', 'vit'],
                        help='模型类型，默认为segnext')
    parser.add_argument('--backbone', type=str, default='resnet101',
                        choices=['resnet50', 'resnet101', 'xception'],
                        help='DeepLabV3+的骨干网络，默认为resnet101')
    parser.add_argument('--segformer_type', type=str, default='b5',
                        choices=['b0', 'b1', 'b2', 'b3', 'b4', 'b5'],
                        help='SegFormer模型的类型，默认为b5')
   
    parser.add_argument('--segnext_type', type=str, default='base',
                        choices=['tiny', 'small', 'base', 'large'],
                        help='SegNeXt模型的类型，默认为base')
    parser.add_argument('--segnext_improved_type', type=str, default='base',
                        choices=['tiny', 'small', 'base', 'large'],
                        help='改进版SegNeXt模型的类型，默认为base')
    parser.add_argument('--vit_type', type=str, default='vit_base',
                        choices=['vit_small', 'vit_base', 'vit_large', 'vit_huge'],
                        help='Vision Transformer模型的类型，默认为vit_base')
    parser.add_argument('--vit_patch_size', type=int, default=16,
                        help='Vision Transformer的patch大小，默认为16')
    parser.add_argument('--vit_img_size', type=int, default=224,
                        help='Vision Transformer的输入图像尺寸，默认为224')
    parser.add_argument('--deep_supervision', action='store_true',
                        help='是否使用深度监督(用于UNet++)')
    parser.add_argument('--num_classes', type=int, default=2,
                        help='类别数量，默认为2')
    parser.add_argument('--mask_shp', type=str, default=None,
                        help='可选的shapefile路径，用于限制预测范围。如果提供，只预测shapefile内部的像素')
    
    # 兼容旧的位置参数
    if len(sys.argv) > 1 and not sys.argv[1].startswith('-'):
        args = []
        if len(sys.argv) >= 2:
            args.extend(['--image', sys.argv[1]])
        if len(sys.argv) >= 3:
            args.extend(['--model', sys.argv[2]])
        if len(sys.argv) >= 4:
            args.extend(['--output', sys.argv[3]])
        if len(sys.argv) >= 5:
            args.extend(['--overlap', sys.argv[4]])
        if len(sys.argv) >= 6:
            args.extend(['--batch_size', sys.argv[5]])
        
        # 解析剩余参数
        return parser.parse_args(args)
    else:
        return parser.parse_args()

# 获取目录中的所有图像文件
def get_image_files(directory, ext='.tif', recursive=False):
    """
    获取指定目录中的所有图像文件
    
    参数:
        directory (str): 要搜索的目录路径
        ext (str): 图像文件扩展名，默认为'.tif'
        recursive (bool): 是否递归搜索子目录，默认为False
    
    返回:
        list: 匹配的图像文件路径列表，按字母顺序排序
    """
    if not os.path.exists(directory):
        print(f"错误: 目录不存在: {directory}")
        return []
        
    image_files = []
    if recursive:
        # 递归查找所有匹配的文件
        for root, _, files in os.walk(directory):
            for file in files:
                if file.lower().endswith(ext.lower()):
                    image_files.append(os.path.join(root, file))
    else:
        # 只在指定目录中查找
        for file in os.listdir(directory):
            if file.lower().endswith(ext.lower()):
                image_files.append(os.path.join(directory, file))
    
    return sorted(image_files)  # 排序，保证处理顺序一致

# 处理单个图像
def process_single_image(image_path, model_path, output_path, args, device, model=None, task_logger=None):
    """
    处理单个图像文件

    参数:
        image_path (str): 输入图像路径
        model_path (str): 模型权重文件路径
        output_path (str): 输出结果路径
        args (Namespace): 命令行参数
        device (torch.device): 计算设备(CPU/GPU)
        model (torch.nn.Module, optional): 预加载的模型，如果为None则加载新模型
        task_logger (Logger, optional): 任务专用logger，如果提供则使用它记录日志

    返回:
        tuple: (成功标志, 处理时间记录, 处理耗时秒数)
    """
    # 使用任务logger或默认的print
    def log_info(message):
        if task_logger:
            task_logger.info(message)
        else:
            print(message)

    def log_error(message):
        if task_logger:
            task_logger.error(message)
        else:
            print(f"ERROR: {message}")

    log_info(f"\n{'='*50}")
    log_info(f"开始处理图像: {image_path}")
    log_info(f"输出路径: {output_path}")
    log_info(f"{'='*50}")

    # 确保输出目录存在
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 检查输入文件是否存在
    if not os.path.exists(image_path):
        log_error(f"输入图像文件不存在: {image_path}")
        return False, [], 0

    if not os.path.exists(model_path):
        log_error(f"模型文件不存在: {model_path}")
        return False, [], 0

    # 配置参数
    area_perc = args.overlap
    batch_size = args.batch_size
    
    # 计算重叠区域长度
    RepetitiveLength = int((1 - math.sqrt(area_perc)) * 256 / 2)
    
    # 记录测试消耗时间
    testtime = []
    # 获取当前时间
    starttime = datetime.datetime.now()
    
    # 读取图像信息
    log_info(f"📖 读取图像: {image_path}")
    dataset = gdal.Open(image_path)
    if dataset is None:
        log_error(f"无法打开图像文件: {image_path}")
        return False, [], 0
    
    im_width = dataset.RasterXSize
    im_height = dataset.RasterYSize
    im_bands = dataset.RasterCount
    im_geotrans = dataset.GetGeoTransform()
    im_proj = dataset.GetProjection()
    
    # 获取nodata值
    nodata_values = []
    for i in range(1, im_bands + 1):
        band = dataset.GetRasterBand(i)
        nodata = band.GetNoDataValue()
        nodata_values.append(nodata)
    
    log_info(f"📏 图像大小: {im_width}x{im_height}, 波段数: {im_bands}")
    log_info(f"🚫 NoData值: {nodata_values}")
    log_info(f"🤖 模型类型: {args.model_type}")
    log_info(f"🔢 类别数量: {args.num_classes}")
    log_info(f"🔄 重叠区域: {RepetitiveLength} 像素")
    log_info(f"📦 批处理大小: {batch_size}")
    log_info(f"💻 计算设备: {device}")

    # 检查是否需要分块处理
    processor = LargeImageProcessor(overlap_pixels=128)
    needs_tiling = processor.should_use_tiling(im_height, im_width, im_bands)

    if needs_tiling:
        log_info("🔧 图像过大，启用分块处理模式")
        return process_large_image_with_tiling(
            image_path, model_path, output_path, args, device, model,
            processor, im_width, im_height, im_bands, im_geotrans, im_proj, nodata_values
        )

    # 原有的直接处理逻辑
    log_info("📖 直接处理模式：读取完整图像数据")

    # 读取完整图像数据
    im_data = dataset.ReadAsArray()
    im_data = im_data.swapaxes(1, 0)
    im_data = im_data.swapaxes(1, 2)

    # 释放dataset引用以减少内存使用
    dataset = None

    # 创建nodata掩码
    nodata_mask = create_nodata_mask(im_data, nodata_values)
    
    # 如果提供了shapefile路径，创建shapefile掩码
    shp_mask = None
    if args.mask_shp:
        shp_mask = create_shp_mask(args.mask_shp, im_geotrans, im_proj, im_width, im_height)
        if shp_mask is not None:
            # 如果同时有nodata掩码和shapefile掩码，合并它们
            if nodata_mask is not None:
                outside_shp = ~shp_mask
                nodata_mask = np.logical_or(nodata_mask, outside_shp)
            else:
                nodata_mask = ~shp_mask
    
    # 裁剪图像
    TifArray, RowOver, ColumnOver = TifCroppingArray(im_data, RepetitiveLength)
    
    # 裁剪nodata掩码
    mask_array = None
    if nodata_mask is not None:
        mask_array = crop_nodata_mask(nodata_mask, RepetitiveLength)
    
    # 释放原始图像数据内存
    del im_data
    if nodata_mask is not None:
        del nodata_mask
    gc.collect()
    
    endtime = datetime.datetime.now()
    text = "读取tif并裁剪预处理完毕,目前耗时间: " + str((endtime - starttime).seconds) + "s"
    log_info(text)
    testtime.append(text)

    # 如果没有传入模型，则加载模型
    if model is None:
        log_info("🔧 创建模型...")
        model = create_model(args, model_path, device)
        log_info("✅ 模型创建完成")
    
    # 创建数据集和数据加载器
    dataset = TifDataset(TifArray, mask_array)
    dataloader = torch.utils.data.DataLoader(
        dataset, 
        batch_size=batch_size, 
        shuffle=False, 
        num_workers=args.num_workers,
        pin_memory=True
    )
    
    # 批量预测
    results = [[] for _ in range(len(TifArray))]
    log_info("🧠 开始模型预测...")
    progress = tqdm(total=len(dataset))
    
    # 记录预测开始时间
    pred_start_time = datetime.datetime.now()
    pred_count = 0
    
    with torch.no_grad():  # 不计算梯度，节省内存
        for batch in dataloader:
            if mask_array is not None:
                imgs, masks, indices = batch
                imgs = imgs.to(device, non_blocking=True)
                masks = masks.to(device, non_blocking=True)
                
                # 使用torch.cuda.amp.autocast进行混合精度计算，加速推理
                with torch.cuda.amp.autocast(enabled=device.type=='cuda' and args.use_amp):
                    outputs = model(imgs)
                    
                    # 处理UNet++深度监督的情况
                    if args.model_type == 'unetplusplus' and args.deep_supervision:
                        outputs = process_unetplusplus_output(outputs, args.deep_supervision)
                
                # 处理nodata区域
                for k in range(outputs.size(0)):
                    output = outputs[k].cpu().numpy()
                    output = np.transpose(output, (1, 2, 0))
                    
                    # 获取索引
                    i, j = indices[0][k].item(), indices[1][k].item()
                    
                    # 处理nodata区域
                    mask = masks[k].cpu().numpy()
                    # 使用向量化操作代替嵌套循环
                    if np.any(mask):
                        # 创建扩展维度的掩码
                        expanded_mask = np.expand_dims(mask, axis=2)
                        # 将背景类概率设为1，其他设为0
                        bg_mask = np.zeros_like(output)
                        bg_mask[:, :, 0] = 1
                        
                        # 改进：为了更好地处理不规则影像边界
                        # 我们需要确保NoData区域的背景类概率非常高，以避免边界混淆
                        bg_mask[:, :, 0] = 1.0  # 确保背景类概率为1
                        
                        # 应用掩码
                        output = np.where(expanded_mask, bg_mask, output)
                        
                        # 额外处理：为了防止NoData区域被错误分类
                        # 在NoData区域周围创建一个过渡区域，减少边界处的错误分类
                        # 这有助于减少不规则影像边界处的预测错误
                        from scipy import ndimage
                        # 对掩码进行膨胀，创建一个过渡区
                        dilated_mask = ndimage.binary_dilation(mask, iterations=3)
                        transition_mask = dilated_mask & ~mask  # 只保留膨胀部分
                        
                        # 在过渡区域，增强背景类的概率
                        if np.any(transition_mask):
                            transition_mask_expanded = np.expand_dims(transition_mask, axis=2)
                            # 创建过渡区域的权重
                            transition_weight = np.zeros_like(output)
                            transition_weight[:, :, 0] = 0.7  # 背景类权重
                            # 应用过渡区域的权重
                            weighted_output = output * (1 - transition_weight) + transition_weight
                            output = np.where(transition_mask_expanded, weighted_output, output)
                    
                    # 保存结果
                    if i >= len(results):
                        results.append([])
                    while j >= len(results[i]):
                        results[i].append(None)
                    results[i][j] = output
            else:
                imgs, indices = batch
                imgs = imgs.to(device, non_blocking=True)
                
                # 使用混合精度计算
                with torch.cuda.amp.autocast(enabled=device.type=='cuda' and args.use_amp):
                    outputs = model(imgs)
                    
                    # 处理UNet++深度监督的情况
                    if args.model_type == 'unetplusplus' and args.deep_supervision:
                        outputs = process_unetplusplus_output(outputs, args.deep_supervision)
                
                # 处理输出
                for k in range(outputs.size(0)):
                    output = outputs[k].cpu().numpy()
                    output = np.transpose(output, (1, 2, 0))
                    
                    # 获取索引
                    i, j = indices[0][k].item(), indices[1][k].item()
                    
                    # 保存结果
                    if i >= len(results):
                        results.append([])
                    while j >= len(results[i]):
                        results[i].append(None)
                    results[i][j] = output
            
            # 主动释放GPU内存
            if device.type == 'cuda' and args.save_memory:
                torch.cuda.empty_cache()
            
            # 更新预测计数和进度条
            pred_count += imgs.size(0)
            progress.update(imgs.size(0))
            
            # 每处理10个批次显示一次处理速度
            if pred_count % (batch_size * 10) == 0:
                elapsed = (datetime.datetime.now() - pred_start_time).total_seconds()
                speed = pred_count / elapsed if elapsed > 0 else 0
                progress.set_postfix({"速度": f"{speed:.2f}it/s"})
    
    progress.close()
    
    # 计算并显示平均处理速度
    pred_elapsed = (datetime.datetime.now() - pred_start_time).total_seconds()
    avg_speed = len(dataset) / pred_elapsed if pred_elapsed > 0 else 0
    print(f"预测平均速度: {avg_speed:.2f}it/s")
    
    endtime = datetime.datetime.now()
    text = "模型预测完毕,目前耗时间: " + str((endtime - starttime).seconds) + "s"
    print(text)
    testtime.append(text)

    # 释放数据集和数据加载器内存
    del dataset, dataloader
    gc.collect()

    if device.type == 'cuda' and args.save_memory:
        torch.cuda.empty_cache()

    # 保存结果 - 使用加权融合处理重叠区域
    result_shape = (im_height, im_width)

    # 调试信息 - 检查所有参数是否正确定义
    print(f"调试: result_shape = {result_shape}")
    print(f"调试: TifArray type = {type(TifArray)}, length = {len(TifArray) if TifArray else 'None'}")
    print(f"调试: results type = {type(results)}, length = {len(results) if results else 'None'}")
    print(f"调试: args.num_classes = {args.num_classes}")
    print(f"调试: RepetitiveLength = {RepetitiveLength}")
    print(f"调试: RowOver = {RowOver}")
    print(f"调试: ColumnOver = {ColumnOver}")

    result_data = weightedFusion(result_shape, TifArray, results, args.num_classes, RepetitiveLength, RowOver, ColumnOver)

    # 评估预测质量
    if mask_array is not None:
        evaluate_prediction_quality(result_data, mask_array)
        # 释放mask_array内存
        del mask_array
    else:
        print("没有掩膜数据，跳过预测质量评估")

    # 释放预测结果内存
    del results, TifArray
    gc.collect()

    # 写入结果 - 修改为直接输出shp文件
    print(f"保存结果到: {output_path}")

    # 检查输出路径的扩展名，如果是.tif则改为.shp
    if output_path.lower().endswith('.tif'):
        shp_output_path = output_path[:-4] + '.shp'
    else:
        shp_output_path = output_path
        if not shp_output_path.lower().endswith('.shp'):
            shp_output_path += '.shp'

    # 创建临时tif文件用于转换
    import tempfile
    with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as tmp_file:
        temp_tif_path = tmp_file.name

    try:
        # 先保存为临时tif文件
        print("创建临时栅格文件...")
        writeTiff(result_data, im_geotrans, im_proj, temp_tif_path)

        # 转换为shapefile
        print(f"转换为矢量文件: {shp_output_path}")
        raster2vector(temp_tif_path, shp_output_path, field_name="class", ignore_values=[0])

        print(f"矢量转换完成！结果已保存到: {shp_output_path}")

    finally:
        # 清理临时文件
        if os.path.exists(temp_tif_path):
            os.unlink(temp_tif_path)
            print("临时文件已清理")

    endtime = datetime.datetime.now()
    text = "结果拼接和矢量转换完毕,总耗时间: " + str((endtime - starttime).seconds) + "s"
    print(text)
    testtime.append(text)

    # 返回处理完成的信息和时间记录
    return True, testtime, (endtime - starttime).seconds

# 批量处理目录中的图像
def process_image_directory(input_dir, model_path, output_dir, args, device):
    """
    批量处理目录中的所有图像
    
    参数:
        input_dir (str): 输入图像目录路径
        model_path (str): 模型权重文件路径
        output_dir (str): 输出结果目录路径
        args (Namespace): 命令行参数
        device (torch.device): 计算设备(CPU/GPU)
    
    返回:
        bool: 是否所有文件都成功处理
    """
    print(f"\n{'='*80}")
    print(f"批量处理目录: {input_dir}")
    print(f"输出目录: {output_dir}")
    print(f"{'='*80}")
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取目录中的所有图像文件
    image_files = get_image_files(input_dir, args.image_ext, args.recursive)
    if not image_files:
        print(f"在目录 {input_dir} 中未找到扩展名为 {args.image_ext} 的图像文件")
        return False
    
    print(f"找到 {len(image_files)} 个图像文件")
    
    # 加载模型（只加载一次，所有图像共用）
    model = create_model(args, model_path, device)

    # 调用完整的处理函数
    return complete_process_image_directory(input_dir, model_path, output_dir, args, device, model)

# 处理预测结果的边界处理函数
def process_prediction_with_boundary_handling(results, mask_array, result_shape):
    """处理预测结果，特别处理边界区域"""
    print("处理预测结果，优化边界区域...")
    
    # 创建最终结果数组和权重图
    result = np.zeros(result_shape, dtype=np.uint8)
    weight_map = np.zeros(result_shape, dtype=np.float32)
    valid_data_mask = np.zeros(result_shape, dtype=bool)
    
    # 创建权重模板
    weight = np.ones((256, 256), dtype=np.float32)
    
    # 使用进度条
    from tqdm import tqdm
    total_blocks = sum(len(row) for row in results if row is not None)
    progress = tqdm(total=total_blocks, desc="拼接预测结果")
    
    for i in range(len(results)):
        for j in range(len(results[i])):
            if results[i][j] is None:
                continue
                
            # 计算在最终结果中的位置
            if i < len(results) - 1:
                row_start = i * (256 - 32)
                row_end = row_start + 256
            else:
                row_start = result_shape[0] - 256
                row_end = result_shape[0]
            
            if j < len(results[i]) - 1:
                col_start = j * (256 - 32)
                col_end = col_start + 256
            else:
                col_start = result_shape[1] - 256
                col_end = result_shape[1]
            
            # 确保索引不超出范围
            row_end = min(row_end, result_shape[0])
            col_end = min(col_end, result_shape[1])
            
            # 处理预测结果
            img = labelVisualize(results[i][j])
            img = img.astype(np.uint8)
            
            actual_height = row_end - row_start
            actual_width = col_end - col_start
            
            # 提取当前区域
            result_region = result[row_start:row_end, col_start:col_end]
            weight_region = weight_map[row_start:row_end, col_start:col_end]
            valid_region = valid_data_mask[row_start:row_end, col_start:col_end]
            
            block_weight = weight[:actual_height, :actual_width]
            
            # 识别有效数据区域
            img_valid_data = (img[:actual_height, :actual_width] > 0)
            
            # 处理NoData区域
            if mask_array is not None and i < len(mask_array) and j < len(mask_array[0]):
                mask_block = mask_array[i][j]
                if mask_block is not None:
                    # 将NoData区域设为背景
                    img[:actual_height, :actual_width][mask_block[:actual_height, :actual_width]] = 0
                    img_valid_data = img_valid_data & ~mask_block[:actual_height, :actual_width]
            
            # 权重融合
            first_assign = (weight_region == 0)
            if np.any(first_assign):
                result_region[first_assign] = img[:actual_height, :actual_width][first_assign]
                weight_region[first_assign] = block_weight[first_assign]
                valid_region[first_assign & img_valid_data] = True
            
            # 更新需要更新的区域
            update_mask = np.logical_and(
                np.logical_not(first_assign),
                block_weight > weight_region
            )
            
            if np.any(update_mask):
                result_region[update_mask] = img[:actual_height, :actual_width][update_mask]
                weight_region[update_mask] = block_weight[update_mask]
                valid_region[update_mask & img_valid_data] = True
            
            progress.update(1)
    
    progress.close()
    return result

# 评估预测质量函数
def evaluate_prediction_quality(result_data, mask_array):
    """评估预测质量，特别关注边界区域的处理效果"""
    print("评估预测质量...")

    total_pixels = result_data.size
    background_pixels = np.sum(result_data == 0)
    target_pixels = total_pixels - background_pixels

    print(f"总像素数: {total_pixels:,}")
    print(f"背景像素数: {background_pixels:,} ({background_pixels/total_pixels*100:.2f}%)")
    print(f"目标像素数: {target_pixels:,} ({target_pixels/total_pixels*100:.2f}%)")

    if mask_array is not None:
        # 统计NoData区域的处理情况
        total_nodata = 0

        for i in range(len(mask_array)):
            for j in range(len(mask_array[i])):
                if mask_array[i][j] is not None:
                    mask_block = mask_array[i][j]
                    total_nodata += np.sum(mask_block)

        print(f"检测到NoData像素: {total_nodata:,}")
        print("边界处理算法已应用，有助于减少锯齿边缘的预测错误")

    # 检查预测结果的连续性（简单的边缘检测）
    try:
        from scipy import ndimage
        # 计算边缘像素数量
        edges = ndimage.sobel(result_data.astype(float))
        edge_pixels = np.sum(edges > 0)
        edge_ratio = edge_pixels / total_pixels

        print(f"边缘像素数: {edge_pixels:,} ({edge_ratio*100:.2f}%)")

        if edge_ratio < 0.1:
            print("✓ 预测结果较为平滑，边界处理效果良好")
        elif edge_ratio < 0.2:
            print("⚠ 预测结果有一定锯齿，但在可接受范围内")
        else:
            print("⚠ 预测结果锯齿较多，建议检查模型或调整参数")

    except ImportError:
        print("未安装scipy，跳过边缘质量评估")

# 继续process_image_directory函数的实现
def complete_process_image_directory(input_dir, model_path, output_dir, args, device, model):
    """完成批量处理目录中图像的函数"""
    # 获取目录中的所有图像文件
    image_files = get_image_files(input_dir, args.image_ext, args.recursive)
    if not image_files:
        print(f"在目录 {input_dir} 中未找到扩展名为 {args.image_ext} 的图像文件")
        return False

    print(f"找到 {len(image_files)} 个图像文件")

    # 创建处理结果记录
    processed_files = []
    failed_files = []
    processing_times = []

    # 批量处理所有图像
    for i, image_path in enumerate(image_files):
        try:
            # 构建输出文件路径 - 修改为.shp扩展名
            rel_path = os.path.relpath(image_path, input_dir)
            # 将输入文件的扩展名改为.shp
            if rel_path.lower().endswith('.tif'):
                rel_path = rel_path[:-4] + '.shp'
            elif rel_path.lower().endswith('.tiff'):
                rel_path = rel_path[:-5] + '.shp'
            else:
                # 如果不是tif文件，添加.shp扩展名
                rel_path = os.path.splitext(rel_path)[0] + '.shp'

            output_path = os.path.join(output_dir, rel_path)
            
            # 确保输出目录存在
            output_file_dir = os.path.dirname(output_path)
            if output_file_dir and not os.path.exists(output_file_dir):
                os.makedirs(output_file_dir)
            
            print(f"\n处理图像 [{i+1}/{len(image_files)}]: {rel_path}")
            
            # 处理单个图像
            success, _, time_cost = process_single_image(
                image_path, model_path, output_path, args, device, model)
            
            if success:
                processed_files.append(rel_path)
                processing_times.append(time_cost)
                print(f"成功处理图像: {rel_path}, 耗时: {time_cost}秒")
            else:
                failed_files.append(rel_path)
                print(f"处理图像失败: {rel_path}")
        
        except Exception as e:
            print(f"处理图像 {image_path} 时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            failed_files.append(os.path.relpath(image_path, input_dir))
    
    # 清理模型内存
    del model
    gc.collect()
    if device.type == 'cuda':
        torch.cuda.empty_cache()
    
    # 打印处理统计
    print(f"\n{'='*80}")
    print(f"目录处理完成: {input_dir}")
    print(f"总图像数: {len(image_files)}")
    print(f"成功处理: {len(processed_files)}")
    print(f"处理失败: {len(failed_files)}")
    
    if processing_times:
        avg_time = sum(processing_times) / len(processing_times)
        print(f"平均处理时间: {avg_time:.2f}秒/图像")
    
    if failed_files:
        print("\n处理失败的文件:")
        for f in failed_files:
            print(f" - {f}")
    
    print(f"{'='*80}")
    
    return len(failed_files) == 0

def main():
    """
    主函数 - 处理命令行参数并执行相应的图像分割处理任务
    
    功能:
    1. 解析命令行参数
    2. 设置GPU/CPU设备和内存管理
    3. 根据参数选择执行模式:
       - 多目录批量处理模式：处理多个输入目录中的图像
       - 单目录批量处理模式：处理单个目录中的所有图像
       - 单文件处理模式：处理单个图像文件
    """
    # 解析命令行参数
    args = parse_args()
    
    # 配置参数
    area_perc = args.overlap
    batch_size = args.batch_size
    
    # 导入torch以避免作用域问题
    import torch
    
    # 设置使用的GPU和内存管理
    device = setup_device(args.gpu)
    setup_torch_memory()
    
    # 监控内存使用
    def print_memory_usage():
        if torch.cuda.is_available():
            print(f"GPU内存: 已分配 {torch.cuda.memory_allocated() / 1024**3:.2f} GB, 缓存 {torch.cuda.memory_reserved() / 1024**3:.2f} GB")
        try:
            import psutil
            print(f"系统内存使用: {psutil.virtual_memory().percent}%, 已用: {psutil.virtual_memory().used / 1024**3:.2f} GB")
        except ImportError:
            print("未安装psutil，无法监控系统内存")
    
    # 打印模型和处理信息
    print(f"模型路径: {args.model}")
    print(f"模型类型: {args.model_type}")
    print(f"类别数量: {args.num_classes}")
    print(f"重叠区域百分比: {area_perc}")
    print(f"批处理大小: {batch_size}")
    
    # 根据模型类型打印特定参数
    if args.model_type == 'deeplabv3_plus':
        print(f"骨干网络: {args.backbone}")
    elif args.model_type == 'segformer':
        print(f"SegFormer类型: {args.segformer_type}")
    
    elif args.model_type == 'unetplusplus':
        print(f"深度监督: {args.deep_supervision}")
        
    # 打印shapefile掩码信息
    if args.mask_shp:
        print(f"使用Shapefile掩码: {args.mask_shp}")
    
    # 根据不同的处理模式执行相应的处理
    if args.image_dirs and args.output_dirs:
        # 多目录批量处理模式
        # 这种模式允许用户同时处理多个目录中的图像，每个输入目录对应一个输出目录
        if len(args.image_dirs) != len(args.output_dirs):
            print("错误: 输入目录数量和输出目录数量必须相同")
            return
        
        print(f"\n{'='*80}")
        print(f"多目录批量处理模式")
        print(f"共 {len(args.image_dirs)} 个目录需要处理")
        print(f"{'='*80}")
        
        for i, (input_dir, output_dir) in enumerate(zip(args.image_dirs, args.output_dirs)):
            print(f"\n正在处理目录对 [{i+1}/{len(args.image_dirs)}]:")
            print(f"输入目录: {input_dir}")
            print(f"输出目录: {output_dir}")
            
            # 处理单个目录
            process_image_directory(input_dir, args.model, output_dir, args, device)
            
            # 主动清理内存
            gc.collect()
            if device.type == 'cuda':
                torch.cuda.empty_cache()
        
        print(f"\n{'='*80}")
        print(f"所有目录处理完成")
        print(f"{'='*80}")
        
    elif args.batch_mode or os.path.isdir(args.image):
        # 单目录批量处理模式
        # 这种模式处理单个目录中的所有图像
        # 可以通过--batch_mode显式启用，或者当--image参数指向目录时自动启用
        input_dir = args.image
        output_dir = args.output
        
        if not os.path.isdir(input_dir):
            print(f"错误: 输入路径不是目录: {input_dir}")
            return
        
        print(f"\n{'='*80}")
        print(f"单目录批量处理模式")
        print(f"输入目录: {input_dir}")
        print(f"输出目录: {output_dir}")
        print(f"{'='*80}")
        
        # 处理单个目录
        process_image_directory(input_dir, args.model, output_dir, args, device)
        
    else:
        # 单文件处理模式
        # 最基本的模式，处理单个图像文件
        TifPath = args.image
        ModelPath = args.model
        ResultPath = args.output
        
        print(f"\n{'='*80}")
        print(f"单文件处理模式")
        print(f"输入图像: {TifPath}")
        print(f"输出路径: {ResultPath}")
        print(f"{'='*80}")
        
        # 处理单个文件
        try:
            print_memory_usage()
        except Exception as e:
            print(f"监控内存使用失败: {e}")
            
        success, testtime, _ = process_single_image(TifPath, ModelPath, ResultPath, args, device)
        
        if success:
            # 记录时间日志
            time = datetime.datetime.strftime(datetime.datetime.now(), '%Y%m%d-%H%M%S')
            with open(f'timelog_{time}.txt', 'w') as f:
                for i in range(len(testtime)):
                    f.write(testtime[i])
                    f.write("\r\n")
            
            # 打印结果信息和使用提示
            print("\n" + "="*80)
            print(f"分割结果已保存到: {ResultPath}")
            
            # 打印分类信息
            if args.num_classes == 2:
                print("\n二分类结果说明:")
                print("  - 像素值0: 背景")
                print("  - 像素值1: 目标")
            else:
                print(f"\n多分类结果说明 (共{args.num_classes}类):")
                print("  - 像素值0: 背景")
                for i in range(1, args.num_classes):
                    print(f"  - 像素值{i}: 类别{i}")
            
            print("\n提示:")
            print("  - 可以使用GIS软件(如ArcGIS、QGIS)打开结果进行可视化")
            print("  - 如需转换为矢量格式，可使用raster2vector函数")
            print("  - 如果边界区域预测不准确，请尝试使用--mask_shp参数提供区域shapefile")
            print("="*80)
        else:
            print(f"\n处理失败: {TifPath}")

if __name__ == "__main__":
    main() 












