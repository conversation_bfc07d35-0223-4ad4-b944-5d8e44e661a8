2025-08-29 15:51:10,068 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250829_155110.log
2025-08-29 15:51:10,075 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:51:10,077 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:51:10,160 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:51:10,165 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:51:10,166 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:51:10,184 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:51:10,187 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:51:10,187 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:51:10,205 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:51:10,208 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:51:10,209 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:51:10,226 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:51:10,229 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:51:10,232 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:51:10,249 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:51:10,251 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-29 15:51:10,252 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-29 15:51:10,269 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-29 15:51:10,303 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-29 15:51:10,312 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-29 15:51:15,363 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-29 15:51:15,376 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-29 15:51:15,381 - analysis_executor - INFO - 加载了 28 个任务状态
