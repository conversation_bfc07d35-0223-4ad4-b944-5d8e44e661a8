2025-08-28 16:23:16,538 - root - INFO - 日志系统初始化完成，日志文件：D:\Drone_Project\geoserverAPIDJV2\geoserver_api\core\..\..\logs\geoserver_20250828_162316.log
2025-08-28 16:23:16,540 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:23:16,541 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:23:16,565 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:23:16,570 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:23:16,571 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:23:16,585 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:23:16,587 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:23:16,588 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:23:16,603 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:23:16,605 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:23:16,606 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:23:16,619 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:23:16,621 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:23:16,622 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:23:16,636 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:23:16,638 - root - INFO - 已连接到GeoServer：http://localhost:8085/geoserver
2025-08-28 16:23:16,638 - root - INFO - 正在检查GeoServer连接: http://localhost:8085/geoserver/rest/about/version.json
2025-08-28 16:23:16,650 - root - INFO - 成功连接到GeoServer，版本: 2.26.1
2025-08-28 16:23:16,669 - batch_executor - INFO - 加载了 88 个任务状态
2025-08-28 16:23:16,677 - geo_publisher - INFO - 加载了 61 个任务状态
2025-08-28 16:23:22,025 - tif_executor - INFO - 加载了 78 个任务状态
2025-08-28 16:23:22,043 - AIModelProcessor - INFO - 新版AI处理器初始化成功，使用动态导入模式
2025-08-28 16:23:22,047 - analysis_executor - INFO - 加载了 28 个任务状态
