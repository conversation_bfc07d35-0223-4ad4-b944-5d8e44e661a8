#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试详细日志的影像范围提取功能
"""

import requests
import time
import json

def test_with_detailed_logging():
    """测试带详细日志的影像范围提取"""
    print("🧪 测试带详细日志的影像范围提取...")
    
    # 测试参数
    base_url = "http://127.0.0.1:8091/api/analysis"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    try:
        print(f"\n📁 测试文件: {test_image_path}")
        
        # 启动任务
        print("\n1️⃣ 启动影像范围提取任务...")
        params = {
            'image': test_image_path,
            'simplify_tolerance': 1.0,
            'min_area': 1000.0
        }
        
        response = requests.get(f"{base_url}/image-extent-extraction/", params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务启动成功")
            print(f"📋 任务ID: {result['data']['task_id']}")
            
            task_id = result['data']['task_id']
            
            # 监控任务状态
            print(f"\n2️⃣ 监控任务状态...")
            max_wait_time = 120  # 最大等待2分钟
            start_time = time.time()
            
            while time.time() - start_time < max_wait_time:
                try:
                    status_response = requests.get(f"{base_url}/status/", params={'task_id': task_id}, timeout=10)
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()['data']
                        task_status = status_data['task_status']
                        message = status_data.get('message', '')
                        
                        print(f"📊 状态: {task_status} - {message}")
                        
                        if task_status == '完成':
                            print("\n✅ 任务完成！")
                            return True
                            
                        elif task_status == '失败':
                            print(f"\n❌ 任务失败: {message}")
                            
                            # 获取详细日志
                            print("\n📋 获取详细日志...")
                            log_response = requests.get(f"{base_url}/logs/", params={'task_id': task_id}, timeout=10)
                            if log_response.status_code == 200:
                                log_data = log_response.json()['data']
                                logs = log_data.get('logs', [])
                                
                                print("\n🔍 完整任务日志:")
                                print("=" * 80)
                                for log_line in logs:
                                    print(log_line)
                                print("=" * 80)
                            
                            return False
                        
                        time.sleep(3)
                    else:
                        print(f"❌ 状态查询失败: {status_response.status_code}")
                        return False
                        
                except Exception as e:
                    print(f"❌ 状态查询异常: {e}")
                    time.sleep(5)
                    continue
            
            print("\n⏰ 任务超时")
            
            # 即使超时也获取日志
            print("\n📋 获取超时任务的日志...")
            try:
                log_response = requests.get(f"{base_url}/logs/", params={'task_id': task_id}, timeout=10)
                if log_response.status_code == 200:
                    log_data = log_response.json()['data']
                    logs = log_data.get('logs', [])
                    
                    print("\n🔍 超时任务日志:")
                    print("=" * 80)
                    for log_line in logs[-50:]:  # 显示最后50行
                        print(log_line)
                    print("=" * 80)
            except Exception as e:
                print(f"❌ 获取日志失败: {e}")
            
            return False
            
        else:
            print(f"❌ 任务启动失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_simple_case():
    """测试简单情况"""
    print("\n🧪 测试简单参数...")
    
    base_url = "http://127.0.0.1:8091/api/analysis/image-extent-extraction/"
    test_image_path = "D:/Drone_Project/nginxData/ODM/Output/20250705171601/20250705171601_out.tif"
    
    # 使用更宽松的参数
    params = {
        'image': test_image_path,
        'simplify_tolerance': 10.0,  # 更大的简化容差
        'min_area': 100.0           # 更小的面积阈值
    }
    
    try:
        response = requests.get(base_url, params=params, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 简单测试任务启动成功: {result['data']['task_id']}")
            return result['data']['task_id']
        else:
            print(f"❌ 简单测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 简单测试异常: {e}")
        return None

def analyze_logs(task_id):
    """分析日志找出问题"""
    print(f"\n🔍 分析任务 {task_id} 的日志...")
    
    try:
        base_url = "http://127.0.0.1:8091/api/analysis"
        log_response = requests.get(f"{base_url}/logs/", params={'task_id': task_id}, timeout=10)
        
        if log_response.status_code == 200:
            log_data = log_response.json()['data']
            logs = log_data.get('logs', [])
            
            print("\n📊 日志分析结果:")
            
            # 统计不同类型的日志
            info_count = sum(1 for log in logs if ' - INFO - ' in log)
            error_count = sum(1 for log in logs if ' - ERROR - ' in log)
            warning_count = sum(1 for log in logs if ' - WARNING - ' in log)
            
            print(f"📈 日志统计: INFO={info_count}, ERROR={error_count}, WARNING={warning_count}")
            
            # 查找关键步骤
            steps_found = []
            for i, log in enumerate(logs):
                if '步骤' in log:
                    steps_found.append((i, log))
            
            print(f"🔄 找到 {len(steps_found)} 个处理步骤:")
            for i, (line_num, log) in enumerate(steps_found):
                print(f"  {i+1}. {log.split(' - INFO - ')[-1]}")
            
            # 查找错误信息
            errors = [log for log in logs if ' - ERROR - ' in log]
            if errors:
                print(f"\n❌ 发现 {len(errors)} 个错误:")
                for i, error in enumerate(errors):
                    print(f"  {i+1}. {error.split(' - ERROR - ')[-1]}")
            
            return True
            
        else:
            print(f"❌ 获取日志失败: {log_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 日志分析异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始详细日志测试")
    
    # 测试主要功能
    success = test_with_detailed_logging()
    
    if not success:
        print("\n🔧 尝试简单参数测试...")
        task_id = test_simple_case()
        
        if task_id:
            # 等待一段时间让任务执行
            print("⏳ 等待任务执行...")
            time.sleep(30)
            
            # 分析日志
            analyze_logs(task_id)
    
    print("\n📖 调试说明:")
    print("1. 现在每个处理步骤都有详细日志")
    print("2. 错误信息包含完整的堆栈跟踪")
    print("3. 可以通过日志精确定位问题位置")
    print("4. 检查影像文件是否可以正常读取")
    print("5. 检查GDAL和OpenCV是否正确安装")
